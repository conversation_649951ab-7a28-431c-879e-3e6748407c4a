package com.hm.base.activiti.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("提交任务实体类")
public class SubmitDto {
    @ApiModelProperty("用户ID")
    @NotNull(message = "用户id不能为空")
    private String userId;

    @ApiModelProperty("流程定义key")
    @NotNull(message = "流程定义key不能为空")
    private String processDefinitionKey;

    @ApiModelProperty("业务key")
    @NotNull(message = "业务key不能为空")
    private String businessKey;
}
