package com.hm.api.consumer.feign.base.dto;

import com.hm.api.common.aop.LogField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @author: curry.wu
 * @create: 2020/10/25 14:26
 **/
@ApiModel(value = "本期贷方发生额")
public class B200006Entity implements Serializable {

    private static final long serialVersionUID = -8573388144817165351L;

    @LogField(tableName = "b200006", value = "ent_id", valueName = "企业ID")
    @ApiModelProperty(value = "企业ID")
    private String entId;

    @LogField(tableName = "b200006", value = "tax_year", valueName = "所属时间")
    @ApiModelProperty(value = "所属时间")
    private String taxYear;

    @LogField(tableName = "b200006", value = "subject_month", valueName = "月份(年度等于12)")
    @ApiModelProperty(value = "月份(年度等于12)")
    private Integer subjectMonth;

    @LogField(tableName = "b200006", value = "b200006_1001", valueName = "库存现金")
    @ApiModelProperty(value = "库存现金")
    private BigDecimal b2000061001;

    @LogField(tableName = "b200006", value = "b200006_1002", valueName = "银行存款")
    @ApiModelProperty(value = "银行存款")
    private BigDecimal b2000061002;

    @LogField(tableName = "b200006", value = "b200006_1003", valueName = "存放中央银行款项")
    @ApiModelProperty(value = "存放中央银行款项")
    private BigDecimal b2000061003;

    @LogField(tableName = "b200006", value = "b200006_1011", valueName = "存放同业")
    @ApiModelProperty(value = "存放同业")
    private BigDecimal b2000061011;

    @LogField(tableName = "b200006", value = "b200006_1012", valueName = "其他货币资金")
    @ApiModelProperty(value = "其他货币资金")
    private BigDecimal b2000061012;

    @LogField(tableName = "b200006", value = "b200006_1021", valueName = "结算备付金")
    @ApiModelProperty(value = "结算备付金")
    private BigDecimal b2000061021;

    @LogField(tableName = "b200006", value = "b200006_1031", valueName = "存出保证金")
    @ApiModelProperty(value = "存出保证金")
    private BigDecimal b2000061031;

    @LogField(tableName = "b200006", value = "b200006_1101", valueName = "交易性金融资产")
    @ApiModelProperty(value = "交易性金融资产")
    private BigDecimal b2000061101;

    @LogField(tableName = "b200006", value = "b200006_1111", valueName = "买入返售金融资产")
    @ApiModelProperty(value = "买入返售金融资产")
    private BigDecimal b2000061111;

    @LogField(tableName = "b200006", value = "b200006_1121", valueName = "应收票据")
    @ApiModelProperty(value = "应收票据")
    private BigDecimal b2000061121;

    @LogField(tableName = "b200006", value = "b200006_1122", valueName = "应收账款")
    @ApiModelProperty(value = "应收账款")
    private BigDecimal b2000061122;

    @LogField(tableName = "b200006", value = "b200006_1123", valueName = "预付账款")
    @ApiModelProperty(value = "预付账款")
    private BigDecimal b2000061123;

    @LogField(tableName = "b200006", value = "b200006_1124", valueName = "合同资产")
    @ApiModelProperty(value = "合同资产")
    private BigDecimal b2000061124;

    @LogField(tableName = "b200006", value = "b200006_1125", valueName = "合同资产减值准备")
    @ApiModelProperty(value = "合同资产减值准备")
    private BigDecimal b2000061125;

    @LogField(tableName = "b200006", value = "b200006_1131", valueName = "应收股利")
    @ApiModelProperty(value = "应收股利")
    private BigDecimal b2000061131;

    @LogField(tableName = "b200006", value = "b200006_1132", valueName = "应收利息")
    @ApiModelProperty(value = "应收利息")
    private BigDecimal b2000061132;

    @LogField(tableName = "b200006", value = "b200006_1201", valueName = "应收代位追偿款")
    @ApiModelProperty(value = "应收代位追偿款")
    private BigDecimal b2000061201;

    @LogField(tableName = "b200006", value = "b200006_1211", valueName = "应收分保账款")
    @ApiModelProperty(value = "应收分保账款")
    private BigDecimal b2000061211;

    @LogField(tableName = "b200006", value = "b200006_1212", valueName = "应收分保合同准备金")
    @ApiModelProperty(value = "应收分保合同准备金")
    private BigDecimal b2000061212;

    @LogField(tableName = "b200006", value = "b200006_1221", valueName = "其他应收款")
    @ApiModelProperty(value = "其他应收款")
    private BigDecimal b2000061221;

    @LogField(tableName = "b200006", value = "b200006_1231", valueName = "坏账准备")
    @ApiModelProperty(value = "坏账准备")
    private BigDecimal b2000061231;

    @LogField(tableName = "b200006", value = "b200006_1301", valueName = "贴现资产")
    @ApiModelProperty(value = "贴现资产")
    private BigDecimal b2000061301;

    @LogField(tableName = "b200006", value = "b200006_1302", valueName = "拆出资金")
    @ApiModelProperty(value = "拆出资金")
    private BigDecimal b2000061302;

    @LogField(tableName = "b200006", value = "b200006_1303", valueName = "贷款")
    @ApiModelProperty(value = "贷款")
    private BigDecimal b2000061303;

    @LogField(tableName = "b200006", value = "b200006_1304", valueName = "贷款损失准备")
    @ApiModelProperty(value = "贷款损失准备")
    private BigDecimal b2000061304;

    @LogField(tableName = "b200006", value = "b200006_1311", valueName = "代理兑付证券")
    @ApiModelProperty(value = "代理兑付证券")
    private BigDecimal b2000061311;

    @LogField(tableName = "b200006", value = "b200006_1321", valueName = "代理业务资产")
    @ApiModelProperty(value = "代理业务资产")
    private BigDecimal b2000061321;

    @LogField(tableName = "b200006", value = "b200006_1401", valueName = "材料采购")
    @ApiModelProperty(value = "材料采购")
    private BigDecimal b2000061401;

    @LogField(tableName = "b200006", value = "b200006_1402", valueName = "在途物资")
    @ApiModelProperty(value = "在途物资")
    private BigDecimal b2000061402;

    @LogField(tableName = "b200006", value = "b200006_1403", valueName = "原材料")
    @ApiModelProperty(value = "原材料")
    private BigDecimal b2000061403;

    @LogField(tableName = "b200006", value = "b200006_1404", valueName = "材料成本差异")
    @ApiModelProperty(value = "材料成本差异")
    private BigDecimal b2000061404;

    @LogField(tableName = "b200006", value = "b200006_1405", valueName = "库存商品")
    @ApiModelProperty(value = "库存商品")
    private BigDecimal b2000061405;

    @LogField(tableName = "b200006", value = "b200006_1406", valueName = "发出商品")
    @ApiModelProperty(value = "发出商品")
    private BigDecimal b2000061406;

    @LogField(tableName = "b200006", value = "b200006_1407", valueName = "商品进销差价")
    @ApiModelProperty(value = "商品进销差价")
    private BigDecimal b2000061407;

    @LogField(tableName = "b200006", value = "b200006_1408", valueName = "委托加工物资")
    @ApiModelProperty(value = "委托加工物资")
    private BigDecimal b2000061408;

    @LogField(tableName = "b200006", value = "b200006_1411", valueName = "周转材料")
    @ApiModelProperty(value = "周转材料")
    private BigDecimal b2000061411;

    @LogField(tableName = "b200006", value = "b200006_1421", valueName = "消耗性生物资产")
    @ApiModelProperty(value = "消耗性生物资产")
    private BigDecimal b2000061421;

    @LogField(tableName = "b200006", value = "b200006_1431", valueName = "贵金属")
    @ApiModelProperty(value = "贵金属")
    private BigDecimal b2000061431;

    @LogField(tableName = "b200006", value = "b200006_1441", valueName = "抵债资产")
    @ApiModelProperty(value = "抵债资产")
    private BigDecimal b2000061441;

    @LogField(tableName = "b200006", value = "b200006_1451", valueName = "损余物资")
    @ApiModelProperty(value = "损余物资")
    private BigDecimal b2000061451;

    @LogField(tableName = "b200006", value = "b200006_1461", valueName = "融资租赁资产")
    @ApiModelProperty(value = "融资租赁资产")
    private BigDecimal b2000061461;

    @LogField(tableName = "b200006", value = "b200006_1471", valueName = "存货跌价准备")
    @ApiModelProperty(value = "存货跌价准备")
    private BigDecimal b2000061471;

    @LogField(tableName = "b200006", value = "b200006_1481", valueName = "持有待售资产")
    @ApiModelProperty(value = "持有待售资产")
    private BigDecimal b2000061481;

    @LogField(tableName = "b200006", value = "b200006_1482", valueName = "持有待售资产减值准备")
    @ApiModelProperty(value = "持有待售资产减值准备")
    private BigDecimal b2000061482;

    @LogField(tableName = "b200006", value = "b200006_1501", valueName = "持有至到期投资")
    @ApiModelProperty(value = "持有至到期投资")
    private BigDecimal b2000061501;

    @LogField(tableName = "b200006", value = "b200006_1502", valueName = "持有至到期投资减值准备")
    @ApiModelProperty(value = "持有至到期投资减值准备")
    private BigDecimal b2000061502;

    @LogField(tableName = "b200006", value = "b200006_1503", valueName = "可供出售金融资产")
    @ApiModelProperty(value = "可供出售金融资产")
    private BigDecimal b2000061503;

    @LogField(tableName = "b200006", value = "b200006_1511", valueName = "长期股权投资")
    @ApiModelProperty(value = "长期股权投资")
    private BigDecimal b2000061511;

    @LogField(tableName = "b200006", value = "b200006_1512", valueName = "长期股权投资减值准备")
    @ApiModelProperty(value = "长期股权投资减值准备")
    private BigDecimal b2000061512;

    @LogField(tableName = "b200006", value = "b200006_1521", valueName = "投资性房地产")
    @ApiModelProperty(value = "投资性房地产")
    private BigDecimal b2000061521;

    @LogField(tableName = "b200006", value = "b200006_1531", valueName = "长期应收款")
    @ApiModelProperty(value = "长期应收款")
    private BigDecimal b2000061531;

    @LogField(tableName = "b200006", value = "b200006_1532", valueName = "未实现融资收益")
    @ApiModelProperty(value = "未实现融资收益")
    private BigDecimal b2000061532;

    @LogField(tableName = "b200006", value = "b200006_1541", valueName = "存出资本保证金")
    @ApiModelProperty(value = "存出资本保证金")
    private BigDecimal b2000061541;

    @LogField(tableName = "b200006", value = "b200006_1601", valueName = "固定资产")
    @ApiModelProperty(value = "固定资产")
    private BigDecimal b2000061601;

    @LogField(tableName = "b200006", value = "b200006_1602", valueName = "累计折旧")
    @ApiModelProperty(value = "累计折旧")
    private BigDecimal b2000061602;

    @LogField(tableName = "b200006", value = "b200006_1603", valueName = "固定资产减值准备")
    @ApiModelProperty(value = "固定资产减值准备")
    private BigDecimal b2000061603;

    @LogField(tableName = "b200006", value = "b200006_1604", valueName = "在建工程")
    @ApiModelProperty(value = "在建工程")
    private BigDecimal b2000061604;

    @LogField(tableName = "b200006", value = "b200006_1605", valueName = "工程物资")
    @ApiModelProperty(value = "工程物资")
    private BigDecimal b2000061605;

    @LogField(tableName = "b200006", value = "b200006_1606", valueName = "固定资产清理")
    @ApiModelProperty(value = "固定资产清理")
    private BigDecimal b2000061606;

    @LogField(tableName = "b200006", value = "b200006_1611", valueName = "未担保余值")
    @ApiModelProperty(value = "未担保余值")
    private BigDecimal b2000061611;

    @LogField(tableName = "b200006", value = "b200006_1621", valueName = "生产性生物资产")
    @ApiModelProperty(value = "生产性生物资产")
    private BigDecimal b2000061621;

    @LogField(tableName = "b200006", value = "b200006_1622", valueName = "生产性生物资产累计折旧")
    @ApiModelProperty(value = "生产性生物资产累计折旧")
    private BigDecimal b2000061622;

    @LogField(tableName = "b200006", value = "b200006_1623", valueName = "公益性生物资产")
    @ApiModelProperty(value = "公益性生物资产")
    private BigDecimal b2000061623;

    @LogField(tableName = "b200006", value = "b200006_1631", valueName = "油气资产")
    @ApiModelProperty(value = "油气资产")
    private BigDecimal b2000061631;

    @LogField(tableName = "b200006", value = "b200006_1632", valueName = "累计折耗")
    @ApiModelProperty(value = "累计折耗")
    private BigDecimal b2000061632;

    @LogField(tableName = "b200006", value = "b200006_1701", valueName = "无形资产")
    @ApiModelProperty(value = "无形资产")
    private BigDecimal b2000061701;

    @LogField(tableName = "b200006", value = "b200006_1702", valueName = "累计摊销")
    @ApiModelProperty(value = "累计摊销")
    private BigDecimal b2000061702;

    @LogField(tableName = "b200006", value = "b200006_1703", valueName = "无形资产减值准备")
    @ApiModelProperty(value = "无形资产减值准备")
    private BigDecimal b2000061703;

    @LogField(tableName = "b200006", value = "b200006_1711", valueName = "商誉")
    @ApiModelProperty(value = "商誉")
    private BigDecimal b2000061711;

    @LogField(tableName = "b200006", value = "b200006_1801", valueName = "长期待摊费用")
    @ApiModelProperty(value = "长期待摊费用")
    private BigDecimal b2000061801;

    @LogField(tableName = "b200006", value = "b200006_1811", valueName = "递延所得税资产")
    @ApiModelProperty(value = "递延所得税资产")
    private BigDecimal b2000061811;

    @LogField(tableName = "b200006", value = "b200006_1821", valueName = "独立账户资产")
    @ApiModelProperty(value = "独立账户资产")
    private BigDecimal b2000061821;

    @LogField(tableName = "b200006", value = "b200006_1901", valueName = "待处理财产损溢")
    @ApiModelProperty(value = "待处理财产损溢")
    private BigDecimal b2000061901;

    @LogField(tableName = "b200006", value = "b200006_2001", valueName = "短期借款")
    @ApiModelProperty(value = "短期借款")
    private BigDecimal b2000062001;

    @LogField(tableName = "b200006", value = "b200006_2002", valueName = "存入保证金")
    @ApiModelProperty(value = "存入保证金")
    private BigDecimal b2000062002;

    @LogField(tableName = "b200006", value = "b200006_2003", valueName = "拆入资金")
    @ApiModelProperty(value = "拆入资金")
    private BigDecimal b2000062003;

    @LogField(tableName = "b200006", value = "b200006_2004", valueName = "向中央银行借款")
    @ApiModelProperty(value = "向中央银行借款")
    private BigDecimal b2000062004;

    @LogField(tableName = "b200006", value = "b200006_2011", valueName = "吸收存款")
    @ApiModelProperty(value = "吸收存款")
    private BigDecimal b2000062011;

    @LogField(tableName = "b200006", value = "b200006_2012", valueName = "同业存放")
    @ApiModelProperty(value = "同业存放")
    private BigDecimal b2000062012;

    @LogField(tableName = "b200006", value = "b200006_2021", valueName = "贴现负债")
    @ApiModelProperty(value = "贴现负债")
    private BigDecimal b2000062021;

    @LogField(tableName = "b200006", value = "b200006_2101", valueName = "交易性金融负债")
    @ApiModelProperty(value = "交易性金融负债")
    private BigDecimal b2000062101;

    @LogField(tableName = "b200006", value = "b200006_2111", valueName = "卖出回购金融资产款")
    @ApiModelProperty(value = "卖出回购金融资产款")
    private BigDecimal b2000062111;

    @LogField(tableName = "b200006", value = "b200006_2201", valueName = "应付票据")
    @ApiModelProperty(value = "应付票据")
    private BigDecimal b2000062201;

    @LogField(tableName = "b200006", value = "b200006_2202", valueName = "应付账款")
    @ApiModelProperty(value = "应付账款")
    private BigDecimal b2000062202;

    @LogField(tableName = "b200006", value = "b200006_2203", valueName = "预收账款")
    @ApiModelProperty(value = "预收账款")
    private BigDecimal b2000062203;

    @LogField(tableName = "b200006", value = "b200006_2204", valueName = "合同负债")
    @ApiModelProperty(value = "合同负债")
    private BigDecimal b2000062204;

    @LogField(tableName = "b200006", value = "b200006_2211", valueName = "应付职工薪酬")
    @ApiModelProperty(value = "应付职工薪酬")
    private BigDecimal b2000062211;

    @LogField(tableName = "b200006", value = "b200006_221101", valueName = "职工工资")
    @ApiModelProperty(value = "职工工资")
    private BigDecimal b200006221101;

    @LogField(tableName = "b200006", value = "b200006_221102", valueName = "职工福利费")
    @ApiModelProperty(value = "职工福利费")
    private BigDecimal b200006221102;

    @LogField(tableName = "b200006", value = "b200006_221103", valueName = "社会保险费")
    @ApiModelProperty(value = "社会保险费")
    private BigDecimal b200006221103;

    @LogField(tableName = "b200006", value = "b200006_221104", valueName = "住房公积金")
    @ApiModelProperty(value = "住房公积金")
    private BigDecimal b200006221104;

    @LogField(tableName = "b200006", value = "b200006_221105", valueName = "工会经费")
    @ApiModelProperty(value = "工会经费")
    private BigDecimal b200006221105;

    @LogField(tableName = "b200006", value = "b200006_221106", valueName = "职工教育经费")
    @ApiModelProperty(value = "职工教育经费")
    private BigDecimal b200006221106;

    @LogField(tableName = "b200006", value = "b200006_221107", valueName = "年终奖")
    @ApiModelProperty(value = "年终奖")
    private BigDecimal b200006221107;

    @LogField(tableName = "b200006", value = "b200006_221108", valueName = "职工培训费用")
    @ApiModelProperty(value = "职工培训费用")
    private BigDecimal b200006221108;

    @LogField(tableName = "b200006", value = "b200006_221109", valueName = "补充养老保险")
    @ApiModelProperty(value = "补充养老保险")
    private BigDecimal b200006221109;

    @LogField(tableName = "b200006", value = "b200006_221110", valueName = "补充医疗保险")
    @ApiModelProperty(value = "补充医疗保险")
    private BigDecimal b200006221110;

    @LogField(tableName = "b200006", value = "b200006_221111", valueName = "股权激励")
    @ApiModelProperty(value = "股权激励")
    private BigDecimal b200006221111;

    @LogField(tableName = "b200006", value = "b200006_221112", valueName = "商业保险费")
    @ApiModelProperty(value = "商业保险费")
    private BigDecimal b200006221112;

    @LogField(tableName = "b200006", value = "b200006_221113", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200006221113;

    @LogField(tableName = "b200006", value = "b200006_2221", valueName = "应交税费")
    @ApiModelProperty(value = "应交税费")
    private BigDecimal b2000062221;

    @LogField(tableName = "b200006", value = "b200006_222101", valueName = "应交增值税")
    @ApiModelProperty(value = "应交增值税")
    private BigDecimal b200006222101;

    @LogField(tableName = "b200006", value = "b200006_22210101", valueName = "进项税额")
    @ApiModelProperty(value = "进项税额")
    private BigDecimal b20000622210101;

    @LogField(tableName = "b200006", value = "b200006_22210102", valueName = "销项税额")
    @ApiModelProperty(value = "销项税额")
    private BigDecimal b20000622210102;

    @LogField(tableName = "b200006", value = "b200006_22210103", valueName = "已交税金")
    @ApiModelProperty(value = "已交税金")
    private BigDecimal b20000622210103;

    @LogField(tableName = "b200006", value = "b200006_22210104", valueName = "出口抵减内销产品应纳税额")
    @ApiModelProperty(value = "出口抵减内销产品应纳税额")
    private BigDecimal b20000622210104;

    @LogField(tableName = "b200006", value = "b200006_22210105", valueName = "转出未交增值税")
    @ApiModelProperty(value = "转出未交增值税")
    private BigDecimal b20000622210105;

    @LogField(tableName = "b200006", value = "b200006_22210106", valueName = "进项税额转出")
    @ApiModelProperty(value = "进项税额转出")
    private BigDecimal b20000622210106;

    @LogField(tableName = "b200006", value = "b200006_22210107", valueName = "减免税款")
    @ApiModelProperty(value = "减免税款")
    private BigDecimal b20000622210107;

    @LogField(tableName = "b200006", value = "b200006_22210108", valueName = "出口退税")
    @ApiModelProperty(value = "出口退税")
    private BigDecimal b20000622210108;

    @LogField(tableName = "b200006", value = "b200006_22210109", valueName = "转出多交增值税")
    @ApiModelProperty(value = "转出多交增值税")
    private BigDecimal b20000622210109;

    @LogField(tableName = "b200006", value = "b200006_22210110", valueName = "销项税额抵减")
    @ApiModelProperty(value = "销项税额抵减")
    private BigDecimal b20000622210110;

    @LogField(tableName = "b200006", value = "b200006_222102", valueName = "未交增值税")
    @ApiModelProperty(value = "未交增值税")
    private BigDecimal b200006222102;

    @LogField(tableName = "b200006", value = "b200006_222103", valueName = "应交营业税")
    @ApiModelProperty(value = "应交营业税")
    private BigDecimal b200006222103;

    @LogField(tableName = "b200006", value = "b200006_222104", valueName = "应交消费税")
    @ApiModelProperty(value = "应交消费税")
    private BigDecimal b200006222104;

    @LogField(tableName = "b200006", value = "b200006_222105", valueName = "应交资源税")
    @ApiModelProperty(value = "应交资源税")
    private BigDecimal b200006222105;

    @LogField(tableName = "b200006", value = "b200006_222106", valueName = "应交所得税")
    @ApiModelProperty(value = "应交所得税")
    private BigDecimal b200006222106;

    @LogField(tableName = "b200006", value = "b200006_222107", valueName = "应交土地增值税")
    @ApiModelProperty(value = "应交土地增值税")
    private BigDecimal b200006222107;

    @LogField(tableName = "b200006", value = "b200006_222108", valueName = "应交城市维护建设税")
    @ApiModelProperty(value = "应交城市维护建设税")
    private BigDecimal b200006222108;

    @LogField(tableName = "b200006", value = "b200006_222109", valueName = "应交房产税")
    @ApiModelProperty(value = "应交房产税")
    private BigDecimal b200006222109;

    @LogField(tableName = "b200006", value = "b200006_222110", valueName = "应交土地使用税")
    @ApiModelProperty(value = "应交土地使用税")
    private BigDecimal b200006222110;

    @LogField(tableName = "b200006", value = "b200006_222111", valueName = "应交车船税")
    @ApiModelProperty(value = "应交车船税")
    private BigDecimal b200006222111;

    @LogField(tableName = "b200006", value = "b200006_222112", valueName = "应交个人所得税")
    @ApiModelProperty(value = "应交个人所得税")
    private BigDecimal b200006222112;

    @LogField(tableName = "b200006", value = "b200006_222113", valueName = "教育费附加")
    @ApiModelProperty(value = "教育费附加")
    private BigDecimal b200006222113;

    @LogField(tableName = "b200006", value = "b200006_222114", valueName = "地方教育费附加")
    @ApiModelProperty(value = "地方教育费附加")
    private BigDecimal b200006222114;

    @LogField(tableName = "b200006", value = "b200006_222115", valueName = "印花税")
    @ApiModelProperty(value = "印花税")
    private BigDecimal b200006222115;

    @LogField(tableName = "b200006", value = "b200006_222116", valueName = "待抵扣进项税额")
    @ApiModelProperty(value = "待抵扣进项税额")
    private BigDecimal b200006222116;

    @LogField(tableName = "b200006", value = "b200006_222117", valueName = "待认证进项税额")
    @ApiModelProperty(value = "待认证进项税额")
    private BigDecimal b200006222117;

    @LogField(tableName = "b200006", value = "b200006_222118", valueName = "预交增值税")
    @ApiModelProperty(value = "预交增值税")
    private BigDecimal b200006222118;

    @LogField(tableName = "b200006", value = "b200006_222119", valueName = "待转销项税额")
    @ApiModelProperty(value = "待转销项税额")
    private BigDecimal b200006222119;

    @LogField(tableName = "b200006", value = "b200006_222120", valueName = "增值税留抵税额")
    @ApiModelProperty(value = "增值税留抵税额")
    private BigDecimal b200006222120;

    @LogField(tableName = "b200006", value = "b200006_222121", valueName = "简易计税")
    @ApiModelProperty(value = "简易计税")
    private BigDecimal b200006222121;

    @LogField(tableName = "b200006", value = "b200006_222122", valueName = "转让金融商品应交增值税")
    @ApiModelProperty(value = "转让金融商品应交增值税")
    private BigDecimal b200006222122;

    @LogField(tableName = "b200006", value = "b200006_222123", valueName = "代扣代缴增值税")
    @ApiModelProperty(value = "代扣代缴增值税")
    private BigDecimal b200006222123;

    @LogField(tableName = "b200006", value = "b200006_2231", valueName = "应付利息")
    @ApiModelProperty(value = "应付利息")
    private BigDecimal b2000062231;

    @LogField(tableName = "b200006", value = "b200006_2232", valueName = "应付股利")
    @ApiModelProperty(value = "应付股利")
    private BigDecimal b2000062232;

    @LogField(tableName = "b200006", value = "b200006_2241", valueName = "其他应付款")
    @ApiModelProperty(value = "其他应付款")
    private BigDecimal b2000062241;

    @LogField(tableName = "b200006", value = "b200006_2251", valueName = "应付保单红利")
    @ApiModelProperty(value = "应付保单红利")
    private BigDecimal b2000062251;

    @LogField(tableName = "b200006", value = "b200006_2261", valueName = "应付分保账款")
    @ApiModelProperty(value = "应付分保账款")
    private BigDecimal b2000062261;

    @LogField(tableName = "b200006", value = "b200006_2311", valueName = "代理买卖证券款")
    @ApiModelProperty(value = "代理买卖证券款")
    private BigDecimal b2000062311;

    @LogField(tableName = "b200006", value = "b200006_2312", valueName = "代理承销证券款")
    @ApiModelProperty(value = "代理承销证券款")
    private BigDecimal b2000062312;

    @LogField(tableName = "b200006", value = "b200006_2313", valueName = "代理兑付证券款")
    @ApiModelProperty(value = "代理兑付证券款")
    private BigDecimal b2000062313;

    @LogField(tableName = "b200006", value = "b200006_2314", valueName = "代理业务负债")
    @ApiModelProperty(value = "代理业务负债")
    private BigDecimal b2000062314;

    @LogField(tableName = "b200006", value = "b200006_2401", valueName = "递延收益")
    @ApiModelProperty(value = "递延收益")
    private BigDecimal b2000062401;

    @LogField(tableName = "b200006", value = "b200006_2245", valueName = "持有待售负债")
    @ApiModelProperty(value = "持有待售负债")
    private BigDecimal b2000062245;

    @LogField(tableName = "b200006", value = "b200006_2501", valueName = "长期借款")
    @ApiModelProperty(value = "长期借款")
    private BigDecimal b2000062501;

    @LogField(tableName = "b200006", value = "b200006_2502", valueName = "应付债券")
    @ApiModelProperty(value = "应付债券")
    private BigDecimal b2000062502;

    @LogField(tableName = "b200006", value = "b200006_2601", valueName = "未到期责任准备金")
    @ApiModelProperty(value = "未到期责任准备金")
    private BigDecimal b2000062601;

    @LogField(tableName = "b200006", value = "b200006_2602", valueName = "保险责任准备金")
    @ApiModelProperty(value = "保险责任准备金")
    private BigDecimal b2000062602;

    @LogField(tableName = "b200006", value = "b200006_2611", valueName = "保户储金")
    @ApiModelProperty(value = "保户储金")
    private BigDecimal b2000062611;

    @LogField(tableName = "b200006", value = "b200006_2621", valueName = "独立账户负债")
    @ApiModelProperty(value = "独立账户负债")
    private BigDecimal b2000062621;

    @LogField(tableName = "b200006", value = "b200006_2701", valueName = "长期应付款")
    @ApiModelProperty(value = "长期应付款")
    private BigDecimal b2000062701;

    @LogField(tableName = "b200006", value = "b200006_2702", valueName = "未确认融资费用")
    @ApiModelProperty(value = "未确认融资费用")
    private BigDecimal b2000062702;

    @LogField(tableName = "b200006", value = "b200006_2711", valueName = "专项应付款")
    @ApiModelProperty(value = "专项应付款")
    private BigDecimal b2000062711;

    @LogField(tableName = "b200006", value = "b200006_2801", valueName = "预计负债")
    @ApiModelProperty(value = "预计负债")
    private BigDecimal b2000062801;

    @LogField(tableName = "b200006", value = "b200006_2901", valueName = "递延所得税负债")
    @ApiModelProperty(value = "递延所得税负债")
    private BigDecimal b2000062901;

    @LogField(tableName = "b200006", value = "b200006_3001", valueName = "清算资金往来")
    @ApiModelProperty(value = "清算资金往来")
    private BigDecimal b2000063001;

    @LogField(tableName = "b200006", value = "b200006_3002", valueName = "货币兑换")
    @ApiModelProperty(value = "货币兑换")
    private BigDecimal b2000063002;

    @LogField(tableName = "b200006", value = "b200006_3101", valueName = "衍生工具")
    @ApiModelProperty(value = "衍生工具")
    private BigDecimal b2000063101;

    @LogField(tableName = "b200006", value = "b200006_3201", valueName = "套期工具")
    @ApiModelProperty(value = "套期工具")
    private BigDecimal b2000063201;

    @LogField(tableName = "b200006", value = "b200006_3202", valueName = "被套期项目")
    @ApiModelProperty(value = "被套期项目")
    private BigDecimal b2000063202;

    @LogField(tableName = "b200006", value = "b200006_4001", valueName = "实收资本")
    @ApiModelProperty(value = "实收资本")
    private BigDecimal b2000064001;

    @LogField(tableName = "b200006", value = "b200006_4002", valueName = "资本公积")
    @ApiModelProperty(value = "资本公积")
    private BigDecimal b2000064002;

    @LogField(tableName = "b200006", value = "b200006_4003", valueName = "其他综合收益")
    @ApiModelProperty(value = "其他综合收益")
    private BigDecimal b2000064003;

    @LogField(tableName = "b200006", value = "b200006_4101", valueName = "盈余公积")
    @ApiModelProperty(value = "盈余公积")
    private BigDecimal b2000064101;

    @LogField(tableName = "b200006", value = "b200006_4102", valueName = "一般风险准备")
    @ApiModelProperty(value = "一般风险准备")
    private BigDecimal b2000064102;

    @LogField(tableName = "b200006", value = "b200006_4103", valueName = "本年利润")
    @ApiModelProperty(value = "本年利润")
    private BigDecimal b2000064103;

    @LogField(tableName = "b200006", value = "b200006_4104", valueName = "利润分配")
    @ApiModelProperty(value = "利润分配")
    private BigDecimal b2000064104;

    @LogField(tableName = "b200006", value = "b200006_4201", valueName = "库存股")
    @ApiModelProperty(value = "库存股")
    private BigDecimal b2000064201;

    @LogField(tableName = "b200006", value = "b200006_4301", valueName = "专项储备")
    @ApiModelProperty(value = "专项储备")
    private BigDecimal b2000064301;

    @LogField(tableName = "b200006", value = "b200006_5001", valueName = "生产成本")
    @ApiModelProperty(value = "生产成本")
    private BigDecimal b2000065001;

    @LogField(tableName = "b200006", value = "b200006_500101", valueName = "直接人工")
    @ApiModelProperty(value = "直接人工")
    private BigDecimal b200006500101;

    @LogField(tableName = "b200006", value = "b200006_500102", valueName = "直接材料")
    @ApiModelProperty(value = "直接材料")
    private BigDecimal b200006500102;

    @LogField(tableName = "b200006", value = "b200006_500103", valueName = "制造费用")
    @ApiModelProperty(value = "制造费用")
    private BigDecimal b200006500103;

    @LogField(tableName = "b200006", value = "b200006_500104", valueName = "职工工资")
    @ApiModelProperty(value = "职工工资")
    private BigDecimal b200006500104;

    @LogField(tableName = "b200006", value = "b200006_500105", valueName = "年终奖")
    @ApiModelProperty(value = "年终奖")
    private BigDecimal b200006500105;

    @LogField(tableName = "b200006", value = "b200006_500106", valueName = "津贴与补贴")
    @ApiModelProperty(value = "津贴与补贴")
    private BigDecimal b200006500106;

    @LogField(tableName = "b200006", value = "b200006_500107", valueName = "股权激励")
    @ApiModelProperty(value = "股权激励")
    private BigDecimal b200006500107;

    @LogField(tableName = "b200006", value = "b200006_500108", valueName = "职工教育经费")
    @ApiModelProperty(value = "职工教育经费")
    private BigDecimal b200006500108;

    @LogField(tableName = "b200006", value = "b200006_500109", valueName = "职工培训费用")
    @ApiModelProperty(value = "职工培训费用")
    private BigDecimal b200006500109;

    @LogField(tableName = "b200006", value = "b200006_500110", valueName = "工会经费")
    @ApiModelProperty(value = "工会经费")
    private BigDecimal b200006500110;

    @LogField(tableName = "b200006", value = "b200006_500111", valueName = "社会保险费")
    @ApiModelProperty(value = "社会保险费")
    private BigDecimal b200006500111;

    @LogField(tableName = "b200006", value = "b200006_500112", valueName = "住房公积金")
    @ApiModelProperty(value = "住房公积金")
    private BigDecimal b200006500112;

    @LogField(tableName = "b200006", value = "b200006_500113", valueName = "职工福利费")
    @ApiModelProperty(value = "职工福利费")
    private BigDecimal b200006500113;

    @LogField(tableName = "b200006", value = "b200006_500114", valueName = "补充养老保险")
    @ApiModelProperty(value = "补充养老保险")
    private BigDecimal b200006500114;

    @LogField(tableName = "b200006", value = "b200006_500115", valueName = "补充医疗保险")
    @ApiModelProperty(value = "补充医疗保险")
    private BigDecimal b200006500115;

    @LogField(tableName = "b200006", value = "b200006_500116", valueName = "其他职工工资")
    @ApiModelProperty(value = "其他职工工资")
    private BigDecimal b200006500116;

    @LogField(tableName = "b200006", value = "b200006_500117", valueName = "商业保险费")
    @ApiModelProperty(value = "商业保险费")
    private BigDecimal b200006500117;

    @LogField(tableName = "b200006", value = "b200006_500118", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200006500118;

    @LogField(tableName = "b200006", value = "b200006_5101", valueName = "制造费用")
    @ApiModelProperty(value = "制造费用")
    private BigDecimal b2000065101;

    @LogField(tableName = "b200006", value = "b200006_510101", valueName = "职工工资")
    @ApiModelProperty(value = "职工工资")
    private BigDecimal b200006510101;

    @LogField(tableName = "b200006", value = "b200006_510102", valueName = "年终奖")
    @ApiModelProperty(value = "年终奖")
    private BigDecimal b200006510102;

    @LogField(tableName = "b200006", value = "b200006_510103", valueName = "津贴与补贴")
    @ApiModelProperty(value = "津贴与补贴")
    private BigDecimal b200006510103;

    @LogField(tableName = "b200006", value = "b200006_510104", valueName = "股权激励")
    @ApiModelProperty(value = "股权激励")
    private BigDecimal b200006510104;

    @LogField(tableName = "b200006", value = "b200006_510105", valueName = "社会保险费")
    @ApiModelProperty(value = "社会保险费")
    private BigDecimal b200006510105;

    @LogField(tableName = "b200006", value = "b200006_510106", valueName = "住房公积金")
    @ApiModelProperty(value = "住房公积金")
    private BigDecimal b200006510106;

    @LogField(tableName = "b200006", value = "b200006_510107", valueName = "补充养老保险")
    @ApiModelProperty(value = "补充养老保险")
    private BigDecimal b200006510107;

    @LogField(tableName = "b200006", value = "b200006_510108", valueName = "补充医疗保险")
    @ApiModelProperty(value = "补充医疗保险")
    private BigDecimal b200006510108;

    @LogField(tableName = "b200006", value = "b200006_510109", valueName = "职工教育经费")
    @ApiModelProperty(value = "职工教育经费")
    private BigDecimal b200006510109;

    @LogField(tableName = "b200006", value = "b200006_510110", valueName = "职工培训费用")
    @ApiModelProperty(value = "职工培训费用")
    private BigDecimal b200006510110;

    @LogField(tableName = "b200006", value = "b200006_510111", valueName = "其他职工工资")
    @ApiModelProperty(value = "其他职工工资")
    private BigDecimal b200006510111;

    @LogField(tableName = "b200006", value = "b200006_510112", valueName = "职工福利费")
    @ApiModelProperty(value = "职工福利费")
    private BigDecimal b200006510112;

    @LogField(tableName = "b200006", value = "b200006_510113", valueName = "工会经费")
    @ApiModelProperty(value = "工会经费")
    private BigDecimal b200006510113;

    @LogField(tableName = "b200006", value = "b200006_510114", valueName = "商业保险费")
    @ApiModelProperty(value = "商业保险费")
    private BigDecimal b200006510114;

    @LogField(tableName = "b200006", value = "b200006_510115", valueName = "折旧费")
    @ApiModelProperty(value = "折旧费")
    private BigDecimal b200006510115;

    @LogField(tableName = "b200006", value = "b200006_510116", valueName = "修理费")
    @ApiModelProperty(value = "修理费")
    private BigDecimal b200006510116;

    @LogField(tableName = "b200006", value = "b200006_510117", valueName = "机物料消耗")
    @ApiModelProperty(value = "机物料消耗")
    private BigDecimal b200006510117;

    @LogField(tableName = "b200006", value = "b200006_510118", valueName = "低值易耗品摊销")
    @ApiModelProperty(value = "低值易耗品摊销")
    private BigDecimal b200006510118;

    @LogField(tableName = "b200006", value = "b200006_510119", valueName = "租赁费")
    @ApiModelProperty(value = "租赁费")
    private BigDecimal b200006510119;

    @LogField(tableName = "b200006", value = "b200006_510120", valueName = "运杂费")
    @ApiModelProperty(value = "运杂费")
    private BigDecimal b200006510120;

    @LogField(tableName = "b200006", value = "b200006_510121", valueName = "保险费")
    @ApiModelProperty(value = "保险费")
    private BigDecimal b200006510121;

    @LogField(tableName = "b200006", value = "b200006_510122", valueName = "差旅费")
    @ApiModelProperty(value = "差旅费")
    private BigDecimal b200006510122;

    @LogField(tableName = "b200006", value = "b200006_510123", valueName = "水电费")
    @ApiModelProperty(value = "水电费")
    private BigDecimal b200006510123;

    @LogField(tableName = "b200006", value = "b200006_510124", valueName = "外部加工费")
    @ApiModelProperty(value = "外部加工费")
    private BigDecimal b200006510124;

    @LogField(tableName = "b200006", value = "b200006_510125", valueName = "厂房租金")
    @ApiModelProperty(value = "厂房租金")
    private BigDecimal b200006510125;

    @LogField(tableName = "b200006", value = "b200006_510126", valueName = "装卸费")
    @ApiModelProperty(value = "装卸费")
    private BigDecimal b200006510126;

    @LogField(tableName = "b200006", value = "b200006_510127", valueName = "设计制图费")
    @ApiModelProperty(value = "设计制图费")
    private BigDecimal b200006510127;

    @LogField(tableName = "b200006", value = "b200006_510128", valueName = "劳动保护费")
    @ApiModelProperty(value = "劳动保护费")
    private BigDecimal b200006510128;

    @LogField(tableName = "b200006", value = "b200006_510129", valueName = "无形资产摊销费")
    @ApiModelProperty(value = "无形资产摊销费")
    private BigDecimal b200006510129;

    @LogField(tableName = "b200006", value = "b200006_510130", valueName = "长期待摊费用摊销费")
    @ApiModelProperty(value = "长期待摊费用摊销费")
    private BigDecimal b200006510130;

    @LogField(tableName = "b200006", value = "b200006_510131", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200006510131;

    @LogField(tableName = "b200006", value = "b200006_5201", valueName = "劳务成本")
    @ApiModelProperty(value = "劳务成本")
    private BigDecimal b2000065201;

    @LogField(tableName = "b200006", value = "b200006_520101", valueName = "职工工资")
    @ApiModelProperty(value = "职工工资")
    private BigDecimal b200006520101;

    @LogField(tableName = "b200006", value = "b200006_520102", valueName = "年终奖")
    @ApiModelProperty(value = "年终奖")
    private BigDecimal b200006520102;

    @LogField(tableName = "b200006", value = "b200006_520103", valueName = "津贴与补贴")
    @ApiModelProperty(value = "津贴与补贴")
    private BigDecimal b200006520103;

    @LogField(tableName = "b200006", value = "b200006_520104", valueName = "股权激励")
    @ApiModelProperty(value = "股权激励")
    private BigDecimal b200006520104;

    @LogField(tableName = "b200006", value = "b200006_520105", valueName = "职工教育经费")
    @ApiModelProperty(value = "职工教育经费")
    private BigDecimal b200006520105;

    @LogField(tableName = "b200006", value = "b200006_520106", valueName = "职工培训费用")
    @ApiModelProperty(value = "职工培训费用")
    private BigDecimal b200006520106;

    @LogField(tableName = "b200006", value = "b200006_520107", valueName = "工会经费")
    @ApiModelProperty(value = "工会经费")
    private BigDecimal b200006520107;

    @LogField(tableName = "b200006", value = "b200006_520108", valueName = "社会保险费")
    @ApiModelProperty(value = "社会保险费")
    private BigDecimal b200006520108;

    @LogField(tableName = "b200006", value = "b200006_520109", valueName = "住房公积金")
    @ApiModelProperty(value = "住房公积金")
    private BigDecimal b200006520109;

    @LogField(tableName = "b200006", value = "b200006_520110", valueName = "职工福利费")
    @ApiModelProperty(value = "职工福利费")
    private BigDecimal b200006520110;

    @LogField(tableName = "b200006", value = "b200006_520111", valueName = "补充养老保险")
    @ApiModelProperty(value = "补充养老保险")
    private BigDecimal b200006520111;

    @LogField(tableName = "b200006", value = "b200006_520112", valueName = "补充医疗保险")
    @ApiModelProperty(value = "补充医疗保险")
    private BigDecimal b200006520112;

    @LogField(tableName = "b200006", value = "b200006_520113", valueName = "其他职工工资")
    @ApiModelProperty(value = "其他职工工资")
    private BigDecimal b200006520113;

    @LogField(tableName = "b200006", value = "b200006_520114", valueName = "商业保险费")
    @ApiModelProperty(value = "商业保险费")
    private BigDecimal b200006520114;

    @LogField(tableName = "b200006", value = "b200006_520115", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200006520115;

    @LogField(tableName = "b200006", value = "b200006_5301", valueName = "研发支出")
    @ApiModelProperty(value = "研发支出")
    private BigDecimal b2000065301;

    @LogField(tableName = "b200006", value = "b200006_530101", valueName = "资本化支出")
    @ApiModelProperty(value = "资本化支出")
    private BigDecimal b200006530101;

    @LogField(tableName = "b200006", value = "b200006_53010101", valueName = "人工成本")
    @ApiModelProperty(value = "人工成本")
    private BigDecimal b20000653010101;

    @LogField(tableName = "b200006", value = "b200006_5301010101", valueName = "职工工资")
    @ApiModelProperty(value = "职工工资")
    private BigDecimal b2000065301010101;

    @LogField(tableName = "b200006", value = "b200006_5301010102", valueName = "年终奖")
    @ApiModelProperty(value = "年终奖")
    private BigDecimal b2000065301010102;

    @LogField(tableName = "b200006", value = "b200006_5301010103", valueName = "津贴与补贴")
    @ApiModelProperty(value = "津贴与补贴")
    private BigDecimal b2000065301010103;

    @LogField(tableName = "b200006", value = "b200006_5301010104", valueName = "股权激励")
    @ApiModelProperty(value = "股权激励")
    private BigDecimal b2000065301010104;

    @LogField(tableName = "b200006", value = "b200006_5301010105", valueName = "职工教育经费")
    @ApiModelProperty(value = "职工教育经费")
    private BigDecimal b2000065301010105;

    @LogField(tableName = "b200006", value = "b200006_5301010106", valueName = "工会经费")
    @ApiModelProperty(value = "工会经费")
    private BigDecimal b2000065301010106;

    @LogField(tableName = "b200006", value = "b200006_5301010107", valueName = "社会保险费")
    @ApiModelProperty(value = "社会保险费")
    private BigDecimal b2000065301010107;

    @LogField(tableName = "b200006", value = "b200006_5301010108", valueName = "商业保险费")
    @ApiModelProperty(value = "商业保险费")
    private BigDecimal b2000065301010108;

    @LogField(tableName = "b200006", value = "b200006_5301010109", valueName = "住房公积金")
    @ApiModelProperty(value = "住房公积金")
    private BigDecimal b2000065301010109;

    @LogField(tableName = "b200006", value = "b200006_5301010110", valueName = "职工福利费")
    @ApiModelProperty(value = "职工福利费")
    private BigDecimal b2000065301010110;

    @LogField(tableName = "b200006", value = "b200006_5301010111", valueName = "补充养老保险")
    @ApiModelProperty(value = "补充养老保险")
    private BigDecimal b2000065301010111;

    @LogField(tableName = "b200006", value = "b200006_5301010112", valueName = "补充医疗保险")
    @ApiModelProperty(value = "补充医疗保险")
    private BigDecimal b2000065301010112;

    @LogField(tableName = "b200006", value = "b200006_5301010113", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000065301010113;

    @LogField(tableName = "b200006", value = "b200006_53010102", valueName = "劳务费")
    @ApiModelProperty(value = "劳务费")
    private BigDecimal b20000653010102;

    @LogField(tableName = "b200006", value = "b200006_53010103", valueName = "直接投入")
    @ApiModelProperty(value = "直接投入")
    private BigDecimal b20000653010103;

    @LogField(tableName = "b200006", value = "b200006_5301010301", valueName = "材料费用")
    @ApiModelProperty(value = "材料费用")
    private BigDecimal b2000065301010301;

    @LogField(tableName = "b200006", value = "b200006_5301010302", valueName = "燃料费用")
    @ApiModelProperty(value = "燃料费用")
    private BigDecimal b2000065301010302;

    @LogField(tableName = "b200006", value = "b200006_5301010303", valueName = "动力费用")
    @ApiModelProperty(value = "动力费用")
    private BigDecimal b2000065301010303;

    @LogField(tableName = "b200006", value = "b200006_5301010304", valueName = "试用品开发及制造费")
    @ApiModelProperty(value = "试用品开发及制造费")
    private BigDecimal b2000065301010304;

    @LogField(tableName = "b200006", value = "b200006_5301010305", valueName = "样品测试购置费")
    @ApiModelProperty(value = "样品测试购置费")
    private BigDecimal b2000065301010305;

    @LogField(tableName = "b200006", value = "b200006_5301010306", valueName = "试制品检验费")
    @ApiModelProperty(value = "试制品检验费")
    private BigDecimal b2000065301010306;

    @LogField(tableName = "b200006", value = "b200006_5301010307", valueName = "运行维护维修费")
    @ApiModelProperty(value = "运行维护维修费")
    private BigDecimal b2000065301010307;

    @LogField(tableName = "b200006", value = "b200006_5301010308", valueName = "运行调整费")
    @ApiModelProperty(value = "运行调整费")
    private BigDecimal b2000065301010308;

    @LogField(tableName = "b200006", value = "b200006_5301010309", valueName = "运行检测费")
    @ApiModelProperty(value = "运行检测费")
    private BigDecimal b2000065301010309;

    @LogField(tableName = "b200006", value = "b200006_5301010310", valueName = "仪器租赁费")
    @ApiModelProperty(value = "仪器租赁费")
    private BigDecimal b2000065301010310;

    @LogField(tableName = "b200006", value = "b200006_5301010311", valueName = "设备租赁费")
    @ApiModelProperty(value = "设备租赁费")
    private BigDecimal b2000065301010311;

    @LogField(tableName = "b200006", value = "b200006_5301010312", valueName = "房屋租赁费")
    @ApiModelProperty(value = "房屋租赁费")
    private BigDecimal b2000065301010312;

    @LogField(tableName = "b200006", value = "b200006_5301010313", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000065301010313;

    @LogField(tableName = "b200006", value = "b200006_53010104", valueName = "低值易耗品摊销")
    @ApiModelProperty(value = "低值易耗品摊销")
    private BigDecimal b20000653010104;

    @LogField(tableName = "b200006", value = "b200006_5301010401", valueName = "直接消耗")
    @ApiModelProperty(value = "直接消耗")
    private BigDecimal b2000065301010401;

    @LogField(tableName = "b200006", value = "b200006_5301010402", valueName = "其他消耗")
    @ApiModelProperty(value = "其他消耗")
    private BigDecimal b2000065301010402;

    @LogField(tableName = "b200006", value = "b200006_53010105", valueName = "物料消耗")
    @ApiModelProperty(value = "物料消耗")
    private BigDecimal b20000653010105;

    @LogField(tableName = "b200006", value = "b200006_5301010501", valueName = "直接消耗")
    @ApiModelProperty(value = "直接消耗")
    private BigDecimal b2000065301010501;

    @LogField(tableName = "b200006", value = "b200006_5301010502", valueName = "其他消耗")
    @ApiModelProperty(value = "其他消耗")
    private BigDecimal b2000065301010502;

    @LogField(tableName = "b200006", value = "b200006_53010106", valueName = "折旧费用")
    @ApiModelProperty(value = "折旧费用")
    private BigDecimal b20000653010106;

    @LogField(tableName = "b200006", value = "b200006_5301010601", valueName = "仪器折旧费")
    @ApiModelProperty(value = "仪器折旧费")
    private BigDecimal b2000065301010601;

    @LogField(tableName = "b200006", value = "b200006_5301010602", valueName = "设备折旧费")
    @ApiModelProperty(value = "设备折旧费")
    private BigDecimal b2000065301010602;

    @LogField(tableName = "b200006", value = "b200006_5301010603", valueName = "房屋折旧费")
    @ApiModelProperty(value = "房屋折旧费")
    private BigDecimal b2000065301010603;

    @LogField(tableName = "b200006", value = "b200006_5301010604", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000065301010604;

    @LogField(tableName = "b200006", value = "b200006_53010107", valueName = "长期待摊费用")
    @ApiModelProperty(value = "长期待摊费用")
    private BigDecimal b20000653010107;

    @LogField(tableName = "b200006", value = "b200006_5301010701", valueName = "设施改建及改装费")
    @ApiModelProperty(value = "设施改建及改装费")
    private BigDecimal b2000065301010701;

    @LogField(tableName = "b200006", value = "b200006_5301010702", valueName = "设施装修及修理费")
    @ApiModelProperty(value = "设施装修及修理费")
    private BigDecimal b2000065301010702;

    @LogField(tableName = "b200006", value = "b200006_5301010703", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000065301010703;

    @LogField(tableName = "b200006", value = "b200006_53010108", valueName = "无形资产摊销")
    @ApiModelProperty(value = "无形资产摊销")
    private BigDecimal b20000653010108;

    @LogField(tableName = "b200006", value = "b200006_5301010801", valueName = "软件")
    @ApiModelProperty(value = "软件")
    private BigDecimal b2000065301010801;

    @LogField(tableName = "b200006", value = "b200006_5301010802", valueName = "专利权")
    @ApiModelProperty(value = "专利权")
    private BigDecimal b2000065301010802;

    @LogField(tableName = "b200006", value = "b200006_5301010803", valueName = "商标")
    @ApiModelProperty(value = "商标")
    private BigDecimal b2000065301010803;

    @LogField(tableName = "b200006", value = "b200006_5301010804", valueName = "非专利技术")
    @ApiModelProperty(value = "非专利技术")
    private BigDecimal b2000065301010804;

    @LogField(tableName = "b200006", value = "b200006_5301010805", valueName = "知识产权")
    @ApiModelProperty(value = "知识产权")
    private BigDecimal b2000065301010805;

    @LogField(tableName = "b200006", value = "b200006_5301010806", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000065301010806;

    @LogField(tableName = "b200006", value = "b200006_53010109", valueName = "设计费用")
    @ApiModelProperty(value = "设计费用")
    private BigDecimal b20000653010109;

    @LogField(tableName = "b200006", value = "b200006_5301010901", valueName = "新产品设计费")
    @ApiModelProperty(value = "新产品设计费")
    private BigDecimal b2000065301010901;

    @LogField(tableName = "b200006", value = "b200006_5301010902", valueName = "新工艺规程制定费")
    @ApiModelProperty(value = "新工艺规程制定费")
    private BigDecimal b2000065301010902;

    @LogField(tableName = "b200006", value = "b200006_5301010903", valueName = "新产品与新工艺工序设计费")
    @ApiModelProperty(value = "新产品与新工艺工序设计费")
    private BigDecimal b2000065301010903;

    @LogField(tableName = "b200006", value = "b200006_5301010904", valueName = "新产品与新工艺技术规范费")
    @ApiModelProperty(value = "新产品与新工艺技术规范费")
    private BigDecimal b2000065301010904;

    @LogField(tableName = "b200006", value = "b200006_5301010905", valueName = "新产品与新工艺操作设计费")
    @ApiModelProperty(value = "新产品与新工艺操作设计费")
    private BigDecimal b2000065301010905;

    @LogField(tableName = "b200006", value = "b200006_5301010906", valueName = "创意设计活动费")
    @ApiModelProperty(value = "创意设计活动费")
    private BigDecimal b2000065301010906;

    @LogField(tableName = "b200006", value = "b200006_5301010907", valueName = "其他设计费用")
    @ApiModelProperty(value = "其他设计费用")
    private BigDecimal b2000065301010907;

    @LogField(tableName = "b200006", value = "b200006_53010110", valueName = "装备调试费")
    @ApiModelProperty(value = "装备调试费")
    private BigDecimal b20000653010110;

    @LogField(tableName = "b200006", value = "b200006_53010111", valueName = "试验费")
    @ApiModelProperty(value = "试验费")
    private BigDecimal b20000653010111;

    @LogField(tableName = "b200006", value = "b200006_5301011101", valueName = "新药临床试验费")
    @ApiModelProperty(value = "新药临床试验费")
    private BigDecimal b2000065301011101;

    @LogField(tableName = "b200006", value = "b200006_5301011102", valueName = "勘探开发技术现场试验费")
    @ApiModelProperty(value = "勘探开发技术现场试验费")
    private BigDecimal b2000065301011102;

    @LogField(tableName = "b200006", value = "b200006_5301011103", valueName = "田间试验费")
    @ApiModelProperty(value = "田间试验费")
    private BigDecimal b2000065301011103;

    @LogField(tableName = "b200006", value = "b200006_5301011104", valueName = "其他试验费用")
    @ApiModelProperty(value = "其他试验费用")
    private BigDecimal b2000065301011104;

    @LogField(tableName = "b200006", value = "b200006_53010112", valueName = "图书资料费")
    @ApiModelProperty(value = "图书资料费")
    private BigDecimal b20000653010112;

    @LogField(tableName = "b200006", value = "b200006_53010113", valueName = "资料翻译费")
    @ApiModelProperty(value = "资料翻译费")
    private BigDecimal b20000653010113;

    @LogField(tableName = "b200006", value = "b200006_53010114", valueName = "专家咨询费")
    @ApiModelProperty(value = "专家咨询费")
    private BigDecimal b20000653010114;

    @LogField(tableName = "b200006", value = "b200006_53010115", valueName = "高新科技研发保险费")
    @ApiModelProperty(value = "高新科技研发保险费")
    private BigDecimal b20000653010115;

    @LogField(tableName = "b200006", value = "b200006_53010116", valueName = "成果费用")
    @ApiModelProperty(value = "成果费用")
    private BigDecimal b20000653010116;

    @LogField(tableName = "b200006", value = "b200006_5301011601", valueName = "检索费")
    @ApiModelProperty(value = "检索费")
    private BigDecimal b2000065301011601;

    @LogField(tableName = "b200006", value = "b200006_5301011602", valueName = "分析费")
    @ApiModelProperty(value = "分析费")
    private BigDecimal b2000065301011602;

    @LogField(tableName = "b200006", value = "b200006_5301011603", valueName = "评议费")
    @ApiModelProperty(value = "评议费")
    private BigDecimal b2000065301011603;

    @LogField(tableName = "b200006", value = "b200006_5301011604", valueName = "论证费")
    @ApiModelProperty(value = "论证费")
    private BigDecimal b2000065301011604;

    @LogField(tableName = "b200006", value = "b200006_5301011605", valueName = "鉴定费")
    @ApiModelProperty(value = "鉴定费")
    private BigDecimal b2000065301011605;

    @LogField(tableName = "b200006", value = "b200006_5301011606", valueName = "评审费")
    @ApiModelProperty(value = "评审费")
    private BigDecimal b2000065301011606;

    @LogField(tableName = "b200006", value = "b200006_5301011607", valueName = "评估费")
    @ApiModelProperty(value = "评估费")
    private BigDecimal b2000065301011607;

    @LogField(tableName = "b200006", value = "b200006_5301011608", valueName = "验收费")
    @ApiModelProperty(value = "验收费")
    private BigDecimal b2000065301011608;

    @LogField(tableName = "b200006", value = "b200006_5301011609", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000065301011609;

    @LogField(tableName = "b200006", value = "b200006_53010117", valueName = "知识产权费用")
    @ApiModelProperty(value = "知识产权费用")
    private BigDecimal b20000653010117;

    @LogField(tableName = "b200006", value = "b200006_5301011701", valueName = "申请费")
    @ApiModelProperty(value = "申请费")
    private BigDecimal b2000065301011701;

    @LogField(tableName = "b200006", value = "b200006_5301011702", valueName = "注册费")
    @ApiModelProperty(value = "注册费")
    private BigDecimal b2000065301011702;

    @LogField(tableName = "b200006", value = "b200006_5301011703", valueName = "代理费")
    @ApiModelProperty(value = "代理费")
    private BigDecimal b2000065301011703;

    @LogField(tableName = "b200006", value = "b200006_5301011704", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000065301011704;

    @LogField(tableName = "b200006", value = "b200006_53010118", valueName = "差旅费")
    @ApiModelProperty(value = "差旅费")
    private BigDecimal b20000653010118;

    @LogField(tableName = "b200006", value = "b200006_53010119", valueName = "会议费")
    @ApiModelProperty(value = "会议费")
    private BigDecimal b20000653010119;

    @LogField(tableName = "b200006", value = "b200006_53010120", valueName = "通讯费")
    @ApiModelProperty(value = "通讯费")
    private BigDecimal b20000653010120;

    @LogField(tableName = "b200006", value = "b200006_53010121", valueName = "办公费")
    @ApiModelProperty(value = "办公费")
    private BigDecimal b20000653010121;

    @LogField(tableName = "b200006", value = "b200006_53010122", valueName = "外事费")
    @ApiModelProperty(value = "外事费")
    private BigDecimal b20000653010122;

    @LogField(tableName = "b200006", value = "b200006_53010123", valueName = "研发人员培训费")
    @ApiModelProperty(value = "研发人员培训费")
    private BigDecimal b20000653010123;

    @LogField(tableName = "b200006", value = "b200006_53010124", valueName = "研发人员培养费")
    @ApiModelProperty(value = "研发人员培养费")
    private BigDecimal b20000653010124;

    @LogField(tableName = "b200006", value = "b200006_53010125", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b20000653010125;

    @LogField(tableName = "b200006", value = "b200006_53010126", valueName = "外包费用")
    @ApiModelProperty(value = "外包费用")
    private BigDecimal b20000653010126;

    @LogField(tableName = "b200006", value = "b200006_5301012601", valueName = "委托研发（境内进行）")
    @ApiModelProperty(value = "委托研发（境内进行）")
    private BigDecimal b2000065301012601;

    @LogField(tableName = "b200006", value = "b200006_5301012602", valueName = "委托研发（境外机构）")
    @ApiModelProperty(value = "委托研发（境外机构）")
    private BigDecimal b2000065301012602;

    @LogField(tableName = "b200006", value = "b200006_5301012603", valueName = "委托研发（境外个人）")
    @ApiModelProperty(value = "委托研发（境外个人）")
    private BigDecimal b2000065301012603;

    @LogField(tableName = "b200006", value = "b200006_5301012604", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000065301012604;

    @LogField(tableName = "b200006", value = "b200006_530102", valueName = "费用化支出")
    @ApiModelProperty(value = "费用化支出")
    private BigDecimal b200006530102;

    @LogField(tableName = "b200006", value = "b200006_53010201", valueName = "人工成本")
    @ApiModelProperty(value = "人工成本")
    private BigDecimal b20000653010201;

    @LogField(tableName = "b200006", value = "b200006_5301020101", valueName = "职工工资")
    @ApiModelProperty(value = "职工工资")
    private BigDecimal b2000065301020101;

    @LogField(tableName = "b200006", value = "b200006_5301020102", valueName = "年终奖")
    @ApiModelProperty(value = "年终奖")
    private BigDecimal b2000065301020102;

    @LogField(tableName = "b200006", value = "b200006_5301020103", valueName = "津贴与补贴")
    @ApiModelProperty(value = "津贴与补贴")
    private BigDecimal b2000065301020103;

    @LogField(tableName = "b200006", value = "b200006_5301020104", valueName = "股权激励")
    @ApiModelProperty(value = "股权激励")
    private BigDecimal b2000065301020104;

    @LogField(tableName = "b200006", value = "b200006_5301020105", valueName = "职工教育经费")
    @ApiModelProperty(value = "职工教育经费")
    private BigDecimal b2000065301020105;

    @LogField(tableName = "b200006", value = "b200006_5301020106", valueName = "工会经费")
    @ApiModelProperty(value = "工会经费")
    private BigDecimal b2000065301020106;

    @LogField(tableName = "b200006", value = "b200006_5301020107", valueName = "社会保险费")
    @ApiModelProperty(value = "社会保险费")
    private BigDecimal b2000065301020107;

    @LogField(tableName = "b200006", value = "b200006_5301020108", valueName = "商业保险费")
    @ApiModelProperty(value = "商业保险费")
    private BigDecimal b2000065301020108;

    @LogField(tableName = "b200006", value = "b200006_5301020109", valueName = "住房公积金")
    @ApiModelProperty(value = "住房公积金")
    private BigDecimal b2000065301020109;

    @LogField(tableName = "b200006", value = "b200006_5301020110", valueName = "职工福利费")
    @ApiModelProperty(value = "职工福利费")
    private BigDecimal b2000065301020110;

    @LogField(tableName = "b200006", value = "b200006_5301020111", valueName = "补充养老保险")
    @ApiModelProperty(value = "补充养老保险")
    private BigDecimal b2000065301020111;

    @LogField(tableName = "b200006", value = "b200006_5301020112", valueName = "补充养老保险")
    @ApiModelProperty(value = "补充养老保险")
    private BigDecimal b2000065301020112;

    @LogField(tableName = "b200006", value = "b200006_5301020113", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000065301020113;

    @LogField(tableName = "b200006", value = "b200006_53010202", valueName = "劳务费")
    @ApiModelProperty(value = "劳务费")
    private BigDecimal b20000653010202;

    @LogField(tableName = "b200006", value = "b200006_53010203", valueName = "直接投入")
    @ApiModelProperty(value = "直接投入")
    private BigDecimal b20000653010203;

    @LogField(tableName = "b200006", value = "b200006_5301020301", valueName = "材料费用")
    @ApiModelProperty(value = "材料费用")
    private BigDecimal b2000065301020301;

    @LogField(tableName = "b200006", value = "b200006_5301020302", valueName = "燃料费用")
    @ApiModelProperty(value = "燃料费用")
    private BigDecimal b2000065301020302;

    @LogField(tableName = "b200006", value = "b200006_5301020303", valueName = "动力费用")
    @ApiModelProperty(value = "动力费用")
    private BigDecimal b2000065301020303;

    @LogField(tableName = "b200006", value = "b200006_5301020304", valueName = "试用品开发及制造费")
    @ApiModelProperty(value = "试用品开发及制造费")
    private BigDecimal b2000065301020304;

    @LogField(tableName = "b200006", value = "b200006_5301020305", valueName = "样品测试购置费")
    @ApiModelProperty(value = "样品测试购置费")
    private BigDecimal b2000065301020305;

    @LogField(tableName = "b200006", value = "b200006_5301020306", valueName = "试制品检验费")
    @ApiModelProperty(value = "试制品检验费")
    private BigDecimal b2000065301020306;

    @LogField(tableName = "b200006", value = "b200006_5301020307", valueName = "运行维护维修费")
    @ApiModelProperty(value = "运行维护维修费")
    private BigDecimal b2000065301020307;

    @LogField(tableName = "b200006", value = "b200006_5301020308", valueName = "运行调整费")
    @ApiModelProperty(value = "运行调整费")
    private BigDecimal b2000065301020308;

    @LogField(tableName = "b200006", value = "b200006_5301020309", valueName = "运行检测费")
    @ApiModelProperty(value = "运行检测费")
    private BigDecimal b2000065301020309;

    @LogField(tableName = "b200006", value = "b200006_5301020310", valueName = "仪器租赁费")
    @ApiModelProperty(value = "仪器租赁费")
    private BigDecimal b2000065301020310;

    @LogField(tableName = "b200006", value = "b200006_5301020311", valueName = "设备租赁费")
    @ApiModelProperty(value = "设备租赁费")
    private BigDecimal b2000065301020311;

    @LogField(tableName = "b200006", value = "b200006_5301020312", valueName = "房屋租赁费")
    @ApiModelProperty(value = "房屋租赁费")
    private BigDecimal b2000065301020312;

    @LogField(tableName = "b200006", value = "b200006_5301020313", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000065301020313;

    @LogField(tableName = "b200006", value = "b200006_53010204", valueName = "低值易耗品摊销")
    @ApiModelProperty(value = "低值易耗品摊销")
    private BigDecimal b20000653010204;

    @LogField(tableName = "b200006", value = "b200006_5301020401", valueName = "直接消耗")
    @ApiModelProperty(value = "直接消耗")
    private BigDecimal b2000065301020401;

    @LogField(tableName = "b200006", value = "b200006_5301020402", valueName = "其他消耗")
    @ApiModelProperty(value = "其他消耗")
    private BigDecimal b2000065301020402;

    @LogField(tableName = "b200006", value = "b200006_53010205", valueName = "物料消耗")
    @ApiModelProperty(value = "物料消耗")
    private BigDecimal b20000653010205;

    @LogField(tableName = "b200006", value = "b200006_5301020501", valueName = "直接消耗")
    @ApiModelProperty(value = "直接消耗")
    private BigDecimal b2000065301020501;

    @LogField(tableName = "b200006", value = "b200006_5301020502", valueName = "其他消耗")
    @ApiModelProperty(value = "其他消耗")
    private BigDecimal b2000065301020502;

    @LogField(tableName = "b200006", value = "b200006_53010206", valueName = "折旧费用")
    @ApiModelProperty(value = "折旧费用")
    private BigDecimal b20000653010206;

    @LogField(tableName = "b200006", value = "b200006_5301020601", valueName = "仪器折旧费")
    @ApiModelProperty(value = "仪器折旧费")
    private BigDecimal b2000065301020601;

    @LogField(tableName = "b200006", value = "b200006_5301020602", valueName = "设备折旧费")
    @ApiModelProperty(value = "设备折旧费")
    private BigDecimal b2000065301020602;

    @LogField(tableName = "b200006", value = "b200006_5301020603", valueName = "房屋折旧费")
    @ApiModelProperty(value = "房屋折旧费")
    private BigDecimal b2000065301020603;

    @LogField(tableName = "b200006", value = "b200006_5301020604", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000065301020604;

    @LogField(tableName = "b200006", value = "b200006_53010207", valueName = "长期待摊费用")
    @ApiModelProperty(value = "长期待摊费用")
    private BigDecimal b20000653010207;

    @LogField(tableName = "b200006", value = "b200006_5301020701", valueName = "设施改建及改装费")
    @ApiModelProperty(value = "设施改建及改装费")
    private BigDecimal b2000065301020701;

    @LogField(tableName = "b200006", value = "b200006_5301020702", valueName = "设施装修及修理费")
    @ApiModelProperty(value = "设施装修及修理费")
    private BigDecimal b2000065301020702;

    @LogField(tableName = "b200006", value = "b200006_5301020703", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000065301020703;

    @LogField(tableName = "b200006", value = "b200006_53010208", valueName = "无形资产摊销")
    @ApiModelProperty(value = "无形资产摊销")
    private BigDecimal b20000653010208;

    @LogField(tableName = "b200006", value = "b200006_5301020801", valueName = "软件")
    @ApiModelProperty(value = "软件")
    private BigDecimal b2000065301020801;

    @LogField(tableName = "b200006", value = "b200006_5301020802", valueName = "专利权")
    @ApiModelProperty(value = "专利权")
    private BigDecimal b2000065301020802;

    @LogField(tableName = "b200006", value = "b200006_5301020803", valueName = "商标")
    @ApiModelProperty(value = "商标")
    private BigDecimal b2000065301020803;

    @LogField(tableName = "b200006", value = "b200006_5301020804", valueName = "非专利技术")
    @ApiModelProperty(value = "非专利技术")
    private BigDecimal b2000065301020804;

    @LogField(tableName = "b200006", value = "b200006_5301020805", valueName = "知识产权")
    @ApiModelProperty(value = "知识产权")
    private BigDecimal b2000065301020805;

    @LogField(tableName = "b200006", value = "b200006_5301020806", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000065301020806;

    @LogField(tableName = "b200006", value = "b200006_53010209", valueName = "设计费用")
    @ApiModelProperty(value = "设计费用")
    private BigDecimal b20000653010209;

    @LogField(tableName = "b200006", value = "b200006_5301020901", valueName = "新产品设计费")
    @ApiModelProperty(value = "新产品设计费")
    private BigDecimal b2000065301020901;

    @LogField(tableName = "b200006", value = "b200006_5301020902", valueName = "新工艺规程制定费")
    @ApiModelProperty(value = "新工艺规程制定费")
    private BigDecimal b2000065301020902;

    @LogField(tableName = "b200006", value = "b200006_5301020903", valueName = "新产品与新工艺工序设计费")
    @ApiModelProperty(value = "新产品与新工艺工序设计费")
    private BigDecimal b2000065301020903;

    @LogField(tableName = "b200006", value = "b200006_5301020904", valueName = "新产品与新工艺技术规范费")
    @ApiModelProperty(value = "新产品与新工艺技术规范费")
    private BigDecimal b2000065301020904;

    @LogField(tableName = "b200006", value = "b200006_5301020905", valueName = "新产品与新工艺操作设计费")
    @ApiModelProperty(value = "新产品与新工艺操作设计费")
    private BigDecimal b2000065301020905;

    @LogField(tableName = "b200006", value = "b200006_5301020906", valueName = "创意设计活动费")
    @ApiModelProperty(value = "创意设计活动费")
    private BigDecimal b2000065301020906;

    @LogField(tableName = "b200006", value = "b200006_5301020907", valueName = "其他设计费用")
    @ApiModelProperty(value = "其他设计费用")
    private BigDecimal b2000065301020907;

    @LogField(tableName = "b200006", value = "b200006_53010210", valueName = "装备调试费")
    @ApiModelProperty(value = "装备调试费")
    private BigDecimal b20000653010210;

    @LogField(tableName = "b200006", value = "b200006_53010211", valueName = "试验费")
    @ApiModelProperty(value = "试验费")
    private BigDecimal b20000653010211;

    @LogField(tableName = "b200006", value = "b200006_5301021101", valueName = "新药临床试验费")
    @ApiModelProperty(value = "新药临床试验费")
    private BigDecimal b2000065301021101;

    @LogField(tableName = "b200006", value = "b200006_5301021102", valueName = "勘探开发技术现场试验费")
    @ApiModelProperty(value = "勘探开发技术现场试验费")
    private BigDecimal b2000065301021102;

    @LogField(tableName = "b200006", value = "b200006_5301021103", valueName = "田间试验费")
    @ApiModelProperty(value = "田间试验费")
    private BigDecimal b2000065301021103;

    @LogField(tableName = "b200006", value = "b200006_5301021104", valueName = "其他试验费用")
    @ApiModelProperty(value = "其他试验费用")
    private BigDecimal b2000065301021104;

    @LogField(tableName = "b200006", value = "b200006_53010212", valueName = "图书资料费")
    @ApiModelProperty(value = "图书资料费")
    private BigDecimal b20000653010212;

    @LogField(tableName = "b200006", value = "b200006_53010213", valueName = "资料翻译费")
    @ApiModelProperty(value = "资料翻译费")
    private BigDecimal b20000653010213;

    @LogField(tableName = "b200006", value = "b200006_53010214", valueName = "专家咨询费")
    @ApiModelProperty(value = "专家咨询费")
    private BigDecimal b20000653010214;

    @LogField(tableName = "b200006", value = "b200006_53010215", valueName = "高新科技研发保险费")
    @ApiModelProperty(value = "高新科技研发保险费")
    private BigDecimal b20000653010215;

    @LogField(tableName = "b200006", value = "b200006_53010216", valueName = "成果费用")
    @ApiModelProperty(value = "成果费用")
    private BigDecimal b20000653010216;

    @LogField(tableName = "b200006", value = "b200006_5301021601", valueName = "检索费")
    @ApiModelProperty(value = "检索费")
    private BigDecimal b2000065301021601;

    @LogField(tableName = "b200006", value = "b200006_5301021602", valueName = "分析费")
    @ApiModelProperty(value = "分析费")
    private BigDecimal b2000065301021602;

    @LogField(tableName = "b200006", value = "b200006_5301021603", valueName = "评议费")
    @ApiModelProperty(value = "评议费")
    private BigDecimal b2000065301021603;

    @LogField(tableName = "b200006", value = "b200006_5301021604", valueName = "论证费")
    @ApiModelProperty(value = "论证费")
    private BigDecimal b2000065301021604;

    @LogField(tableName = "b200006", value = "b200006_5301021605", valueName = "鉴定费")
    @ApiModelProperty(value = "鉴定费")
    private BigDecimal b2000065301021605;

    @LogField(tableName = "b200006", value = "b200006_5301021606", valueName = "评审费")
    @ApiModelProperty(value = "评审费")
    private BigDecimal b2000065301021606;

    @LogField(tableName = "b200006", value = "b200006_5301021607", valueName = "评估费")
    @ApiModelProperty(value = "评估费")
    private BigDecimal b2000065301021607;

    @LogField(tableName = "b200006", value = "b200006_5301021608", valueName = "验收费")
    @ApiModelProperty(value = "验收费")
    private BigDecimal b2000065301021608;

    @LogField(tableName = "b200006", value = "b200006_5301021609", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000065301021609;

    @LogField(tableName = "b200006", value = "b200006_53010217", valueName = "知识产权费用")
    @ApiModelProperty(value = "知识产权费用")
    private BigDecimal b20000653010217;

    @LogField(tableName = "b200006", value = "b200006_5301021701", valueName = "申请费")
    @ApiModelProperty(value = "申请费")
    private BigDecimal b2000065301021701;

    @LogField(tableName = "b200006", value = "b200006_5301021702", valueName = "注册费")
    @ApiModelProperty(value = "注册费")
    private BigDecimal b2000065301021702;

    @LogField(tableName = "b200006", value = "b200006_5301021703", valueName = "代理费")
    @ApiModelProperty(value = "代理费")
    private BigDecimal b2000065301021703;

    @LogField(tableName = "b200006", value = "b200006_5301021704", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000065301021704;

    @LogField(tableName = "b200006", value = "b200006_53010218", valueName = "差旅费")
    @ApiModelProperty(value = "差旅费")
    private BigDecimal b20000653010218;

    @LogField(tableName = "b200006", value = "b200006_53010219", valueName = "会议费")
    @ApiModelProperty(value = "会议费")
    private BigDecimal b20000653010219;

    @LogField(tableName = "b200006", value = "b200006_53010220", valueName = "通讯费")
    @ApiModelProperty(value = "通讯费")
    private BigDecimal b20000653010220;

    @LogField(tableName = "b200006", value = "b200006_53010221", valueName = "办公费")
    @ApiModelProperty(value = "办公费")
    private BigDecimal b20000653010221;

    @LogField(tableName = "b200006", value = "b200006_53010222", valueName = "外事费")
    @ApiModelProperty(value = "外事费")
    private BigDecimal b20000653010222;

    @LogField(tableName = "b200006", value = "b200006_53010223", valueName = "研发人员培训费")
    @ApiModelProperty(value = "研发人员培训费")
    private BigDecimal b20000653010223;

    @LogField(tableName = "b200006", value = "b200006_53010224", valueName = "研发人员培养费")
    @ApiModelProperty(value = "研发人员培养费")
    private BigDecimal b20000653010224;

    @LogField(tableName = "b200006", value = "b200006_53010225", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b20000653010225;

    @LogField(tableName = "b200006", value = "b200006_53010226", valueName = "外包费用")
    @ApiModelProperty(value = "外包费用")
    private BigDecimal b20000653010226;

    @LogField(tableName = "b200006", value = "b200006_5301022601", valueName = "委托研发（境内进行）")
    @ApiModelProperty(value = "委托研发（境内进行）")
    private BigDecimal b2000065301022601;

    @LogField(tableName = "b200006", value = "b200006_5301022602", valueName = "委托研发（境外机构）")
    @ApiModelProperty(value = "委托研发（境外机构）")
    private BigDecimal b2000065301022602;

    @LogField(tableName = "b200006", value = "b200006_5301022603", valueName = "委托研发（境外个人）")
    @ApiModelProperty(value = "委托研发（境外个人）")
    private BigDecimal b2000065301022603;

    @LogField(tableName = "b200006", value = "b200006_5301022604", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000065301022604;

    @LogField(tableName = "b200006", value = "b200006_5401", valueName = "工程施工")
    @ApiModelProperty(value = "工程施工")
    private BigDecimal b2000065401;

    @LogField(tableName = "b200006", value = "b200006_5402", valueName = "工程结算")
    @ApiModelProperty(value = "工程结算")
    private BigDecimal b2000065402;

    @LogField(tableName = "b200006", value = "b200006_5403", valueName = "机械作业")
    @ApiModelProperty(value = "机械作业")
    private BigDecimal b2000065403;

    @LogField(tableName = "b200006", value = "b200006_5501", valueName = "合同履约成本")
    @ApiModelProperty(value = "合同履约成本")
    private BigDecimal b2000065501;

    @LogField(tableName = "b200006", value = "b200006_550101", valueName = "职工工资")
    @ApiModelProperty(value = "职工工资")
    private BigDecimal b200006550101;

    @LogField(tableName = "b200006", value = "b200006_550102", valueName = "年终奖")
    @ApiModelProperty(value = "年终奖")
    private BigDecimal b200006550102;

    @LogField(tableName = "b200006", value = "b200006_550103", valueName = "津贴与补贴")
    @ApiModelProperty(value = "津贴与补贴")
    private BigDecimal b200006550103;

    @LogField(tableName = "b200006", value = "b200006_550104", valueName = "股权激励")
    @ApiModelProperty(value = "股权激励")
    private BigDecimal b200006550104;

    @LogField(tableName = "b200006", value = "b200006_550105", valueName = "职工教育经费")
    @ApiModelProperty(value = "职工教育经费")
    private BigDecimal b200006550105;

    @LogField(tableName = "b200006", value = "b200006_550106", valueName = "职工培训费用")
    @ApiModelProperty(value = "职工培训费用")
    private BigDecimal b200006550106;

    @LogField(tableName = "b200006", value = "b200006_550107", valueName = "工会经费")
    @ApiModelProperty(value = "工会经费")
    private BigDecimal b200006550107;

    @LogField(tableName = "b200006", value = "b200006_550108", valueName = "社会保险费")
    @ApiModelProperty(value = "社会保险费")
    private BigDecimal b200006550108;

    @LogField(tableName = "b200006", value = "b200006_550109", valueName = "住房公积金")
    @ApiModelProperty(value = "住房公积金")
    private BigDecimal b200006550109;

    @LogField(tableName = "b200006", value = "b200006_550110", valueName = "职工福利费")
    @ApiModelProperty(value = "职工福利费")
    private BigDecimal b200006550110;

    @LogField(tableName = "b200006", value = "b200006_550111", valueName = "补充养老保险")
    @ApiModelProperty(value = "补充养老保险")
    private BigDecimal b200006550111;

    @LogField(tableName = "b200006", value = "b200006_550112", valueName = "补充医疗保险")
    @ApiModelProperty(value = "补充医疗保险")
    private BigDecimal b200006550112;

    @LogField(tableName = "b200006", value = "b200006_550113", valueName = "其他职工工资")
    @ApiModelProperty(value = "其他职工工资")
    private BigDecimal b200006550113;

    @LogField(tableName = "b200006", value = "b200006_550114", valueName = "商业保险费")
    @ApiModelProperty(value = "商业保险费")
    private BigDecimal b200006550114;

    @LogField(tableName = "b200006", value = "b200006_550115", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200006550115;

    @LogField(tableName = "b200006", value = "b200006_5502", valueName = "合同履约成本减值准备")
    @ApiModelProperty(value = "合同履约成本减值准备")
    private BigDecimal b2000065502;

    @LogField(tableName = "b200006", value = "b200006_5503", valueName = "合同取得成本")
    @ApiModelProperty(value = "合同取得成本")
    private BigDecimal b2000065503;

    @LogField(tableName = "b200006", value = "b200006_5504", valueName = "合同取得成本减值准备")
    @ApiModelProperty(value = "合同取得成本减值准备")
    private BigDecimal b2000065504;

    @LogField(tableName = "b200006", value = "b200006_6001", valueName = "主营业务收入")
    @ApiModelProperty(value = "主营业务收入")
    private BigDecimal b2000066001;

    @LogField(tableName = "b200006", value = "b200006_600101", valueName = "销售商品收入")
    @ApiModelProperty(value = "销售商品收入")
    private BigDecimal b200006600101;

    @LogField(tableName = "b200006", value = "b200006_600102", valueName = "提供劳务收入")
    @ApiModelProperty(value = "提供劳务收入")
    private BigDecimal b200006600102;

    @LogField(tableName = "b200006", value = "b200006_600103", valueName = "建造合同收入")
    @ApiModelProperty(value = "建造合同收入")
    private BigDecimal b200006600103;

    @LogField(tableName = "b200006", value = "b200006_600104", valueName = "让渡资产使用权收入")
    @ApiModelProperty(value = "让渡资产使用权收入")
    private BigDecimal b200006600104;

    @LogField(tableName = "b200006", value = "b200006_600105", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200006600105;

    @LogField(tableName = "b200006", value = "b200006_6011", valueName = "利息收入")
    @ApiModelProperty(value = "利息收入")
    private BigDecimal b2000066011;

    @LogField(tableName = "b200006", value = "b200006_6021", valueName = "手续费及佣金收入")
    @ApiModelProperty(value = "手续费及佣金收入")
    private BigDecimal b2000066021;

    @LogField(tableName = "b200006", value = "b200006_6031", valueName = "保费收入")
    @ApiModelProperty(value = "保费收入")
    private BigDecimal b2000066031;

    @LogField(tableName = "b200006", value = "b200006_6041", valueName = "租赁收入")
    @ApiModelProperty(value = "租赁收入")
    private BigDecimal b2000066041;

    @LogField(tableName = "b200006", value = "b200006_6051", valueName = "其他业务收入")
    @ApiModelProperty(value = "其他业务收入")
    private BigDecimal b2000066051;

    @LogField(tableName = "b200006", value = "b200006_605101", valueName = "销售材料收入")
    @ApiModelProperty(value = "销售材料收入")
    private BigDecimal b200006605101;

    @LogField(tableName = "b200006", value = "b200006_605102", valueName = "出租固定资产收入")
    @ApiModelProperty(value = "出租固定资产收入")
    private BigDecimal b200006605102;

    @LogField(tableName = "b200006", value = "b200006_605103", valueName = "出租无形资产收入")
    @ApiModelProperty(value = "出租无形资产收入")
    private BigDecimal b200006605103;

    @LogField(tableName = "b200006", value = "b200006_605104", valueName = "出租包装物和商品收入")
    @ApiModelProperty(value = "出租包装物和商品收入")
    private BigDecimal b200006605104;

    @LogField(tableName = "b200006", value = "b200006_605105", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200006605105;

    @LogField(tableName = "b200006", value = "b200006_6061", valueName = "汇兑损益")
    @ApiModelProperty(value = "汇兑损益")
    private BigDecimal b2000066061;

    @LogField(tableName = "b200006", value = "b200006_6101", valueName = "公允价值变动损益")
    @ApiModelProperty(value = "公允价值变动损益")
    private BigDecimal b2000066101;

    @LogField(tableName = "b200006", value = "b200006_6111", valueName = "投资收益")
    @ApiModelProperty(value = "投资收益")
    private BigDecimal b2000066111;

    @LogField(tableName = "b200006", value = "b200006_611101", valueName = "交易性金融资产")
    @ApiModelProperty(value = "交易性金融资产")
    private BigDecimal b200006611101;

    @LogField(tableName = "b200006", value = "b200006_61110101", valueName = "国债")
    @ApiModelProperty(value = "国债")
    private BigDecimal b20000661110101;

    @LogField(tableName = "b200006", value = "b200006_6111010101", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000066111010101;

    @LogField(tableName = "b200006", value = "b200006_6111010102", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000066111010102;

    @LogField(tableName = "b200006", value = "b200006_61110102", valueName = "证券投资基金")
    @ApiModelProperty(value = "证券投资基金")
    private BigDecimal b20000661110102;

    @LogField(tableName = "b200006", value = "b200006_6111010201", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000066111010201;

    @LogField(tableName = "b200006", value = "b200006_6111010202", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000066111010202;

    @LogField(tableName = "b200006", value = "b200006_61110103", valueName = "地方政府债券")
    @ApiModelProperty(value = "地方政府债券")
    private BigDecimal b20000661110103;

    @LogField(tableName = "b200006", value = "b200006_6111010301", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000066111010301;

    @LogField(tableName = "b200006", value = "b200006_6111010302", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000066111010302;

    @LogField(tableName = "b200006", value = "b200006_61110104", valueName = "铁路债券")
    @ApiModelProperty(value = "铁路债券")
    private BigDecimal b20000661110104;

    @LogField(tableName = "b200006", value = "b200006_6111010401", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000066111010401;

    @LogField(tableName = "b200006", value = "b200006_6111010402", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000066111010402;

    @LogField(tableName = "b200006", value = "b200006_61110105", valueName = "连续持有公开发行并上市流通12个月及以上的股票")
    @ApiModelProperty(value = "连续持有公开发行并上市流通12个月及以上的股票")
    private BigDecimal b20000661110105;

    @LogField(tableName = "b200006", value = "b200006_6111010501", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000066111010501;

    @LogField(tableName = "b200006", value = "b200006_6111010502", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000066111010502;

    @LogField(tableName = "b200006", value = "b200006_61110106", valueName = "永续债")
    @ApiModelProperty(value = "永续债")
    private BigDecimal b20000661110106;

    @LogField(tableName = "b200006", value = "b200006_6111010601", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000066111010601;

    @LogField(tableName = "b200006", value = "b200006_6111010602", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000066111010602;

    @LogField(tableName = "b200006", value = "b200006_61110107", valueName = "创新企业CDR")
    @ApiModelProperty(value = "创新企业CDR")
    private BigDecimal b20000661110107;

    @LogField(tableName = "b200006", value = "b200006_6111010701", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000066111010701;

    @LogField(tableName = "b200006", value = "b200006_6111010702", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000066111010702;

    @LogField(tableName = "b200006", value = "b200006_61110108", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b20000661110108;

    @LogField(tableName = "b200006", value = "b200006_6111010801", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000066111010801;

    @LogField(tableName = "b200006", value = "b200006_6111010802", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000066111010802;

    @LogField(tableName = "b200006", value = "b200006_611102", valueName = "可供出售金融资产")
    @ApiModelProperty(value = "可供出售金融资产")
    private BigDecimal b200006611102;

    @LogField(tableName = "b200006", value = "b200006_61110201", valueName = "国债")
    @ApiModelProperty(value = "国债")
    private BigDecimal b20000661110201;

    @LogField(tableName = "b200006", value = "b200006_6111020101", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000066111020101;

    @LogField(tableName = "b200006", value = "b200006_6111020102", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000066111020102;

    @LogField(tableName = "b200006", value = "b200006_61110202", valueName = "证券投资基金")
    @ApiModelProperty(value = "证券投资基金")
    private BigDecimal b20000661110202;

    @LogField(tableName = "b200006", value = "b200006_6111020201", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000066111020201;

    @LogField(tableName = "b200006", value = "b200006_6111020202", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000066111020202;

    @LogField(tableName = "b200006", value = "b200006_61110203", valueName = "地方政府债券")
    @ApiModelProperty(value = "地方政府债券")
    private BigDecimal b20000661110203;

    @LogField(tableName = "b200006", value = "b200006_6111020301", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000066111020301;

    @LogField(tableName = "b200006", value = "b200006_6111020302", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000066111020302;

    @LogField(tableName = "b200006", value = "b200006_61110204", valueName = "铁路债券")
    @ApiModelProperty(value = "铁路债券")
    private BigDecimal b20000661110204;

    @LogField(tableName = "b200006", value = "b200006_6111020401", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000066111020401;

    @LogField(tableName = "b200006", value = "b200006_6111020402", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000066111020402;

    @LogField(tableName = "b200006", value = "b200006_61110205", valueName = "连续持有公开发行并上市流通12个月及以上的股票")
    @ApiModelProperty(value = "连续持有公开发行并上市流通12个月及以上的股票")
    private BigDecimal b20000661110205;

    @LogField(tableName = "b200006", value = "b200006_6111020501", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000066111020501;

    @LogField(tableName = "b200006", value = "b200006_6111020502", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000066111020502;

    @LogField(tableName = "b200006", value = "b200006_61110206", valueName = "永续债")
    @ApiModelProperty(value = "永续债")
    private BigDecimal b20000661110206;

    @LogField(tableName = "b200006", value = "b200006_6111020601", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000066111020601;

    @LogField(tableName = "b200006", value = "b200006_6111020602", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000066111020602;

    @LogField(tableName = "b200006", value = "b200006_61110207", valueName = "创新企业CDR")
    @ApiModelProperty(value = "创新企业CDR")
    private BigDecimal b20000661110207;

    @LogField(tableName = "b200006", value = "b200006_6111020701", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000066111020701;

    @LogField(tableName = "b200006", value = "b200006_6111020702", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000066111020702;

    @LogField(tableName = "b200006", value = "b200006_61110208", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b20000661110208;

    @LogField(tableName = "b200006", value = "b200006_6111020801", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000066111020801;

    @LogField(tableName = "b200006", value = "b200006_6111020802", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000066111020802;

    @LogField(tableName = "b200006", value = "b200006_611103", valueName = "持有至到期投资")
    @ApiModelProperty(value = "持有至到期投资")
    private BigDecimal b200006611103;

    @LogField(tableName = "b200006", value = "b200006_61110301", valueName = "国债")
    @ApiModelProperty(value = "国债")
    private BigDecimal b20000661110301;

    @LogField(tableName = "b200006", value = "b200006_6111030101", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000066111030101;

    @LogField(tableName = "b200006", value = "b200006_6111030102", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000066111030102;

    @LogField(tableName = "b200006", value = "b200006_61110302", valueName = "地方政府债券")
    @ApiModelProperty(value = "地方政府债券")
    private BigDecimal b20000661110302;

    @LogField(tableName = "b200006", value = "b200006_6111030201", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000066111030201;

    @LogField(tableName = "b200006", value = "b200006_6111030202", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000066111030202;

    @LogField(tableName = "b200006", value = "b200006_61110303", valueName = "铁路债券")
    @ApiModelProperty(value = "铁路债券")
    private BigDecimal b20000661110303;

    @LogField(tableName = "b200006", value = "b200006_6111030301", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000066111030301;

    @LogField(tableName = "b200006", value = "b200006_6111030302", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000066111030302;

    @LogField(tableName = "b200006", value = "b200006_61110304", valueName = "永续债")
    @ApiModelProperty(value = "永续债")
    private BigDecimal b20000661110304;

    @LogField(tableName = "b200006", value = "b200006_6111030401", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000066111030401;

    @LogField(tableName = "b200006", value = "b200006_6111030402", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000066111030402;

    @LogField(tableName = "b200006", value = "b200006_61110305", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b20000661110305;

    @LogField(tableName = "b200006", value = "b200006_6111030501", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000066111030501;

    @LogField(tableName = "b200006", value = "b200006_6111030502", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000066111030502;

    @LogField(tableName = "b200006", value = "b200006_611104", valueName = "衍生工具")
    @ApiModelProperty(value = "衍生工具")
    private BigDecimal b200006611104;

    @LogField(tableName = "b200006", value = "b200006_61110401", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b20000661110401;

    @LogField(tableName = "b200006", value = "b200006_61110402", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b20000661110402;

    @LogField(tableName = "b200006", value = "b200006_611105", valueName = "交易性金融负债")
    @ApiModelProperty(value = "交易性金融负债")
    private BigDecimal b200006611105;

    @LogField(tableName = "b200006", value = "b200006_61110501", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b20000661110501;

    @LogField(tableName = "b200006", value = "b200006_61110502", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b20000661110502;

    @LogField(tableName = "b200006", value = "b200006_611106", valueName = "长期股权投资")
    @ApiModelProperty(value = "长期股权投资")
    private BigDecimal b200006611106;

    @LogField(tableName = "b200006", value = "b200006_61110601", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b20000661110601;

    @LogField(tableName = "b200006", value = "b200006_61110602", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b20000661110602;

    @LogField(tableName = "b200006", value = "b200006_611107", valueName = "长期债券投资")
    @ApiModelProperty(value = "长期债券投资")
    private BigDecimal b200006611107;

    @LogField(tableName = "b200006", value = "b200006_61110701", valueName = "国债")
    @ApiModelProperty(value = "国债")
    private BigDecimal b20000661110701;

    @LogField(tableName = "b200006", value = "b200006_6111070101", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000066111070101;

    @LogField(tableName = "b200006", value = "b200006_6111070102", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000066111070102;

    @LogField(tableName = "b200006", value = "b200006_61110702", valueName = "地方政府债券")
    @ApiModelProperty(value = "地方政府债券")
    private BigDecimal b20000661110702;

    @LogField(tableName = "b200006", value = "b200006_6111070201", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000066111070201;

    @LogField(tableName = "b200006", value = "b200006_6111070202", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000066111070202;

    @LogField(tableName = "b200006", value = "b200006_61110703", valueName = "铁路债券")
    @ApiModelProperty(value = "铁路债券")
    private BigDecimal b20000661110703;

    @LogField(tableName = "b200006", value = "b200006_6111070301", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000066111070301;

    @LogField(tableName = "b200006", value = "b200006_6111070302", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000066111070302;

    @LogField(tableName = "b200006", value = "b200006_61110704", valueName = "永续债")
    @ApiModelProperty(value = "永续债")
    private BigDecimal b20000661110704;

    @LogField(tableName = "b200006", value = "b200006_6111070401", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000066111070401;

    @LogField(tableName = "b200006", value = "b200006_6111070402", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000066111070402;

    @LogField(tableName = "b200006", value = "b200006_61110705", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b20000661110705;

    @LogField(tableName = "b200006", value = "b200006_6111070501", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000066111070501;

    @LogField(tableName = "b200006", value = "b200006_6111070502", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000066111070502;

    @LogField(tableName = "b200006", value = "b200006_611108", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200006611108;

    @LogField(tableName = "b200006", value = "b200006_61110801", valueName = "国债")
    @ApiModelProperty(value = "国债")
    private BigDecimal b20000661110801;

    @LogField(tableName = "b200006", value = "b200006_6111080101", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000066111080101;

    @LogField(tableName = "b200006", value = "b200006_6111080102", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000066111080102;

    @LogField(tableName = "b200006", value = "b200006_61110802", valueName = "证券投资基金")
    @ApiModelProperty(value = "证券投资基金")
    private BigDecimal b20000661110802;

    @LogField(tableName = "b200006", value = "b200006_6111080201", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000066111080201;

    @LogField(tableName = "b200006", value = "b200006_6111080202", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000066111080202;

    @LogField(tableName = "b200006", value = "b200006_61110803", valueName = "地方政府债券")
    @ApiModelProperty(value = "地方政府债券")
    private BigDecimal b20000661110803;

    @LogField(tableName = "b200006", value = "b200006_6111080301", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000066111080301;

    @LogField(tableName = "b200006", value = "b200006_6111080302", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000066111080302;

    @LogField(tableName = "b200006", value = "b200006_61110804", valueName = "铁路债券")
    @ApiModelProperty(value = "铁路债券")
    private BigDecimal b20000661110804;

    @LogField(tableName = "b200006", value = "b200006_6111080401", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000066111080401;

    @LogField(tableName = "b200006", value = "b200006_6111080402", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000066111080402;

    @LogField(tableName = "b200006", value = "b200006_61110805", valueName = "连续持有公开发行并上市流通12个月及以上的股票")
    @ApiModelProperty(value = "连续持有公开发行并上市流通12个月及以上的股票")
    private BigDecimal b20000661110805;

    @LogField(tableName = "b200006", value = "b200006_6111080501", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000066111080501;

    @LogField(tableName = "b200006", value = "b200006_6111080502", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000066111080502;

    @LogField(tableName = "b200006", value = "b200006_61110806", valueName = "永续债")
    @ApiModelProperty(value = "永续债")
    private BigDecimal b20000661110806;

    @LogField(tableName = "b200006", value = "b200006_6111080601", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000066111080601;

    @LogField(tableName = "b200006", value = "b200006_6111080602", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000066111080602;

    @LogField(tableName = "b200006", value = "b200006_61110807", valueName = "创新企业CDR")
    @ApiModelProperty(value = "创新企业CDR")
    private BigDecimal b20000661110807;

    @LogField(tableName = "b200006", value = "b200006_6111080701", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000066111080701;

    @LogField(tableName = "b200006", value = "b200006_6111080702", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000066111080702;

    @LogField(tableName = "b200006", value = "b200006_61110808", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b20000661110808;

    @LogField(tableName = "b200006", value = "b200006_6111080801", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000066111080801;

    @LogField(tableName = "b200006", value = "b200006_6111080802", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000066111080802;

    @LogField(tableName = "b200006", value = "b200006_6115", valueName = "资产处置损益")
    @ApiModelProperty(value = "资产处置损益")
    private BigDecimal b2000066115;

    @LogField(tableName = "b200006", value = "b200006_611501", valueName = "固定资产损失")
    @ApiModelProperty(value = "固定资产损失")
    private BigDecimal b200006611501;

    @LogField(tableName = "b200006", value = "b200006_611502", valueName = "无形资产损失")
    @ApiModelProperty(value = "无形资产损失")
    private BigDecimal b200006611502;

    @LogField(tableName = "b200006", value = "b200006_611503", valueName = "在建工程损失")
    @ApiModelProperty(value = "在建工程损失")
    private BigDecimal b200006611503;

    @LogField(tableName = "b200006", value = "b200006_611504", valueName = "生产性生物资产损失")
    @ApiModelProperty(value = "生产性生物资产损失")
    private BigDecimal b200006611504;

    @LogField(tableName = "b200006", value = "b200006_611505", valueName = "打包出售资产损失")
    @ApiModelProperty(value = "打包出售资产损失")
    private BigDecimal b200006611505;

    @LogField(tableName = "b200006", value = "b200006_611506", valueName = "其他资产损失")
    @ApiModelProperty(value = "其他资产损失")
    private BigDecimal b200006611506;

    @LogField(tableName = "b200006", value = "b200006_611507", valueName = "资产处置收益")
    @ApiModelProperty(value = "资产处置收益")
    private BigDecimal b200006611507;

    @LogField(tableName = "b200006", value = "b200006_6117", valueName = "其他收益")
    @ApiModelProperty(value = "其他收益")
    private BigDecimal b2000066117;

    @LogField(tableName = "b200006", value = "b200006_6201", valueName = "摊回保险责任准备金")
    @ApiModelProperty(value = "摊回保险责任准备金")
    private BigDecimal b2000066201;

    @LogField(tableName = "b200006", value = "b200006_6202", valueName = "摊回赔付支出")
    @ApiModelProperty(value = "摊回赔付支出")
    private BigDecimal b2000066202;

    @LogField(tableName = "b200006", value = "b200006_6203", valueName = "摊回分保费用")
    @ApiModelProperty(value = "摊回分保费用")
    private BigDecimal b2000066203;

    @LogField(tableName = "b200006", value = "b200006_6301", valueName = "营业外收入")
    @ApiModelProperty(value = "营业外收入")
    private BigDecimal b2000066301;

    @LogField(tableName = "b200006", value = "b200006_630101", valueName = "非流动资产处置利得")
    @ApiModelProperty(value = "非流动资产处置利得")
    private BigDecimal b200006630101;

    @LogField(tableName = "b200006", value = "b200006_630102", valueName = "非货币性资产交换利得")
    @ApiModelProperty(value = "非货币性资产交换利得")
    private BigDecimal b200006630102;

    @LogField(tableName = "b200006", value = "b200006_630103", valueName = "债务重组利得")
    @ApiModelProperty(value = "债务重组利得")
    private BigDecimal b200006630103;

    @LogField(tableName = "b200006", value = "b200006_630104", valueName = "政府补助利得")
    @ApiModelProperty(value = "政府补助利得")
    private BigDecimal b200006630104;

    @LogField(tableName = "b200006", value = "b200006_630105", valueName = "盘盈利得")
    @ApiModelProperty(value = "盘盈利得")
    private BigDecimal b200006630105;

    @LogField(tableName = "b200006", value = "b200006_630106", valueName = "捐赠利得")
    @ApiModelProperty(value = "捐赠利得")
    private BigDecimal b200006630106;

    @LogField(tableName = "b200006", value = "b200006_630107", valueName = "罚没利得")
    @ApiModelProperty(value = "罚没利得")
    private BigDecimal b200006630107;

    @LogField(tableName = "b200006", value = "b200006_630108", valueName = "确实无法偿付的应付款项")
    @ApiModelProperty(value = "确实无法偿付的应付款项")
    private BigDecimal b200006630108;

    @LogField(tableName = "b200006", value = "b200006_630109", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200006630109;

    @LogField(tableName = "b200006", value = "b200006_6401", valueName = "主营业务成本")
    @ApiModelProperty(value = "主营业务成本")
    private BigDecimal b2000066401;

    @LogField(tableName = "b200006", value = "b200006_640101", valueName = "销售商品成本")
    @ApiModelProperty(value = "销售商品成本")
    private BigDecimal b200006640101;

    @LogField(tableName = "b200006", value = "b200006_640102", valueName = "提供劳务成本")
    @ApiModelProperty(value = "提供劳务成本")
    private BigDecimal b200006640102;

    @LogField(tableName = "b200006", value = "b200006_640103", valueName = "职工工资")
    @ApiModelProperty(value = "职工工资")
    private BigDecimal b200006640103;

    @LogField(tableName = "b200006", value = "b200006_640104", valueName = "年终奖")
    @ApiModelProperty(value = "年终奖")
    private BigDecimal b200006640104;

    @LogField(tableName = "b200006", value = "b200006_640105", valueName = "津贴与补贴")
    @ApiModelProperty(value = "津贴与补贴")
    private BigDecimal b200006640105;

    @LogField(tableName = "b200006", value = "b200006_640106", valueName = "股权激励")
    @ApiModelProperty(value = "股权激励")
    private BigDecimal b200006640106;

    @LogField(tableName = "b200006", value = "b200006_640107", valueName = "社会保险费")
    @ApiModelProperty(value = "社会保险费")
    private BigDecimal b200006640107;

    @LogField(tableName = "b200006", value = "b200006_640108", valueName = "住房公积金")
    @ApiModelProperty(value = "住房公积金")
    private BigDecimal b200006640108;

    @LogField(tableName = "b200006", value = "b200006_640109", valueName = "补充养老保险")
    @ApiModelProperty(value = "补充养老保险")
    private BigDecimal b200006640109;

    @LogField(tableName = "b200006", value = "b200006_640110", valueName = "补充医疗保险")
    @ApiModelProperty(value = "补充医疗保险")
    private BigDecimal b200006640110;

    @LogField(tableName = "b200006", value = "b200006_640111", valueName = "职工福利费")
    @ApiModelProperty(value = "职工福利费")
    private BigDecimal b200006640111;

    @LogField(tableName = "b200006", value = "b200006_640112", valueName = "职工教育经费")
    @ApiModelProperty(value = "职工教育经费")
    private BigDecimal b200006640112;

    @LogField(tableName = "b200006", value = "b200006_640113", valueName = "职工培训费用")
    @ApiModelProperty(value = "职工培训费用")
    private BigDecimal b200006640113;

    @LogField(tableName = "b200006", value = "b200006_640114", valueName = "工会经费")
    @ApiModelProperty(value = "工会经费")
    private BigDecimal b200006640114;

    @LogField(tableName = "b200006", value = "b200006_640115", valueName = "其他职工工资")
    @ApiModelProperty(value = "其他职工工资")
    private BigDecimal b200006640115;

    @LogField(tableName = "b200006", value = "b200006_640116", valueName = "商业保险费")
    @ApiModelProperty(value = "商业保险费")
    private BigDecimal b200006640116;

    @LogField(tableName = "b200006", value = "b200006_640117", valueName = "建造合同成本")
    @ApiModelProperty(value = "建造合同成本")
    private BigDecimal b200006640117;

    @LogField(tableName = "b200006", value = "b200006_640118", valueName = "让渡资产使用权成本")
    @ApiModelProperty(value = "让渡资产使用权成本")
    private BigDecimal b200006640118;

    @LogField(tableName = "b200006", value = "b200006_640119", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200006640119;

    @LogField(tableName = "b200006", value = "b200006_6402", valueName = "其他业务成本")
    @ApiModelProperty(value = "其他业务成本")
    private BigDecimal b2000066402;

    @LogField(tableName = "b200006", value = "b200006_640201", valueName = "销售材料成本")
    @ApiModelProperty(value = "销售材料成本")
    private BigDecimal b200006640201;

    @LogField(tableName = "b200006", value = "b200006_640202", valueName = "出租固定资产成本")
    @ApiModelProperty(value = "出租固定资产成本")
    private BigDecimal b200006640202;

    @LogField(tableName = "b200006", value = "b200006_640203", valueName = "出租无形资产成本")
    @ApiModelProperty(value = "出租无形资产成本")
    private BigDecimal b200006640203;

    @LogField(tableName = "b200006", value = "b200006_640204", valueName = "出租包装物和商品成本")
    @ApiModelProperty(value = "出租包装物和商品成本")
    private BigDecimal b200006640204;

    @LogField(tableName = "b200006", value = "b200006_640205", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200006640205;

    @LogField(tableName = "b200006", value = "b200006_6403", valueName = "税金及附加")
    @ApiModelProperty(value = "税金及附加")
    private BigDecimal b2000066403;

    @LogField(tableName = "b200006", value = "b200006_6411", valueName = "利息支出")
    @ApiModelProperty(value = "利息支出")
    private BigDecimal b2000066411;

    @LogField(tableName = "b200006", value = "b200006_6421", valueName = "手续费及佣金支出")
    @ApiModelProperty(value = "手续费及佣金支出")
    private BigDecimal b2000066421;

    @LogField(tableName = "b200006", value = "b200006_6501", valueName = "提取未到期责任准备金")
    @ApiModelProperty(value = "提取未到期责任准备金")
    private BigDecimal b2000066501;

    @LogField(tableName = "b200006", value = "b200006_6502", valueName = "提取保险责任准备金")
    @ApiModelProperty(value = "提取保险责任准备金")
    private BigDecimal b2000066502;

    @LogField(tableName = "b200006", value = "b200006_6511", valueName = "赔付支出")
    @ApiModelProperty(value = "赔付支出")
    private BigDecimal b2000066511;

    @LogField(tableName = "b200006", value = "b200006_6521", valueName = "保单红利支出")
    @ApiModelProperty(value = "保单红利支出")
    private BigDecimal b2000066521;

    @LogField(tableName = "b200006", value = "b200006_6531", valueName = "退保金")
    @ApiModelProperty(value = "退保金")
    private BigDecimal b2000066531;

    @LogField(tableName = "b200006", value = "b200006_6541", valueName = "分出保费")
    @ApiModelProperty(value = "分出保费")
    private BigDecimal b2000066541;

    @LogField(tableName = "b200006", value = "b200006_6542", valueName = "分保费用")
    @ApiModelProperty(value = "分保费用")
    private BigDecimal b2000066542;

    @LogField(tableName = "b200006", value = "b200006_6601", valueName = "销售费用")
    @ApiModelProperty(value = "销售费用")
    private BigDecimal b2000066601;

    @LogField(tableName = "b200006", value = "b200006_660101", valueName = "职工工资")
    @ApiModelProperty(value = "职工工资")
    private BigDecimal b200006660101;

    @LogField(tableName = "b200006", value = "b200006_660102", valueName = "年终奖")
    @ApiModelProperty(value = "年终奖")
    private BigDecimal b200006660102;

    @LogField(tableName = "b200006", value = "b200006_660103", valueName = "津贴与补贴")
    @ApiModelProperty(value = "津贴与补贴")
    private BigDecimal b200006660103;

    @LogField(tableName = "b200006", value = "b200006_660104", valueName = "股权激励")
    @ApiModelProperty(value = "股权激励")
    private BigDecimal b200006660104;

    @LogField(tableName = "b200006", value = "b200006_660105", valueName = "社会保险费")
    @ApiModelProperty(value = "社会保险费")
    private BigDecimal b200006660105;

    @LogField(tableName = "b200006", value = "b200006_660106", valueName = "住房公积金")
    @ApiModelProperty(value = "住房公积金")
    private BigDecimal b200006660106;

    @LogField(tableName = "b200006", value = "b200006_660107", valueName = "补充医疗保险")
    @ApiModelProperty(value = "补充医疗保险")
    private BigDecimal b200006660107;

    @LogField(tableName = "b200006", value = "b200006_660108", valueName = "补充养老保险")
    @ApiModelProperty(value = "补充养老保险")
    private BigDecimal b200006660108;

    @LogField(tableName = "b200006", value = "b200006_660109", valueName = "职工福利费")
    @ApiModelProperty(value = "职工福利费")
    private BigDecimal b200006660109;

    @LogField(tableName = "b200006", value = "b200006_660110", valueName = "职工教育经费")
    @ApiModelProperty(value = "职工教育经费")
    private BigDecimal b200006660110;

    @LogField(tableName = "b200006", value = "b200006_660111", valueName = "职工培训费用")
    @ApiModelProperty(value = "职工培训费用")
    private BigDecimal b200006660111;

    @LogField(tableName = "b200006", value = "b200006_660112", valueName = "工会经费")
    @ApiModelProperty(value = "工会经费")
    private BigDecimal b200006660112;

    @LogField(tableName = "b200006", value = "b200006_660113", valueName = "其他职工工资")
    @ApiModelProperty(value = "其他职工工资")
    private BigDecimal b200006660113;

    @LogField(tableName = "b200006", value = "b200006_660114", valueName = "商业保险费")
    @ApiModelProperty(value = "商业保险费")
    private BigDecimal b200006660114;

    @LogField(tableName = "b200006", value = "b200006_660115", valueName = "折旧费")
    @ApiModelProperty(value = "折旧费")
    private BigDecimal b200006660115;

    @LogField(tableName = "b200006", value = "b200006_660116", valueName = "无形资产摊销费")
    @ApiModelProperty(value = "无形资产摊销费")
    private BigDecimal b200006660116;

    @LogField(tableName = "b200006", value = "b200006_660117", valueName = "长期待摊费用摊销费")
    @ApiModelProperty(value = "长期待摊费用摊销费")
    private BigDecimal b200006660117;

    @LogField(tableName = "b200006", value = "b200006_660118", valueName = "修理费")
    @ApiModelProperty(value = "修理费")
    private BigDecimal b200006660118;

    @LogField(tableName = "b200006", value = "b200006_660119", valueName = "物料消耗")
    @ApiModelProperty(value = "物料消耗")
    private BigDecimal b200006660119;

    @LogField(tableName = "b200006", value = "b200006_660120", valueName = "低值易耗品摊销")
    @ApiModelProperty(value = "低值易耗品摊销")
    private BigDecimal b200006660120;

    @LogField(tableName = "b200006", value = "b200006_660121", valueName = "办公费")
    @ApiModelProperty(value = "办公费")
    private BigDecimal b200006660121;

    @LogField(tableName = "b200006", value = "b200006_660122", valueName = "差旅费")
    @ApiModelProperty(value = "差旅费")
    private BigDecimal b200006660122;

    @LogField(tableName = "b200006", value = "b200006_660123", valueName = "业务招待费")
    @ApiModelProperty(value = "业务招待费")
    private BigDecimal b200006660123;

    @LogField(tableName = "b200006", value = "b200006_660124", valueName = "通信费")
    @ApiModelProperty(value = "通信费")
    private BigDecimal b200006660124;

    @LogField(tableName = "b200006", value = "b200006_660125", valueName = "车辆费")
    @ApiModelProperty(value = "车辆费")
    private BigDecimal b200006660125;

    @LogField(tableName = "b200006", value = "b200006_660126", valueName = "能源费")
    @ApiModelProperty(value = "能源费")
    private BigDecimal b200006660126;

    @LogField(tableName = "b200006", value = "b200006_660127", valueName = "运杂费")
    @ApiModelProperty(value = "运杂费")
    private BigDecimal b200006660127;

    @LogField(tableName = "b200006", value = "b200006_660128", valueName = "交通费")
    @ApiModelProperty(value = "交通费")
    private BigDecimal b200006660128;

    @LogField(tableName = "b200006", value = "b200006_660129", valueName = "房租")
    @ApiModelProperty(value = "房租")
    private BigDecimal b200006660129;

    @LogField(tableName = "b200006", value = "b200006_660130", valueName = "物业管理费")
    @ApiModelProperty(value = "物业管理费")
    private BigDecimal b200006660130;

    @LogField(tableName = "b200006", value = "b200006_660131", valueName = "水电费")
    @ApiModelProperty(value = "水电费")
    private BigDecimal b200006660131;

    @LogField(tableName = "b200006", value = "b200006_660132", valueName = "中介服务费")
    @ApiModelProperty(value = "中介服务费")
    private BigDecimal b200006660132;

    @LogField(tableName = "b200006", value = "b200006_660133", valueName = "快递费")
    @ApiModelProperty(value = "快递费")
    private BigDecimal b200006660133;

    @LogField(tableName = "b200006", value = "b200006_660134", valueName = "保险费")
    @ApiModelProperty(value = "保险费")
    private BigDecimal b200006660134;

    @LogField(tableName = "b200006", value = "b200006_660135", valueName = "租赁费")
    @ApiModelProperty(value = "租赁费")
    private BigDecimal b200006660135;

    @LogField(tableName = "b200006", value = "b200006_660136", valueName = "装卸费")
    @ApiModelProperty(value = "装卸费")
    private BigDecimal b200006660136;

    @LogField(tableName = "b200006", value = "b200006_660137", valueName = "包装费")
    @ApiModelProperty(value = "包装费")
    private BigDecimal b200006660137;

    @LogField(tableName = "b200006", value = "b200006_660138", valueName = "通关费用")
    @ApiModelProperty(value = "通关费用")
    private BigDecimal b200006660138;

    @LogField(tableName = "b200006", value = "b200006_660139", valueName = "宣传展览费")
    @ApiModelProperty(value = "宣传展览费")
    private BigDecimal b200006660139;

    @LogField(tableName = "b200006", value = "b200006_660140", valueName = "仓储费")
    @ApiModelProperty(value = "仓储费")
    private BigDecimal b200006660140;

    @LogField(tableName = "b200006", value = "b200006_660141", valueName = "调试费")
    @ApiModelProperty(value = "调试费")
    private BigDecimal b200006660141;

    @LogField(tableName = "b200006", value = "b200006_660142", valueName = "广告费")
    @ApiModelProperty(value = "广告费")
    private BigDecimal b200006660142;

    @LogField(tableName = "b200006", value = "b200006_660143", valueName = "业务提成/佣金（销售服务费）")
    @ApiModelProperty(value = "业务提成/佣金（销售服务费）")
    private BigDecimal b200006660143;

    @LogField(tableName = "b200006", value = "b200006_660144", valueName = "投标费")
    @ApiModelProperty(value = "投标费")
    private BigDecimal b200006660144;

    @LogField(tableName = "b200006", value = "b200006_660145", valueName = "售后服务费")
    @ApiModelProperty(value = "售后服务费")
    private BigDecimal b200006660145;

    @LogField(tableName = "b200006", value = "b200006_660146", valueName = "其他经营费用")
    @ApiModelProperty(value = "其他经营费用")
    private BigDecimal b200006660146;

    @LogField(tableName = "b200006", value = "b200006_660147", valueName = "劳务费")
    @ApiModelProperty(value = "劳务费")
    private BigDecimal b200006660147;

    @LogField(tableName = "b200006", value = "b200006_660148", valueName = "董事会费")
    @ApiModelProperty(value = "董事会费")
    private BigDecimal b200006660148;

    @LogField(tableName = "b200006", value = "b200006_660149", valueName = "诉讼费")
    @ApiModelProperty(value = "诉讼费")
    private BigDecimal b200006660149;

    @LogField(tableName = "b200006", value = "b200006_660150", valueName = "技术转让费")
    @ApiModelProperty(value = "技术转让费")
    private BigDecimal b200006660150;

    @LogField(tableName = "b200006", value = "b200006_660151", valueName = "研究费用")
    @ApiModelProperty(value = "研究费用")
    private BigDecimal b200006660151;

    @LogField(tableName = "b200006", value = "b200006_660152", valueName = "税金")
    @ApiModelProperty(value = "税金")
    private BigDecimal b200006660152;

    @LogField(tableName = "b200006", value = "b200006_660153", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200006660153;

    @LogField(tableName = "b200006", value = "b200006_6602", valueName = "管理费用")
    @ApiModelProperty(value = "管理费用")
    private BigDecimal b2000066602;

    @LogField(tableName = "b200006", value = "b200006_660201", valueName = "职工工资")
    @ApiModelProperty(value = "职工工资")
    private BigDecimal b200006660201;

    @LogField(tableName = "b200006", value = "b200006_660202", valueName = "年终奖")
    @ApiModelProperty(value = "年终奖")
    private BigDecimal b200006660202;

    @LogField(tableName = "b200006", value = "b200006_660203", valueName = "津贴与补贴")
    @ApiModelProperty(value = "津贴与补贴")
    private BigDecimal b200006660203;

    @LogField(tableName = "b200006", value = "b200006_660204", valueName = "股权激励")
    @ApiModelProperty(value = "股权激励")
    private BigDecimal b200006660204;

    @LogField(tableName = "b200006", value = "b200006_660205", valueName = "社会保险费")
    @ApiModelProperty(value = "社会保险费")
    private BigDecimal b200006660205;

    @LogField(tableName = "b200006", value = "b200006_660206", valueName = "住房公积金")
    @ApiModelProperty(value = "住房公积金")
    private BigDecimal b200006660206;

    @LogField(tableName = "b200006", value = "b200006_660207", valueName = "补充医疗保险")
    @ApiModelProperty(value = "补充医疗保险")
    private BigDecimal b200006660207;

    @LogField(tableName = "b200006", value = "b200006_660208", valueName = "补充养老保险")
    @ApiModelProperty(value = "补充养老保险")
    private BigDecimal b200006660208;

    @LogField(tableName = "b200006", value = "b200006_660209", valueName = "职工福利费")
    @ApiModelProperty(value = "职工福利费")
    private BigDecimal b200006660209;

    @LogField(tableName = "b200006", value = "b200006_660210", valueName = "职工教育经费")
    @ApiModelProperty(value = "职工教育经费")
    private BigDecimal b200006660210;

    @LogField(tableName = "b200006", value = "b200006_660211", valueName = "职工培训费用")
    @ApiModelProperty(value = "职工培训费用")
    private BigDecimal b200006660211;

    @LogField(tableName = "b200006", value = "b200006_660212", valueName = "工会经费")
    @ApiModelProperty(value = "工会经费")
    private BigDecimal b200006660212;

    @LogField(tableName = "b200006", value = "b200006_660213", valueName = "其他职工工资")
    @ApiModelProperty(value = "其他职工工资")
    private BigDecimal b200006660213;

    @LogField(tableName = "b200006", value = "b200006_660214", valueName = "商业保险费")
    @ApiModelProperty(value = "商业保险费")
    private BigDecimal b200006660214;

    @LogField(tableName = "b200006", value = "b200006_660215", valueName = "差旅费")
    @ApiModelProperty(value = "差旅费")
    private BigDecimal b200006660215;

    @LogField(tableName = "b200006", value = "b200006_660216", valueName = "办公费")
    @ApiModelProperty(value = "办公费")
    private BigDecimal b200006660216;

    @LogField(tableName = "b200006", value = "b200006_660217", valueName = "折旧费")
    @ApiModelProperty(value = "折旧费")
    private BigDecimal b200006660217;

    @LogField(tableName = "b200006", value = "b200006_660218", valueName = "修理费")
    @ApiModelProperty(value = "修理费")
    private BigDecimal b200006660218;

    @LogField(tableName = "b200006", value = "b200006_660219", valueName = "物料消耗")
    @ApiModelProperty(value = "物料消耗")
    private BigDecimal b200006660219;

    @LogField(tableName = "b200006", value = "b200006_660220", valueName = "低值易耗品摊销")
    @ApiModelProperty(value = "低值易耗品摊销")
    private BigDecimal b200006660220;

    @LogField(tableName = "b200006", value = "b200006_660221", valueName = "会议费")
    @ApiModelProperty(value = "会议费")
    private BigDecimal b200006660221;

    @LogField(tableName = "b200006", value = "b200006_660222", valueName = "咨询费")
    @ApiModelProperty(value = "咨询费")
    private BigDecimal b200006660222;

    @LogField(tableName = "b200006", value = "b200006_660223", valueName = "软件使用费")
    @ApiModelProperty(value = "软件使用费")
    private BigDecimal b200006660223;

    @LogField(tableName = "b200006", value = "b200006_660224", valueName = "招聘费")
    @ApiModelProperty(value = "招聘费")
    private BigDecimal b200006660224;

    @LogField(tableName = "b200006", value = "b200006_660225", valueName = "专业服务费")
    @ApiModelProperty(value = "专业服务费")
    private BigDecimal b200006660225;

    @LogField(tableName = "b200006", value = "b200006_660226", valueName = "税金")
    @ApiModelProperty(value = "税金")
    private BigDecimal b200006660226;

    @LogField(tableName = "b200006", value = "b200006_660227", valueName = "技术开发费")
    @ApiModelProperty(value = "技术开发费")
    private BigDecimal b200006660227;

    @LogField(tableName = "b200006", value = "b200006_660228", valueName = "技术转让费")
    @ApiModelProperty(value = "技术转让费")
    private BigDecimal b200006660228;

    @LogField(tableName = "b200006", value = "b200006_660229", valueName = "无形资产摊销费")
    @ApiModelProperty(value = "无形资产摊销费")
    private BigDecimal b200006660229;

    @LogField(tableName = "b200006", value = "b200006_660230", valueName = "业务招待费")
    @ApiModelProperty(value = "业务招待费")
    private BigDecimal b200006660230;

    @LogField(tableName = "b200006", value = "b200006_660231", valueName = "研发费用")
    @ApiModelProperty(value = "研发费用")
    private BigDecimal b200006660231;

    @LogField(tableName = "b200006", value = "b200006_660232", valueName = "仓储费用")
    @ApiModelProperty(value = "仓储费用")
    private BigDecimal b200006660232;

    @LogField(tableName = "b200006", value = "b200006_660233", valueName = "保险费")
    @ApiModelProperty(value = "保险费")
    private BigDecimal b200006660233;

    @LogField(tableName = "b200006", value = "b200006_660234", valueName = "房租")
    @ApiModelProperty(value = "房租")
    private BigDecimal b200006660234;

    @LogField(tableName = "b200006", value = "b200006_660235", valueName = "物业管理费")
    @ApiModelProperty(value = "物业管理费")
    private BigDecimal b200006660235;

    @LogField(tableName = "b200006", value = "b200006_660236", valueName = "快递费")
    @ApiModelProperty(value = "快递费")
    private BigDecimal b200006660236;

    @LogField(tableName = "b200006", value = "b200006_660237", valueName = "中介服务费")
    @ApiModelProperty(value = "中介服务费")
    private BigDecimal b200006660237;

    @LogField(tableName = "b200006", value = "b200006_660238", valueName = "开办费")
    @ApiModelProperty(value = "开办费")
    private BigDecimal b200006660238;

    @LogField(tableName = "b200006", value = "b200006_660239", valueName = "水电费")
    @ApiModelProperty(value = "水电费")
    private BigDecimal b200006660239;

    @LogField(tableName = "b200006", value = "b200006_660240", valueName = "运杂费")
    @ApiModelProperty(value = "运杂费")
    private BigDecimal b200006660240;

    @LogField(tableName = "b200006", value = "b200006_660241", valueName = "劳务费")
    @ApiModelProperty(value = "劳务费")
    private BigDecimal b200006660241;

    @LogField(tableName = "b200006", value = "b200006_660242", valueName = "广告费")
    @ApiModelProperty(value = "广告费")
    private BigDecimal b200006660242;

    @LogField(tableName = "b200006", value = "b200006_660243", valueName = "佣金和手续费")
    @ApiModelProperty(value = "佣金和手续费")
    private BigDecimal b200006660243;

    @LogField(tableName = "b200006", value = "b200006_660244", valueName = "董事会费")
    @ApiModelProperty(value = "董事会费")
    private BigDecimal b200006660244;

    @LogField(tableName = "b200006", value = "b200006_660245", valueName = "诉讼费")
    @ApiModelProperty(value = "诉讼费")
    private BigDecimal b200006660245;

    @LogField(tableName = "b200006", value = "b200006_660246", valueName = "包装费")
    @ApiModelProperty(value = "包装费")
    private BigDecimal b200006660246;

    @LogField(tableName = "b200006", value = "b200006_660247", valueName = "党组织工作经费")
    @ApiModelProperty(value = "党组织工作经费")
    private BigDecimal b200006660247;

    @LogField(tableName = "b200006", value = "b200006_660248", valueName = "长期待摊费用摊销费")
    @ApiModelProperty(value = "长期待摊费用摊销费")
    private BigDecimal b200006660248;

    @LogField(tableName = "b200006", value = "b200006_660249", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200006660249;

    @LogField(tableName = "b200006", value = "b200006_660250", valueName = "现金及银行存款损失")
    @ApiModelProperty(value = "现金及银行存款损失")
    private BigDecimal b200006660250;

    @LogField(tableName = "b200006", value = "b200006_660251", valueName = "应收及预付款项坏账损失")
    @ApiModelProperty(value = "应收及预付款项坏账损失")
    private BigDecimal b200006660251;

    @LogField(tableName = "b200006", value = "b200006_660252", valueName = "存货损失")
    @ApiModelProperty(value = "存货损失")
    private BigDecimal b200006660252;

    @LogField(tableName = "b200006", value = "b200006_660253", valueName = "固定资产损失")
    @ApiModelProperty(value = "固定资产损失")
    private BigDecimal b200006660253;

    @LogField(tableName = "b200006", value = "b200006_660254", valueName = "无形资产损失")
    @ApiModelProperty(value = "无形资产损失")
    private BigDecimal b200006660254;

    @LogField(tableName = "b200006", value = "b200006_660255", valueName = "在建工程损失")
    @ApiModelProperty(value = "在建工程损失")
    private BigDecimal b200006660255;

    @LogField(tableName = "b200006", value = "b200006_660256", valueName = "生产性生物资产损失")
    @ApiModelProperty(value = "生产性生物资产损失")
    private BigDecimal b200006660256;

    @LogField(tableName = "b200006", value = "b200006_660257", valueName = "非金融企业债权性投资损失")
    @ApiModelProperty(value = "非金融企业债权性投资损失")
    private BigDecimal b200006660257;

    @LogField(tableName = "b200006", value = "b200006_660258", valueName = "股权（权益）性投资损失")
    @ApiModelProperty(value = "股权（权益）性投资损失")
    private BigDecimal b200006660258;

    @LogField(tableName = "b200006", value = "b200006_660259", valueName = "通过各种交易场所、市场买卖债券、股票、期货、基金以及金融衍生产品等发生的损失")
    @ApiModelProperty(value = "通过各种交易场所、市场买卖债券、股票、期货、基金以及金融衍生产品等发生的损失")
    private BigDecimal b200006660259;

    @LogField(tableName = "b200006", value = "b200006_660260", valueName = "打包出售资产损失")
    @ApiModelProperty(value = "打包出售资产损失")
    private BigDecimal b200006660260;

    @LogField(tableName = "b200006", value = "b200006_660261", valueName = "其他资产损失")
    @ApiModelProperty(value = "其他资产损失")
    private BigDecimal b200006660261;

    @LogField(tableName = "b200006", value = "b200006_6603", valueName = "财务费用")
    @ApiModelProperty(value = "财务费用")
    private BigDecimal b2000066603;

    @LogField(tableName = "b200006", value = "b200006_660301", valueName = "汇兑损益")
    @ApiModelProperty(value = "汇兑损益")
    private BigDecimal b200006660301;

    @LogField(tableName = "b200006", value = "b200006_660302", valueName = "利息费用")
    @ApiModelProperty(value = "利息费用")
    private BigDecimal b200006660302;

    @LogField(tableName = "b200006", value = "b200006_660303", valueName = "佣金和手续费")
    @ApiModelProperty(value = "佣金和手续费")
    private BigDecimal b200006660303;

    @LogField(tableName = "b200006", value = "b200006_660304", valueName = "账户管理费")
    @ApiModelProperty(value = "账户管理费")
    private BigDecimal b200006660304;

    @LogField(tableName = "b200006", value = "b200006_660305", valueName = "利息收入")
    @ApiModelProperty(value = "利息收入")
    private BigDecimal b200006660305;

    @LogField(tableName = "b200006", value = "b200006_66030501", valueName = "因未实现融资收益确认的利息收入")
    @ApiModelProperty(value = "因未实现融资收益确认的利息收入")
    private BigDecimal b20000666030501;

    @LogField(tableName = "b200006", value = "b200006_66030502", valueName = "其他利息收入")
    @ApiModelProperty(value = "其他利息收入")
    private BigDecimal b20000666030502;

    @LogField(tableName = "b200006", value = "b200006_660306", valueName = "现金折扣")
    @ApiModelProperty(value = "现金折扣")
    private BigDecimal b200006660306;

    @LogField(tableName = "b200006", value = "b200006_660307", valueName = "银行手续费")
    @ApiModelProperty(value = "银行手续费")
    private BigDecimal b200006660307;

    @LogField(tableName = "b200006", value = "b200006_660308", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200006660308;

    @LogField(tableName = "b200006", value = "b200006_6604", valueName = "勘探费用")
    @ApiModelProperty(value = "勘探费用")
    private BigDecimal b2000066604;

    @LogField(tableName = "b200006", value = "b200006_6701", valueName = "资产减值损失")
    @ApiModelProperty(value = "资产减值损失")
    private BigDecimal b2000066701;

    @LogField(tableName = "b200006", value = "b200006_6702", valueName = "信用减值损失")
    @ApiModelProperty(value = "信用减值损失")
    private BigDecimal b2000066702;

    @LogField(tableName = "b200006", value = "b200006_6711", valueName = "营业外支出")
    @ApiModelProperty(value = "营业外支出")
    private BigDecimal b2000066711;

    @LogField(tableName = "b200006", value = "b200006_671101", valueName = "非流动资产处置净损失")
    @ApiModelProperty(value = "非流动资产处置净损失")
    private BigDecimal b200006671101;

    @LogField(tableName = "b200006", value = "b200006_67110101", valueName = "存货损失")
    @ApiModelProperty(value = "存货损失")
    private BigDecimal b20000667110101;

    @LogField(tableName = "b200006", value = "b200006_67110102", valueName = "固定资产损失")
    @ApiModelProperty(value = "固定资产损失")
    private BigDecimal b20000667110102;

    @LogField(tableName = "b200006", value = "b200006_67110103", valueName = "无形资产损失")
    @ApiModelProperty(value = "无形资产损失")
    private BigDecimal b20000667110103;

    @LogField(tableName = "b200006", value = "b200006_67110104", valueName = "在建工程损失")
    @ApiModelProperty(value = "在建工程损失")
    private BigDecimal b20000667110104;

    @LogField(tableName = "b200006", value = "b200006_67110105", valueName = "生产性生物资产损失")
    @ApiModelProperty(value = "生产性生物资产损失")
    private BigDecimal b20000667110105;

    @LogField(tableName = "b200006", value = "b200006_67110106", valueName = "打包出售资产损失")
    @ApiModelProperty(value = "打包出售资产损失")
    private BigDecimal b20000667110106;

    @LogField(tableName = "b200006", value = "b200006_67110107", valueName = "其他资产损失")
    @ApiModelProperty(value = "其他资产损失")
    private BigDecimal b20000667110107;

    @LogField(tableName = "b200006", value = "b200006_671102", valueName = "非货币性资产交换损失")
    @ApiModelProperty(value = "非货币性资产交换损失")
    private BigDecimal b200006671102;

    @LogField(tableName = "b200006", value = "b200006_67110201", valueName = "存货损失")
    @ApiModelProperty(value = "存货损失")
    private BigDecimal b20000667110201;

    @LogField(tableName = "b200006", value = "b200006_67110202", valueName = "固定资产损失")
    @ApiModelProperty(value = "固定资产损失")
    private BigDecimal b20000667110202;

    @LogField(tableName = "b200006", value = "b200006_67110203", valueName = "无形资产损失")
    @ApiModelProperty(value = "无形资产损失")
    private BigDecimal b20000667110203;

    @LogField(tableName = "b200006", value = "b200006_67110204", valueName = "在建工程损失")
    @ApiModelProperty(value = "在建工程损失")
    private BigDecimal b20000667110204;

    @LogField(tableName = "b200006", value = "b200006_67110205", valueName = "生产性生物资产损失")
    @ApiModelProperty(value = "生产性生物资产损失")
    private BigDecimal b20000667110205;

    @LogField(tableName = "b200006", value = "b200006_67110206", valueName = "打包出售资产损失")
    @ApiModelProperty(value = "打包出售资产损失")
    private BigDecimal b20000667110206;

    @LogField(tableName = "b200006", value = "b200006_67110207", valueName = "其他资产损失")
    @ApiModelProperty(value = "其他资产损失")
    private BigDecimal b20000667110207;

    @LogField(tableName = "b200006", value = "b200006_671103", valueName = "债务重组损失")
    @ApiModelProperty(value = "债务重组损失")
    private BigDecimal b200006671103;

    @LogField(tableName = "b200006", value = "b200006_671104", valueName = "非常损失")
    @ApiModelProperty(value = "非常损失")
    private BigDecimal b200006671104;

    @LogField(tableName = "b200006", value = "b200006_67110401", valueName = "存货损失")
    @ApiModelProperty(value = "存货损失")
    private BigDecimal b20000667110401;

    @LogField(tableName = "b200006", value = "b200006_67110402", valueName = "固定资产损失")
    @ApiModelProperty(value = "固定资产损失")
    private BigDecimal b20000667110402;

    @LogField(tableName = "b200006", value = "b200006_67110403", valueName = "无形资产损失")
    @ApiModelProperty(value = "无形资产损失")
    private BigDecimal b20000667110403;

    @LogField(tableName = "b200006", value = "b200006_67110404", valueName = "在建工程损失")
    @ApiModelProperty(value = "在建工程损失")
    private BigDecimal b20000667110404;

    @LogField(tableName = "b200006", value = "b200006_67110405", valueName = "生产性生物资产损失")
    @ApiModelProperty(value = "生产性生物资产损失")
    private BigDecimal b20000667110405;

    @LogField(tableName = "b200006", value = "b200006_67110406", valueName = "打包出售资产损失")
    @ApiModelProperty(value = "打包出售资产损失")
    private BigDecimal b20000667110406;

    @LogField(tableName = "b200006", value = "b200006_67110407", valueName = "其他资产损失")
    @ApiModelProperty(value = "其他资产损失")
    private BigDecimal b20000667110407;

    @LogField(tableName = "b200006", value = "b200006_671105", valueName = "捐赠支出")
    @ApiModelProperty(value = "捐赠支出")
    private BigDecimal b200006671105;

    @LogField(tableName = "b200006", value = "b200006_671106", valueName = "赞助支出")
    @ApiModelProperty(value = "赞助支出")
    private BigDecimal b200006671106;

    @LogField(tableName = "b200006", value = "b200006_671107", valueName = "罚没支出")
    @ApiModelProperty(value = "罚没支出")
    private BigDecimal b200006671107;

    @LogField(tableName = "b200006", value = "b200006_67110701", valueName = "经营性处罚")
    @ApiModelProperty(value = "经营性处罚")
    private BigDecimal b20000667110701;

    @LogField(tableName = "b200006", value = "b200006_67110702", valueName = "税收滞纳金")
    @ApiModelProperty(value = "税收滞纳金")
    private BigDecimal b20000667110702;

    @LogField(tableName = "b200006", value = "b200006_67110703", valueName = "行政性处罚")
    @ApiModelProperty(value = "行政性处罚")
    private BigDecimal b20000667110703;

    @LogField(tableName = "b200006", value = "b200006_671108", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200006671108;

    @LogField(tableName = "b200006", value = "b200006_67110801", valueName = "现金及银行存款损失")
    @ApiModelProperty(value = "现金及银行存款损失")
    private BigDecimal b20000667110801;

    @LogField(tableName = "b200006", value = "b200006_67110802", valueName = "应收及预付款项坏账损失")
    @ApiModelProperty(value = "应收及预付款项坏账损失")
    private BigDecimal b20000667110802;

    @LogField(tableName = "b200006", value = "b200006_67110803", valueName = "存货损失")
    @ApiModelProperty(value = "存货损失")
    private BigDecimal b20000667110803;

    @LogField(tableName = "b200006", value = "b200006_67110804", valueName = "固定资产损失")
    @ApiModelProperty(value = "固定资产损失")
    private BigDecimal b20000667110804;

    @LogField(tableName = "b200006", value = "b200006_67110805", valueName = "无形资产损失")
    @ApiModelProperty(value = "无形资产损失")
    private BigDecimal b20000667110805;

    @LogField(tableName = "b200006", value = "b200006_67110806", valueName = "在建工程损失")
    @ApiModelProperty(value = "在建工程损失")
    private BigDecimal b20000667110806;

    @LogField(tableName = "b200006", value = "b200006_67110807", valueName = "生产性生物资产损失")
    @ApiModelProperty(value = "生产性生物资产损失")
    private BigDecimal b20000667110807;

    @LogField(tableName = "b200006", value = "b200006_67110808", valueName = "非金融企业债权性投资损失")
    @ApiModelProperty(value = "非金融企业债权性投资损失")
    private BigDecimal b20000667110808;

    @LogField(tableName = "b200006", value = "b200006_67110809", valueName = "股权（权益）性投资损失")
    @ApiModelProperty(value = "股权（权益）性投资损失")
    private BigDecimal b20000667110809;

    @LogField(tableName = "b200006", value = "b200006_67110810", valueName = "通过各种交易场所、市场买卖债券、股票、期货、基金以及金融衍生产品等发生的损失")
    @ApiModelProperty(value = "通过各种交易场所、市场买卖债券、股票、期货、基金以及金融衍生产品等发生的损失")
    private BigDecimal b20000667110810;

    @LogField(tableName = "b200006", value = "b200006_67110811", valueName = "打包出售资产损失")
    @ApiModelProperty(value = "打包出售资产损失")
    private BigDecimal b20000667110811;

    @LogField(tableName = "b200006", value = "b200006_67110812", valueName = "其他资产损失")
    @ApiModelProperty(value = "其他资产损失")
    private BigDecimal b20000667110812;

    @LogField(tableName = "b200006", value = "b200006_67110813", valueName = "其他支出")
    @ApiModelProperty(value = "其他支出")
    private BigDecimal b20000667110813;

    @LogField(tableName = "b200006", value = "b200006_6801", valueName = "所得税费用")
    @ApiModelProperty(value = "所得税费用")
    private BigDecimal b2000066801;

    @LogField(tableName = "b200006", value = "b200006_6901", valueName = "以前年度损益调整")
    @ApiModelProperty(value = "以前年度损益调整")
    private BigDecimal b2000066901;

    @LogField(tableName = "b200006", value = "b200006_222124", valueName = "应交税费-纳税检查调整")
    @ApiModelProperty(value = "应交税费-纳税检查调整")
    private BigDecimal b200006222124;

    @LogField(tableName = "b200006", value = "b200006_222125", valueName = "应交税费-加计抵减进项税额")
    @ApiModelProperty(value = "应交税费-加计抵减进项税额")
    private BigDecimal b200006222125;

    @LogField(tableName = "b200006", value = "b200006_67110501", valueName = "营业外支出—捐赠支出—公益性捐赠")
    @ApiModelProperty(value = "营业外支出—捐赠支出—公益性捐赠")
    private BigDecimal b20000667110501;

    @LogField(tableName = "b200006", value = "b200006_67110502", valueName = "营业外支出—捐赠支出—非公益性捐赠")
    @ApiModelProperty(value = "营业外支出—捐赠支出—非公益性捐赠")
    private BigDecimal b20000667110502;

    @ApiModelProperty(value = "创建时间")
    private String createTime;

    @ApiModelProperty(value = "更新时间")
    private String updateTime;

    public String getEntId() {
        return entId;
    }

    public void setEntId(String entId) {
        this.entId = entId;
    }

    public String getTaxYear() {
        return taxYear;
    }

    public void setTaxYear(String taxYear) {
        this.taxYear = taxYear;
    }

    public BigDecimal getB2000061001() {
        return b2000061001;
    }

    public void setB2000061001(BigDecimal b2000061001) {
        this.b2000061001 = b2000061001;
    }

    public BigDecimal getB2000061002() {
        return b2000061002;
    }

    public void setB2000061002(BigDecimal b2000061002) {
        this.b2000061002 = b2000061002;
    }

    public BigDecimal getB2000061003() {
        return b2000061003;
    }

    public void setB2000061003(BigDecimal b2000061003) {
        this.b2000061003 = b2000061003;
    }

    public BigDecimal getB2000061011() {
        return b2000061011;
    }

    public void setB2000061011(BigDecimal b2000061011) {
        this.b2000061011 = b2000061011;
    }

    public BigDecimal getB2000061012() {
        return b2000061012;
    }

    public void setB2000061012(BigDecimal b2000061012) {
        this.b2000061012 = b2000061012;
    }

    public BigDecimal getB2000061021() {
        return b2000061021;
    }

    public void setB2000061021(BigDecimal b2000061021) {
        this.b2000061021 = b2000061021;
    }

    public BigDecimal getB2000061031() {
        return b2000061031;
    }

    public void setB2000061031(BigDecimal b2000061031) {
        this.b2000061031 = b2000061031;
    }

    public BigDecimal getB2000061101() {
        return b2000061101;
    }

    public void setB2000061101(BigDecimal b2000061101) {
        this.b2000061101 = b2000061101;
    }

    public BigDecimal getB2000061111() {
        return b2000061111;
    }

    public void setB2000061111(BigDecimal b2000061111) {
        this.b2000061111 = b2000061111;
    }

    public BigDecimal getB2000061121() {
        return b2000061121;
    }

    public void setB2000061121(BigDecimal b2000061121) {
        this.b2000061121 = b2000061121;
    }

    public BigDecimal getB2000061122() {
        return b2000061122;
    }

    public void setB2000061122(BigDecimal b2000061122) {
        this.b2000061122 = b2000061122;
    }

    public BigDecimal getB2000061123() {
        return b2000061123;
    }

    public void setB2000061123(BigDecimal b2000061123) {
        this.b2000061123 = b2000061123;
    }

    public BigDecimal getB2000061124() {
        return b2000061124;
    }

    public void setB2000061124(BigDecimal b2000061124) {
        this.b2000061124 = b2000061124;
    }

    public BigDecimal getB2000061125() {
        return b2000061125;
    }

    public void setB2000061125(BigDecimal b2000061125) {
        this.b2000061125 = b2000061125;
    }

    public BigDecimal getB2000061131() {
        return b2000061131;
    }

    public void setB2000061131(BigDecimal b2000061131) {
        this.b2000061131 = b2000061131;
    }

    public BigDecimal getB2000061132() {
        return b2000061132;
    }

    public void setB2000061132(BigDecimal b2000061132) {
        this.b2000061132 = b2000061132;
    }

    public BigDecimal getB2000061201() {
        return b2000061201;
    }

    public void setB2000061201(BigDecimal b2000061201) {
        this.b2000061201 = b2000061201;
    }

    public BigDecimal getB2000061211() {
        return b2000061211;
    }

    public void setB2000061211(BigDecimal b2000061211) {
        this.b2000061211 = b2000061211;
    }

    public BigDecimal getB2000061212() {
        return b2000061212;
    }

    public void setB2000061212(BigDecimal b2000061212) {
        this.b2000061212 = b2000061212;
    }

    public BigDecimal getB2000061221() {
        return b2000061221;
    }

    public void setB2000061221(BigDecimal b2000061221) {
        this.b2000061221 = b2000061221;
    }

    public BigDecimal getB2000061231() {
        return b2000061231;
    }

    public void setB2000061231(BigDecimal b2000061231) {
        this.b2000061231 = b2000061231;
    }

    public BigDecimal getB2000061301() {
        return b2000061301;
    }

    public void setB2000061301(BigDecimal b2000061301) {
        this.b2000061301 = b2000061301;
    }

    public BigDecimal getB2000061302() {
        return b2000061302;
    }

    public void setB2000061302(BigDecimal b2000061302) {
        this.b2000061302 = b2000061302;
    }

    public BigDecimal getB2000061303() {
        return b2000061303;
    }

    public void setB2000061303(BigDecimal b2000061303) {
        this.b2000061303 = b2000061303;
    }

    public BigDecimal getB2000061304() {
        return b2000061304;
    }

    public void setB2000061304(BigDecimal b2000061304) {
        this.b2000061304 = b2000061304;
    }

    public BigDecimal getB2000061311() {
        return b2000061311;
    }

    public void setB2000061311(BigDecimal b2000061311) {
        this.b2000061311 = b2000061311;
    }

    public BigDecimal getB2000061321() {
        return b2000061321;
    }

    public void setB2000061321(BigDecimal b2000061321) {
        this.b2000061321 = b2000061321;
    }

    public BigDecimal getB2000061401() {
        return b2000061401;
    }

    public void setB2000061401(BigDecimal b2000061401) {
        this.b2000061401 = b2000061401;
    }

    public BigDecimal getB2000061402() {
        return b2000061402;
    }

    public void setB2000061402(BigDecimal b2000061402) {
        this.b2000061402 = b2000061402;
    }

    public BigDecimal getB2000061403() {
        return b2000061403;
    }

    public void setB2000061403(BigDecimal b2000061403) {
        this.b2000061403 = b2000061403;
    }

    public BigDecimal getB2000061404() {
        return b2000061404;
    }

    public void setB2000061404(BigDecimal b2000061404) {
        this.b2000061404 = b2000061404;
    }

    public BigDecimal getB2000061405() {
        return b2000061405;
    }

    public void setB2000061405(BigDecimal b2000061405) {
        this.b2000061405 = b2000061405;
    }

    public BigDecimal getB2000061406() {
        return b2000061406;
    }

    public void setB2000061406(BigDecimal b2000061406) {
        this.b2000061406 = b2000061406;
    }

    public BigDecimal getB2000061407() {
        return b2000061407;
    }

    public void setB2000061407(BigDecimal b2000061407) {
        this.b2000061407 = b2000061407;
    }

    public BigDecimal getB2000061408() {
        return b2000061408;
    }

    public void setB2000061408(BigDecimal b2000061408) {
        this.b2000061408 = b2000061408;
    }

    public BigDecimal getB2000061411() {
        return b2000061411;
    }

    public void setB2000061411(BigDecimal b2000061411) {
        this.b2000061411 = b2000061411;
    }

    public BigDecimal getB2000061421() {
        return b2000061421;
    }

    public void setB2000061421(BigDecimal b2000061421) {
        this.b2000061421 = b2000061421;
    }

    public BigDecimal getB2000061431() {
        return b2000061431;
    }

    public void setB2000061431(BigDecimal b2000061431) {
        this.b2000061431 = b2000061431;
    }

    public BigDecimal getB2000061441() {
        return b2000061441;
    }

    public void setB2000061441(BigDecimal b2000061441) {
        this.b2000061441 = b2000061441;
    }

    public BigDecimal getB2000061451() {
        return b2000061451;
    }

    public void setB2000061451(BigDecimal b2000061451) {
        this.b2000061451 = b2000061451;
    }

    public BigDecimal getB2000061461() {
        return b2000061461;
    }

    public void setB2000061461(BigDecimal b2000061461) {
        this.b2000061461 = b2000061461;
    }

    public BigDecimal getB2000061471() {
        return b2000061471;
    }

    public void setB2000061471(BigDecimal b2000061471) {
        this.b2000061471 = b2000061471;
    }

    public BigDecimal getB2000061481() {
        return b2000061481;
    }

    public void setB2000061481(BigDecimal b2000061481) {
        this.b2000061481 = b2000061481;
    }

    public BigDecimal getB2000061482() {
        return b2000061482;
    }

    public void setB2000061482(BigDecimal b2000061482) {
        this.b2000061482 = b2000061482;
    }

    public BigDecimal getB2000061501() {
        return b2000061501;
    }

    public void setB2000061501(BigDecimal b2000061501) {
        this.b2000061501 = b2000061501;
    }

    public BigDecimal getB2000061502() {
        return b2000061502;
    }

    public void setB2000061502(BigDecimal b2000061502) {
        this.b2000061502 = b2000061502;
    }

    public BigDecimal getB2000061503() {
        return b2000061503;
    }

    public void setB2000061503(BigDecimal b2000061503) {
        this.b2000061503 = b2000061503;
    }

    public BigDecimal getB2000061511() {
        return b2000061511;
    }

    public void setB2000061511(BigDecimal b2000061511) {
        this.b2000061511 = b2000061511;
    }

    public BigDecimal getB2000061512() {
        return b2000061512;
    }

    public void setB2000061512(BigDecimal b2000061512) {
        this.b2000061512 = b2000061512;
    }

    public BigDecimal getB2000061521() {
        return b2000061521;
    }

    public void setB2000061521(BigDecimal b2000061521) {
        this.b2000061521 = b2000061521;
    }

    public BigDecimal getB2000061531() {
        return b2000061531;
    }

    public void setB2000061531(BigDecimal b2000061531) {
        this.b2000061531 = b2000061531;
    }

    public BigDecimal getB2000061532() {
        return b2000061532;
    }

    public void setB2000061532(BigDecimal b2000061532) {
        this.b2000061532 = b2000061532;
    }

    public BigDecimal getB2000061541() {
        return b2000061541;
    }

    public void setB2000061541(BigDecimal b2000061541) {
        this.b2000061541 = b2000061541;
    }

    public BigDecimal getB2000061601() {
        return b2000061601;
    }

    public void setB2000061601(BigDecimal b2000061601) {
        this.b2000061601 = b2000061601;
    }

    public BigDecimal getB2000061602() {
        return b2000061602;
    }

    public void setB2000061602(BigDecimal b2000061602) {
        this.b2000061602 = b2000061602;
    }

    public BigDecimal getB2000061603() {
        return b2000061603;
    }

    public void setB2000061603(BigDecimal b2000061603) {
        this.b2000061603 = b2000061603;
    }

    public BigDecimal getB2000061604() {
        return b2000061604;
    }

    public void setB2000061604(BigDecimal b2000061604) {
        this.b2000061604 = b2000061604;
    }

    public BigDecimal getB2000061605() {
        return b2000061605;
    }

    public void setB2000061605(BigDecimal b2000061605) {
        this.b2000061605 = b2000061605;
    }

    public BigDecimal getB2000061606() {
        return b2000061606;
    }

    public void setB2000061606(BigDecimal b2000061606) {
        this.b2000061606 = b2000061606;
    }

    public BigDecimal getB2000061611() {
        return b2000061611;
    }

    public void setB2000061611(BigDecimal b2000061611) {
        this.b2000061611 = b2000061611;
    }

    public BigDecimal getB2000061621() {
        return b2000061621;
    }

    public void setB2000061621(BigDecimal b2000061621) {
        this.b2000061621 = b2000061621;
    }

    public BigDecimal getB2000061622() {
        return b2000061622;
    }

    public void setB2000061622(BigDecimal b2000061622) {
        this.b2000061622 = b2000061622;
    }

    public BigDecimal getB2000061623() {
        return b2000061623;
    }

    public void setB2000061623(BigDecimal b2000061623) {
        this.b2000061623 = b2000061623;
    }

    public BigDecimal getB2000061631() {
        return b2000061631;
    }

    public void setB2000061631(BigDecimal b2000061631) {
        this.b2000061631 = b2000061631;
    }

    public BigDecimal getB2000061632() {
        return b2000061632;
    }

    public void setB2000061632(BigDecimal b2000061632) {
        this.b2000061632 = b2000061632;
    }

    public BigDecimal getB2000061701() {
        return b2000061701;
    }

    public void setB2000061701(BigDecimal b2000061701) {
        this.b2000061701 = b2000061701;
    }

    public BigDecimal getB2000061702() {
        return b2000061702;
    }

    public void setB2000061702(BigDecimal b2000061702) {
        this.b2000061702 = b2000061702;
    }

    public BigDecimal getB2000061703() {
        return b2000061703;
    }

    public void setB2000061703(BigDecimal b2000061703) {
        this.b2000061703 = b2000061703;
    }

    public BigDecimal getB2000061711() {
        return b2000061711;
    }

    public void setB2000061711(BigDecimal b2000061711) {
        this.b2000061711 = b2000061711;
    }

    public BigDecimal getB2000061801() {
        return b2000061801;
    }

    public void setB2000061801(BigDecimal b2000061801) {
        this.b2000061801 = b2000061801;
    }

    public BigDecimal getB2000061811() {
        return b2000061811;
    }

    public void setB2000061811(BigDecimal b2000061811) {
        this.b2000061811 = b2000061811;
    }

    public BigDecimal getB2000061821() {
        return b2000061821;
    }

    public void setB2000061821(BigDecimal b2000061821) {
        this.b2000061821 = b2000061821;
    }

    public BigDecimal getB2000061901() {
        return b2000061901;
    }

    public void setB2000061901(BigDecimal b2000061901) {
        this.b2000061901 = b2000061901;
    }

    public BigDecimal getB2000062001() {
        return b2000062001;
    }

    public void setB2000062001(BigDecimal b2000062001) {
        this.b2000062001 = b2000062001;
    }

    public BigDecimal getB2000062002() {
        return b2000062002;
    }

    public void setB2000062002(BigDecimal b2000062002) {
        this.b2000062002 = b2000062002;
    }

    public BigDecimal getB2000062003() {
        return b2000062003;
    }

    public void setB2000062003(BigDecimal b2000062003) {
        this.b2000062003 = b2000062003;
    }

    public BigDecimal getB2000062004() {
        return b2000062004;
    }

    public void setB2000062004(BigDecimal b2000062004) {
        this.b2000062004 = b2000062004;
    }

    public BigDecimal getB2000062011() {
        return b2000062011;
    }

    public void setB2000062011(BigDecimal b2000062011) {
        this.b2000062011 = b2000062011;
    }

    public BigDecimal getB2000062012() {
        return b2000062012;
    }

    public void setB2000062012(BigDecimal b2000062012) {
        this.b2000062012 = b2000062012;
    }

    public BigDecimal getB2000062021() {
        return b2000062021;
    }

    public void setB2000062021(BigDecimal b2000062021) {
        this.b2000062021 = b2000062021;
    }

    public BigDecimal getB2000062101() {
        return b2000062101;
    }

    public void setB2000062101(BigDecimal b2000062101) {
        this.b2000062101 = b2000062101;
    }

    public BigDecimal getB2000062111() {
        return b2000062111;
    }

    public void setB2000062111(BigDecimal b2000062111) {
        this.b2000062111 = b2000062111;
    }

    public BigDecimal getB2000062201() {
        return b2000062201;
    }

    public void setB2000062201(BigDecimal b2000062201) {
        this.b2000062201 = b2000062201;
    }

    public BigDecimal getB2000062202() {
        return b2000062202;
    }

    public void setB2000062202(BigDecimal b2000062202) {
        this.b2000062202 = b2000062202;
    }

    public BigDecimal getB2000062203() {
        return b2000062203;
    }

    public void setB2000062203(BigDecimal b2000062203) {
        this.b2000062203 = b2000062203;
    }

    public BigDecimal getB2000062204() {
        return b2000062204;
    }

    public void setB2000062204(BigDecimal b2000062204) {
        this.b2000062204 = b2000062204;
    }

    public BigDecimal getB2000062211() {
        return b2000062211;
    }

    public void setB2000062211(BigDecimal b2000062211) {
        this.b2000062211 = b2000062211;
    }

    public BigDecimal getB200006221101() {
        return b200006221101;
    }

    public void setB200006221101(BigDecimal b200006221101) {
        this.b200006221101 = b200006221101;
    }

    public BigDecimal getB200006221102() {
        return b200006221102;
    }

    public void setB200006221102(BigDecimal b200006221102) {
        this.b200006221102 = b200006221102;
    }

    public BigDecimal getB200006221103() {
        return b200006221103;
    }

    public void setB200006221103(BigDecimal b200006221103) {
        this.b200006221103 = b200006221103;
    }

    public BigDecimal getB200006221104() {
        return b200006221104;
    }

    public void setB200006221104(BigDecimal b200006221104) {
        this.b200006221104 = b200006221104;
    }

    public BigDecimal getB200006221105() {
        return b200006221105;
    }

    public void setB200006221105(BigDecimal b200006221105) {
        this.b200006221105 = b200006221105;
    }

    public BigDecimal getB200006221106() {
        return b200006221106;
    }

    public void setB200006221106(BigDecimal b200006221106) {
        this.b200006221106 = b200006221106;
    }

    public BigDecimal getB200006221107() {
        return b200006221107;
    }

    public void setB200006221107(BigDecimal b200006221107) {
        this.b200006221107 = b200006221107;
    }

    public BigDecimal getB200006221108() {
        return b200006221108;
    }

    public void setB200006221108(BigDecimal b200006221108) {
        this.b200006221108 = b200006221108;
    }

    public BigDecimal getB200006221109() {
        return b200006221109;
    }

    public void setB200006221109(BigDecimal b200006221109) {
        this.b200006221109 = b200006221109;
    }

    public BigDecimal getB200006221110() {
        return b200006221110;
    }

    public void setB200006221110(BigDecimal b200006221110) {
        this.b200006221110 = b200006221110;
    }

    public BigDecimal getB200006221111() {
        return b200006221111;
    }

    public void setB200006221111(BigDecimal b200006221111) {
        this.b200006221111 = b200006221111;
    }

    public BigDecimal getB200006221112() {
        return b200006221112;
    }

    public void setB200006221112(BigDecimal b200006221112) {
        this.b200006221112 = b200006221112;
    }

    public BigDecimal getB200006221113() {
        return b200006221113;
    }

    public void setB200006221113(BigDecimal b200006221113) {
        this.b200006221113 = b200006221113;
    }

    public BigDecimal getB2000062221() {
        return b2000062221;
    }

    public void setB2000062221(BigDecimal b2000062221) {
        this.b2000062221 = b2000062221;
    }

    public BigDecimal getB200006222101() {
        return b200006222101;
    }

    public void setB200006222101(BigDecimal b200006222101) {
        this.b200006222101 = b200006222101;
    }

    public BigDecimal getB20000622210101() {
        return b20000622210101;
    }

    public void setB20000622210101(BigDecimal b20000622210101) {
        this.b20000622210101 = b20000622210101;
    }

    public BigDecimal getB20000622210102() {
        return b20000622210102;
    }

    public void setB20000622210102(BigDecimal b20000622210102) {
        this.b20000622210102 = b20000622210102;
    }

    public BigDecimal getB20000622210103() {
        return b20000622210103;
    }

    public void setB20000622210103(BigDecimal b20000622210103) {
        this.b20000622210103 = b20000622210103;
    }

    public BigDecimal getB20000622210104() {
        return b20000622210104;
    }

    public void setB20000622210104(BigDecimal b20000622210104) {
        this.b20000622210104 = b20000622210104;
    }

    public BigDecimal getB20000622210105() {
        return b20000622210105;
    }

    public void setB20000622210105(BigDecimal b20000622210105) {
        this.b20000622210105 = b20000622210105;
    }

    public BigDecimal getB20000622210106() {
        return b20000622210106;
    }

    public void setB20000622210106(BigDecimal b20000622210106) {
        this.b20000622210106 = b20000622210106;
    }

    public BigDecimal getB20000622210107() {
        return b20000622210107;
    }

    public void setB20000622210107(BigDecimal b20000622210107) {
        this.b20000622210107 = b20000622210107;
    }

    public BigDecimal getB20000622210108() {
        return b20000622210108;
    }

    public void setB20000622210108(BigDecimal b20000622210108) {
        this.b20000622210108 = b20000622210108;
    }

    public BigDecimal getB20000622210109() {
        return b20000622210109;
    }

    public void setB20000622210109(BigDecimal b20000622210109) {
        this.b20000622210109 = b20000622210109;
    }

    public BigDecimal getB20000622210110() {
        return b20000622210110;
    }

    public void setB20000622210110(BigDecimal b20000622210110) {
        this.b20000622210110 = b20000622210110;
    }

    public BigDecimal getB200006222102() {
        return b200006222102;
    }

    public void setB200006222102(BigDecimal b200006222102) {
        this.b200006222102 = b200006222102;
    }

    public BigDecimal getB200006222103() {
        return b200006222103;
    }

    public void setB200006222103(BigDecimal b200006222103) {
        this.b200006222103 = b200006222103;
    }

    public BigDecimal getB200006222104() {
        return b200006222104;
    }

    public void setB200006222104(BigDecimal b200006222104) {
        this.b200006222104 = b200006222104;
    }

    public BigDecimal getB200006222105() {
        return b200006222105;
    }

    public void setB200006222105(BigDecimal b200006222105) {
        this.b200006222105 = b200006222105;
    }

    public BigDecimal getB200006222106() {
        return b200006222106;
    }

    public void setB200006222106(BigDecimal b200006222106) {
        this.b200006222106 = b200006222106;
    }

    public BigDecimal getB200006222107() {
        return b200006222107;
    }

    public void setB200006222107(BigDecimal b200006222107) {
        this.b200006222107 = b200006222107;
    }

    public BigDecimal getB200006222108() {
        return b200006222108;
    }

    public void setB200006222108(BigDecimal b200006222108) {
        this.b200006222108 = b200006222108;
    }

    public BigDecimal getB200006222109() {
        return b200006222109;
    }

    public void setB200006222109(BigDecimal b200006222109) {
        this.b200006222109 = b200006222109;
    }

    public BigDecimal getB200006222110() {
        return b200006222110;
    }

    public void setB200006222110(BigDecimal b200006222110) {
        this.b200006222110 = b200006222110;
    }

    public BigDecimal getB200006222111() {
        return b200006222111;
    }

    public void setB200006222111(BigDecimal b200006222111) {
        this.b200006222111 = b200006222111;
    }

    public BigDecimal getB200006222112() {
        return b200006222112;
    }

    public void setB200006222112(BigDecimal b200006222112) {
        this.b200006222112 = b200006222112;
    }

    public BigDecimal getB200006222113() {
        return b200006222113;
    }

    public void setB200006222113(BigDecimal b200006222113) {
        this.b200006222113 = b200006222113;
    }

    public BigDecimal getB200006222114() {
        return b200006222114;
    }

    public void setB200006222114(BigDecimal b200006222114) {
        this.b200006222114 = b200006222114;
    }

    public BigDecimal getB200006222115() {
        return b200006222115;
    }

    public void setB200006222115(BigDecimal b200006222115) {
        this.b200006222115 = b200006222115;
    }

    public BigDecimal getB200006222116() {
        return b200006222116;
    }

    public void setB200006222116(BigDecimal b200006222116) {
        this.b200006222116 = b200006222116;
    }

    public BigDecimal getB200006222117() {
        return b200006222117;
    }

    public void setB200006222117(BigDecimal b200006222117) {
        this.b200006222117 = b200006222117;
    }

    public BigDecimal getB200006222118() {
        return b200006222118;
    }

    public void setB200006222118(BigDecimal b200006222118) {
        this.b200006222118 = b200006222118;
    }

    public BigDecimal getB200006222119() {
        return b200006222119;
    }

    public void setB200006222119(BigDecimal b200006222119) {
        this.b200006222119 = b200006222119;
    }

    public BigDecimal getB200006222120() {
        return b200006222120;
    }

    public void setB200006222120(BigDecimal b200006222120) {
        this.b200006222120 = b200006222120;
    }

    public BigDecimal getB200006222121() {
        return b200006222121;
    }

    public void setB200006222121(BigDecimal b200006222121) {
        this.b200006222121 = b200006222121;
    }

    public BigDecimal getB200006222122() {
        return b200006222122;
    }

    public void setB200006222122(BigDecimal b200006222122) {
        this.b200006222122 = b200006222122;
    }

    public BigDecimal getB200006222123() {
        return b200006222123;
    }

    public void setB200006222123(BigDecimal b200006222123) {
        this.b200006222123 = b200006222123;
    }

    public BigDecimal getB2000062231() {
        return b2000062231;
    }

    public void setB2000062231(BigDecimal b2000062231) {
        this.b2000062231 = b2000062231;
    }

    public BigDecimal getB2000062232() {
        return b2000062232;
    }

    public void setB2000062232(BigDecimal b2000062232) {
        this.b2000062232 = b2000062232;
    }

    public BigDecimal getB2000062241() {
        return b2000062241;
    }

    public void setB2000062241(BigDecimal b2000062241) {
        this.b2000062241 = b2000062241;
    }

    public BigDecimal getB2000062251() {
        return b2000062251;
    }

    public void setB2000062251(BigDecimal b2000062251) {
        this.b2000062251 = b2000062251;
    }

    public BigDecimal getB2000062261() {
        return b2000062261;
    }

    public void setB2000062261(BigDecimal b2000062261) {
        this.b2000062261 = b2000062261;
    }

    public BigDecimal getB2000062311() {
        return b2000062311;
    }

    public void setB2000062311(BigDecimal b2000062311) {
        this.b2000062311 = b2000062311;
    }

    public BigDecimal getB2000062312() {
        return b2000062312;
    }

    public void setB2000062312(BigDecimal b2000062312) {
        this.b2000062312 = b2000062312;
    }

    public BigDecimal getB2000062313() {
        return b2000062313;
    }

    public void setB2000062313(BigDecimal b2000062313) {
        this.b2000062313 = b2000062313;
    }

    public BigDecimal getB2000062314() {
        return b2000062314;
    }

    public void setB2000062314(BigDecimal b2000062314) {
        this.b2000062314 = b2000062314;
    }

    public BigDecimal getB2000062401() {
        return b2000062401;
    }

    public void setB2000062401(BigDecimal b2000062401) {
        this.b2000062401 = b2000062401;
    }

    public BigDecimal getB2000062245() {
        return b2000062245;
    }

    public void setB2000062245(BigDecimal b2000062245) {
        this.b2000062245 = b2000062245;
    }

    public BigDecimal getB2000062501() {
        return b2000062501;
    }

    public void setB2000062501(BigDecimal b2000062501) {
        this.b2000062501 = b2000062501;
    }

    public BigDecimal getB2000062502() {
        return b2000062502;
    }

    public void setB2000062502(BigDecimal b2000062502) {
        this.b2000062502 = b2000062502;
    }

    public BigDecimal getB2000062601() {
        return b2000062601;
    }

    public void setB2000062601(BigDecimal b2000062601) {
        this.b2000062601 = b2000062601;
    }

    public BigDecimal getB2000062602() {
        return b2000062602;
    }

    public void setB2000062602(BigDecimal b2000062602) {
        this.b2000062602 = b2000062602;
    }

    public BigDecimal getB2000062611() {
        return b2000062611;
    }

    public void setB2000062611(BigDecimal b2000062611) {
        this.b2000062611 = b2000062611;
    }

    public BigDecimal getB2000062621() {
        return b2000062621;
    }

    public void setB2000062621(BigDecimal b2000062621) {
        this.b2000062621 = b2000062621;
    }

    public BigDecimal getB2000062701() {
        return b2000062701;
    }

    public void setB2000062701(BigDecimal b2000062701) {
        this.b2000062701 = b2000062701;
    }

    public BigDecimal getB2000062702() {
        return b2000062702;
    }

    public void setB2000062702(BigDecimal b2000062702) {
        this.b2000062702 = b2000062702;
    }

    public BigDecimal getB2000062711() {
        return b2000062711;
    }

    public void setB2000062711(BigDecimal b2000062711) {
        this.b2000062711 = b2000062711;
    }

    public BigDecimal getB2000062801() {
        return b2000062801;
    }

    public void setB2000062801(BigDecimal b2000062801) {
        this.b2000062801 = b2000062801;
    }

    public BigDecimal getB2000062901() {
        return b2000062901;
    }

    public void setB2000062901(BigDecimal b2000062901) {
        this.b2000062901 = b2000062901;
    }

    public BigDecimal getB2000063001() {
        return b2000063001;
    }

    public void setB2000063001(BigDecimal b2000063001) {
        this.b2000063001 = b2000063001;
    }

    public BigDecimal getB2000063002() {
        return b2000063002;
    }

    public void setB2000063002(BigDecimal b2000063002) {
        this.b2000063002 = b2000063002;
    }

    public BigDecimal getB2000063101() {
        return b2000063101;
    }

    public void setB2000063101(BigDecimal b2000063101) {
        this.b2000063101 = b2000063101;
    }

    public BigDecimal getB2000063201() {
        return b2000063201;
    }

    public void setB2000063201(BigDecimal b2000063201) {
        this.b2000063201 = b2000063201;
    }

    public BigDecimal getB2000063202() {
        return b2000063202;
    }

    public void setB2000063202(BigDecimal b2000063202) {
        this.b2000063202 = b2000063202;
    }

    public BigDecimal getB2000064001() {
        return b2000064001;
    }

    public void setB2000064001(BigDecimal b2000064001) {
        this.b2000064001 = b2000064001;
    }

    public BigDecimal getB2000064002() {
        return b2000064002;
    }

    public void setB2000064002(BigDecimal b2000064002) {
        this.b2000064002 = b2000064002;
    }

    public BigDecimal getB2000064003() {
        return b2000064003;
    }

    public void setB2000064003(BigDecimal b2000064003) {
        this.b2000064003 = b2000064003;
    }

    public BigDecimal getB2000064101() {
        return b2000064101;
    }

    public void setB2000064101(BigDecimal b2000064101) {
        this.b2000064101 = b2000064101;
    }

    public BigDecimal getB2000064102() {
        return b2000064102;
    }

    public void setB2000064102(BigDecimal b2000064102) {
        this.b2000064102 = b2000064102;
    }

    public BigDecimal getB2000064103() {
        return b2000064103;
    }

    public void setB2000064103(BigDecimal b2000064103) {
        this.b2000064103 = b2000064103;
    }

    public BigDecimal getB2000064104() {
        return b2000064104;
    }

    public void setB2000064104(BigDecimal b2000064104) {
        this.b2000064104 = b2000064104;
    }

    public BigDecimal getB2000064201() {
        return b2000064201;
    }

    public void setB2000064201(BigDecimal b2000064201) {
        this.b2000064201 = b2000064201;
    }

    public BigDecimal getB2000064301() {
        return b2000064301;
    }

    public void setB2000064301(BigDecimal b2000064301) {
        this.b2000064301 = b2000064301;
    }

    public BigDecimal getB2000065001() {
        return b2000065001;
    }

    public void setB2000065001(BigDecimal b2000065001) {
        this.b2000065001 = b2000065001;
    }

    public BigDecimal getB200006500101() {
        return b200006500101;
    }

    public void setB200006500101(BigDecimal b200006500101) {
        this.b200006500101 = b200006500101;
    }

    public BigDecimal getB200006500102() {
        return b200006500102;
    }

    public void setB200006500102(BigDecimal b200006500102) {
        this.b200006500102 = b200006500102;
    }

    public BigDecimal getB200006500103() {
        return b200006500103;
    }

    public void setB200006500103(BigDecimal b200006500103) {
        this.b200006500103 = b200006500103;
    }

    public BigDecimal getB200006500104() {
        return b200006500104;
    }

    public void setB200006500104(BigDecimal b200006500104) {
        this.b200006500104 = b200006500104;
    }

    public BigDecimal getB200006500105() {
        return b200006500105;
    }

    public void setB200006500105(BigDecimal b200006500105) {
        this.b200006500105 = b200006500105;
    }

    public BigDecimal getB200006500106() {
        return b200006500106;
    }

    public void setB200006500106(BigDecimal b200006500106) {
        this.b200006500106 = b200006500106;
    }

    public BigDecimal getB200006500107() {
        return b200006500107;
    }

    public void setB200006500107(BigDecimal b200006500107) {
        this.b200006500107 = b200006500107;
    }

    public BigDecimal getB200006500108() {
        return b200006500108;
    }

    public void setB200006500108(BigDecimal b200006500108) {
        this.b200006500108 = b200006500108;
    }

    public BigDecimal getB200006500109() {
        return b200006500109;
    }

    public void setB200006500109(BigDecimal b200006500109) {
        this.b200006500109 = b200006500109;
    }

    public BigDecimal getB200006500110() {
        return b200006500110;
    }

    public void setB200006500110(BigDecimal b200006500110) {
        this.b200006500110 = b200006500110;
    }

    public BigDecimal getB200006500111() {
        return b200006500111;
    }

    public void setB200006500111(BigDecimal b200006500111) {
        this.b200006500111 = b200006500111;
    }

    public BigDecimal getB200006500112() {
        return b200006500112;
    }

    public void setB200006500112(BigDecimal b200006500112) {
        this.b200006500112 = b200006500112;
    }

    public BigDecimal getB200006500113() {
        return b200006500113;
    }

    public void setB200006500113(BigDecimal b200006500113) {
        this.b200006500113 = b200006500113;
    }

    public BigDecimal getB200006500114() {
        return b200006500114;
    }

    public void setB200006500114(BigDecimal b200006500114) {
        this.b200006500114 = b200006500114;
    }

    public BigDecimal getB200006500115() {
        return b200006500115;
    }

    public void setB200006500115(BigDecimal b200006500115) {
        this.b200006500115 = b200006500115;
    }

    public BigDecimal getB200006500116() {
        return b200006500116;
    }

    public void setB200006500116(BigDecimal b200006500116) {
        this.b200006500116 = b200006500116;
    }

    public BigDecimal getB200006500117() {
        return b200006500117;
    }

    public void setB200006500117(BigDecimal b200006500117) {
        this.b200006500117 = b200006500117;
    }

    public BigDecimal getB200006500118() {
        return b200006500118;
    }

    public void setB200006500118(BigDecimal b200006500118) {
        this.b200006500118 = b200006500118;
    }

    public BigDecimal getB2000065101() {
        return b2000065101;
    }

    public void setB2000065101(BigDecimal b2000065101) {
        this.b2000065101 = b2000065101;
    }

    public BigDecimal getB200006510101() {
        return b200006510101;
    }

    public void setB200006510101(BigDecimal b200006510101) {
        this.b200006510101 = b200006510101;
    }

    public BigDecimal getB200006510102() {
        return b200006510102;
    }

    public void setB200006510102(BigDecimal b200006510102) {
        this.b200006510102 = b200006510102;
    }

    public BigDecimal getB200006510103() {
        return b200006510103;
    }

    public void setB200006510103(BigDecimal b200006510103) {
        this.b200006510103 = b200006510103;
    }

    public BigDecimal getB200006510104() {
        return b200006510104;
    }

    public void setB200006510104(BigDecimal b200006510104) {
        this.b200006510104 = b200006510104;
    }

    public BigDecimal getB200006510105() {
        return b200006510105;
    }

    public void setB200006510105(BigDecimal b200006510105) {
        this.b200006510105 = b200006510105;
    }

    public BigDecimal getB200006510106() {
        return b200006510106;
    }

    public void setB200006510106(BigDecimal b200006510106) {
        this.b200006510106 = b200006510106;
    }

    public BigDecimal getB200006510107() {
        return b200006510107;
    }

    public void setB200006510107(BigDecimal b200006510107) {
        this.b200006510107 = b200006510107;
    }

    public BigDecimal getB200006510108() {
        return b200006510108;
    }

    public void setB200006510108(BigDecimal b200006510108) {
        this.b200006510108 = b200006510108;
    }

    public BigDecimal getB200006510109() {
        return b200006510109;
    }

    public void setB200006510109(BigDecimal b200006510109) {
        this.b200006510109 = b200006510109;
    }

    public BigDecimal getB200006510110() {
        return b200006510110;
    }

    public void setB200006510110(BigDecimal b200006510110) {
        this.b200006510110 = b200006510110;
    }

    public BigDecimal getB200006510111() {
        return b200006510111;
    }

    public void setB200006510111(BigDecimal b200006510111) {
        this.b200006510111 = b200006510111;
    }

    public BigDecimal getB200006510112() {
        return b200006510112;
    }

    public void setB200006510112(BigDecimal b200006510112) {
        this.b200006510112 = b200006510112;
    }

    public BigDecimal getB200006510113() {
        return b200006510113;
    }

    public void setB200006510113(BigDecimal b200006510113) {
        this.b200006510113 = b200006510113;
    }

    public BigDecimal getB200006510114() {
        return b200006510114;
    }

    public void setB200006510114(BigDecimal b200006510114) {
        this.b200006510114 = b200006510114;
    }

    public BigDecimal getB200006510115() {
        return b200006510115;
    }

    public void setB200006510115(BigDecimal b200006510115) {
        this.b200006510115 = b200006510115;
    }

    public BigDecimal getB200006510116() {
        return b200006510116;
    }

    public void setB200006510116(BigDecimal b200006510116) {
        this.b200006510116 = b200006510116;
    }

    public BigDecimal getB200006510117() {
        return b200006510117;
    }

    public void setB200006510117(BigDecimal b200006510117) {
        this.b200006510117 = b200006510117;
    }

    public BigDecimal getB200006510118() {
        return b200006510118;
    }

    public void setB200006510118(BigDecimal b200006510118) {
        this.b200006510118 = b200006510118;
    }

    public BigDecimal getB200006510119() {
        return b200006510119;
    }

    public void setB200006510119(BigDecimal b200006510119) {
        this.b200006510119 = b200006510119;
    }

    public BigDecimal getB200006510120() {
        return b200006510120;
    }

    public void setB200006510120(BigDecimal b200006510120) {
        this.b200006510120 = b200006510120;
    }

    public BigDecimal getB200006510121() {
        return b200006510121;
    }

    public void setB200006510121(BigDecimal b200006510121) {
        this.b200006510121 = b200006510121;
    }

    public BigDecimal getB200006510122() {
        return b200006510122;
    }

    public void setB200006510122(BigDecimal b200006510122) {
        this.b200006510122 = b200006510122;
    }

    public BigDecimal getB200006510123() {
        return b200006510123;
    }

    public void setB200006510123(BigDecimal b200006510123) {
        this.b200006510123 = b200006510123;
    }

    public BigDecimal getB200006510124() {
        return b200006510124;
    }

    public void setB200006510124(BigDecimal b200006510124) {
        this.b200006510124 = b200006510124;
    }

    public BigDecimal getB200006510125() {
        return b200006510125;
    }

    public void setB200006510125(BigDecimal b200006510125) {
        this.b200006510125 = b200006510125;
    }

    public BigDecimal getB200006510126() {
        return b200006510126;
    }

    public void setB200006510126(BigDecimal b200006510126) {
        this.b200006510126 = b200006510126;
    }

    public BigDecimal getB200006510127() {
        return b200006510127;
    }

    public void setB200006510127(BigDecimal b200006510127) {
        this.b200006510127 = b200006510127;
    }

    public BigDecimal getB200006510128() {
        return b200006510128;
    }

    public void setB200006510128(BigDecimal b200006510128) {
        this.b200006510128 = b200006510128;
    }

    public BigDecimal getB200006510129() {
        return b200006510129;
    }

    public void setB200006510129(BigDecimal b200006510129) {
        this.b200006510129 = b200006510129;
    }

    public BigDecimal getB200006510130() {
        return b200006510130;
    }

    public void setB200006510130(BigDecimal b200006510130) {
        this.b200006510130 = b200006510130;
    }

    public BigDecimal getB200006510131() {
        return b200006510131;
    }

    public void setB200006510131(BigDecimal b200006510131) {
        this.b200006510131 = b200006510131;
    }

    public BigDecimal getB2000065201() {
        return b2000065201;
    }

    public void setB2000065201(BigDecimal b2000065201) {
        this.b2000065201 = b2000065201;
    }

    public BigDecimal getB200006520101() {
        return b200006520101;
    }

    public void setB200006520101(BigDecimal b200006520101) {
        this.b200006520101 = b200006520101;
    }

    public BigDecimal getB200006520102() {
        return b200006520102;
    }

    public void setB200006520102(BigDecimal b200006520102) {
        this.b200006520102 = b200006520102;
    }

    public BigDecimal getB200006520103() {
        return b200006520103;
    }

    public void setB200006520103(BigDecimal b200006520103) {
        this.b200006520103 = b200006520103;
    }

    public BigDecimal getB200006520104() {
        return b200006520104;
    }

    public void setB200006520104(BigDecimal b200006520104) {
        this.b200006520104 = b200006520104;
    }

    public BigDecimal getB200006520105() {
        return b200006520105;
    }

    public void setB200006520105(BigDecimal b200006520105) {
        this.b200006520105 = b200006520105;
    }

    public BigDecimal getB200006520106() {
        return b200006520106;
    }

    public void setB200006520106(BigDecimal b200006520106) {
        this.b200006520106 = b200006520106;
    }

    public BigDecimal getB200006520107() {
        return b200006520107;
    }

    public void setB200006520107(BigDecimal b200006520107) {
        this.b200006520107 = b200006520107;
    }

    public BigDecimal getB200006520108() {
        return b200006520108;
    }

    public void setB200006520108(BigDecimal b200006520108) {
        this.b200006520108 = b200006520108;
    }

    public BigDecimal getB200006520109() {
        return b200006520109;
    }

    public void setB200006520109(BigDecimal b200006520109) {
        this.b200006520109 = b200006520109;
    }

    public BigDecimal getB200006520110() {
        return b200006520110;
    }

    public void setB200006520110(BigDecimal b200006520110) {
        this.b200006520110 = b200006520110;
    }

    public BigDecimal getB200006520111() {
        return b200006520111;
    }

    public void setB200006520111(BigDecimal b200006520111) {
        this.b200006520111 = b200006520111;
    }

    public BigDecimal getB200006520112() {
        return b200006520112;
    }

    public void setB200006520112(BigDecimal b200006520112) {
        this.b200006520112 = b200006520112;
    }

    public BigDecimal getB200006520113() {
        return b200006520113;
    }

    public void setB200006520113(BigDecimal b200006520113) {
        this.b200006520113 = b200006520113;
    }

    public BigDecimal getB200006520114() {
        return b200006520114;
    }

    public void setB200006520114(BigDecimal b200006520114) {
        this.b200006520114 = b200006520114;
    }

    public BigDecimal getB200006520115() {
        return b200006520115;
    }

    public void setB200006520115(BigDecimal b200006520115) {
        this.b200006520115 = b200006520115;
    }

    public BigDecimal getB2000065301() {
        return b2000065301;
    }

    public void setB2000065301(BigDecimal b2000065301) {
        this.b2000065301 = b2000065301;
    }

    public BigDecimal getB200006530101() {
        return b200006530101;
    }

    public void setB200006530101(BigDecimal b200006530101) {
        this.b200006530101 = b200006530101;
    }

    public BigDecimal getB20000653010101() {
        return b20000653010101;
    }

    public void setB20000653010101(BigDecimal b20000653010101) {
        this.b20000653010101 = b20000653010101;
    }

    public BigDecimal getB2000065301010101() {
        return b2000065301010101;
    }

    public void setB2000065301010101(BigDecimal b2000065301010101) {
        this.b2000065301010101 = b2000065301010101;
    }

    public BigDecimal getB2000065301010102() {
        return b2000065301010102;
    }

    public void setB2000065301010102(BigDecimal b2000065301010102) {
        this.b2000065301010102 = b2000065301010102;
    }

    public BigDecimal getB2000065301010103() {
        return b2000065301010103;
    }

    public void setB2000065301010103(BigDecimal b2000065301010103) {
        this.b2000065301010103 = b2000065301010103;
    }

    public BigDecimal getB2000065301010104() {
        return b2000065301010104;
    }

    public void setB2000065301010104(BigDecimal b2000065301010104) {
        this.b2000065301010104 = b2000065301010104;
    }

    public BigDecimal getB2000065301010105() {
        return b2000065301010105;
    }

    public void setB2000065301010105(BigDecimal b2000065301010105) {
        this.b2000065301010105 = b2000065301010105;
    }

    public BigDecimal getB2000065301010106() {
        return b2000065301010106;
    }

    public void setB2000065301010106(BigDecimal b2000065301010106) {
        this.b2000065301010106 = b2000065301010106;
    }

    public BigDecimal getB2000065301010107() {
        return b2000065301010107;
    }

    public void setB2000065301010107(BigDecimal b2000065301010107) {
        this.b2000065301010107 = b2000065301010107;
    }

    public BigDecimal getB2000065301010108() {
        return b2000065301010108;
    }

    public void setB2000065301010108(BigDecimal b2000065301010108) {
        this.b2000065301010108 = b2000065301010108;
    }

    public BigDecimal getB2000065301010109() {
        return b2000065301010109;
    }

    public void setB2000065301010109(BigDecimal b2000065301010109) {
        this.b2000065301010109 = b2000065301010109;
    }

    public BigDecimal getB2000065301010110() {
        return b2000065301010110;
    }

    public void setB2000065301010110(BigDecimal b2000065301010110) {
        this.b2000065301010110 = b2000065301010110;
    }

    public BigDecimal getB2000065301010111() {
        return b2000065301010111;
    }

    public void setB2000065301010111(BigDecimal b2000065301010111) {
        this.b2000065301010111 = b2000065301010111;
    }

    public BigDecimal getB2000065301010112() {
        return b2000065301010112;
    }

    public void setB2000065301010112(BigDecimal b2000065301010112) {
        this.b2000065301010112 = b2000065301010112;
    }

    public BigDecimal getB2000065301010113() {
        return b2000065301010113;
    }

    public void setB2000065301010113(BigDecimal b2000065301010113) {
        this.b2000065301010113 = b2000065301010113;
    }

    public BigDecimal getB20000653010102() {
        return b20000653010102;
    }

    public void setB20000653010102(BigDecimal b20000653010102) {
        this.b20000653010102 = b20000653010102;
    }

    public BigDecimal getB20000653010103() {
        return b20000653010103;
    }

    public void setB20000653010103(BigDecimal b20000653010103) {
        this.b20000653010103 = b20000653010103;
    }

    public BigDecimal getB2000065301010301() {
        return b2000065301010301;
    }

    public void setB2000065301010301(BigDecimal b2000065301010301) {
        this.b2000065301010301 = b2000065301010301;
    }

    public BigDecimal getB2000065301010302() {
        return b2000065301010302;
    }

    public void setB2000065301010302(BigDecimal b2000065301010302) {
        this.b2000065301010302 = b2000065301010302;
    }

    public BigDecimal getB2000065301010303() {
        return b2000065301010303;
    }

    public void setB2000065301010303(BigDecimal b2000065301010303) {
        this.b2000065301010303 = b2000065301010303;
    }

    public BigDecimal getB2000065301010304() {
        return b2000065301010304;
    }

    public void setB2000065301010304(BigDecimal b2000065301010304) {
        this.b2000065301010304 = b2000065301010304;
    }

    public BigDecimal getB2000065301010305() {
        return b2000065301010305;
    }

    public void setB2000065301010305(BigDecimal b2000065301010305) {
        this.b2000065301010305 = b2000065301010305;
    }

    public BigDecimal getB2000065301010306() {
        return b2000065301010306;
    }

    public void setB2000065301010306(BigDecimal b2000065301010306) {
        this.b2000065301010306 = b2000065301010306;
    }

    public BigDecimal getB2000065301010307() {
        return b2000065301010307;
    }

    public void setB2000065301010307(BigDecimal b2000065301010307) {
        this.b2000065301010307 = b2000065301010307;
    }

    public BigDecimal getB2000065301010308() {
        return b2000065301010308;
    }

    public void setB2000065301010308(BigDecimal b2000065301010308) {
        this.b2000065301010308 = b2000065301010308;
    }

    public BigDecimal getB2000065301010309() {
        return b2000065301010309;
    }

    public void setB2000065301010309(BigDecimal b2000065301010309) {
        this.b2000065301010309 = b2000065301010309;
    }

    public BigDecimal getB2000065301010310() {
        return b2000065301010310;
    }

    public void setB2000065301010310(BigDecimal b2000065301010310) {
        this.b2000065301010310 = b2000065301010310;
    }

    public BigDecimal getB2000065301010311() {
        return b2000065301010311;
    }

    public void setB2000065301010311(BigDecimal b2000065301010311) {
        this.b2000065301010311 = b2000065301010311;
    }

    public BigDecimal getB2000065301010312() {
        return b2000065301010312;
    }

    public void setB2000065301010312(BigDecimal b2000065301010312) {
        this.b2000065301010312 = b2000065301010312;
    }

    public BigDecimal getB2000065301010313() {
        return b2000065301010313;
    }

    public void setB2000065301010313(BigDecimal b2000065301010313) {
        this.b2000065301010313 = b2000065301010313;
    }

    public BigDecimal getB20000653010104() {
        return b20000653010104;
    }

    public void setB20000653010104(BigDecimal b20000653010104) {
        this.b20000653010104 = b20000653010104;
    }

    public BigDecimal getB2000065301010401() {
        return b2000065301010401;
    }

    public void setB2000065301010401(BigDecimal b2000065301010401) {
        this.b2000065301010401 = b2000065301010401;
    }

    public BigDecimal getB2000065301010402() {
        return b2000065301010402;
    }

    public void setB2000065301010402(BigDecimal b2000065301010402) {
        this.b2000065301010402 = b2000065301010402;
    }

    public BigDecimal getB20000653010105() {
        return b20000653010105;
    }

    public void setB20000653010105(BigDecimal b20000653010105) {
        this.b20000653010105 = b20000653010105;
    }

    public BigDecimal getB2000065301010501() {
        return b2000065301010501;
    }

    public void setB2000065301010501(BigDecimal b2000065301010501) {
        this.b2000065301010501 = b2000065301010501;
    }

    public BigDecimal getB2000065301010502() {
        return b2000065301010502;
    }

    public void setB2000065301010502(BigDecimal b2000065301010502) {
        this.b2000065301010502 = b2000065301010502;
    }

    public BigDecimal getB20000653010106() {
        return b20000653010106;
    }

    public void setB20000653010106(BigDecimal b20000653010106) {
        this.b20000653010106 = b20000653010106;
    }

    public BigDecimal getB2000065301010601() {
        return b2000065301010601;
    }

    public void setB2000065301010601(BigDecimal b2000065301010601) {
        this.b2000065301010601 = b2000065301010601;
    }

    public BigDecimal getB2000065301010602() {
        return b2000065301010602;
    }

    public void setB2000065301010602(BigDecimal b2000065301010602) {
        this.b2000065301010602 = b2000065301010602;
    }

    public BigDecimal getB2000065301010603() {
        return b2000065301010603;
    }

    public void setB2000065301010603(BigDecimal b2000065301010603) {
        this.b2000065301010603 = b2000065301010603;
    }

    public BigDecimal getB2000065301010604() {
        return b2000065301010604;
    }

    public void setB2000065301010604(BigDecimal b2000065301010604) {
        this.b2000065301010604 = b2000065301010604;
    }

    public BigDecimal getB20000653010107() {
        return b20000653010107;
    }

    public void setB20000653010107(BigDecimal b20000653010107) {
        this.b20000653010107 = b20000653010107;
    }

    public BigDecimal getB2000065301010701() {
        return b2000065301010701;
    }

    public void setB2000065301010701(BigDecimal b2000065301010701) {
        this.b2000065301010701 = b2000065301010701;
    }

    public BigDecimal getB2000065301010702() {
        return b2000065301010702;
    }

    public void setB2000065301010702(BigDecimal b2000065301010702) {
        this.b2000065301010702 = b2000065301010702;
    }

    public BigDecimal getB2000065301010703() {
        return b2000065301010703;
    }

    public void setB2000065301010703(BigDecimal b2000065301010703) {
        this.b2000065301010703 = b2000065301010703;
    }

    public BigDecimal getB20000653010108() {
        return b20000653010108;
    }

    public void setB20000653010108(BigDecimal b20000653010108) {
        this.b20000653010108 = b20000653010108;
    }

    public BigDecimal getB2000065301010801() {
        return b2000065301010801;
    }

    public void setB2000065301010801(BigDecimal b2000065301010801) {
        this.b2000065301010801 = b2000065301010801;
    }

    public BigDecimal getB2000065301010802() {
        return b2000065301010802;
    }

    public void setB2000065301010802(BigDecimal b2000065301010802) {
        this.b2000065301010802 = b2000065301010802;
    }

    public BigDecimal getB2000065301010803() {
        return b2000065301010803;
    }

    public void setB2000065301010803(BigDecimal b2000065301010803) {
        this.b2000065301010803 = b2000065301010803;
    }

    public BigDecimal getB2000065301010804() {
        return b2000065301010804;
    }

    public void setB2000065301010804(BigDecimal b2000065301010804) {
        this.b2000065301010804 = b2000065301010804;
    }

    public BigDecimal getB2000065301010805() {
        return b2000065301010805;
    }

    public void setB2000065301010805(BigDecimal b2000065301010805) {
        this.b2000065301010805 = b2000065301010805;
    }

    public BigDecimal getB2000065301010806() {
        return b2000065301010806;
    }

    public void setB2000065301010806(BigDecimal b2000065301010806) {
        this.b2000065301010806 = b2000065301010806;
    }

    public BigDecimal getB20000653010109() {
        return b20000653010109;
    }

    public void setB20000653010109(BigDecimal b20000653010109) {
        this.b20000653010109 = b20000653010109;
    }

    public BigDecimal getB2000065301010901() {
        return b2000065301010901;
    }

    public void setB2000065301010901(BigDecimal b2000065301010901) {
        this.b2000065301010901 = b2000065301010901;
    }

    public BigDecimal getB2000065301010902() {
        return b2000065301010902;
    }

    public void setB2000065301010902(BigDecimal b2000065301010902) {
        this.b2000065301010902 = b2000065301010902;
    }

    public BigDecimal getB2000065301010903() {
        return b2000065301010903;
    }

    public void setB2000065301010903(BigDecimal b2000065301010903) {
        this.b2000065301010903 = b2000065301010903;
    }

    public BigDecimal getB2000065301010904() {
        return b2000065301010904;
    }

    public void setB2000065301010904(BigDecimal b2000065301010904) {
        this.b2000065301010904 = b2000065301010904;
    }

    public BigDecimal getB2000065301010905() {
        return b2000065301010905;
    }

    public void setB2000065301010905(BigDecimal b2000065301010905) {
        this.b2000065301010905 = b2000065301010905;
    }

    public BigDecimal getB2000065301010906() {
        return b2000065301010906;
    }

    public void setB2000065301010906(BigDecimal b2000065301010906) {
        this.b2000065301010906 = b2000065301010906;
    }

    public BigDecimal getB2000065301010907() {
        return b2000065301010907;
    }

    public void setB2000065301010907(BigDecimal b2000065301010907) {
        this.b2000065301010907 = b2000065301010907;
    }

    public BigDecimal getB20000653010110() {
        return b20000653010110;
    }

    public void setB20000653010110(BigDecimal b20000653010110) {
        this.b20000653010110 = b20000653010110;
    }

    public BigDecimal getB20000653010111() {
        return b20000653010111;
    }

    public void setB20000653010111(BigDecimal b20000653010111) {
        this.b20000653010111 = b20000653010111;
    }

    public BigDecimal getB2000065301011101() {
        return b2000065301011101;
    }

    public void setB2000065301011101(BigDecimal b2000065301011101) {
        this.b2000065301011101 = b2000065301011101;
    }

    public BigDecimal getB2000065301011102() {
        return b2000065301011102;
    }

    public void setB2000065301011102(BigDecimal b2000065301011102) {
        this.b2000065301011102 = b2000065301011102;
    }

    public BigDecimal getB2000065301011103() {
        return b2000065301011103;
    }

    public void setB2000065301011103(BigDecimal b2000065301011103) {
        this.b2000065301011103 = b2000065301011103;
    }

    public BigDecimal getB2000065301011104() {
        return b2000065301011104;
    }

    public void setB2000065301011104(BigDecimal b2000065301011104) {
        this.b2000065301011104 = b2000065301011104;
    }

    public BigDecimal getB20000653010112() {
        return b20000653010112;
    }

    public void setB20000653010112(BigDecimal b20000653010112) {
        this.b20000653010112 = b20000653010112;
    }

    public BigDecimal getB20000653010113() {
        return b20000653010113;
    }

    public void setB20000653010113(BigDecimal b20000653010113) {
        this.b20000653010113 = b20000653010113;
    }

    public BigDecimal getB20000653010114() {
        return b20000653010114;
    }

    public void setB20000653010114(BigDecimal b20000653010114) {
        this.b20000653010114 = b20000653010114;
    }

    public BigDecimal getB20000653010115() {
        return b20000653010115;
    }

    public void setB20000653010115(BigDecimal b20000653010115) {
        this.b20000653010115 = b20000653010115;
    }

    public BigDecimal getB20000653010116() {
        return b20000653010116;
    }

    public void setB20000653010116(BigDecimal b20000653010116) {
        this.b20000653010116 = b20000653010116;
    }

    public BigDecimal getB2000065301011601() {
        return b2000065301011601;
    }

    public void setB2000065301011601(BigDecimal b2000065301011601) {
        this.b2000065301011601 = b2000065301011601;
    }

    public BigDecimal getB2000065301011602() {
        return b2000065301011602;
    }

    public void setB2000065301011602(BigDecimal b2000065301011602) {
        this.b2000065301011602 = b2000065301011602;
    }

    public BigDecimal getB2000065301011603() {
        return b2000065301011603;
    }

    public void setB2000065301011603(BigDecimal b2000065301011603) {
        this.b2000065301011603 = b2000065301011603;
    }

    public BigDecimal getB2000065301011604() {
        return b2000065301011604;
    }

    public void setB2000065301011604(BigDecimal b2000065301011604) {
        this.b2000065301011604 = b2000065301011604;
    }

    public BigDecimal getB2000065301011605() {
        return b2000065301011605;
    }

    public void setB2000065301011605(BigDecimal b2000065301011605) {
        this.b2000065301011605 = b2000065301011605;
    }

    public BigDecimal getB2000065301011606() {
        return b2000065301011606;
    }

    public void setB2000065301011606(BigDecimal b2000065301011606) {
        this.b2000065301011606 = b2000065301011606;
    }

    public BigDecimal getB2000065301011607() {
        return b2000065301011607;
    }

    public void setB2000065301011607(BigDecimal b2000065301011607) {
        this.b2000065301011607 = b2000065301011607;
    }

    public BigDecimal getB2000065301011608() {
        return b2000065301011608;
    }

    public void setB2000065301011608(BigDecimal b2000065301011608) {
        this.b2000065301011608 = b2000065301011608;
    }

    public BigDecimal getB2000065301011609() {
        return b2000065301011609;
    }

    public void setB2000065301011609(BigDecimal b2000065301011609) {
        this.b2000065301011609 = b2000065301011609;
    }

    public BigDecimal getB20000653010117() {
        return b20000653010117;
    }

    public void setB20000653010117(BigDecimal b20000653010117) {
        this.b20000653010117 = b20000653010117;
    }

    public BigDecimal getB2000065301011701() {
        return b2000065301011701;
    }

    public void setB2000065301011701(BigDecimal b2000065301011701) {
        this.b2000065301011701 = b2000065301011701;
    }

    public BigDecimal getB2000065301011702() {
        return b2000065301011702;
    }

    public void setB2000065301011702(BigDecimal b2000065301011702) {
        this.b2000065301011702 = b2000065301011702;
    }

    public BigDecimal getB2000065301011703() {
        return b2000065301011703;
    }

    public void setB2000065301011703(BigDecimal b2000065301011703) {
        this.b2000065301011703 = b2000065301011703;
    }

    public BigDecimal getB2000065301011704() {
        return b2000065301011704;
    }

    public void setB2000065301011704(BigDecimal b2000065301011704) {
        this.b2000065301011704 = b2000065301011704;
    }

    public BigDecimal getB20000653010118() {
        return b20000653010118;
    }

    public void setB20000653010118(BigDecimal b20000653010118) {
        this.b20000653010118 = b20000653010118;
    }

    public BigDecimal getB20000653010119() {
        return b20000653010119;
    }

    public void setB20000653010119(BigDecimal b20000653010119) {
        this.b20000653010119 = b20000653010119;
    }

    public BigDecimal getB20000653010120() {
        return b20000653010120;
    }

    public void setB20000653010120(BigDecimal b20000653010120) {
        this.b20000653010120 = b20000653010120;
    }

    public BigDecimal getB20000653010121() {
        return b20000653010121;
    }

    public void setB20000653010121(BigDecimal b20000653010121) {
        this.b20000653010121 = b20000653010121;
    }

    public BigDecimal getB20000653010122() {
        return b20000653010122;
    }

    public void setB20000653010122(BigDecimal b20000653010122) {
        this.b20000653010122 = b20000653010122;
    }

    public BigDecimal getB20000653010123() {
        return b20000653010123;
    }

    public void setB20000653010123(BigDecimal b20000653010123) {
        this.b20000653010123 = b20000653010123;
    }

    public BigDecimal getB20000653010124() {
        return b20000653010124;
    }

    public void setB20000653010124(BigDecimal b20000653010124) {
        this.b20000653010124 = b20000653010124;
    }

    public BigDecimal getB20000653010125() {
        return b20000653010125;
    }

    public void setB20000653010125(BigDecimal b20000653010125) {
        this.b20000653010125 = b20000653010125;
    }

    public BigDecimal getB20000653010126() {
        return b20000653010126;
    }

    public void setB20000653010126(BigDecimal b20000653010126) {
        this.b20000653010126 = b20000653010126;
    }

    public BigDecimal getB2000065301012601() {
        return b2000065301012601;
    }

    public void setB2000065301012601(BigDecimal b2000065301012601) {
        this.b2000065301012601 = b2000065301012601;
    }

    public BigDecimal getB2000065301012602() {
        return b2000065301012602;
    }

    public void setB2000065301012602(BigDecimal b2000065301012602) {
        this.b2000065301012602 = b2000065301012602;
    }

    public BigDecimal getB2000065301012603() {
        return b2000065301012603;
    }

    public void setB2000065301012603(BigDecimal b2000065301012603) {
        this.b2000065301012603 = b2000065301012603;
    }

    public BigDecimal getB2000065301012604() {
        return b2000065301012604;
    }

    public void setB2000065301012604(BigDecimal b2000065301012604) {
        this.b2000065301012604 = b2000065301012604;
    }

    public BigDecimal getB200006530102() {
        return b200006530102;
    }

    public void setB200006530102(BigDecimal b200006530102) {
        this.b200006530102 = b200006530102;
    }

    public BigDecimal getB20000653010201() {
        return b20000653010201;
    }

    public void setB20000653010201(BigDecimal b20000653010201) {
        this.b20000653010201 = b20000653010201;
    }

    public BigDecimal getB2000065301020101() {
        return b2000065301020101;
    }

    public void setB2000065301020101(BigDecimal b2000065301020101) {
        this.b2000065301020101 = b2000065301020101;
    }

    public BigDecimal getB2000065301020102() {
        return b2000065301020102;
    }

    public void setB2000065301020102(BigDecimal b2000065301020102) {
        this.b2000065301020102 = b2000065301020102;
    }

    public BigDecimal getB2000065301020103() {
        return b2000065301020103;
    }

    public void setB2000065301020103(BigDecimal b2000065301020103) {
        this.b2000065301020103 = b2000065301020103;
    }

    public BigDecimal getB2000065301020104() {
        return b2000065301020104;
    }

    public void setB2000065301020104(BigDecimal b2000065301020104) {
        this.b2000065301020104 = b2000065301020104;
    }

    public BigDecimal getB2000065301020105() {
        return b2000065301020105;
    }

    public void setB2000065301020105(BigDecimal b2000065301020105) {
        this.b2000065301020105 = b2000065301020105;
    }

    public BigDecimal getB2000065301020106() {
        return b2000065301020106;
    }

    public void setB2000065301020106(BigDecimal b2000065301020106) {
        this.b2000065301020106 = b2000065301020106;
    }

    public BigDecimal getB2000065301020107() {
        return b2000065301020107;
    }

    public void setB2000065301020107(BigDecimal b2000065301020107) {
        this.b2000065301020107 = b2000065301020107;
    }

    public BigDecimal getB2000065301020108() {
        return b2000065301020108;
    }

    public void setB2000065301020108(BigDecimal b2000065301020108) {
        this.b2000065301020108 = b2000065301020108;
    }

    public BigDecimal getB2000065301020109() {
        return b2000065301020109;
    }

    public void setB2000065301020109(BigDecimal b2000065301020109) {
        this.b2000065301020109 = b2000065301020109;
    }

    public BigDecimal getB2000065301020110() {
        return b2000065301020110;
    }

    public void setB2000065301020110(BigDecimal b2000065301020110) {
        this.b2000065301020110 = b2000065301020110;
    }

    public BigDecimal getB2000065301020111() {
        return b2000065301020111;
    }

    public void setB2000065301020111(BigDecimal b2000065301020111) {
        this.b2000065301020111 = b2000065301020111;
    }

    public BigDecimal getB2000065301020112() {
        return b2000065301020112;
    }

    public void setB2000065301020112(BigDecimal b2000065301020112) {
        this.b2000065301020112 = b2000065301020112;
    }

    public BigDecimal getB2000065301020113() {
        return b2000065301020113;
    }

    public void setB2000065301020113(BigDecimal b2000065301020113) {
        this.b2000065301020113 = b2000065301020113;
    }

    public BigDecimal getB20000653010202() {
        return b20000653010202;
    }

    public void setB20000653010202(BigDecimal b20000653010202) {
        this.b20000653010202 = b20000653010202;
    }

    public BigDecimal getB20000653010203() {
        return b20000653010203;
    }

    public void setB20000653010203(BigDecimal b20000653010203) {
        this.b20000653010203 = b20000653010203;
    }

    public BigDecimal getB2000065301020301() {
        return b2000065301020301;
    }

    public void setB2000065301020301(BigDecimal b2000065301020301) {
        this.b2000065301020301 = b2000065301020301;
    }

    public BigDecimal getB2000065301020302() {
        return b2000065301020302;
    }

    public void setB2000065301020302(BigDecimal b2000065301020302) {
        this.b2000065301020302 = b2000065301020302;
    }

    public BigDecimal getB2000065301020303() {
        return b2000065301020303;
    }

    public void setB2000065301020303(BigDecimal b2000065301020303) {
        this.b2000065301020303 = b2000065301020303;
    }

    public BigDecimal getB2000065301020304() {
        return b2000065301020304;
    }

    public void setB2000065301020304(BigDecimal b2000065301020304) {
        this.b2000065301020304 = b2000065301020304;
    }

    public BigDecimal getB2000065301020305() {
        return b2000065301020305;
    }

    public void setB2000065301020305(BigDecimal b2000065301020305) {
        this.b2000065301020305 = b2000065301020305;
    }

    public BigDecimal getB2000065301020306() {
        return b2000065301020306;
    }

    public void setB2000065301020306(BigDecimal b2000065301020306) {
        this.b2000065301020306 = b2000065301020306;
    }

    public BigDecimal getB2000065301020307() {
        return b2000065301020307;
    }

    public void setB2000065301020307(BigDecimal b2000065301020307) {
        this.b2000065301020307 = b2000065301020307;
    }

    public BigDecimal getB2000065301020308() {
        return b2000065301020308;
    }

    public void setB2000065301020308(BigDecimal b2000065301020308) {
        this.b2000065301020308 = b2000065301020308;
    }

    public BigDecimal getB2000065301020309() {
        return b2000065301020309;
    }

    public void setB2000065301020309(BigDecimal b2000065301020309) {
        this.b2000065301020309 = b2000065301020309;
    }

    public BigDecimal getB2000065301020310() {
        return b2000065301020310;
    }

    public void setB2000065301020310(BigDecimal b2000065301020310) {
        this.b2000065301020310 = b2000065301020310;
    }

    public BigDecimal getB2000065301020311() {
        return b2000065301020311;
    }

    public void setB2000065301020311(BigDecimal b2000065301020311) {
        this.b2000065301020311 = b2000065301020311;
    }

    public BigDecimal getB2000065301020312() {
        return b2000065301020312;
    }

    public void setB2000065301020312(BigDecimal b2000065301020312) {
        this.b2000065301020312 = b2000065301020312;
    }

    public BigDecimal getB2000065301020313() {
        return b2000065301020313;
    }

    public void setB2000065301020313(BigDecimal b2000065301020313) {
        this.b2000065301020313 = b2000065301020313;
    }

    public BigDecimal getB20000653010204() {
        return b20000653010204;
    }

    public void setB20000653010204(BigDecimal b20000653010204) {
        this.b20000653010204 = b20000653010204;
    }

    public BigDecimal getB2000065301020401() {
        return b2000065301020401;
    }

    public void setB2000065301020401(BigDecimal b2000065301020401) {
        this.b2000065301020401 = b2000065301020401;
    }

    public BigDecimal getB2000065301020402() {
        return b2000065301020402;
    }

    public void setB2000065301020402(BigDecimal b2000065301020402) {
        this.b2000065301020402 = b2000065301020402;
    }

    public BigDecimal getB20000653010205() {
        return b20000653010205;
    }

    public void setB20000653010205(BigDecimal b20000653010205) {
        this.b20000653010205 = b20000653010205;
    }

    public BigDecimal getB2000065301020501() {
        return b2000065301020501;
    }

    public void setB2000065301020501(BigDecimal b2000065301020501) {
        this.b2000065301020501 = b2000065301020501;
    }

    public BigDecimal getB2000065301020502() {
        return b2000065301020502;
    }

    public void setB2000065301020502(BigDecimal b2000065301020502) {
        this.b2000065301020502 = b2000065301020502;
    }

    public BigDecimal getB20000653010206() {
        return b20000653010206;
    }

    public void setB20000653010206(BigDecimal b20000653010206) {
        this.b20000653010206 = b20000653010206;
    }

    public BigDecimal getB2000065301020601() {
        return b2000065301020601;
    }

    public void setB2000065301020601(BigDecimal b2000065301020601) {
        this.b2000065301020601 = b2000065301020601;
    }

    public BigDecimal getB2000065301020602() {
        return b2000065301020602;
    }

    public void setB2000065301020602(BigDecimal b2000065301020602) {
        this.b2000065301020602 = b2000065301020602;
    }

    public BigDecimal getB2000065301020603() {
        return b2000065301020603;
    }

    public void setB2000065301020603(BigDecimal b2000065301020603) {
        this.b2000065301020603 = b2000065301020603;
    }

    public BigDecimal getB2000065301020604() {
        return b2000065301020604;
    }

    public void setB2000065301020604(BigDecimal b2000065301020604) {
        this.b2000065301020604 = b2000065301020604;
    }

    public BigDecimal getB20000653010207() {
        return b20000653010207;
    }

    public void setB20000653010207(BigDecimal b20000653010207) {
        this.b20000653010207 = b20000653010207;
    }

    public BigDecimal getB2000065301020701() {
        return b2000065301020701;
    }

    public void setB2000065301020701(BigDecimal b2000065301020701) {
        this.b2000065301020701 = b2000065301020701;
    }

    public BigDecimal getB2000065301020702() {
        return b2000065301020702;
    }

    public void setB2000065301020702(BigDecimal b2000065301020702) {
        this.b2000065301020702 = b2000065301020702;
    }

    public BigDecimal getB2000065301020703() {
        return b2000065301020703;
    }

    public void setB2000065301020703(BigDecimal b2000065301020703) {
        this.b2000065301020703 = b2000065301020703;
    }

    public BigDecimal getB20000653010208() {
        return b20000653010208;
    }

    public void setB20000653010208(BigDecimal b20000653010208) {
        this.b20000653010208 = b20000653010208;
    }

    public BigDecimal getB2000065301020801() {
        return b2000065301020801;
    }

    public void setB2000065301020801(BigDecimal b2000065301020801) {
        this.b2000065301020801 = b2000065301020801;
    }

    public BigDecimal getB2000065301020802() {
        return b2000065301020802;
    }

    public void setB2000065301020802(BigDecimal b2000065301020802) {
        this.b2000065301020802 = b2000065301020802;
    }

    public BigDecimal getB2000065301020803() {
        return b2000065301020803;
    }

    public void setB2000065301020803(BigDecimal b2000065301020803) {
        this.b2000065301020803 = b2000065301020803;
    }

    public BigDecimal getB2000065301020804() {
        return b2000065301020804;
    }

    public void setB2000065301020804(BigDecimal b2000065301020804) {
        this.b2000065301020804 = b2000065301020804;
    }

    public BigDecimal getB2000065301020805() {
        return b2000065301020805;
    }

    public void setB2000065301020805(BigDecimal b2000065301020805) {
        this.b2000065301020805 = b2000065301020805;
    }

    public BigDecimal getB2000065301020806() {
        return b2000065301020806;
    }

    public void setB2000065301020806(BigDecimal b2000065301020806) {
        this.b2000065301020806 = b2000065301020806;
    }

    public BigDecimal getB20000653010209() {
        return b20000653010209;
    }

    public void setB20000653010209(BigDecimal b20000653010209) {
        this.b20000653010209 = b20000653010209;
    }

    public BigDecimal getB2000065301020901() {
        return b2000065301020901;
    }

    public void setB2000065301020901(BigDecimal b2000065301020901) {
        this.b2000065301020901 = b2000065301020901;
    }

    public BigDecimal getB2000065301020902() {
        return b2000065301020902;
    }

    public void setB2000065301020902(BigDecimal b2000065301020902) {
        this.b2000065301020902 = b2000065301020902;
    }

    public BigDecimal getB2000065301020903() {
        return b2000065301020903;
    }

    public void setB2000065301020903(BigDecimal b2000065301020903) {
        this.b2000065301020903 = b2000065301020903;
    }

    public BigDecimal getB2000065301020904() {
        return b2000065301020904;
    }

    public void setB2000065301020904(BigDecimal b2000065301020904) {
        this.b2000065301020904 = b2000065301020904;
    }

    public BigDecimal getB2000065301020905() {
        return b2000065301020905;
    }

    public void setB2000065301020905(BigDecimal b2000065301020905) {
        this.b2000065301020905 = b2000065301020905;
    }

    public BigDecimal getB2000065301020906() {
        return b2000065301020906;
    }

    public void setB2000065301020906(BigDecimal b2000065301020906) {
        this.b2000065301020906 = b2000065301020906;
    }

    public BigDecimal getB2000065301020907() {
        return b2000065301020907;
    }

    public void setB2000065301020907(BigDecimal b2000065301020907) {
        this.b2000065301020907 = b2000065301020907;
    }

    public BigDecimal getB20000653010210() {
        return b20000653010210;
    }

    public void setB20000653010210(BigDecimal b20000653010210) {
        this.b20000653010210 = b20000653010210;
    }

    public BigDecimal getB20000653010211() {
        return b20000653010211;
    }

    public void setB20000653010211(BigDecimal b20000653010211) {
        this.b20000653010211 = b20000653010211;
    }

    public BigDecimal getB2000065301021101() {
        return b2000065301021101;
    }

    public void setB2000065301021101(BigDecimal b2000065301021101) {
        this.b2000065301021101 = b2000065301021101;
    }

    public BigDecimal getB2000065301021102() {
        return b2000065301021102;
    }

    public void setB2000065301021102(BigDecimal b2000065301021102) {
        this.b2000065301021102 = b2000065301021102;
    }

    public BigDecimal getB2000065301021103() {
        return b2000065301021103;
    }

    public void setB2000065301021103(BigDecimal b2000065301021103) {
        this.b2000065301021103 = b2000065301021103;
    }

    public BigDecimal getB2000065301021104() {
        return b2000065301021104;
    }

    public void setB2000065301021104(BigDecimal b2000065301021104) {
        this.b2000065301021104 = b2000065301021104;
    }

    public BigDecimal getB20000653010212() {
        return b20000653010212;
    }

    public void setB20000653010212(BigDecimal b20000653010212) {
        this.b20000653010212 = b20000653010212;
    }

    public BigDecimal getB20000653010213() {
        return b20000653010213;
    }

    public void setB20000653010213(BigDecimal b20000653010213) {
        this.b20000653010213 = b20000653010213;
    }

    public BigDecimal getB20000653010214() {
        return b20000653010214;
    }

    public void setB20000653010214(BigDecimal b20000653010214) {
        this.b20000653010214 = b20000653010214;
    }

    public BigDecimal getB20000653010215() {
        return b20000653010215;
    }

    public void setB20000653010215(BigDecimal b20000653010215) {
        this.b20000653010215 = b20000653010215;
    }

    public BigDecimal getB20000653010216() {
        return b20000653010216;
    }

    public void setB20000653010216(BigDecimal b20000653010216) {
        this.b20000653010216 = b20000653010216;
    }

    public BigDecimal getB2000065301021601() {
        return b2000065301021601;
    }

    public void setB2000065301021601(BigDecimal b2000065301021601) {
        this.b2000065301021601 = b2000065301021601;
    }

    public BigDecimal getB2000065301021602() {
        return b2000065301021602;
    }

    public void setB2000065301021602(BigDecimal b2000065301021602) {
        this.b2000065301021602 = b2000065301021602;
    }

    public BigDecimal getB2000065301021603() {
        return b2000065301021603;
    }

    public void setB2000065301021603(BigDecimal b2000065301021603) {
        this.b2000065301021603 = b2000065301021603;
    }

    public BigDecimal getB2000065301021604() {
        return b2000065301021604;
    }

    public void setB2000065301021604(BigDecimal b2000065301021604) {
        this.b2000065301021604 = b2000065301021604;
    }

    public BigDecimal getB2000065301021605() {
        return b2000065301021605;
    }

    public void setB2000065301021605(BigDecimal b2000065301021605) {
        this.b2000065301021605 = b2000065301021605;
    }

    public BigDecimal getB2000065301021606() {
        return b2000065301021606;
    }

    public void setB2000065301021606(BigDecimal b2000065301021606) {
        this.b2000065301021606 = b2000065301021606;
    }

    public BigDecimal getB2000065301021607() {
        return b2000065301021607;
    }

    public void setB2000065301021607(BigDecimal b2000065301021607) {
        this.b2000065301021607 = b2000065301021607;
    }

    public BigDecimal getB2000065301021608() {
        return b2000065301021608;
    }

    public void setB2000065301021608(BigDecimal b2000065301021608) {
        this.b2000065301021608 = b2000065301021608;
    }

    public BigDecimal getB2000065301021609() {
        return b2000065301021609;
    }

    public void setB2000065301021609(BigDecimal b2000065301021609) {
        this.b2000065301021609 = b2000065301021609;
    }

    public BigDecimal getB20000653010217() {
        return b20000653010217;
    }

    public void setB20000653010217(BigDecimal b20000653010217) {
        this.b20000653010217 = b20000653010217;
    }

    public BigDecimal getB2000065301021701() {
        return b2000065301021701;
    }

    public void setB2000065301021701(BigDecimal b2000065301021701) {
        this.b2000065301021701 = b2000065301021701;
    }

    public BigDecimal getB2000065301021702() {
        return b2000065301021702;
    }

    public void setB2000065301021702(BigDecimal b2000065301021702) {
        this.b2000065301021702 = b2000065301021702;
    }

    public BigDecimal getB2000065301021703() {
        return b2000065301021703;
    }

    public void setB2000065301021703(BigDecimal b2000065301021703) {
        this.b2000065301021703 = b2000065301021703;
    }

    public BigDecimal getB2000065301021704() {
        return b2000065301021704;
    }

    public void setB2000065301021704(BigDecimal b2000065301021704) {
        this.b2000065301021704 = b2000065301021704;
    }

    public BigDecimal getB20000653010218() {
        return b20000653010218;
    }

    public void setB20000653010218(BigDecimal b20000653010218) {
        this.b20000653010218 = b20000653010218;
    }

    public BigDecimal getB20000653010219() {
        return b20000653010219;
    }

    public void setB20000653010219(BigDecimal b20000653010219) {
        this.b20000653010219 = b20000653010219;
    }

    public BigDecimal getB20000653010220() {
        return b20000653010220;
    }

    public void setB20000653010220(BigDecimal b20000653010220) {
        this.b20000653010220 = b20000653010220;
    }

    public BigDecimal getB20000653010221() {
        return b20000653010221;
    }

    public void setB20000653010221(BigDecimal b20000653010221) {
        this.b20000653010221 = b20000653010221;
    }

    public BigDecimal getB20000653010222() {
        return b20000653010222;
    }

    public void setB20000653010222(BigDecimal b20000653010222) {
        this.b20000653010222 = b20000653010222;
    }

    public BigDecimal getB20000653010223() {
        return b20000653010223;
    }

    public void setB20000653010223(BigDecimal b20000653010223) {
        this.b20000653010223 = b20000653010223;
    }

    public BigDecimal getB20000653010224() {
        return b20000653010224;
    }

    public void setB20000653010224(BigDecimal b20000653010224) {
        this.b20000653010224 = b20000653010224;
    }

    public BigDecimal getB20000653010225() {
        return b20000653010225;
    }

    public void setB20000653010225(BigDecimal b20000653010225) {
        this.b20000653010225 = b20000653010225;
    }

    public BigDecimal getB20000653010226() {
        return b20000653010226;
    }

    public void setB20000653010226(BigDecimal b20000653010226) {
        this.b20000653010226 = b20000653010226;
    }

    public BigDecimal getB2000065301022601() {
        return b2000065301022601;
    }

    public void setB2000065301022601(BigDecimal b2000065301022601) {
        this.b2000065301022601 = b2000065301022601;
    }

    public BigDecimal getB2000065301022602() {
        return b2000065301022602;
    }

    public void setB2000065301022602(BigDecimal b2000065301022602) {
        this.b2000065301022602 = b2000065301022602;
    }

    public BigDecimal getB2000065301022603() {
        return b2000065301022603;
    }

    public void setB2000065301022603(BigDecimal b2000065301022603) {
        this.b2000065301022603 = b2000065301022603;
    }

    public BigDecimal getB2000065301022604() {
        return b2000065301022604;
    }

    public void setB2000065301022604(BigDecimal b2000065301022604) {
        this.b2000065301022604 = b2000065301022604;
    }

    public BigDecimal getB2000065401() {
        return b2000065401;
    }

    public void setB2000065401(BigDecimal b2000065401) {
        this.b2000065401 = b2000065401;
    }

    public BigDecimal getB2000065402() {
        return b2000065402;
    }

    public void setB2000065402(BigDecimal b2000065402) {
        this.b2000065402 = b2000065402;
    }

    public BigDecimal getB2000065403() {
        return b2000065403;
    }

    public void setB2000065403(BigDecimal b2000065403) {
        this.b2000065403 = b2000065403;
    }

    public BigDecimal getB2000065501() {
        return b2000065501;
    }

    public void setB2000065501(BigDecimal b2000065501) {
        this.b2000065501 = b2000065501;
    }

    public BigDecimal getB200006550101() {
        return b200006550101;
    }

    public void setB200006550101(BigDecimal b200006550101) {
        this.b200006550101 = b200006550101;
    }

    public BigDecimal getB200006550102() {
        return b200006550102;
    }

    public void setB200006550102(BigDecimal b200006550102) {
        this.b200006550102 = b200006550102;
    }

    public BigDecimal getB200006550103() {
        return b200006550103;
    }

    public void setB200006550103(BigDecimal b200006550103) {
        this.b200006550103 = b200006550103;
    }

    public BigDecimal getB200006550104() {
        return b200006550104;
    }

    public void setB200006550104(BigDecimal b200006550104) {
        this.b200006550104 = b200006550104;
    }

    public BigDecimal getB200006550105() {
        return b200006550105;
    }

    public void setB200006550105(BigDecimal b200006550105) {
        this.b200006550105 = b200006550105;
    }

    public BigDecimal getB200006550106() {
        return b200006550106;
    }

    public void setB200006550106(BigDecimal b200006550106) {
        this.b200006550106 = b200006550106;
    }

    public BigDecimal getB200006550107() {
        return b200006550107;
    }

    public void setB200006550107(BigDecimal b200006550107) {
        this.b200006550107 = b200006550107;
    }

    public BigDecimal getB200006550108() {
        return b200006550108;
    }

    public void setB200006550108(BigDecimal b200006550108) {
        this.b200006550108 = b200006550108;
    }

    public BigDecimal getB200006550109() {
        return b200006550109;
    }

    public void setB200006550109(BigDecimal b200006550109) {
        this.b200006550109 = b200006550109;
    }

    public BigDecimal getB200006550110() {
        return b200006550110;
    }

    public void setB200006550110(BigDecimal b200006550110) {
        this.b200006550110 = b200006550110;
    }

    public BigDecimal getB200006550111() {
        return b200006550111;
    }

    public void setB200006550111(BigDecimal b200006550111) {
        this.b200006550111 = b200006550111;
    }

    public BigDecimal getB200006550112() {
        return b200006550112;
    }

    public void setB200006550112(BigDecimal b200006550112) {
        this.b200006550112 = b200006550112;
    }

    public BigDecimal getB200006550113() {
        return b200006550113;
    }

    public void setB200006550113(BigDecimal b200006550113) {
        this.b200006550113 = b200006550113;
    }

    public BigDecimal getB200006550114() {
        return b200006550114;
    }

    public void setB200006550114(BigDecimal b200006550114) {
        this.b200006550114 = b200006550114;
    }

    public BigDecimal getB200006550115() {
        return b200006550115;
    }

    public void setB200006550115(BigDecimal b200006550115) {
        this.b200006550115 = b200006550115;
    }

    public BigDecimal getB2000065502() {
        return b2000065502;
    }

    public void setB2000065502(BigDecimal b2000065502) {
        this.b2000065502 = b2000065502;
    }

    public BigDecimal getB2000065503() {
        return b2000065503;
    }

    public void setB2000065503(BigDecimal b2000065503) {
        this.b2000065503 = b2000065503;
    }

    public BigDecimal getB2000065504() {
        return b2000065504;
    }

    public void setB2000065504(BigDecimal b2000065504) {
        this.b2000065504 = b2000065504;
    }

    public BigDecimal getB2000066001() {
        return b2000066001;
    }

    public void setB2000066001(BigDecimal b2000066001) {
        this.b2000066001 = b2000066001;
    }

    public BigDecimal getB200006600101() {
        return b200006600101;
    }

    public void setB200006600101(BigDecimal b200006600101) {
        this.b200006600101 = b200006600101;
    }

    public BigDecimal getB200006600102() {
        return b200006600102;
    }

    public void setB200006600102(BigDecimal b200006600102) {
        this.b200006600102 = b200006600102;
    }

    public BigDecimal getB200006600103() {
        return b200006600103;
    }

    public void setB200006600103(BigDecimal b200006600103) {
        this.b200006600103 = b200006600103;
    }

    public BigDecimal getB200006600104() {
        return b200006600104;
    }

    public void setB200006600104(BigDecimal b200006600104) {
        this.b200006600104 = b200006600104;
    }

    public BigDecimal getB200006600105() {
        return b200006600105;
    }

    public void setB200006600105(BigDecimal b200006600105) {
        this.b200006600105 = b200006600105;
    }

    public BigDecimal getB2000066011() {
        return b2000066011;
    }

    public void setB2000066011(BigDecimal b2000066011) {
        this.b2000066011 = b2000066011;
    }

    public BigDecimal getB2000066021() {
        return b2000066021;
    }

    public void setB2000066021(BigDecimal b2000066021) {
        this.b2000066021 = b2000066021;
    }

    public BigDecimal getB2000066031() {
        return b2000066031;
    }

    public void setB2000066031(BigDecimal b2000066031) {
        this.b2000066031 = b2000066031;
    }

    public BigDecimal getB2000066041() {
        return b2000066041;
    }

    public void setB2000066041(BigDecimal b2000066041) {
        this.b2000066041 = b2000066041;
    }

    public BigDecimal getB2000066051() {
        return b2000066051;
    }

    public void setB2000066051(BigDecimal b2000066051) {
        this.b2000066051 = b2000066051;
    }

    public BigDecimal getB200006605101() {
        return b200006605101;
    }

    public void setB200006605101(BigDecimal b200006605101) {
        this.b200006605101 = b200006605101;
    }

    public BigDecimal getB200006605102() {
        return b200006605102;
    }

    public void setB200006605102(BigDecimal b200006605102) {
        this.b200006605102 = b200006605102;
    }

    public BigDecimal getB200006605103() {
        return b200006605103;
    }

    public void setB200006605103(BigDecimal b200006605103) {
        this.b200006605103 = b200006605103;
    }

    public BigDecimal getB200006605104() {
        return b200006605104;
    }

    public void setB200006605104(BigDecimal b200006605104) {
        this.b200006605104 = b200006605104;
    }

    public BigDecimal getB200006605105() {
        return b200006605105;
    }

    public void setB200006605105(BigDecimal b200006605105) {
        this.b200006605105 = b200006605105;
    }

    public BigDecimal getB2000066061() {
        return b2000066061;
    }

    public void setB2000066061(BigDecimal b2000066061) {
        this.b2000066061 = b2000066061;
    }

    public BigDecimal getB2000066101() {
        return b2000066101;
    }

    public void setB2000066101(BigDecimal b2000066101) {
        this.b2000066101 = b2000066101;
    }

    public BigDecimal getB2000066111() {
        return b2000066111;
    }

    public void setB2000066111(BigDecimal b2000066111) {
        this.b2000066111 = b2000066111;
    }

    public BigDecimal getB200006611101() {
        return b200006611101;
    }

    public void setB200006611101(BigDecimal b200006611101) {
        this.b200006611101 = b200006611101;
    }

    public BigDecimal getB20000661110101() {
        return b20000661110101;
    }

    public void setB20000661110101(BigDecimal b20000661110101) {
        this.b20000661110101 = b20000661110101;
    }

    public BigDecimal getB2000066111010101() {
        return b2000066111010101;
    }

    public void setB2000066111010101(BigDecimal b2000066111010101) {
        this.b2000066111010101 = b2000066111010101;
    }

    public BigDecimal getB2000066111010102() {
        return b2000066111010102;
    }

    public void setB2000066111010102(BigDecimal b2000066111010102) {
        this.b2000066111010102 = b2000066111010102;
    }

    public BigDecimal getB20000661110102() {
        return b20000661110102;
    }

    public void setB20000661110102(BigDecimal b20000661110102) {
        this.b20000661110102 = b20000661110102;
    }

    public BigDecimal getB2000066111010201() {
        return b2000066111010201;
    }

    public void setB2000066111010201(BigDecimal b2000066111010201) {
        this.b2000066111010201 = b2000066111010201;
    }

    public BigDecimal getB2000066111010202() {
        return b2000066111010202;
    }

    public void setB2000066111010202(BigDecimal b2000066111010202) {
        this.b2000066111010202 = b2000066111010202;
    }

    public BigDecimal getB20000661110103() {
        return b20000661110103;
    }

    public void setB20000661110103(BigDecimal b20000661110103) {
        this.b20000661110103 = b20000661110103;
    }

    public BigDecimal getB2000066111010301() {
        return b2000066111010301;
    }

    public void setB2000066111010301(BigDecimal b2000066111010301) {
        this.b2000066111010301 = b2000066111010301;
    }

    public BigDecimal getB2000066111010302() {
        return b2000066111010302;
    }

    public void setB2000066111010302(BigDecimal b2000066111010302) {
        this.b2000066111010302 = b2000066111010302;
    }

    public BigDecimal getB20000661110104() {
        return b20000661110104;
    }

    public void setB20000661110104(BigDecimal b20000661110104) {
        this.b20000661110104 = b20000661110104;
    }

    public BigDecimal getB2000066111010401() {
        return b2000066111010401;
    }

    public void setB2000066111010401(BigDecimal b2000066111010401) {
        this.b2000066111010401 = b2000066111010401;
    }

    public BigDecimal getB2000066111010402() {
        return b2000066111010402;
    }

    public void setB2000066111010402(BigDecimal b2000066111010402) {
        this.b2000066111010402 = b2000066111010402;
    }

    public BigDecimal getB20000661110105() {
        return b20000661110105;
    }

    public void setB20000661110105(BigDecimal b20000661110105) {
        this.b20000661110105 = b20000661110105;
    }

    public BigDecimal getB2000066111010501() {
        return b2000066111010501;
    }

    public void setB2000066111010501(BigDecimal b2000066111010501) {
        this.b2000066111010501 = b2000066111010501;
    }

    public BigDecimal getB2000066111010502() {
        return b2000066111010502;
    }

    public void setB2000066111010502(BigDecimal b2000066111010502) {
        this.b2000066111010502 = b2000066111010502;
    }

    public BigDecimal getB20000661110106() {
        return b20000661110106;
    }

    public void setB20000661110106(BigDecimal b20000661110106) {
        this.b20000661110106 = b20000661110106;
    }

    public BigDecimal getB2000066111010601() {
        return b2000066111010601;
    }

    public void setB2000066111010601(BigDecimal b2000066111010601) {
        this.b2000066111010601 = b2000066111010601;
    }

    public BigDecimal getB2000066111010602() {
        return b2000066111010602;
    }

    public void setB2000066111010602(BigDecimal b2000066111010602) {
        this.b2000066111010602 = b2000066111010602;
    }

    public BigDecimal getB20000661110107() {
        return b20000661110107;
    }

    public void setB20000661110107(BigDecimal b20000661110107) {
        this.b20000661110107 = b20000661110107;
    }

    public BigDecimal getB2000066111010701() {
        return b2000066111010701;
    }

    public void setB2000066111010701(BigDecimal b2000066111010701) {
        this.b2000066111010701 = b2000066111010701;
    }

    public BigDecimal getB2000066111010702() {
        return b2000066111010702;
    }

    public void setB2000066111010702(BigDecimal b2000066111010702) {
        this.b2000066111010702 = b2000066111010702;
    }

    public BigDecimal getB20000661110108() {
        return b20000661110108;
    }

    public void setB20000661110108(BigDecimal b20000661110108) {
        this.b20000661110108 = b20000661110108;
    }

    public BigDecimal getB2000066111010801() {
        return b2000066111010801;
    }

    public void setB2000066111010801(BigDecimal b2000066111010801) {
        this.b2000066111010801 = b2000066111010801;
    }

    public BigDecimal getB2000066111010802() {
        return b2000066111010802;
    }

    public void setB2000066111010802(BigDecimal b2000066111010802) {
        this.b2000066111010802 = b2000066111010802;
    }

    public BigDecimal getB200006611102() {
        return b200006611102;
    }

    public void setB200006611102(BigDecimal b200006611102) {
        this.b200006611102 = b200006611102;
    }

    public BigDecimal getB20000661110201() {
        return b20000661110201;
    }

    public void setB20000661110201(BigDecimal b20000661110201) {
        this.b20000661110201 = b20000661110201;
    }

    public BigDecimal getB2000066111020101() {
        return b2000066111020101;
    }

    public void setB2000066111020101(BigDecimal b2000066111020101) {
        this.b2000066111020101 = b2000066111020101;
    }

    public BigDecimal getB2000066111020102() {
        return b2000066111020102;
    }

    public void setB2000066111020102(BigDecimal b2000066111020102) {
        this.b2000066111020102 = b2000066111020102;
    }

    public BigDecimal getB20000661110202() {
        return b20000661110202;
    }

    public void setB20000661110202(BigDecimal b20000661110202) {
        this.b20000661110202 = b20000661110202;
    }

    public BigDecimal getB2000066111020201() {
        return b2000066111020201;
    }

    public void setB2000066111020201(BigDecimal b2000066111020201) {
        this.b2000066111020201 = b2000066111020201;
    }

    public BigDecimal getB2000066111020202() {
        return b2000066111020202;
    }

    public void setB2000066111020202(BigDecimal b2000066111020202) {
        this.b2000066111020202 = b2000066111020202;
    }

    public BigDecimal getB20000661110203() {
        return b20000661110203;
    }

    public void setB20000661110203(BigDecimal b20000661110203) {
        this.b20000661110203 = b20000661110203;
    }

    public BigDecimal getB2000066111020301() {
        return b2000066111020301;
    }

    public void setB2000066111020301(BigDecimal b2000066111020301) {
        this.b2000066111020301 = b2000066111020301;
    }

    public BigDecimal getB2000066111020302() {
        return b2000066111020302;
    }

    public void setB2000066111020302(BigDecimal b2000066111020302) {
        this.b2000066111020302 = b2000066111020302;
    }

    public BigDecimal getB20000661110204() {
        return b20000661110204;
    }

    public void setB20000661110204(BigDecimal b20000661110204) {
        this.b20000661110204 = b20000661110204;
    }

    public BigDecimal getB2000066111020401() {
        return b2000066111020401;
    }

    public void setB2000066111020401(BigDecimal b2000066111020401) {
        this.b2000066111020401 = b2000066111020401;
    }

    public BigDecimal getB2000066111020402() {
        return b2000066111020402;
    }

    public void setB2000066111020402(BigDecimal b2000066111020402) {
        this.b2000066111020402 = b2000066111020402;
    }

    public BigDecimal getB20000661110205() {
        return b20000661110205;
    }

    public void setB20000661110205(BigDecimal b20000661110205) {
        this.b20000661110205 = b20000661110205;
    }

    public BigDecimal getB2000066111020501() {
        return b2000066111020501;
    }

    public void setB2000066111020501(BigDecimal b2000066111020501) {
        this.b2000066111020501 = b2000066111020501;
    }

    public BigDecimal getB2000066111020502() {
        return b2000066111020502;
    }

    public void setB2000066111020502(BigDecimal b2000066111020502) {
        this.b2000066111020502 = b2000066111020502;
    }

    public BigDecimal getB20000661110206() {
        return b20000661110206;
    }

    public void setB20000661110206(BigDecimal b20000661110206) {
        this.b20000661110206 = b20000661110206;
    }

    public BigDecimal getB2000066111020601() {
        return b2000066111020601;
    }

    public void setB2000066111020601(BigDecimal b2000066111020601) {
        this.b2000066111020601 = b2000066111020601;
    }

    public BigDecimal getB2000066111020602() {
        return b2000066111020602;
    }

    public void setB2000066111020602(BigDecimal b2000066111020602) {
        this.b2000066111020602 = b2000066111020602;
    }

    public BigDecimal getB20000661110207() {
        return b20000661110207;
    }

    public void setB20000661110207(BigDecimal b20000661110207) {
        this.b20000661110207 = b20000661110207;
    }

    public BigDecimal getB2000066111020701() {
        return b2000066111020701;
    }

    public void setB2000066111020701(BigDecimal b2000066111020701) {
        this.b2000066111020701 = b2000066111020701;
    }

    public BigDecimal getB2000066111020702() {
        return b2000066111020702;
    }

    public void setB2000066111020702(BigDecimal b2000066111020702) {
        this.b2000066111020702 = b2000066111020702;
    }

    public BigDecimal getB20000661110208() {
        return b20000661110208;
    }

    public void setB20000661110208(BigDecimal b20000661110208) {
        this.b20000661110208 = b20000661110208;
    }

    public BigDecimal getB2000066111020801() {
        return b2000066111020801;
    }

    public void setB2000066111020801(BigDecimal b2000066111020801) {
        this.b2000066111020801 = b2000066111020801;
    }

    public BigDecimal getB2000066111020802() {
        return b2000066111020802;
    }

    public void setB2000066111020802(BigDecimal b2000066111020802) {
        this.b2000066111020802 = b2000066111020802;
    }

    public BigDecimal getB200006611103() {
        return b200006611103;
    }

    public void setB200006611103(BigDecimal b200006611103) {
        this.b200006611103 = b200006611103;
    }

    public BigDecimal getB20000661110301() {
        return b20000661110301;
    }

    public void setB20000661110301(BigDecimal b20000661110301) {
        this.b20000661110301 = b20000661110301;
    }

    public BigDecimal getB2000066111030101() {
        return b2000066111030101;
    }

    public void setB2000066111030101(BigDecimal b2000066111030101) {
        this.b2000066111030101 = b2000066111030101;
    }

    public BigDecimal getB2000066111030102() {
        return b2000066111030102;
    }

    public void setB2000066111030102(BigDecimal b2000066111030102) {
        this.b2000066111030102 = b2000066111030102;
    }

    public BigDecimal getB20000661110302() {
        return b20000661110302;
    }

    public void setB20000661110302(BigDecimal b20000661110302) {
        this.b20000661110302 = b20000661110302;
    }

    public BigDecimal getB2000066111030201() {
        return b2000066111030201;
    }

    public void setB2000066111030201(BigDecimal b2000066111030201) {
        this.b2000066111030201 = b2000066111030201;
    }

    public BigDecimal getB2000066111030202() {
        return b2000066111030202;
    }

    public void setB2000066111030202(BigDecimal b2000066111030202) {
        this.b2000066111030202 = b2000066111030202;
    }

    public BigDecimal getB20000661110303() {
        return b20000661110303;
    }

    public void setB20000661110303(BigDecimal b20000661110303) {
        this.b20000661110303 = b20000661110303;
    }

    public BigDecimal getB2000066111030301() {
        return b2000066111030301;
    }

    public void setB2000066111030301(BigDecimal b2000066111030301) {
        this.b2000066111030301 = b2000066111030301;
    }

    public BigDecimal getB2000066111030302() {
        return b2000066111030302;
    }

    public void setB2000066111030302(BigDecimal b2000066111030302) {
        this.b2000066111030302 = b2000066111030302;
    }

    public BigDecimal getB20000661110304() {
        return b20000661110304;
    }

    public void setB20000661110304(BigDecimal b20000661110304) {
        this.b20000661110304 = b20000661110304;
    }

    public BigDecimal getB2000066111030401() {
        return b2000066111030401;
    }

    public void setB2000066111030401(BigDecimal b2000066111030401) {
        this.b2000066111030401 = b2000066111030401;
    }

    public BigDecimal getB2000066111030402() {
        return b2000066111030402;
    }

    public void setB2000066111030402(BigDecimal b2000066111030402) {
        this.b2000066111030402 = b2000066111030402;
    }

    public BigDecimal getB20000661110305() {
        return b20000661110305;
    }

    public void setB20000661110305(BigDecimal b20000661110305) {
        this.b20000661110305 = b20000661110305;
    }

    public BigDecimal getB2000066111030501() {
        return b2000066111030501;
    }

    public void setB2000066111030501(BigDecimal b2000066111030501) {
        this.b2000066111030501 = b2000066111030501;
    }

    public BigDecimal getB2000066111030502() {
        return b2000066111030502;
    }

    public void setB2000066111030502(BigDecimal b2000066111030502) {
        this.b2000066111030502 = b2000066111030502;
    }

    public BigDecimal getB200006611104() {
        return b200006611104;
    }

    public void setB200006611104(BigDecimal b200006611104) {
        this.b200006611104 = b200006611104;
    }

    public BigDecimal getB20000661110401() {
        return b20000661110401;
    }

    public void setB20000661110401(BigDecimal b20000661110401) {
        this.b20000661110401 = b20000661110401;
    }

    public BigDecimal getB20000661110402() {
        return b20000661110402;
    }

    public void setB20000661110402(BigDecimal b20000661110402) {
        this.b20000661110402 = b20000661110402;
    }

    public BigDecimal getB200006611105() {
        return b200006611105;
    }

    public void setB200006611105(BigDecimal b200006611105) {
        this.b200006611105 = b200006611105;
    }

    public BigDecimal getB20000661110501() {
        return b20000661110501;
    }

    public void setB20000661110501(BigDecimal b20000661110501) {
        this.b20000661110501 = b20000661110501;
    }

    public BigDecimal getB20000661110502() {
        return b20000661110502;
    }

    public void setB20000661110502(BigDecimal b20000661110502) {
        this.b20000661110502 = b20000661110502;
    }

    public BigDecimal getB200006611106() {
        return b200006611106;
    }

    public void setB200006611106(BigDecimal b200006611106) {
        this.b200006611106 = b200006611106;
    }

    public BigDecimal getB20000661110601() {
        return b20000661110601;
    }

    public void setB20000661110601(BigDecimal b20000661110601) {
        this.b20000661110601 = b20000661110601;
    }

    public BigDecimal getB20000661110602() {
        return b20000661110602;
    }

    public void setB20000661110602(BigDecimal b20000661110602) {
        this.b20000661110602 = b20000661110602;
    }

    public BigDecimal getB200006611107() {
        return b200006611107;
    }

    public void setB200006611107(BigDecimal b200006611107) {
        this.b200006611107 = b200006611107;
    }

    public BigDecimal getB20000661110701() {
        return b20000661110701;
    }

    public void setB20000661110701(BigDecimal b20000661110701) {
        this.b20000661110701 = b20000661110701;
    }

    public BigDecimal getB2000066111070101() {
        return b2000066111070101;
    }

    public void setB2000066111070101(BigDecimal b2000066111070101) {
        this.b2000066111070101 = b2000066111070101;
    }

    public BigDecimal getB2000066111070102() {
        return b2000066111070102;
    }

    public void setB2000066111070102(BigDecimal b2000066111070102) {
        this.b2000066111070102 = b2000066111070102;
    }

    public BigDecimal getB20000661110702() {
        return b20000661110702;
    }

    public void setB20000661110702(BigDecimal b20000661110702) {
        this.b20000661110702 = b20000661110702;
    }

    public BigDecimal getB2000066111070201() {
        return b2000066111070201;
    }

    public void setB2000066111070201(BigDecimal b2000066111070201) {
        this.b2000066111070201 = b2000066111070201;
    }

    public BigDecimal getB2000066111070202() {
        return b2000066111070202;
    }

    public void setB2000066111070202(BigDecimal b2000066111070202) {
        this.b2000066111070202 = b2000066111070202;
    }

    public BigDecimal getB20000661110703() {
        return b20000661110703;
    }

    public void setB20000661110703(BigDecimal b20000661110703) {
        this.b20000661110703 = b20000661110703;
    }

    public BigDecimal getB2000066111070301() {
        return b2000066111070301;
    }

    public void setB2000066111070301(BigDecimal b2000066111070301) {
        this.b2000066111070301 = b2000066111070301;
    }

    public BigDecimal getB2000066111070302() {
        return b2000066111070302;
    }

    public void setB2000066111070302(BigDecimal b2000066111070302) {
        this.b2000066111070302 = b2000066111070302;
    }

    public BigDecimal getB20000661110704() {
        return b20000661110704;
    }

    public void setB20000661110704(BigDecimal b20000661110704) {
        this.b20000661110704 = b20000661110704;
    }

    public BigDecimal getB2000066111070401() {
        return b2000066111070401;
    }

    public void setB2000066111070401(BigDecimal b2000066111070401) {
        this.b2000066111070401 = b2000066111070401;
    }

    public BigDecimal getB2000066111070402() {
        return b2000066111070402;
    }

    public void setB2000066111070402(BigDecimal b2000066111070402) {
        this.b2000066111070402 = b2000066111070402;
    }

    public BigDecimal getB20000661110705() {
        return b20000661110705;
    }

    public void setB20000661110705(BigDecimal b20000661110705) {
        this.b20000661110705 = b20000661110705;
    }

    public BigDecimal getB2000066111070501() {
        return b2000066111070501;
    }

    public void setB2000066111070501(BigDecimal b2000066111070501) {
        this.b2000066111070501 = b2000066111070501;
    }

    public BigDecimal getB2000066111070502() {
        return b2000066111070502;
    }

    public void setB2000066111070502(BigDecimal b2000066111070502) {
        this.b2000066111070502 = b2000066111070502;
    }

    public BigDecimal getB200006611108() {
        return b200006611108;
    }

    public void setB200006611108(BigDecimal b200006611108) {
        this.b200006611108 = b200006611108;
    }

    public BigDecimal getB20000661110801() {
        return b20000661110801;
    }

    public void setB20000661110801(BigDecimal b20000661110801) {
        this.b20000661110801 = b20000661110801;
    }

    public BigDecimal getB2000066111080101() {
        return b2000066111080101;
    }

    public void setB2000066111080101(BigDecimal b2000066111080101) {
        this.b2000066111080101 = b2000066111080101;
    }

    public BigDecimal getB2000066111080102() {
        return b2000066111080102;
    }

    public void setB2000066111080102(BigDecimal b2000066111080102) {
        this.b2000066111080102 = b2000066111080102;
    }

    public BigDecimal getB20000661110802() {
        return b20000661110802;
    }

    public void setB20000661110802(BigDecimal b20000661110802) {
        this.b20000661110802 = b20000661110802;
    }

    public BigDecimal getB2000066111080201() {
        return b2000066111080201;
    }

    public void setB2000066111080201(BigDecimal b2000066111080201) {
        this.b2000066111080201 = b2000066111080201;
    }

    public BigDecimal getB2000066111080202() {
        return b2000066111080202;
    }

    public void setB2000066111080202(BigDecimal b2000066111080202) {
        this.b2000066111080202 = b2000066111080202;
    }

    public BigDecimal getB20000661110803() {
        return b20000661110803;
    }

    public void setB20000661110803(BigDecimal b20000661110803) {
        this.b20000661110803 = b20000661110803;
    }

    public BigDecimal getB2000066111080301() {
        return b2000066111080301;
    }

    public void setB2000066111080301(BigDecimal b2000066111080301) {
        this.b2000066111080301 = b2000066111080301;
    }

    public BigDecimal getB2000066111080302() {
        return b2000066111080302;
    }

    public void setB2000066111080302(BigDecimal b2000066111080302) {
        this.b2000066111080302 = b2000066111080302;
    }

    public BigDecimal getB20000661110804() {
        return b20000661110804;
    }

    public void setB20000661110804(BigDecimal b20000661110804) {
        this.b20000661110804 = b20000661110804;
    }

    public BigDecimal getB2000066111080401() {
        return b2000066111080401;
    }

    public void setB2000066111080401(BigDecimal b2000066111080401) {
        this.b2000066111080401 = b2000066111080401;
    }

    public BigDecimal getB2000066111080402() {
        return b2000066111080402;
    }

    public void setB2000066111080402(BigDecimal b2000066111080402) {
        this.b2000066111080402 = b2000066111080402;
    }

    public BigDecimal getB20000661110805() {
        return b20000661110805;
    }

    public void setB20000661110805(BigDecimal b20000661110805) {
        this.b20000661110805 = b20000661110805;
    }

    public BigDecimal getB2000066111080501() {
        return b2000066111080501;
    }

    public void setB2000066111080501(BigDecimal b2000066111080501) {
        this.b2000066111080501 = b2000066111080501;
    }

    public BigDecimal getB2000066111080502() {
        return b2000066111080502;
    }

    public void setB2000066111080502(BigDecimal b2000066111080502) {
        this.b2000066111080502 = b2000066111080502;
    }

    public BigDecimal getB20000661110806() {
        return b20000661110806;
    }

    public void setB20000661110806(BigDecimal b20000661110806) {
        this.b20000661110806 = b20000661110806;
    }

    public BigDecimal getB2000066111080601() {
        return b2000066111080601;
    }

    public void setB2000066111080601(BigDecimal b2000066111080601) {
        this.b2000066111080601 = b2000066111080601;
    }

    public BigDecimal getB2000066111080602() {
        return b2000066111080602;
    }

    public void setB2000066111080602(BigDecimal b2000066111080602) {
        this.b2000066111080602 = b2000066111080602;
    }

    public BigDecimal getB20000661110807() {
        return b20000661110807;
    }

    public void setB20000661110807(BigDecimal b20000661110807) {
        this.b20000661110807 = b20000661110807;
    }

    public BigDecimal getB2000066111080701() {
        return b2000066111080701;
    }

    public void setB2000066111080701(BigDecimal b2000066111080701) {
        this.b2000066111080701 = b2000066111080701;
    }

    public BigDecimal getB2000066111080702() {
        return b2000066111080702;
    }

    public void setB2000066111080702(BigDecimal b2000066111080702) {
        this.b2000066111080702 = b2000066111080702;
    }

    public BigDecimal getB20000661110808() {
        return b20000661110808;
    }

    public void setB20000661110808(BigDecimal b20000661110808) {
        this.b20000661110808 = b20000661110808;
    }

    public BigDecimal getB2000066111080801() {
        return b2000066111080801;
    }

    public void setB2000066111080801(BigDecimal b2000066111080801) {
        this.b2000066111080801 = b2000066111080801;
    }

    public BigDecimal getB2000066111080802() {
        return b2000066111080802;
    }

    public void setB2000066111080802(BigDecimal b2000066111080802) {
        this.b2000066111080802 = b2000066111080802;
    }

    public BigDecimal getB2000066115() {
        return b2000066115;
    }

    public void setB2000066115(BigDecimal b2000066115) {
        this.b2000066115 = b2000066115;
    }

    public BigDecimal getB200006611501() {
        return b200006611501;
    }

    public void setB200006611501(BigDecimal b200006611501) {
        this.b200006611501 = b200006611501;
    }

    public BigDecimal getB200006611502() {
        return b200006611502;
    }

    public void setB200006611502(BigDecimal b200006611502) {
        this.b200006611502 = b200006611502;
    }

    public BigDecimal getB200006611503() {
        return b200006611503;
    }

    public void setB200006611503(BigDecimal b200006611503) {
        this.b200006611503 = b200006611503;
    }

    public BigDecimal getB200006611504() {
        return b200006611504;
    }

    public void setB200006611504(BigDecimal b200006611504) {
        this.b200006611504 = b200006611504;
    }

    public BigDecimal getB200006611505() {
        return b200006611505;
    }

    public void setB200006611505(BigDecimal b200006611505) {
        this.b200006611505 = b200006611505;
    }

    public BigDecimal getB200006611506() {
        return b200006611506;
    }

    public void setB200006611506(BigDecimal b200006611506) {
        this.b200006611506 = b200006611506;
    }

    public BigDecimal getB200006611507() {
        return b200006611507;
    }

    public void setB200006611507(BigDecimal b200006611507) {
        this.b200006611507 = b200006611507;
    }

    public BigDecimal getB2000066117() {
        return b2000066117;
    }

    public void setB2000066117(BigDecimal b2000066117) {
        this.b2000066117 = b2000066117;
    }

    public BigDecimal getB2000066201() {
        return b2000066201;
    }

    public void setB2000066201(BigDecimal b2000066201) {
        this.b2000066201 = b2000066201;
    }

    public BigDecimal getB2000066202() {
        return b2000066202;
    }

    public void setB2000066202(BigDecimal b2000066202) {
        this.b2000066202 = b2000066202;
    }

    public BigDecimal getB2000066203() {
        return b2000066203;
    }

    public void setB2000066203(BigDecimal b2000066203) {
        this.b2000066203 = b2000066203;
    }

    public BigDecimal getB2000066301() {
        return b2000066301;
    }

    public void setB2000066301(BigDecimal b2000066301) {
        this.b2000066301 = b2000066301;
    }

    public BigDecimal getB200006630101() {
        return b200006630101;
    }

    public void setB200006630101(BigDecimal b200006630101) {
        this.b200006630101 = b200006630101;
    }

    public BigDecimal getB200006630102() {
        return b200006630102;
    }

    public void setB200006630102(BigDecimal b200006630102) {
        this.b200006630102 = b200006630102;
    }

    public BigDecimal getB200006630103() {
        return b200006630103;
    }

    public void setB200006630103(BigDecimal b200006630103) {
        this.b200006630103 = b200006630103;
    }

    public BigDecimal getB200006630104() {
        return b200006630104;
    }

    public void setB200006630104(BigDecimal b200006630104) {
        this.b200006630104 = b200006630104;
    }

    public BigDecimal getB200006630105() {
        return b200006630105;
    }

    public void setB200006630105(BigDecimal b200006630105) {
        this.b200006630105 = b200006630105;
    }

    public BigDecimal getB200006630106() {
        return b200006630106;
    }

    public void setB200006630106(BigDecimal b200006630106) {
        this.b200006630106 = b200006630106;
    }

    public BigDecimal getB200006630107() {
        return b200006630107;
    }

    public void setB200006630107(BigDecimal b200006630107) {
        this.b200006630107 = b200006630107;
    }

    public BigDecimal getB200006630108() {
        return b200006630108;
    }

    public void setB200006630108(BigDecimal b200006630108) {
        this.b200006630108 = b200006630108;
    }

    public BigDecimal getB200006630109() {
        return b200006630109;
    }

    public void setB200006630109(BigDecimal b200006630109) {
        this.b200006630109 = b200006630109;
    }

    public BigDecimal getB2000066401() {
        return b2000066401;
    }

    public void setB2000066401(BigDecimal b2000066401) {
        this.b2000066401 = b2000066401;
    }

    public BigDecimal getB200006640101() {
        return b200006640101;
    }

    public void setB200006640101(BigDecimal b200006640101) {
        this.b200006640101 = b200006640101;
    }

    public BigDecimal getB200006640102() {
        return b200006640102;
    }

    public void setB200006640102(BigDecimal b200006640102) {
        this.b200006640102 = b200006640102;
    }

    public BigDecimal getB200006640103() {
        return b200006640103;
    }

    public void setB200006640103(BigDecimal b200006640103) {
        this.b200006640103 = b200006640103;
    }

    public BigDecimal getB200006640104() {
        return b200006640104;
    }

    public void setB200006640104(BigDecimal b200006640104) {
        this.b200006640104 = b200006640104;
    }

    public BigDecimal getB200006640105() {
        return b200006640105;
    }

    public void setB200006640105(BigDecimal b200006640105) {
        this.b200006640105 = b200006640105;
    }

    public BigDecimal getB200006640106() {
        return b200006640106;
    }

    public void setB200006640106(BigDecimal b200006640106) {
        this.b200006640106 = b200006640106;
    }

    public BigDecimal getB200006640107() {
        return b200006640107;
    }

    public void setB200006640107(BigDecimal b200006640107) {
        this.b200006640107 = b200006640107;
    }

    public BigDecimal getB200006640108() {
        return b200006640108;
    }

    public void setB200006640108(BigDecimal b200006640108) {
        this.b200006640108 = b200006640108;
    }

    public BigDecimal getB200006640109() {
        return b200006640109;
    }

    public void setB200006640109(BigDecimal b200006640109) {
        this.b200006640109 = b200006640109;
    }

    public BigDecimal getB200006640110() {
        return b200006640110;
    }

    public void setB200006640110(BigDecimal b200006640110) {
        this.b200006640110 = b200006640110;
    }

    public BigDecimal getB200006640111() {
        return b200006640111;
    }

    public void setB200006640111(BigDecimal b200006640111) {
        this.b200006640111 = b200006640111;
    }

    public BigDecimal getB200006640112() {
        return b200006640112;
    }

    public void setB200006640112(BigDecimal b200006640112) {
        this.b200006640112 = b200006640112;
    }

    public BigDecimal getB200006640113() {
        return b200006640113;
    }

    public void setB200006640113(BigDecimal b200006640113) {
        this.b200006640113 = b200006640113;
    }

    public BigDecimal getB200006640114() {
        return b200006640114;
    }

    public void setB200006640114(BigDecimal b200006640114) {
        this.b200006640114 = b200006640114;
    }

    public BigDecimal getB200006640115() {
        return b200006640115;
    }

    public void setB200006640115(BigDecimal b200006640115) {
        this.b200006640115 = b200006640115;
    }

    public BigDecimal getB200006640116() {
        return b200006640116;
    }

    public void setB200006640116(BigDecimal b200006640116) {
        this.b200006640116 = b200006640116;
    }

    public BigDecimal getB200006640117() {
        return b200006640117;
    }

    public void setB200006640117(BigDecimal b200006640117) {
        this.b200006640117 = b200006640117;
    }

    public BigDecimal getB200006640118() {
        return b200006640118;
    }

    public void setB200006640118(BigDecimal b200006640118) {
        this.b200006640118 = b200006640118;
    }

    public BigDecimal getB200006640119() {
        return b200006640119;
    }

    public void setB200006640119(BigDecimal b200006640119) {
        this.b200006640119 = b200006640119;
    }

    public BigDecimal getB2000066402() {
        return b2000066402;
    }

    public void setB2000066402(BigDecimal b2000066402) {
        this.b2000066402 = b2000066402;
    }

    public BigDecimal getB200006640201() {
        return b200006640201;
    }

    public void setB200006640201(BigDecimal b200006640201) {
        this.b200006640201 = b200006640201;
    }

    public BigDecimal getB200006640202() {
        return b200006640202;
    }

    public void setB200006640202(BigDecimal b200006640202) {
        this.b200006640202 = b200006640202;
    }

    public BigDecimal getB200006640203() {
        return b200006640203;
    }

    public void setB200006640203(BigDecimal b200006640203) {
        this.b200006640203 = b200006640203;
    }

    public BigDecimal getB200006640204() {
        return b200006640204;
    }

    public void setB200006640204(BigDecimal b200006640204) {
        this.b200006640204 = b200006640204;
    }

    public BigDecimal getB200006640205() {
        return b200006640205;
    }

    public void setB200006640205(BigDecimal b200006640205) {
        this.b200006640205 = b200006640205;
    }

    public BigDecimal getB2000066403() {
        return b2000066403;
    }

    public void setB2000066403(BigDecimal b2000066403) {
        this.b2000066403 = b2000066403;
    }

    public BigDecimal getB2000066411() {
        return b2000066411;
    }

    public void setB2000066411(BigDecimal b2000066411) {
        this.b2000066411 = b2000066411;
    }

    public BigDecimal getB2000066421() {
        return b2000066421;
    }

    public void setB2000066421(BigDecimal b2000066421) {
        this.b2000066421 = b2000066421;
    }

    public BigDecimal getB2000066501() {
        return b2000066501;
    }

    public void setB2000066501(BigDecimal b2000066501) {
        this.b2000066501 = b2000066501;
    }

    public BigDecimal getB2000066502() {
        return b2000066502;
    }

    public void setB2000066502(BigDecimal b2000066502) {
        this.b2000066502 = b2000066502;
    }

    public BigDecimal getB2000066511() {
        return b2000066511;
    }

    public void setB2000066511(BigDecimal b2000066511) {
        this.b2000066511 = b2000066511;
    }

    public BigDecimal getB2000066521() {
        return b2000066521;
    }

    public void setB2000066521(BigDecimal b2000066521) {
        this.b2000066521 = b2000066521;
    }

    public BigDecimal getB2000066531() {
        return b2000066531;
    }

    public void setB2000066531(BigDecimal b2000066531) {
        this.b2000066531 = b2000066531;
    }

    public BigDecimal getB2000066541() {
        return b2000066541;
    }

    public void setB2000066541(BigDecimal b2000066541) {
        this.b2000066541 = b2000066541;
    }

    public BigDecimal getB2000066542() {
        return b2000066542;
    }

    public void setB2000066542(BigDecimal b2000066542) {
        this.b2000066542 = b2000066542;
    }

    public BigDecimal getB2000066601() {
        return b2000066601;
    }

    public void setB2000066601(BigDecimal b2000066601) {
        this.b2000066601 = b2000066601;
    }

    public BigDecimal getB200006660101() {
        return b200006660101;
    }

    public void setB200006660101(BigDecimal b200006660101) {
        this.b200006660101 = b200006660101;
    }

    public BigDecimal getB200006660102() {
        return b200006660102;
    }

    public void setB200006660102(BigDecimal b200006660102) {
        this.b200006660102 = b200006660102;
    }

    public BigDecimal getB200006660103() {
        return b200006660103;
    }

    public void setB200006660103(BigDecimal b200006660103) {
        this.b200006660103 = b200006660103;
    }

    public BigDecimal getB200006660104() {
        return b200006660104;
    }

    public void setB200006660104(BigDecimal b200006660104) {
        this.b200006660104 = b200006660104;
    }

    public BigDecimal getB200006660105() {
        return b200006660105;
    }

    public void setB200006660105(BigDecimal b200006660105) {
        this.b200006660105 = b200006660105;
    }

    public BigDecimal getB200006660106() {
        return b200006660106;
    }

    public void setB200006660106(BigDecimal b200006660106) {
        this.b200006660106 = b200006660106;
    }

    public BigDecimal getB200006660107() {
        return b200006660107;
    }

    public void setB200006660107(BigDecimal b200006660107) {
        this.b200006660107 = b200006660107;
    }

    public BigDecimal getB200006660108() {
        return b200006660108;
    }

    public void setB200006660108(BigDecimal b200006660108) {
        this.b200006660108 = b200006660108;
    }

    public BigDecimal getB200006660109() {
        return b200006660109;
    }

    public void setB200006660109(BigDecimal b200006660109) {
        this.b200006660109 = b200006660109;
    }

    public BigDecimal getB200006660110() {
        return b200006660110;
    }

    public void setB200006660110(BigDecimal b200006660110) {
        this.b200006660110 = b200006660110;
    }

    public BigDecimal getB200006660111() {
        return b200006660111;
    }

    public void setB200006660111(BigDecimal b200006660111) {
        this.b200006660111 = b200006660111;
    }

    public BigDecimal getB200006660112() {
        return b200006660112;
    }

    public void setB200006660112(BigDecimal b200006660112) {
        this.b200006660112 = b200006660112;
    }

    public BigDecimal getB200006660113() {
        return b200006660113;
    }

    public void setB200006660113(BigDecimal b200006660113) {
        this.b200006660113 = b200006660113;
    }

    public BigDecimal getB200006660114() {
        return b200006660114;
    }

    public void setB200006660114(BigDecimal b200006660114) {
        this.b200006660114 = b200006660114;
    }

    public BigDecimal getB200006660115() {
        return b200006660115;
    }

    public void setB200006660115(BigDecimal b200006660115) {
        this.b200006660115 = b200006660115;
    }

    public BigDecimal getB200006660116() {
        return b200006660116;
    }

    public void setB200006660116(BigDecimal b200006660116) {
        this.b200006660116 = b200006660116;
    }

    public BigDecimal getB200006660117() {
        return b200006660117;
    }

    public void setB200006660117(BigDecimal b200006660117) {
        this.b200006660117 = b200006660117;
    }

    public BigDecimal getB200006660118() {
        return b200006660118;
    }

    public void setB200006660118(BigDecimal b200006660118) {
        this.b200006660118 = b200006660118;
    }

    public BigDecimal getB200006660119() {
        return b200006660119;
    }

    public void setB200006660119(BigDecimal b200006660119) {
        this.b200006660119 = b200006660119;
    }

    public BigDecimal getB200006660120() {
        return b200006660120;
    }

    public void setB200006660120(BigDecimal b200006660120) {
        this.b200006660120 = b200006660120;
    }

    public BigDecimal getB200006660121() {
        return b200006660121;
    }

    public void setB200006660121(BigDecimal b200006660121) {
        this.b200006660121 = b200006660121;
    }

    public BigDecimal getB200006660122() {
        return b200006660122;
    }

    public void setB200006660122(BigDecimal b200006660122) {
        this.b200006660122 = b200006660122;
    }

    public BigDecimal getB200006660123() {
        return b200006660123;
    }

    public void setB200006660123(BigDecimal b200006660123) {
        this.b200006660123 = b200006660123;
    }

    public BigDecimal getB200006660124() {
        return b200006660124;
    }

    public void setB200006660124(BigDecimal b200006660124) {
        this.b200006660124 = b200006660124;
    }

    public BigDecimal getB200006660125() {
        return b200006660125;
    }

    public void setB200006660125(BigDecimal b200006660125) {
        this.b200006660125 = b200006660125;
    }

    public BigDecimal getB200006660126() {
        return b200006660126;
    }

    public void setB200006660126(BigDecimal b200006660126) {
        this.b200006660126 = b200006660126;
    }

    public BigDecimal getB200006660127() {
        return b200006660127;
    }

    public void setB200006660127(BigDecimal b200006660127) {
        this.b200006660127 = b200006660127;
    }

    public BigDecimal getB200006660128() {
        return b200006660128;
    }

    public void setB200006660128(BigDecimal b200006660128) {
        this.b200006660128 = b200006660128;
    }

    public BigDecimal getB200006660129() {
        return b200006660129;
    }

    public void setB200006660129(BigDecimal b200006660129) {
        this.b200006660129 = b200006660129;
    }

    public BigDecimal getB200006660130() {
        return b200006660130;
    }

    public void setB200006660130(BigDecimal b200006660130) {
        this.b200006660130 = b200006660130;
    }

    public BigDecimal getB200006660131() {
        return b200006660131;
    }

    public void setB200006660131(BigDecimal b200006660131) {
        this.b200006660131 = b200006660131;
    }

    public BigDecimal getB200006660132() {
        return b200006660132;
    }

    public void setB200006660132(BigDecimal b200006660132) {
        this.b200006660132 = b200006660132;
    }

    public BigDecimal getB200006660133() {
        return b200006660133;
    }

    public void setB200006660133(BigDecimal b200006660133) {
        this.b200006660133 = b200006660133;
    }

    public BigDecimal getB200006660134() {
        return b200006660134;
    }

    public void setB200006660134(BigDecimal b200006660134) {
        this.b200006660134 = b200006660134;
    }

    public BigDecimal getB200006660135() {
        return b200006660135;
    }

    public void setB200006660135(BigDecimal b200006660135) {
        this.b200006660135 = b200006660135;
    }

    public BigDecimal getB200006660136() {
        return b200006660136;
    }

    public void setB200006660136(BigDecimal b200006660136) {
        this.b200006660136 = b200006660136;
    }

    public BigDecimal getB200006660137() {
        return b200006660137;
    }

    public void setB200006660137(BigDecimal b200006660137) {
        this.b200006660137 = b200006660137;
    }

    public BigDecimal getB200006660138() {
        return b200006660138;
    }

    public void setB200006660138(BigDecimal b200006660138) {
        this.b200006660138 = b200006660138;
    }

    public BigDecimal getB200006660139() {
        return b200006660139;
    }

    public void setB200006660139(BigDecimal b200006660139) {
        this.b200006660139 = b200006660139;
    }

    public BigDecimal getB200006660140() {
        return b200006660140;
    }

    public void setB200006660140(BigDecimal b200006660140) {
        this.b200006660140 = b200006660140;
    }

    public BigDecimal getB200006660141() {
        return b200006660141;
    }

    public void setB200006660141(BigDecimal b200006660141) {
        this.b200006660141 = b200006660141;
    }

    public BigDecimal getB200006660142() {
        return b200006660142;
    }

    public void setB200006660142(BigDecimal b200006660142) {
        this.b200006660142 = b200006660142;
    }

    public BigDecimal getB200006660143() {
        return b200006660143;
    }

    public void setB200006660143(BigDecimal b200006660143) {
        this.b200006660143 = b200006660143;
    }

    public BigDecimal getB200006660144() {
        return b200006660144;
    }

    public void setB200006660144(BigDecimal b200006660144) {
        this.b200006660144 = b200006660144;
    }

    public BigDecimal getB200006660145() {
        return b200006660145;
    }

    public void setB200006660145(BigDecimal b200006660145) {
        this.b200006660145 = b200006660145;
    }

    public BigDecimal getB200006660146() {
        return b200006660146;
    }

    public void setB200006660146(BigDecimal b200006660146) {
        this.b200006660146 = b200006660146;
    }

    public BigDecimal getB200006660147() {
        return b200006660147;
    }

    public void setB200006660147(BigDecimal b200006660147) {
        this.b200006660147 = b200006660147;
    }

    public BigDecimal getB200006660148() {
        return b200006660148;
    }

    public void setB200006660148(BigDecimal b200006660148) {
        this.b200006660148 = b200006660148;
    }

    public BigDecimal getB200006660149() {
        return b200006660149;
    }

    public void setB200006660149(BigDecimal b200006660149) {
        this.b200006660149 = b200006660149;
    }

    public BigDecimal getB200006660150() {
        return b200006660150;
    }

    public void setB200006660150(BigDecimal b200006660150) {
        this.b200006660150 = b200006660150;
    }

    public BigDecimal getB200006660151() {
        return b200006660151;
    }

    public void setB200006660151(BigDecimal b200006660151) {
        this.b200006660151 = b200006660151;
    }

    public BigDecimal getB200006660152() {
        return b200006660152;
    }

    public void setB200006660152(BigDecimal b200006660152) {
        this.b200006660152 = b200006660152;
    }

    public BigDecimal getB200006660153() {
        return b200006660153;
    }

    public void setB200006660153(BigDecimal b200006660153) {
        this.b200006660153 = b200006660153;
    }

    public BigDecimal getB2000066602() {
        return b2000066602;
    }

    public void setB2000066602(BigDecimal b2000066602) {
        this.b2000066602 = b2000066602;
    }

    public BigDecimal getB200006660201() {
        return b200006660201;
    }

    public void setB200006660201(BigDecimal b200006660201) {
        this.b200006660201 = b200006660201;
    }

    public BigDecimal getB200006660202() {
        return b200006660202;
    }

    public void setB200006660202(BigDecimal b200006660202) {
        this.b200006660202 = b200006660202;
    }

    public BigDecimal getB200006660203() {
        return b200006660203;
    }

    public void setB200006660203(BigDecimal b200006660203) {
        this.b200006660203 = b200006660203;
    }

    public BigDecimal getB200006660204() {
        return b200006660204;
    }

    public void setB200006660204(BigDecimal b200006660204) {
        this.b200006660204 = b200006660204;
    }

    public BigDecimal getB200006660205() {
        return b200006660205;
    }

    public void setB200006660205(BigDecimal b200006660205) {
        this.b200006660205 = b200006660205;
    }

    public BigDecimal getB200006660206() {
        return b200006660206;
    }

    public void setB200006660206(BigDecimal b200006660206) {
        this.b200006660206 = b200006660206;
    }

    public BigDecimal getB200006660207() {
        return b200006660207;
    }

    public void setB200006660207(BigDecimal b200006660207) {
        this.b200006660207 = b200006660207;
    }

    public BigDecimal getB200006660208() {
        return b200006660208;
    }

    public void setB200006660208(BigDecimal b200006660208) {
        this.b200006660208 = b200006660208;
    }

    public BigDecimal getB200006660209() {
        return b200006660209;
    }

    public void setB200006660209(BigDecimal b200006660209) {
        this.b200006660209 = b200006660209;
    }

    public BigDecimal getB200006660210() {
        return b200006660210;
    }

    public void setB200006660210(BigDecimal b200006660210) {
        this.b200006660210 = b200006660210;
    }

    public BigDecimal getB200006660211() {
        return b200006660211;
    }

    public void setB200006660211(BigDecimal b200006660211) {
        this.b200006660211 = b200006660211;
    }

    public BigDecimal getB200006660212() {
        return b200006660212;
    }

    public void setB200006660212(BigDecimal b200006660212) {
        this.b200006660212 = b200006660212;
    }

    public BigDecimal getB200006660213() {
        return b200006660213;
    }

    public void setB200006660213(BigDecimal b200006660213) {
        this.b200006660213 = b200006660213;
    }

    public BigDecimal getB200006660214() {
        return b200006660214;
    }

    public void setB200006660214(BigDecimal b200006660214) {
        this.b200006660214 = b200006660214;
    }

    public BigDecimal getB200006660215() {
        return b200006660215;
    }

    public void setB200006660215(BigDecimal b200006660215) {
        this.b200006660215 = b200006660215;
    }

    public BigDecimal getB200006660216() {
        return b200006660216;
    }

    public void setB200006660216(BigDecimal b200006660216) {
        this.b200006660216 = b200006660216;
    }

    public BigDecimal getB200006660217() {
        return b200006660217;
    }

    public void setB200006660217(BigDecimal b200006660217) {
        this.b200006660217 = b200006660217;
    }

    public BigDecimal getB200006660218() {
        return b200006660218;
    }

    public void setB200006660218(BigDecimal b200006660218) {
        this.b200006660218 = b200006660218;
    }

    public BigDecimal getB200006660219() {
        return b200006660219;
    }

    public void setB200006660219(BigDecimal b200006660219) {
        this.b200006660219 = b200006660219;
    }

    public BigDecimal getB200006660220() {
        return b200006660220;
    }

    public void setB200006660220(BigDecimal b200006660220) {
        this.b200006660220 = b200006660220;
    }

    public BigDecimal getB200006660221() {
        return b200006660221;
    }

    public void setB200006660221(BigDecimal b200006660221) {
        this.b200006660221 = b200006660221;
    }

    public BigDecimal getB200006660222() {
        return b200006660222;
    }

    public void setB200006660222(BigDecimal b200006660222) {
        this.b200006660222 = b200006660222;
    }

    public BigDecimal getB200006660223() {
        return b200006660223;
    }

    public void setB200006660223(BigDecimal b200006660223) {
        this.b200006660223 = b200006660223;
    }

    public BigDecimal getB200006660224() {
        return b200006660224;
    }

    public void setB200006660224(BigDecimal b200006660224) {
        this.b200006660224 = b200006660224;
    }

    public BigDecimal getB200006660225() {
        return b200006660225;
    }

    public void setB200006660225(BigDecimal b200006660225) {
        this.b200006660225 = b200006660225;
    }

    public BigDecimal getB200006660226() {
        return b200006660226;
    }

    public void setB200006660226(BigDecimal b200006660226) {
        this.b200006660226 = b200006660226;
    }

    public BigDecimal getB200006660227() {
        return b200006660227;
    }

    public void setB200006660227(BigDecimal b200006660227) {
        this.b200006660227 = b200006660227;
    }

    public BigDecimal getB200006660228() {
        return b200006660228;
    }

    public void setB200006660228(BigDecimal b200006660228) {
        this.b200006660228 = b200006660228;
    }

    public BigDecimal getB200006660229() {
        return b200006660229;
    }

    public void setB200006660229(BigDecimal b200006660229) {
        this.b200006660229 = b200006660229;
    }

    public BigDecimal getB200006660230() {
        return b200006660230;
    }

    public void setB200006660230(BigDecimal b200006660230) {
        this.b200006660230 = b200006660230;
    }

    public BigDecimal getB200006660231() {
        return b200006660231;
    }

    public void setB200006660231(BigDecimal b200006660231) {
        this.b200006660231 = b200006660231;
    }

    public BigDecimal getB200006660232() {
        return b200006660232;
    }

    public void setB200006660232(BigDecimal b200006660232) {
        this.b200006660232 = b200006660232;
    }

    public BigDecimal getB200006660233() {
        return b200006660233;
    }

    public void setB200006660233(BigDecimal b200006660233) {
        this.b200006660233 = b200006660233;
    }

    public BigDecimal getB200006660234() {
        return b200006660234;
    }

    public void setB200006660234(BigDecimal b200006660234) {
        this.b200006660234 = b200006660234;
    }

    public BigDecimal getB200006660235() {
        return b200006660235;
    }

    public void setB200006660235(BigDecimal b200006660235) {
        this.b200006660235 = b200006660235;
    }

    public BigDecimal getB200006660236() {
        return b200006660236;
    }

    public void setB200006660236(BigDecimal b200006660236) {
        this.b200006660236 = b200006660236;
    }

    public BigDecimal getB200006660237() {
        return b200006660237;
    }

    public void setB200006660237(BigDecimal b200006660237) {
        this.b200006660237 = b200006660237;
    }

    public BigDecimal getB200006660238() {
        return b200006660238;
    }

    public void setB200006660238(BigDecimal b200006660238) {
        this.b200006660238 = b200006660238;
    }

    public BigDecimal getB200006660239() {
        return b200006660239;
    }

    public void setB200006660239(BigDecimal b200006660239) {
        this.b200006660239 = b200006660239;
    }

    public BigDecimal getB200006660240() {
        return b200006660240;
    }

    public void setB200006660240(BigDecimal b200006660240) {
        this.b200006660240 = b200006660240;
    }

    public BigDecimal getB200006660241() {
        return b200006660241;
    }

    public void setB200006660241(BigDecimal b200006660241) {
        this.b200006660241 = b200006660241;
    }

    public BigDecimal getB200006660242() {
        return b200006660242;
    }

    public void setB200006660242(BigDecimal b200006660242) {
        this.b200006660242 = b200006660242;
    }

    public BigDecimal getB200006660243() {
        return b200006660243;
    }

    public void setB200006660243(BigDecimal b200006660243) {
        this.b200006660243 = b200006660243;
    }

    public BigDecimal getB200006660244() {
        return b200006660244;
    }

    public void setB200006660244(BigDecimal b200006660244) {
        this.b200006660244 = b200006660244;
    }

    public BigDecimal getB200006660245() {
        return b200006660245;
    }

    public void setB200006660245(BigDecimal b200006660245) {
        this.b200006660245 = b200006660245;
    }

    public BigDecimal getB200006660246() {
        return b200006660246;
    }

    public void setB200006660246(BigDecimal b200006660246) {
        this.b200006660246 = b200006660246;
    }

    public BigDecimal getB200006660247() {
        return b200006660247;
    }

    public void setB200006660247(BigDecimal b200006660247) {
        this.b200006660247 = b200006660247;
    }

    public BigDecimal getB200006660248() {
        return b200006660248;
    }

    public void setB200006660248(BigDecimal b200006660248) {
        this.b200006660248 = b200006660248;
    }

    public BigDecimal getB200006660249() {
        return b200006660249;
    }

    public void setB200006660249(BigDecimal b200006660249) {
        this.b200006660249 = b200006660249;
    }

    public BigDecimal getB200006660250() {
        return b200006660250;
    }

    public void setB200006660250(BigDecimal b200006660250) {
        this.b200006660250 = b200006660250;
    }

    public BigDecimal getB200006660251() {
        return b200006660251;
    }

    public void setB200006660251(BigDecimal b200006660251) {
        this.b200006660251 = b200006660251;
    }

    public BigDecimal getB200006660252() {
        return b200006660252;
    }

    public void setB200006660252(BigDecimal b200006660252) {
        this.b200006660252 = b200006660252;
    }

    public BigDecimal getB200006660253() {
        return b200006660253;
    }

    public void setB200006660253(BigDecimal b200006660253) {
        this.b200006660253 = b200006660253;
    }

    public BigDecimal getB200006660254() {
        return b200006660254;
    }

    public void setB200006660254(BigDecimal b200006660254) {
        this.b200006660254 = b200006660254;
    }

    public BigDecimal getB200006660255() {
        return b200006660255;
    }

    public void setB200006660255(BigDecimal b200006660255) {
        this.b200006660255 = b200006660255;
    }

    public BigDecimal getB200006660256() {
        return b200006660256;
    }

    public void setB200006660256(BigDecimal b200006660256) {
        this.b200006660256 = b200006660256;
    }

    public BigDecimal getB200006660257() {
        return b200006660257;
    }

    public void setB200006660257(BigDecimal b200006660257) {
        this.b200006660257 = b200006660257;
    }

    public BigDecimal getB200006660258() {
        return b200006660258;
    }

    public void setB200006660258(BigDecimal b200006660258) {
        this.b200006660258 = b200006660258;
    }

    public BigDecimal getB200006660259() {
        return b200006660259;
    }

    public void setB200006660259(BigDecimal b200006660259) {
        this.b200006660259 = b200006660259;
    }

    public BigDecimal getB200006660260() {
        return b200006660260;
    }

    public void setB200006660260(BigDecimal b200006660260) {
        this.b200006660260 = b200006660260;
    }

    public BigDecimal getB200006660261() {
        return b200006660261;
    }

    public void setB200006660261(BigDecimal b200006660261) {
        this.b200006660261 = b200006660261;
    }

    public BigDecimal getB2000066603() {
        return b2000066603;
    }

    public void setB2000066603(BigDecimal b2000066603) {
        this.b2000066603 = b2000066603;
    }

    public BigDecimal getB200006660301() {
        return b200006660301;
    }

    public void setB200006660301(BigDecimal b200006660301) {
        this.b200006660301 = b200006660301;
    }

    public BigDecimal getB200006660302() {
        return b200006660302;
    }

    public void setB200006660302(BigDecimal b200006660302) {
        this.b200006660302 = b200006660302;
    }

    public BigDecimal getB200006660303() {
        return b200006660303;
    }

    public void setB200006660303(BigDecimal b200006660303) {
        this.b200006660303 = b200006660303;
    }

    public BigDecimal getB200006660304() {
        return b200006660304;
    }

    public void setB200006660304(BigDecimal b200006660304) {
        this.b200006660304 = b200006660304;
    }

    public BigDecimal getB200006660305() {
        return b200006660305;
    }

    public void setB200006660305(BigDecimal b200006660305) {
        this.b200006660305 = b200006660305;
    }

    public BigDecimal getB20000666030501() {
        return b20000666030501;
    }

    public void setB20000666030501(BigDecimal b20000666030501) {
        this.b20000666030501 = b20000666030501;
    }

    public BigDecimal getB20000666030502() {
        return b20000666030502;
    }

    public void setB20000666030502(BigDecimal b20000666030502) {
        this.b20000666030502 = b20000666030502;
    }

    public BigDecimal getB200006660306() {
        return b200006660306;
    }

    public void setB200006660306(BigDecimal b200006660306) {
        this.b200006660306 = b200006660306;
    }

    public BigDecimal getB200006660307() {
        return b200006660307;
    }

    public void setB200006660307(BigDecimal b200006660307) {
        this.b200006660307 = b200006660307;
    }

    public BigDecimal getB200006660308() {
        return b200006660308;
    }

    public void setB200006660308(BigDecimal b200006660308) {
        this.b200006660308 = b200006660308;
    }

    public BigDecimal getB2000066604() {
        return b2000066604;
    }

    public void setB2000066604(BigDecimal b2000066604) {
        this.b2000066604 = b2000066604;
    }

    public BigDecimal getB2000066701() {
        return b2000066701;
    }

    public void setB2000066701(BigDecimal b2000066701) {
        this.b2000066701 = b2000066701;
    }

    public BigDecimal getB2000066702() {
        return b2000066702;
    }

    public void setB2000066702(BigDecimal b2000066702) {
        this.b2000066702 = b2000066702;
    }

    public BigDecimal getB2000066711() {
        return b2000066711;
    }

    public void setB2000066711(BigDecimal b2000066711) {
        this.b2000066711 = b2000066711;
    }

    public BigDecimal getB200006671101() {
        return b200006671101;
    }

    public void setB200006671101(BigDecimal b200006671101) {
        this.b200006671101 = b200006671101;
    }

    public BigDecimal getB20000667110101() {
        return b20000667110101;
    }

    public void setB20000667110101(BigDecimal b20000667110101) {
        this.b20000667110101 = b20000667110101;
    }

    public BigDecimal getB20000667110102() {
        return b20000667110102;
    }

    public void setB20000667110102(BigDecimal b20000667110102) {
        this.b20000667110102 = b20000667110102;
    }

    public BigDecimal getB20000667110103() {
        return b20000667110103;
    }

    public void setB20000667110103(BigDecimal b20000667110103) {
        this.b20000667110103 = b20000667110103;
    }

    public BigDecimal getB20000667110104() {
        return b20000667110104;
    }

    public void setB20000667110104(BigDecimal b20000667110104) {
        this.b20000667110104 = b20000667110104;
    }

    public BigDecimal getB20000667110105() {
        return b20000667110105;
    }

    public void setB20000667110105(BigDecimal b20000667110105) {
        this.b20000667110105 = b20000667110105;
    }

    public BigDecimal getB20000667110106() {
        return b20000667110106;
    }

    public void setB20000667110106(BigDecimal b20000667110106) {
        this.b20000667110106 = b20000667110106;
    }

    public BigDecimal getB20000667110107() {
        return b20000667110107;
    }

    public void setB20000667110107(BigDecimal b20000667110107) {
        this.b20000667110107 = b20000667110107;
    }

    public BigDecimal getB200006671102() {
        return b200006671102;
    }

    public void setB200006671102(BigDecimal b200006671102) {
        this.b200006671102 = b200006671102;
    }

    public BigDecimal getB20000667110201() {
        return b20000667110201;
    }

    public void setB20000667110201(BigDecimal b20000667110201) {
        this.b20000667110201 = b20000667110201;
    }

    public BigDecimal getB20000667110202() {
        return b20000667110202;
    }

    public void setB20000667110202(BigDecimal b20000667110202) {
        this.b20000667110202 = b20000667110202;
    }

    public BigDecimal getB20000667110203() {
        return b20000667110203;
    }

    public void setB20000667110203(BigDecimal b20000667110203) {
        this.b20000667110203 = b20000667110203;
    }

    public BigDecimal getB20000667110204() {
        return b20000667110204;
    }

    public void setB20000667110204(BigDecimal b20000667110204) {
        this.b20000667110204 = b20000667110204;
    }

    public BigDecimal getB20000667110205() {
        return b20000667110205;
    }

    public void setB20000667110205(BigDecimal b20000667110205) {
        this.b20000667110205 = b20000667110205;
    }

    public BigDecimal getB20000667110206() {
        return b20000667110206;
    }

    public void setB20000667110206(BigDecimal b20000667110206) {
        this.b20000667110206 = b20000667110206;
    }

    public BigDecimal getB20000667110207() {
        return b20000667110207;
    }

    public void setB20000667110207(BigDecimal b20000667110207) {
        this.b20000667110207 = b20000667110207;
    }

    public BigDecimal getB200006671103() {
        return b200006671103;
    }

    public void setB200006671103(BigDecimal b200006671103) {
        this.b200006671103 = b200006671103;
    }

    public BigDecimal getB200006671104() {
        return b200006671104;
    }

    public void setB200006671104(BigDecimal b200006671104) {
        this.b200006671104 = b200006671104;
    }

    public BigDecimal getB20000667110401() {
        return b20000667110401;
    }

    public void setB20000667110401(BigDecimal b20000667110401) {
        this.b20000667110401 = b20000667110401;
    }

    public BigDecimal getB20000667110402() {
        return b20000667110402;
    }

    public void setB20000667110402(BigDecimal b20000667110402) {
        this.b20000667110402 = b20000667110402;
    }

    public BigDecimal getB20000667110403() {
        return b20000667110403;
    }

    public void setB20000667110403(BigDecimal b20000667110403) {
        this.b20000667110403 = b20000667110403;
    }

    public BigDecimal getB20000667110404() {
        return b20000667110404;
    }

    public void setB20000667110404(BigDecimal b20000667110404) {
        this.b20000667110404 = b20000667110404;
    }

    public BigDecimal getB20000667110405() {
        return b20000667110405;
    }

    public void setB20000667110405(BigDecimal b20000667110405) {
        this.b20000667110405 = b20000667110405;
    }

    public BigDecimal getB20000667110406() {
        return b20000667110406;
    }

    public void setB20000667110406(BigDecimal b20000667110406) {
        this.b20000667110406 = b20000667110406;
    }

    public BigDecimal getB20000667110407() {
        return b20000667110407;
    }

    public void setB20000667110407(BigDecimal b20000667110407) {
        this.b20000667110407 = b20000667110407;
    }

    public BigDecimal getB200006671105() {
        return b200006671105;
    }

    public void setB200006671105(BigDecimal b200006671105) {
        this.b200006671105 = b200006671105;
    }

    public BigDecimal getB200006671106() {
        return b200006671106;
    }

    public void setB200006671106(BigDecimal b200006671106) {
        this.b200006671106 = b200006671106;
    }

    public BigDecimal getB200006671107() {
        return b200006671107;
    }

    public void setB200006671107(BigDecimal b200006671107) {
        this.b200006671107 = b200006671107;
    }

    public BigDecimal getB20000667110701() {
        return b20000667110701;
    }

    public void setB20000667110701(BigDecimal b20000667110701) {
        this.b20000667110701 = b20000667110701;
    }

    public BigDecimal getB20000667110702() {
        return b20000667110702;
    }

    public void setB20000667110702(BigDecimal b20000667110702) {
        this.b20000667110702 = b20000667110702;
    }

    public BigDecimal getB20000667110703() {
        return b20000667110703;
    }

    public void setB20000667110703(BigDecimal b20000667110703) {
        this.b20000667110703 = b20000667110703;
    }

    public BigDecimal getB200006671108() {
        return b200006671108;
    }

    public void setB200006671108(BigDecimal b200006671108) {
        this.b200006671108 = b200006671108;
    }

    public BigDecimal getB20000667110801() {
        return b20000667110801;
    }

    public void setB20000667110801(BigDecimal b20000667110801) {
        this.b20000667110801 = b20000667110801;
    }

    public BigDecimal getB20000667110802() {
        return b20000667110802;
    }

    public void setB20000667110802(BigDecimal b20000667110802) {
        this.b20000667110802 = b20000667110802;
    }

    public BigDecimal getB20000667110803() {
        return b20000667110803;
    }

    public void setB20000667110803(BigDecimal b20000667110803) {
        this.b20000667110803 = b20000667110803;
    }

    public BigDecimal getB20000667110804() {
        return b20000667110804;
    }

    public void setB20000667110804(BigDecimal b20000667110804) {
        this.b20000667110804 = b20000667110804;
    }

    public BigDecimal getB20000667110805() {
        return b20000667110805;
    }

    public void setB20000667110805(BigDecimal b20000667110805) {
        this.b20000667110805 = b20000667110805;
    }

    public BigDecimal getB20000667110806() {
        return b20000667110806;
    }

    public void setB20000667110806(BigDecimal b20000667110806) {
        this.b20000667110806 = b20000667110806;
    }

    public BigDecimal getB20000667110807() {
        return b20000667110807;
    }

    public void setB20000667110807(BigDecimal b20000667110807) {
        this.b20000667110807 = b20000667110807;
    }

    public BigDecimal getB20000667110808() {
        return b20000667110808;
    }

    public void setB20000667110808(BigDecimal b20000667110808) {
        this.b20000667110808 = b20000667110808;
    }

    public BigDecimal getB20000667110809() {
        return b20000667110809;
    }

    public void setB20000667110809(BigDecimal b20000667110809) {
        this.b20000667110809 = b20000667110809;
    }

    public BigDecimal getB20000667110810() {
        return b20000667110810;
    }

    public void setB20000667110810(BigDecimal b20000667110810) {
        this.b20000667110810 = b20000667110810;
    }

    public BigDecimal getB20000667110811() {
        return b20000667110811;
    }

    public void setB20000667110811(BigDecimal b20000667110811) {
        this.b20000667110811 = b20000667110811;
    }

    public BigDecimal getB20000667110812() {
        return b20000667110812;
    }

    public void setB20000667110812(BigDecimal b20000667110812) {
        this.b20000667110812 = b20000667110812;
    }

    public BigDecimal getB20000667110813() {
        return b20000667110813;
    }

    public void setB20000667110813(BigDecimal b20000667110813) {
        this.b20000667110813 = b20000667110813;
    }

    public BigDecimal getB2000066801() {
        return b2000066801;
    }

    public void setB2000066801(BigDecimal b2000066801) {
        this.b2000066801 = b2000066801;
    }

    public BigDecimal getB2000066901() {
        return b2000066901;
    }

    public void setB2000066901(BigDecimal b2000066901) {
        this.b2000066901 = b2000066901;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getSubjectMonth() {
        return subjectMonth;
    }

    public void setSubjectMonth(Integer subjectMonth) {
        this.subjectMonth = subjectMonth;
    }

    public BigDecimal getB200006222124() {
        return b200006222124;
    }

    public void setB200006222124(BigDecimal b200006222124) {
        this.b200006222124 = b200006222124;
    }

    public BigDecimal getB200006222125() {
        return b200006222125;
    }

    public void setB200006222125(BigDecimal b200006222125) {
        this.b200006222125 = b200006222125;
    }

    public BigDecimal getB20000667110501() {
        return b20000667110501;
    }

    public void setB20000667110501(BigDecimal b20000667110501) {
        this.b20000667110501 = b20000667110501;
    }

    public BigDecimal getB20000667110502() {
        return b20000667110502;
    }

    public void setB20000667110502(BigDecimal b20000667110502) {
        this.b20000667110502 = b20000667110502;
    }
}
