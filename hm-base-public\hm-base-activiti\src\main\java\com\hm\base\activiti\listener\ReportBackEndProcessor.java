package com.hm.base.activiti.listener;

import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.TaskListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */
@Component
@Transactional
@Slf4j
public class ReportBackEndProcessor implements TaskListener {

    private static final long serialVersionUID = 1L;

    @Override
    public void notify(DelegateTask delegateTask) {
        Object realityStartTime = delegateTask.getVariable("realityStartTime");
        Object realityEndTime = delegateTask.getVariable("realityEndTime");
        log.info("realityEndTime:{}", realityEndTime);
        log.info("realityStartTime:{}", realityStartTime);
    }

}
