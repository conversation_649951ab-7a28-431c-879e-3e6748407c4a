package com.hm.base.activiti.config;

import com.hm.api.common.config.BaseSwaggerConfig;
import com.hm.api.common.domain.SwaggerProperties;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.swagger2.annotations.EnableSwagger2WebMvc;

/**
 * Swagger API文档相关配置
 */
@Configuration
@EnableSwagger2WebMvc
public class SwaggerConfig extends BaseSwaggerConfig {

    @Override
    public SwaggerProperties swaggerProperties() {
        return SwaggerProperties.SwaggerPropertiesBuilder
                .aSwaggerProperties()
                .withApiBasePackage("com.hm.base.activiti")
                .withGroupName("hm-base-activiti")
                .withTitle("工作流引擎 API")
                .withDescription("hm-base-activiti项目提供的RESTful APIs")
                .withContactName("hm")
                .withVersion("1.0")
                .withEnableSecurity(false)
                .build();
    }
}
