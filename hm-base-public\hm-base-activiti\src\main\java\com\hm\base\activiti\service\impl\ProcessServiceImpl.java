package com.hm.base.activiti.service.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.hm.api.common.exception.BizException;
import com.hm.base.activiti.config.ICustomProcessDiagramGenerator;
import com.hm.base.activiti.config.WorkflowConstants;
import com.hm.base.activiti.dto.CompleteDto;
import com.hm.base.activiti.dto.HistoricDto;
import com.hm.base.activiti.service.ProcessService;
import com.hm.base.activiti.vo.HistoricActivityVo;
import com.hm.base.activiti.vo.TaskVo;
import lombok.extern.slf4j.Slf4j;
import org.activiti.bpmn.model.*;
import org.activiti.engine.*;
import org.activiti.engine.history.HistoricActivityInstance;
import org.activiti.engine.history.HistoricActivityInstanceQuery;
import org.activiti.engine.history.HistoricProcessInstance;
import org.activiti.engine.history.HistoricTaskInstance;
import org.activiti.engine.impl.RepositoryServiceImpl;
import org.activiti.engine.impl.persistence.entity.ProcessDefinitionEntity;
import org.activiti.engine.impl.persistence.entity.TaskEntityImpl;
import org.activiti.engine.repository.ProcessDefinition;
import org.activiti.engine.repository.ProcessDefinitionQuery;
import org.activiti.engine.runtime.Execution;
import org.activiti.engine.runtime.ProcessInstance;
import org.activiti.engine.task.Comment;
import org.activiti.engine.task.Task;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.awt.*;
import java.io.InputStream;
import java.io.OutputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ProcessServiceImpl implements ProcessService {
    @Autowired
    private RuntimeService runtimeService;
    @Autowired
    private TaskService taskService;
    @Autowired
    private HistoryService historyService;
    @Autowired
    private IdentityService identityService;
    @Autowired
    private RepositoryService repositoryService;
    @Autowired
    private ProcessEngine processEngine;

    @Override
    public ProcessInstance submitApply(String businessKey, String userId, String processDefinitionKey, Map<String, Object> variables) {
        // 业务key和流程定义id保证唯一
        List<HistoricProcessInstance> list = historyService.createHistoricProcessInstanceQuery()
                .processInstanceBusinessKey(businessKey)
                .processDefinitionKey(processDefinitionKey).list();
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(list)) {
            throw new BizException("任务流程：" + processDefinitionKey + "，已存在此业务key:" + businessKey);
        }
        // 用来设置启动流程的人员ID，引擎会自动把用户ID保存到activiti:initiator中
        identityService.setAuthenticatedUserId(userId);
        // 启动流程时设置业务 key
        return runtimeService.startProcessInstanceByKey(processDefinitionKey, businessKey, variables);
    }

    @Override
    public void complete(String userId, CompleteDto dto) {
        // 获取排他网关变量
        Task task = taskService.createTaskQuery().taskId(dto.getTaskId()).singleResult();
        if (task == null) {
            throw new BizException("任务不存在:" + dto.getTaskId());
        }
        String defId = getProcessDefIdByInstanceId(dto.getInstanceId());
        String expression = getExcGatewayExpression(defId, task.getTaskDefinitionKey());
        // 校验变量是否存在
        Map<String, Object> variables = dto.getVariables();
        if (StringUtils.isNotEmpty(expression)) {
            if (!variables.containsKey(expression)) {
                throw new BizException("表达式变量缺失:" + expression);
            }
            Object exp = variables.get(expression);
            try {
                variables.put(expression, exp instanceof Boolean ? exp : Boolean.valueOf(String.valueOf(exp)));
            } catch (Exception e) {
                throw new BizException("表达式类型错误:" + variables.get(expression) + "," + e.getMessage());
            }
        }
        // 保存评论
        if (variables != null && variables.containsKey("comment")) {
            identityService.setAuthenticatedUserId(userId);
            taskService.addComment(dto.getTaskId(), dto.getInstanceId(), String.valueOf(variables.get("comment")));
        }
        // 被委派人处理完成任务
        // p.s. 被委托的流程需要先 resolved 这个任务再提交。
        // 所以在 complete 之前需要先 resolved
        // resolveTask() 要在 claim() 之前，不然 act_hi_taskinst 表的 assignee 字段会为 null
        taskService.resolveTask(dto.getTaskId(), dto.getVariables());
        // 只有签收任务，act_hi_taskinst 表的 assignee 字段才不为 null
        taskService.claim(dto.getTaskId(), userId);
        taskService.complete(dto.getTaskId(), dto.getVariables());
    }

    @Override
    public List<TaskVo> findTodoTasks(String userId, List<String> roleIdList, String moduleKey) {
        List<TaskVo> list = Lists.newArrayList();
        List<Task> tasks = new ArrayList<>();
        if (roleIdList.isEmpty()) {
            roleIdList.add("DEFAULT_ROLE");
        }
        // 根据当前人的ID查询
        List<Task> todoList = taskService
                .createTaskQuery()
                .processDefinitionKey(moduleKey)
                .taskAssignee(userId)
                .list();
        // 根据当前人未签收的任务
        List<Task> unsignedTasks = taskService
                .createTaskQuery()
                .processDefinitionKey(moduleKey)
                .taskCandidateGroupIn(roleIdList)
                .taskCandidateUser(userId)
                .list();
        // 合并
        tasks.addAll(todoList);
        tasks.addAll(unsignedTasks);

        for (Task task : tasks) {
            TaskEntityImpl taskImpl = (TaskEntityImpl) task;
            String processInstanceId = taskImpl.getProcessInstanceId();
            ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();
            String businessKey = processInstance.getBusinessKey();
            TaskVo taskVo = new TaskVo();
            taskVo.setVariables(getExcGatewayExpression(getProcessDefIdByInstanceId(processInstanceId), taskImpl.getTaskDefinitionKey()));
            taskVo.setBusinessKey(businessKey);
            taskVo.setTaskId(taskImpl.getId());
            taskVo.setInstanceId(taskImpl.getProcessInstanceId());

            if (taskImpl.getSuspensionState() == 2) {
                taskVo.setTaskName("已挂起");
            } else {
                taskVo.setTaskName(taskImpl.getName());
            }
            list.add(taskVo);
        }
        return list;
    }

    @Override
    public List<Map<String, Object>> findDoneTasks(String userId, String processDefinitionKey) {
        List<Map<String, Object>> list = Lists.newArrayList();
        Map<String, Object> map;
        List<HistoricTaskInstance> historicList = historyService
                .createHistoricTaskInstanceQuery()
                .processDefinitionKey(processDefinitionKey)
                .taskAssignee(userId)
                .finished()
                .orderByHistoricTaskInstanceEndTime()
                .desc()
                .list();

        for (HistoricTaskInstance historic : historicList) {
            String processInstanceId = historic.getProcessInstanceId();
            HistoricProcessInstance processInstance = historyService.createHistoricProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();
            String businessKey = processInstance.getBusinessKey();

            map = Maps.newHashMap();
            map.put("businessKey", businessKey);
            map.put("taskId", historic.getId());
            map.put("taskName", historic.getName());
            map.put("endTime", historic.getEndTime());
            list.add(map);
        }
        return list;
    }

    @Override
    public String getProcessDefIdByInstanceId(String instanceId) {
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                .processInstanceId(instanceId)
                .singleResult();
        if (processInstance == null) {
            throw new BizException("实例id不正确：" + instanceId);
        }
        return processInstance.getProcessDefinitionId();
    }

    @Override
    public String getExcGatewayExpression(String processDefId, String taskDefKey) {
        BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefId);
        FlowElement flowElement = bpmnModel.getProcesses().get(0).getFlowElement(taskDefKey);
        UserTask nowUserTask = (UserTask) flowElement;//获取当前UserTask
        List<SequenceFlow> nowOutgoingFlows = nowUserTask.getOutgoingFlows();
        FlowElement nextFlowElement = nowOutgoingFlows.get(0).getTargetFlowElement();
        String expression = null;
        if (nextFlowElement instanceof ExclusiveGateway) {
            ExclusiveGateway exclusiveGateway = (ExclusiveGateway) nextFlowElement;
            List<SequenceFlow> outgoingFlows = exclusiveGateway.getOutgoingFlows();//排他网关流向节点
            SequenceFlow flow = outgoingFlows.get(0);
            expression = flow.getConditionExpression()
                    .replaceAll("\\$", "")
                    .replaceAll("\\{", "")
                    .replaceAll("}", "")
                    .replaceAll("!", "");
        }
        return expression;
    }

    @Override
    public void cancelApply(String instanceId, String deleteReason) {
        // 执行此方法后未审批的任务 act_ru_task 会被删除，流程历史 act_hi_taskinst 不会被删除，并且流程历史的状态为finished完成
        runtimeService.deleteProcessInstance(instanceId, deleteReason);
    }

    @Override
    public void suspendOrActiveApply(String instanceId, String suspendState) {
        if ("1".equals(suspendState)) {
            // 当流程实例被挂起时，无法通过下一个节点对应的任务id来继续这个流程实例。
            // 通过挂起某一特定的流程实例，可以终止当前的流程实例，而不影响到该流程定义的其他流程实例。
            // 激活之后可以继续该流程实例，不会对后续任务造成影响。
            // 直观变化：act_ru_task 的 SUSPENSION_STATE_ 为 2
            runtimeService.suspendProcessInstanceById(instanceId);
        } else if ("2".equals(suspendState)) {
            runtimeService.activateProcessInstanceById(instanceId);
        }
    }

    @Override
    public List<HistoricActivityVo> selectHistoryList(HistoricDto dto) {
        List<HistoricActivityVo> activityList = new ArrayList<>();
        HistoricActivityInstanceQuery query = historyService.createHistoricActivityInstanceQuery();
        if (StringUtils.isNotBlank(dto.getAssignee())) {
            query.taskAssignee(dto.getAssignee());
        }
        if (StringUtils.isNotBlank(dto.getActivityName())) {
            query.activityName(dto.getActivityName());
        }
        List<HistoricActivityInstance> list = query.processInstanceId(dto.getInstanceId())
                .activityType("userTask")
                .finished()
                .orderByHistoricActivityInstanceStartTime()
                .desc()
                .list();
        list.forEach(instance -> {
            HistoricActivityVo activity = new HistoricActivityVo();
            BeanUtils.copyProperties(instance, activity);
            String taskId = instance.getTaskId();
            List<Comment> comment = taskService.getTaskComments(taskId, "comment");
            if (!CollectionUtils.isEmpty(comment)) {
                activity.setComment(comment.get(0).getFullMessage());
            }
            activityList.add(activity);
        });
        return activityList;
    }

    @Override
    public void deleteCandidate(String taskId, String groupId) {
        taskService.deleteCandidateGroup(taskId, groupId);
    }

    @Override
    public void addCandidate(String taskId, String groupId) {
        taskService.addCandidateGroup(taskId, groupId);
    }

    @Override
    public List<HistoricActivityInstance> findHisTasks(String instanceId) {
        return historyService.createHistoricActivityInstanceQuery()
                .processInstanceId(instanceId).orderByHistoricActivityInstanceId().asc().list();
    }

    @Override
    public void readResource(String pProcessInstanceId, HttpServletResponse response) {
        String processDefinitionId;
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(pProcessInstanceId).singleResult();
        if (processInstance == null) {
            HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery().processInstanceId(pProcessInstanceId).singleResult();
            processDefinitionId = historicProcessInstance.getProcessDefinitionId();
        } else {
            processDefinitionId = processInstance.getProcessDefinitionId();
        }
        ProcessDefinitionQuery pdq = repositoryService.createProcessDefinitionQuery();
        ProcessDefinition pd = pdq.processDefinitionId(processDefinitionId).singleResult();

        String resourceName = pd.getDiagramResourceName();

        if (resourceName.endsWith(".png") && !StringUtils.isEmpty(pProcessInstanceId)) {
            getActivitiProccessImage(pProcessInstanceId, response);
        } else {
            try {
                InputStream resourceAsStream = repositoryService.getResourceAsStream(pd.getDeploymentId(), resourceName);
                ServletOutputStream outputStream = response.getOutputStream();
                IOUtils.copy(resourceAsStream, outputStream);
                IOUtils.closeQuietly(resourceAsStream);
                IOUtils.closeQuietly(outputStream);
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
    }

    @Override
    public Object businessHisList(String businessKey, String processDefinitionKey) {
        return historyService.createHistoricProcessInstanceQuery()
                .processDefinitionKey(processDefinitionKey)
                .processInstanceBusinessKey(businessKey).list();
    }


    /**
     * 获取流程图像，已执行节点和流程线高亮显示
     */
    private void getActivitiProccessImage(String pProcessInstanceId, HttpServletResponse response) {
        //logger.info("[开始]-获取流程图图像");
        try {
            //  获取历史流程实例
            HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
                    .processInstanceId(pProcessInstanceId).singleResult();

            if (historicProcessInstance == null) {
                //throw new BusinessException("获取流程实例ID[" + pProcessInstanceId + "]对应的历史流程实例失败！");
            } else {
                // 获取流程定义
                ProcessDefinitionEntity processDefinition = (ProcessDefinitionEntity) ((RepositoryServiceImpl) repositoryService)
                        .getDeployedProcessDefinition(historicProcessInstance.getProcessDefinitionId());

                // 获取流程历史中已执行节点，并按照节点在流程中执行先后顺序排序
                List<HistoricActivityInstance> historicActivityInstanceList = historyService.createHistoricActivityInstanceQuery()
                        .processInstanceId(pProcessInstanceId).orderByHistoricActivityInstanceId().asc().list();

                // 已执行的节点ID集合
                List<String> executedActivityIdList = new ArrayList<String>();
                int index = 1;
                //logger.info("获取已经执行的节点ID");
                for (HistoricActivityInstance activityInstance : historicActivityInstanceList) {
                    executedActivityIdList.add(activityInstance.getActivityId());

                    //logger.info("第[" + index + "]个已执行节点=" + activityInstance.getActivityId() + " : " +activityInstance.getActivityName());
                    index++;
                }

                BpmnModel bpmnModel = repositoryService.getBpmnModel(historicProcessInstance.getProcessDefinitionId());

                // 已执行的线集合
                List<String> flowIds = new ArrayList<String>();
                // 获取流程走过的线 (getHighLightedFlows是下面的方法)
                flowIds = getHighLightedFlows(bpmnModel, processDefinition, historicActivityInstanceList);

                // 获取流程图图像字符流
                //ProcessDiagramGenerator pec = processEngine.getProcessEngineConfiguration().getProcessDiagramGenerator();
                //配置字体
                //InputStream imageStream = pec.generateDiagram(bpmnModel, "png", executedActivityIdList, flowIds,"宋体","微软雅黑","黑体",null,2.0);

                Set<String> currIds = runtimeService.createExecutionQuery().processInstanceId(pProcessInstanceId).list()
                        .stream().map(Execution::getActivityId).collect(Collectors.toSet());

                ICustomProcessDiagramGenerator diagramGenerator = (ICustomProcessDiagramGenerator) processEngine.getProcessEngineConfiguration().getProcessDiagramGenerator();
                InputStream imageStream = diagramGenerator.generateDiagram(bpmnModel, "png", executedActivityIdList,
                        flowIds, "宋体", "宋体", "宋体", null, 1.0, new Color[]{WorkflowConstants.COLOR_NORMAL, WorkflowConstants.COLOR_CURRENT}, currIds);

                response.setContentType("image/png");
                OutputStream os = response.getOutputStream();
                IOUtils.copy(imageStream, os);
                os.close();
                imageStream.close();
            }
        } catch (Exception e) {
            log.error("出现异常", e);
        }
    }

    private List<String> getHighLightedFlows(BpmnModel bpmnModel, ProcessDefinitionEntity processDefinitionEntity, List<HistoricActivityInstance> historicActivityInstances) {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"); //24小时制
        List<String> highFlows = new ArrayList<>();
        // 用以保存高亮的线flowId

        for (int i = 0; i < historicActivityInstances.size() - 1; i++) {
            // 对历史流程节点进行遍历
            // 得到节点定义的详细信息
            FlowNode activityImpl = (FlowNode) bpmnModel.getMainProcess().getFlowElement(historicActivityInstances.get(i).getActivityId());


            List<FlowNode> sameStartTimeNodes = new ArrayList<>();
            // 用以保存后续开始时间相同的节点
            FlowNode sameActivityImpl1 = null;

            HistoricActivityInstance activityImpl_ = historicActivityInstances.get(i);
            // 第一个节点
            HistoricActivityInstance activityImp2_;

            for (int k = i + 1; k <= historicActivityInstances.size() - 1; k++) {
                activityImp2_ = historicActivityInstances.get(k);// 后续第1个节点

                if (activityImpl_.getActivityType().equals("userTask") && activityImp2_.getActivityType().equals("userTask") &&
                        df.format(activityImpl_.getStartTime()).equals(df.format(activityImp2_.getStartTime()))) {
                    //都是usertask，且主节点与后续节点的开始时间相同，说明不是真实的后继节点

                } else {
                    sameActivityImpl1 = (FlowNode) bpmnModel.getMainProcess().getFlowElement(historicActivityInstances.get(k).getActivityId());
                    //找到紧跟在后面的一个节点
                    break;
                }
            }
            sameStartTimeNodes.add(sameActivityImpl1);
            // 将后面第一个节点放在时间相同节点的集合里
            for (int j = i + 1; j < historicActivityInstances.size() - 1; j++) {
                // 后续第一个节点
                HistoricActivityInstance activityImpl1 = historicActivityInstances.get(j);
                // 后续第二个节点
                HistoricActivityInstance activityImpl2 = historicActivityInstances.get(j + 1);

                if (df.format(activityImpl1.getStartTime()).equals(df.format(activityImpl2.getStartTime()))) {
                    // 如果第一个节点和第二个节点开始时间相同保存
                    FlowNode sameActivityImpl2 = (FlowNode) bpmnModel.getMainProcess().getFlowElement(activityImpl2.getActivityId());
                    sameStartTimeNodes.add(sameActivityImpl2);
                } else {
                    // 有不相同跳出循环
                    break;
                }
            }
            // 取出节点的所有出去的线
            List<SequenceFlow> pvmTransitions = activityImpl.getOutgoingFlows();

            // 对所有的线进行遍历
            for (SequenceFlow pvmTransition : pvmTransitions) {
                FlowNode pvmActivityImpl = (FlowNode) bpmnModel.getMainProcess().getFlowElement(pvmTransition.getTargetRef());
                // 如果取出的线的目标节点存在时间相同的节点里，保存该线的id，进行高亮显示
                if (sameStartTimeNodes.contains(pvmActivityImpl)) {
                    highFlows.add(pvmTransition.getId());
                }
            }
        }
        return highFlows;

    }
}
