package com.hm.base.activiti;


import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.ComponentScan;


@SpringBootApplication(exclude = {
        org.activiti.spring.boot.SecurityAutoConfiguration.class,
        org.springframework.boot.actuate.autoconfigure.security.servlet.ManagementWebSecurityAutoConfiguration.class,
        org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration.class
})
@EnableDiscoveryClient
@ComponentScan(basePackages = {"com.hm.base.activiti.**", "com.hm.api.common.**"})
public class HmBaseActivitiApplication {
    public static void main(String[] args) {
        SpringApplication.run(HmBaseActivitiApplication.class, args);
    }
}
