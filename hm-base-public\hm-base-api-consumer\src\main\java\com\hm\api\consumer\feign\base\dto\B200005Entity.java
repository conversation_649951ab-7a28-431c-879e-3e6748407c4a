package com.hm.api.consumer.feign.base.dto;

import com.hm.api.common.aop.LogField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @author: curry.wu
 * @create: 2020/10/25 14:26
 **/
@ApiModel(value = "本期借方发生额")
public class B200005Entity implements Serializable {

    private static final long serialVersionUID = -1135399094998664033L;

    @LogField(tableName = "b200005", value = "ent_id", valueName = "企业ID")
    @ApiModelProperty(value = "企业ID")
    private String entId;

    @LogField(tableName = "b200005", value = "tax_year", valueName = "所属时间")
    @ApiModelProperty(value = "所属时间")
    private String taxYear;

    @LogField(tableName = "b200005", value = "subject_month", valueName = "月份(年度等于12)")
    @ApiModelProperty(value = "月份(年度等于12)")
    private Integer subjectMonth;

    @LogField(tableName = "b200005", value = "b200005_1001", valueName = "库存现金")
    @ApiModelProperty(value = "库存现金")
    private BigDecimal b2000051001;

    @LogField(tableName = "b200005", value = "b200005_1002", valueName = "银行存款")
    @ApiModelProperty(value = "银行存款")
    private BigDecimal b2000051002;

    @LogField(tableName = "b200005", value = "b200005_1003", valueName = "存放中央银行款项")
    @ApiModelProperty(value = "存放中央银行款项")
    private BigDecimal b2000051003;

    @LogField(tableName = "b200005", value = "b200005_1011", valueName = "存放同业")
    @ApiModelProperty(value = "存放同业")
    private BigDecimal b2000051011;

    @LogField(tableName = "b200005", value = "b200005_1012", valueName = "其他货币资金")
    @ApiModelProperty(value = "其他货币资金")
    private BigDecimal b2000051012;

    @LogField(tableName = "b200005", value = "b200005_1021", valueName = "结算备付金")
    @ApiModelProperty(value = "结算备付金")
    private BigDecimal b2000051021;

    @LogField(tableName = "b200005", value = "b200005_1031", valueName = "存出保证金")
    @ApiModelProperty(value = "存出保证金")
    private BigDecimal b2000051031;

    @LogField(tableName = "b200005", value = "b200005_1101", valueName = "交易性金融资产")
    @ApiModelProperty(value = "交易性金融资产")
    private BigDecimal b2000051101;

    @LogField(tableName = "b200005", value = "b200005_1111", valueName = "买入返售金融资产")
    @ApiModelProperty(value = "买入返售金融资产")
    private BigDecimal b2000051111;

    @LogField(tableName = "b200005", value = "b200005_1121", valueName = "应收票据")
    @ApiModelProperty(value = "应收票据")
    private BigDecimal b2000051121;

    @LogField(tableName = "b200005", value = "b200005_1122", valueName = "应收账款")
    @ApiModelProperty(value = "应收账款")
    private BigDecimal b2000051122;

    @LogField(tableName = "b200005", value = "b200005_1123", valueName = "预付账款")
    @ApiModelProperty(value = "预付账款")
    private BigDecimal b2000051123;

    @LogField(tableName = "b200005", value = "b200005_1124", valueName = "合同资产")
    @ApiModelProperty(value = "合同资产")
    private BigDecimal b2000051124;

    @LogField(tableName = "b200005", value = "b200005_1125", valueName = "合同资产减值准备")
    @ApiModelProperty(value = "合同资产减值准备")
    private BigDecimal b2000051125;

    @LogField(tableName = "b200005", value = "b200005_1131", valueName = "应收股利")
    @ApiModelProperty(value = "应收股利")
    private BigDecimal b2000051131;

    @LogField(tableName = "b200005", value = "b200005_1132", valueName = "应收利息")
    @ApiModelProperty(value = "应收利息")
    private BigDecimal b2000051132;

    @LogField(tableName = "b200005", value = "b200005_1201", valueName = "应收代位追偿款")
    @ApiModelProperty(value = "应收代位追偿款")
    private BigDecimal b2000051201;

    @LogField(tableName = "b200005", value = "b200005_1211", valueName = "应收分保账款")
    @ApiModelProperty(value = "应收分保账款")
    private BigDecimal b2000051211;

    @LogField(tableName = "b200005", value = "b200005_1212", valueName = "应收分保合同准备金")
    @ApiModelProperty(value = "应收分保合同准备金")
    private BigDecimal b2000051212;

    @LogField(tableName = "b200005", value = "b200005_1221", valueName = "其他应收款")
    @ApiModelProperty(value = "其他应收款")
    private BigDecimal b2000051221;

    @LogField(tableName = "b200005", value = "b200005_1231", valueName = "坏账准备")
    @ApiModelProperty(value = "坏账准备")
    private BigDecimal b2000051231;

    @LogField(tableName = "b200005", value = "b200005_1301", valueName = "贴现资产")
    @ApiModelProperty(value = "贴现资产")
    private BigDecimal b2000051301;

    @LogField(tableName = "b200005", value = "b200005_1302", valueName = "拆出资金")
    @ApiModelProperty(value = "拆出资金")
    private BigDecimal b2000051302;

    @LogField(tableName = "b200005", value = "b200005_1303", valueName = "贷款")
    @ApiModelProperty(value = "贷款")
    private BigDecimal b2000051303;

    @LogField(tableName = "b200005", value = "b200005_1304", valueName = "贷款损失准备")
    @ApiModelProperty(value = "贷款损失准备")
    private BigDecimal b2000051304;

    @LogField(tableName = "b200005", value = "b200005_1311", valueName = "代理兑付证券")
    @ApiModelProperty(value = "代理兑付证券")
    private BigDecimal b2000051311;

    @LogField(tableName = "b200005", value = "b200005_1321", valueName = "代理业务资产")
    @ApiModelProperty(value = "代理业务资产")
    private BigDecimal b2000051321;

    @LogField(tableName = "b200005", value = "b200005_1401", valueName = "材料采购")
    @ApiModelProperty(value = "材料采购")
    private BigDecimal b2000051401;

    @LogField(tableName = "b200005", value = "b200005_1402", valueName = "在途物资")
    @ApiModelProperty(value = "在途物资")
    private BigDecimal b2000051402;

    @LogField(tableName = "b200005", value = "b200005_1403", valueName = "原材料")
    @ApiModelProperty(value = "原材料")
    private BigDecimal b2000051403;

    @LogField(tableName = "b200005", value = "b200005_1404", valueName = "材料成本差异")
    @ApiModelProperty(value = "材料成本差异")
    private BigDecimal b2000051404;

    @LogField(tableName = "b200005", value = "b200005_1405", valueName = "库存商品")
    @ApiModelProperty(value = "库存商品")
    private BigDecimal b2000051405;

    @LogField(tableName = "b200005", value = "b200005_1406", valueName = "发出商品")
    @ApiModelProperty(value = "发出商品")
    private BigDecimal b2000051406;

    @LogField(tableName = "b200005", value = "b200005_1407", valueName = "商品进销差价")
    @ApiModelProperty(value = "商品进销差价")
    private BigDecimal b2000051407;

    @LogField(tableName = "b200005", value = "b200005_1408", valueName = "委托加工物资")
    @ApiModelProperty(value = "委托加工物资")
    private BigDecimal b2000051408;

    @LogField(tableName = "b200005", value = "b200005_1411", valueName = "周转材料")
    @ApiModelProperty(value = "周转材料")
    private BigDecimal b2000051411;

    @LogField(tableName = "b200005", value = "b200005_1421", valueName = "消耗性生物资产")
    @ApiModelProperty(value = "消耗性生物资产")
    private BigDecimal b2000051421;

    @LogField(tableName = "b200005", value = "b200005_1431", valueName = "贵金属")
    @ApiModelProperty(value = "贵金属")
    private BigDecimal b2000051431;

    @LogField(tableName = "b200005", value = "b200005_1441", valueName = "抵债资产")
    @ApiModelProperty(value = "抵债资产")
    private BigDecimal b2000051441;

    @LogField(tableName = "b200005", value = "b200005_1451", valueName = "损余物资")
    @ApiModelProperty(value = "损余物资")
    private BigDecimal b2000051451;

    @LogField(tableName = "b200005", value = "b200005_1461", valueName = "融资租赁资产")
    @ApiModelProperty(value = "融资租赁资产")
    private BigDecimal b2000051461;

    @LogField(tableName = "b200005", value = "b200005_1471", valueName = "存货跌价准备")
    @ApiModelProperty(value = "存货跌价准备")
    private BigDecimal b2000051471;

    @LogField(tableName = "b200005", value = "b200005_1481", valueName = "持有待售资产")
    @ApiModelProperty(value = "持有待售资产")
    private BigDecimal b2000051481;

    @LogField(tableName = "b200005", value = "b200005_1482", valueName = "持有待售资产减值准备")
    @ApiModelProperty(value = "持有待售资产减值准备")
    private BigDecimal b2000051482;

    @LogField(tableName = "b200005", value = "b200005_1501", valueName = "持有至到期投资")
    @ApiModelProperty(value = "持有至到期投资")
    private BigDecimal b2000051501;

    @LogField(tableName = "b200005", value = "b200005_1502", valueName = "持有至到期投资减值准备")
    @ApiModelProperty(value = "持有至到期投资减值准备")
    private BigDecimal b2000051502;

    @LogField(tableName = "b200005", value = "b200005_1503", valueName = "可供出售金融资产")
    @ApiModelProperty(value = "可供出售金融资产")
    private BigDecimal b2000051503;

    @LogField(tableName = "b200005", value = "b200005_1511", valueName = "长期股权投资")
    @ApiModelProperty(value = "长期股权投资")
    private BigDecimal b2000051511;

    @LogField(tableName = "b200005", value = "b200005_1512", valueName = "长期股权投资减值准备")
    @ApiModelProperty(value = "长期股权投资减值准备")
    private BigDecimal b2000051512;

    @LogField(tableName = "b200005", value = "b200005_1521", valueName = "投资性房地产")
    @ApiModelProperty(value = "投资性房地产")
    private BigDecimal b2000051521;

    @LogField(tableName = "b200005", value = "b200005_1531", valueName = "长期应收款")
    @ApiModelProperty(value = "长期应收款")
    private BigDecimal b2000051531;

    @LogField(tableName = "b200005", value = "b200005_1532", valueName = "未实现融资收益")
    @ApiModelProperty(value = "未实现融资收益")
    private BigDecimal b2000051532;

    @LogField(tableName = "b200005", value = "b200005_1541", valueName = "存出资本保证金")
    @ApiModelProperty(value = "存出资本保证金")
    private BigDecimal b2000051541;

    @LogField(tableName = "b200005", value = "b200005_1601", valueName = "固定资产")
    @ApiModelProperty(value = "固定资产")
    private BigDecimal b2000051601;

    @LogField(tableName = "b200005", value = "b200005_1602", valueName = "累计折旧")
    @ApiModelProperty(value = "累计折旧")
    private BigDecimal b2000051602;

    @LogField(tableName = "b200005", value = "b200005_1603", valueName = "固定资产减值准备")
    @ApiModelProperty(value = "固定资产减值准备")
    private BigDecimal b2000051603;

    @LogField(tableName = "b200005", value = "b200005_1604", valueName = "在建工程")
    @ApiModelProperty(value = "在建工程")
    private BigDecimal b2000051604;

    @LogField(tableName = "b200005", value = "b200005_1605", valueName = "工程物资")
    @ApiModelProperty(value = "工程物资")
    private BigDecimal b2000051605;

    @LogField(tableName = "b200005", value = "b200005_1606", valueName = "固定资产清理")
    @ApiModelProperty(value = "固定资产清理")
    private BigDecimal b2000051606;

    @LogField(tableName = "b200005", value = "b200005_1611", valueName = "未担保余值")
    @ApiModelProperty(value = "未担保余值")
    private BigDecimal b2000051611;

    @LogField(tableName = "b200005", value = "b200005_1621", valueName = "生产性生物资产")
    @ApiModelProperty(value = "生产性生物资产")
    private BigDecimal b2000051621;

    @LogField(tableName = "b200005", value = "b200005_1622", valueName = "生产性生物资产累计折旧")
    @ApiModelProperty(value = "生产性生物资产累计折旧")
    private BigDecimal b2000051622;

    @LogField(tableName = "b200005", value = "b200005_1623", valueName = "公益性生物资产")
    @ApiModelProperty(value = "公益性生物资产")
    private BigDecimal b2000051623;

    @LogField(tableName = "b200005", value = "b200005_1631", valueName = "油气资产")
    @ApiModelProperty(value = "油气资产")
    private BigDecimal b2000051631;

    @LogField(tableName = "b200005", value = "b200005_1632", valueName = "累计折耗")
    @ApiModelProperty(value = "累计折耗")
    private BigDecimal b2000051632;

    @LogField(tableName = "b200005", value = "b200005_1701", valueName = "无形资产")
    @ApiModelProperty(value = "无形资产")
    private BigDecimal b2000051701;

    @LogField(tableName = "b200005", value = "b200005_1702", valueName = "累计摊销")
    @ApiModelProperty(value = "累计摊销")
    private BigDecimal b2000051702;

    @LogField(tableName = "b200005", value = "b200005_1703", valueName = "无形资产减值准备")
    @ApiModelProperty(value = "无形资产减值准备")
    private BigDecimal b2000051703;

    @LogField(tableName = "b200005", value = "b200005_1711", valueName = "商誉")
    @ApiModelProperty(value = "商誉")
    private BigDecimal b2000051711;

    @LogField(tableName = "b200005", value = "b200005_1801", valueName = "长期待摊费用")
    @ApiModelProperty(value = "长期待摊费用")
    private BigDecimal b2000051801;

    @LogField(tableName = "b200005", value = "b200005_1811", valueName = "递延所得税资产")
    @ApiModelProperty(value = "递延所得税资产")
    private BigDecimal b2000051811;

    @LogField(tableName = "b200005", value = "b200005_1821", valueName = "独立账户资产")
    @ApiModelProperty(value = "独立账户资产")
    private BigDecimal b2000051821;

    @LogField(tableName = "b200005", value = "b200005_1901", valueName = "待处理财产损溢")
    @ApiModelProperty(value = "待处理财产损溢")
    private BigDecimal b2000051901;

    @LogField(tableName = "b200005", value = "b200005_2001", valueName = "短期借款")
    @ApiModelProperty(value = "短期借款")
    private BigDecimal b2000052001;

    @LogField(tableName = "b200005", value = "b200005_2002", valueName = "存入保证金")
    @ApiModelProperty(value = "存入保证金")
    private BigDecimal b2000052002;

    @LogField(tableName = "b200005", value = "b200005_2003", valueName = "拆入资金")
    @ApiModelProperty(value = "拆入资金")
    private BigDecimal b2000052003;

    @LogField(tableName = "b200005", value = "b200005_2004", valueName = "向中央银行借款")
    @ApiModelProperty(value = "向中央银行借款")
    private BigDecimal b2000052004;

    @LogField(tableName = "b200005", value = "b200005_2011", valueName = "吸收存款")
    @ApiModelProperty(value = "吸收存款")
    private BigDecimal b2000052011;

    @LogField(tableName = "b200005", value = "b200005_2012", valueName = "同业存放")
    @ApiModelProperty(value = "同业存放")
    private BigDecimal b2000052012;

    @LogField(tableName = "b200005", value = "b200005_2021", valueName = "贴现负债")
    @ApiModelProperty(value = "贴现负债")
    private BigDecimal b2000052021;

    @LogField(tableName = "b200005", value = "b200005_2101", valueName = "交易性金融负债")
    @ApiModelProperty(value = "交易性金融负债")
    private BigDecimal b2000052101;

    @LogField(tableName = "b200005", value = "b200005_2111", valueName = "卖出回购金融资产款")
    @ApiModelProperty(value = "卖出回购金融资产款")
    private BigDecimal b2000052111;

    @LogField(tableName = "b200005", value = "b200005_2201", valueName = "应付票据")
    @ApiModelProperty(value = "应付票据")
    private BigDecimal b2000052201;

    @LogField(tableName = "b200005", value = "b200005_2202", valueName = "应付账款")
    @ApiModelProperty(value = "应付账款")
    private BigDecimal b2000052202;

    @LogField(tableName = "b200005", value = "b200005_2203", valueName = "预收账款")
    @ApiModelProperty(value = "预收账款")
    private BigDecimal b2000052203;

    @LogField(tableName = "b200005", value = "b200005_2204", valueName = "合同负债")
    @ApiModelProperty(value = "合同负债")
    private BigDecimal b2000052204;

    @LogField(tableName = "b200005", value = "b200005_2211", valueName = "应付职工薪酬")
    @ApiModelProperty(value = "应付职工薪酬")
    private BigDecimal b2000052211;

    @LogField(tableName = "b200005", value = "b200005_221101", valueName = "职工工资")
    @ApiModelProperty(value = "职工工资")
    private BigDecimal b200005221101;

    @LogField(tableName = "b200005", value = "b200005_221102", valueName = "职工福利费")
    @ApiModelProperty(value = "职工福利费")
    private BigDecimal b200005221102;

    @LogField(tableName = "b200005", value = "b200005_221103", valueName = "社会保险费")
    @ApiModelProperty(value = "社会保险费")
    private BigDecimal b200005221103;

    @LogField(tableName = "b200005", value = "b200005_221104", valueName = "住房公积金")
    @ApiModelProperty(value = "住房公积金")
    private BigDecimal b200005221104;

    @LogField(tableName = "b200005", value = "b200005_221105", valueName = "工会经费")
    @ApiModelProperty(value = "工会经费")
    private BigDecimal b200005221105;

    @LogField(tableName = "b200005", value = "b200005_221106", valueName = "职工教育经费")
    @ApiModelProperty(value = "职工教育经费")
    private BigDecimal b200005221106;

    @LogField(tableName = "b200005", value = "b200005_221107", valueName = "年终奖")
    @ApiModelProperty(value = "年终奖")
    private BigDecimal b200005221107;

    @LogField(tableName = "b200005", value = "b200005_221108", valueName = "职工培训费用")
    @ApiModelProperty(value = "职工培训费用")
    private BigDecimal b200005221108;

    @LogField(tableName = "b200005", value = "b200005_221109", valueName = "补充养老保险")
    @ApiModelProperty(value = "补充养老保险")
    private BigDecimal b200005221109;

    @LogField(tableName = "b200005", value = "b200005_221110", valueName = "补充医疗保险")
    @ApiModelProperty(value = "补充医疗保险")
    private BigDecimal b200005221110;

    @LogField(tableName = "b200005", value = "b200005_221111", valueName = "股权激励")
    @ApiModelProperty(value = "股权激励")
    private BigDecimal b200005221111;

    @LogField(tableName = "b200005", value = "b200005_221112", valueName = "商业保险费")
    @ApiModelProperty(value = "商业保险费")
    private BigDecimal b200005221112;

    @LogField(tableName = "b200005", value = "b200005_221113", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200005221113;

    @LogField(tableName = "b200005", value = "b200005_2221", valueName = "应交税费")
    @ApiModelProperty(value = "应交税费")
    private BigDecimal b2000052221;

    @LogField(tableName = "b200005", value = "b200005_222101", valueName = "应交增值税")
    @ApiModelProperty(value = "应交增值税")
    private BigDecimal b200005222101;

    @LogField(tableName = "b200005", value = "b200005_22210101", valueName = "进项税额")
    @ApiModelProperty(value = "进项税额")
    private BigDecimal b20000522210101;

    @LogField(tableName = "b200005", value = "b200005_22210102", valueName = "销项税额")
    @ApiModelProperty(value = "销项税额")
    private BigDecimal b20000522210102;

    @LogField(tableName = "b200005", value = "b200005_22210103", valueName = "已交税金")
    @ApiModelProperty(value = "已交税金")
    private BigDecimal b20000522210103;

    @LogField(tableName = "b200005", value = "b200005_22210104", valueName = "出口抵减内销产品应纳税额")
    @ApiModelProperty(value = "出口抵减内销产品应纳税额")
    private BigDecimal b20000522210104;

    @LogField(tableName = "b200005", value = "b200005_22210105", valueName = "转出未交增值税")
    @ApiModelProperty(value = "转出未交增值税")
    private BigDecimal b20000522210105;

    @LogField(tableName = "b200005", value = "b200005_22210106", valueName = "进项税额转出")
    @ApiModelProperty(value = "进项税额转出")
    private BigDecimal b20000522210106;

    @LogField(tableName = "b200005", value = "b200005_22210107", valueName = "减免税款")
    @ApiModelProperty(value = "减免税款")
    private BigDecimal b20000522210107;

    @LogField(tableName = "b200005", value = "b200005_22210108", valueName = "出口退税")
    @ApiModelProperty(value = "出口退税")
    private BigDecimal b20000522210108;

    @LogField(tableName = "b200005", value = "b200005_22210109", valueName = "转出多交增值税")
    @ApiModelProperty(value = "转出多交增值税")
    private BigDecimal b20000522210109;

    @LogField(tableName = "b200005", value = "b200005_22210110", valueName = "销项税额抵减")
    @ApiModelProperty(value = "销项税额抵减")
    private BigDecimal b20000522210110;

    @LogField(tableName = "b200005", value = "b200005_222102", valueName = "未交增值税")
    @ApiModelProperty(value = "未交增值税")
    private BigDecimal b200005222102;

    @LogField(tableName = "b200005", value = "b200005_222103", valueName = "应交营业税")
    @ApiModelProperty(value = "应交营业税")
    private BigDecimal b200005222103;

    @LogField(tableName = "b200005", value = "b200005_222104", valueName = "应交消费税")
    @ApiModelProperty(value = "应交消费税")
    private BigDecimal b200005222104;

    @LogField(tableName = "b200005", value = "b200005_222105", valueName = "应交资源税")
    @ApiModelProperty(value = "应交资源税")
    private BigDecimal b200005222105;

    @LogField(tableName = "b200005", value = "b200005_222106", valueName = "应交所得税")
    @ApiModelProperty(value = "应交所得税")
    private BigDecimal b200005222106;

    @LogField(tableName = "b200005", value = "b200005_222107", valueName = "应交土地增值税")
    @ApiModelProperty(value = "应交土地增值税")
    private BigDecimal b200005222107;

    @LogField(tableName = "b200005", value = "b200005_222108", valueName = "应交城市维护建设税")
    @ApiModelProperty(value = "应交城市维护建设税")
    private BigDecimal b200005222108;

    @LogField(tableName = "b200005", value = "b200005_222109", valueName = "应交房产税")
    @ApiModelProperty(value = "应交房产税")
    private BigDecimal b200005222109;

    @LogField(tableName = "b200005", value = "b200005_222110", valueName = "应交土地使用税")
    @ApiModelProperty(value = "应交土地使用税")
    private BigDecimal b200005222110;

    @LogField(tableName = "b200005", value = "b200005_222111", valueName = "应交车船税")
    @ApiModelProperty(value = "应交车船税")
    private BigDecimal b200005222111;

    @LogField(tableName = "b200005", value = "b200005_222112", valueName = "应交个人所得税")
    @ApiModelProperty(value = "应交个人所得税")
    private BigDecimal b200005222112;

    @LogField(tableName = "b200005", value = "b200005_222113", valueName = "教育费附加")
    @ApiModelProperty(value = "教育费附加")
    private BigDecimal b200005222113;

    @LogField(tableName = "b200005", value = "b200005_222114", valueName = "地方教育费附加")
    @ApiModelProperty(value = "地方教育费附加")
    private BigDecimal b200005222114;

    @LogField(tableName = "b200005", value = "b200005_222115", valueName = "印花税")
    @ApiModelProperty(value = "印花税")
    private BigDecimal b200005222115;

    @LogField(tableName = "b200005", value = "b200005_222116", valueName = "待抵扣进项税额")
    @ApiModelProperty(value = "待抵扣进项税额")
    private BigDecimal b200005222116;

    @LogField(tableName = "b200005", value = "b200005_222117", valueName = "待认证进项税额")
    @ApiModelProperty(value = "待认证进项税额")
    private BigDecimal b200005222117;

    @LogField(tableName = "b200005", value = "b200005_222118", valueName = "预交增值税")
    @ApiModelProperty(value = "预交增值税")
    private BigDecimal b200005222118;

    @LogField(tableName = "b200005", value = "b200005_222119", valueName = "待转销项税额")
    @ApiModelProperty(value = "待转销项税额")
    private BigDecimal b200005222119;

    @LogField(tableName = "b200005", value = "b200005_222120", valueName = "增值税留抵税额")
    @ApiModelProperty(value = "增值税留抵税额")
    private BigDecimal b200005222120;

    @LogField(tableName = "b200005", value = "b200005_222121", valueName = "简易计税")
    @ApiModelProperty(value = "简易计税")
    private BigDecimal b200005222121;

    @LogField(tableName = "b200005", value = "b200005_222122", valueName = "转让金融商品应交增值税")
    @ApiModelProperty(value = "转让金融商品应交增值税")
    private BigDecimal b200005222122;

    @LogField(tableName = "b200005", value = "b200005_222123", valueName = "代扣代缴增值税")
    @ApiModelProperty(value = "代扣代缴增值税")
    private BigDecimal b200005222123;

    @LogField(tableName = "b200005", value = "b200005_2231", valueName = "应付利息")
    @ApiModelProperty(value = "应付利息")
    private BigDecimal b2000052231;

    @LogField(tableName = "b200005", value = "b200005_2232", valueName = "应付股利")
    @ApiModelProperty(value = "应付股利")
    private BigDecimal b2000052232;

    @LogField(tableName = "b200005", value = "b200005_2241", valueName = "其他应付款")
    @ApiModelProperty(value = "其他应付款")
    private BigDecimal b2000052241;

    @LogField(tableName = "b200005", value = "b200005_2251", valueName = "应付保单红利")
    @ApiModelProperty(value = "应付保单红利")
    private BigDecimal b2000052251;

    @LogField(tableName = "b200005", value = "b200005_2261", valueName = "应付分保账款")
    @ApiModelProperty(value = "应付分保账款")
    private BigDecimal b2000052261;

    @LogField(tableName = "b200005", value = "b200005_2311", valueName = "代理买卖证券款")
    @ApiModelProperty(value = "代理买卖证券款")
    private BigDecimal b2000052311;

    @LogField(tableName = "b200005", value = "b200005_2312", valueName = "代理承销证券款")
    @ApiModelProperty(value = "代理承销证券款")
    private BigDecimal b2000052312;

    @LogField(tableName = "b200005", value = "b200005_2313", valueName = "代理兑付证券款")
    @ApiModelProperty(value = "代理兑付证券款")
    private BigDecimal b2000052313;

    @LogField(tableName = "b200005", value = "b200005_2314", valueName = "代理业务负债")
    @ApiModelProperty(value = "代理业务负债")
    private BigDecimal b2000052314;

    @LogField(tableName = "b200005", value = "b200005_2401", valueName = "递延收益")
    @ApiModelProperty(value = "递延收益")
    private BigDecimal b2000052401;

    @LogField(tableName = "b200005", value = "b200005_2245", valueName = "持有待售负债")
    @ApiModelProperty(value = "持有待售负债")
    private BigDecimal b2000052245;

    @LogField(tableName = "b200005", value = "b200005_2501", valueName = "长期借款")
    @ApiModelProperty(value = "长期借款")
    private BigDecimal b2000052501;

    @LogField(tableName = "b200005", value = "b200005_2502", valueName = "应付债券")
    @ApiModelProperty(value = "应付债券")
    private BigDecimal b2000052502;

    @LogField(tableName = "b200005", value = "b200005_2601", valueName = "未到期责任准备金")
    @ApiModelProperty(value = "未到期责任准备金")
    private BigDecimal b2000052601;

    @LogField(tableName = "b200005", value = "b200005_2602", valueName = "保险责任准备金")
    @ApiModelProperty(value = "保险责任准备金")
    private BigDecimal b2000052602;

    @LogField(tableName = "b200005", value = "b200005_2611", valueName = "保户储金")
    @ApiModelProperty(value = "保户储金")
    private BigDecimal b2000052611;

    @LogField(tableName = "b200005", value = "b200005_2621", valueName = "独立账户负债")
    @ApiModelProperty(value = "独立账户负债")
    private BigDecimal b2000052621;

    @LogField(tableName = "b200005", value = "b200005_2701", valueName = "长期应付款")
    @ApiModelProperty(value = "长期应付款")
    private BigDecimal b2000052701;

    @LogField(tableName = "b200005", value = "b200005_2702", valueName = "未确认融资费用")
    @ApiModelProperty(value = "未确认融资费用")
    private BigDecimal b2000052702;

    @LogField(tableName = "b200005", value = "b200005_2711", valueName = "专项应付款")
    @ApiModelProperty(value = "专项应付款")
    private BigDecimal b2000052711;

    @LogField(tableName = "b200005", value = "b200005_2801", valueName = "预计负债")
    @ApiModelProperty(value = "预计负债")
    private BigDecimal b2000052801;

    @LogField(tableName = "b200005", value = "b200005_2901", valueName = "递延所得税负债")
    @ApiModelProperty(value = "递延所得税负债")
    private BigDecimal b2000052901;

    @LogField(tableName = "b200005", value = "b200005_3001", valueName = "清算资金往来")
    @ApiModelProperty(value = "清算资金往来")
    private BigDecimal b2000053001;

    @LogField(tableName = "b200005", value = "b200005_3002", valueName = "货币兑换")
    @ApiModelProperty(value = "货币兑换")
    private BigDecimal b2000053002;

    @LogField(tableName = "b200005", value = "b200005_3101", valueName = "衍生工具")
    @ApiModelProperty(value = "衍生工具")
    private BigDecimal b2000053101;

    @LogField(tableName = "b200005", value = "b200005_3201", valueName = "套期工具")
    @ApiModelProperty(value = "套期工具")
    private BigDecimal b2000053201;

    @LogField(tableName = "b200005", value = "b200005_3202", valueName = "被套期项目")
    @ApiModelProperty(value = "被套期项目")
    private BigDecimal b2000053202;

    @LogField(tableName = "b200005", value = "b200005_4001", valueName = "实收资本")
    @ApiModelProperty(value = "实收资本")
    private BigDecimal b2000054001;

    @LogField(tableName = "b200005", value = "b200005_4002", valueName = "资本公积")
    @ApiModelProperty(value = "资本公积")
    private BigDecimal b2000054002;

    @LogField(tableName = "b200005", value = "b200005_4003", valueName = "其他综合收益")
    @ApiModelProperty(value = "其他综合收益")
    private BigDecimal b2000054003;

    @LogField(tableName = "b200005", value = "b200005_4101", valueName = "盈余公积")
    @ApiModelProperty(value = "盈余公积")
    private BigDecimal b2000054101;

    @LogField(tableName = "b200005", value = "b200005_4102", valueName = "一般风险准备")
    @ApiModelProperty(value = "一般风险准备")
    private BigDecimal b2000054102;

    @LogField(tableName = "b200005", value = "b200005_4103", valueName = "本年利润")
    @ApiModelProperty(value = "本年利润")
    private BigDecimal b2000054103;

    @LogField(tableName = "b200005", value = "b200005_4104", valueName = "利润分配")
    @ApiModelProperty(value = "利润分配")
    private BigDecimal b2000054104;

    @LogField(tableName = "b200005", value = "b200005_4201", valueName = "库存股")
    @ApiModelProperty(value = "库存股")
    private BigDecimal b2000054201;

    @LogField(tableName = "b200005", value = "b200005_4301", valueName = "专项储备")
    @ApiModelProperty(value = "专项储备")
    private BigDecimal b2000054301;

    @LogField(tableName = "b200005", value = "b200005_5001", valueName = "生产成本")
    @ApiModelProperty(value = "生产成本")
    private BigDecimal b2000055001;

    @LogField(tableName = "b200005", value = "b200005_500101", valueName = "直接人工")
    @ApiModelProperty(value = "直接人工")
    private BigDecimal b200005500101;

    @LogField(tableName = "b200005", value = "b200005_500102", valueName = "直接材料")
    @ApiModelProperty(value = "直接材料")
    private BigDecimal b200005500102;

    @LogField(tableName = "b200005", value = "b200005_500103", valueName = "制造费用")
    @ApiModelProperty(value = "制造费用")
    private BigDecimal b200005500103;

    @LogField(tableName = "b200005", value = "b200005_500104", valueName = "职工工资")
    @ApiModelProperty(value = "职工工资")
    private BigDecimal b200005500104;

    @LogField(tableName = "b200005", value = "b200005_500105", valueName = "年终奖")
    @ApiModelProperty(value = "年终奖")
    private BigDecimal b200005500105;

    @LogField(tableName = "b200005", value = "b200005_500106", valueName = "津贴与补贴")
    @ApiModelProperty(value = "津贴与补贴")
    private BigDecimal b200005500106;

    @LogField(tableName = "b200005", value = "b200005_500107", valueName = "股权激励")
    @ApiModelProperty(value = "股权激励")
    private BigDecimal b200005500107;

    @LogField(tableName = "b200005", value = "b200005_500108", valueName = "职工教育经费")
    @ApiModelProperty(value = "职工教育经费")
    private BigDecimal b200005500108;

    @LogField(tableName = "b200005", value = "b200005_500109", valueName = "职工培训费用")
    @ApiModelProperty(value = "职工培训费用")
    private BigDecimal b200005500109;

    @LogField(tableName = "b200005", value = "b200005_500110", valueName = "工会经费")
    @ApiModelProperty(value = "工会经费")
    private BigDecimal b200005500110;

    @LogField(tableName = "b200005", value = "b200005_500111", valueName = "社会保险费")
    @ApiModelProperty(value = "社会保险费")
    private BigDecimal b200005500111;

    @LogField(tableName = "b200005", value = "b200005_500112", valueName = "住房公积金")
    @ApiModelProperty(value = "住房公积金")
    private BigDecimal b200005500112;

    @LogField(tableName = "b200005", value = "b200005_500113", valueName = "职工福利费")
    @ApiModelProperty(value = "职工福利费")
    private BigDecimal b200005500113;

    @LogField(tableName = "b200005", value = "b200005_500114", valueName = "补充养老保险")
    @ApiModelProperty(value = "补充养老保险")
    private BigDecimal b200005500114;

    @LogField(tableName = "b200005", value = "b200005_500115", valueName = "补充医疗保险")
    @ApiModelProperty(value = "补充医疗保险")
    private BigDecimal b200005500115;

    @LogField(tableName = "b200005", value = "b200005_500116", valueName = "其他职工工资")
    @ApiModelProperty(value = "其他职工工资")
    private BigDecimal b200005500116;

    @LogField(tableName = "b200005", value = "b200005_500117", valueName = "商业保险费")
    @ApiModelProperty(value = "商业保险费")
    private BigDecimal b200005500117;

    @LogField(tableName = "b200005", value = "b200005_500118", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200005500118;

    @LogField(tableName = "b200005", value = "b200005_5101", valueName = "制造费用")
    @ApiModelProperty(value = "制造费用")
    private BigDecimal b2000055101;

    @LogField(tableName = "b200005", value = "b200005_510101", valueName = "职工工资")
    @ApiModelProperty(value = "职工工资")
    private BigDecimal b200005510101;

    @LogField(tableName = "b200005", value = "b200005_510102", valueName = "年终奖")
    @ApiModelProperty(value = "年终奖")
    private BigDecimal b200005510102;

    @LogField(tableName = "b200005", value = "b200005_510103", valueName = "津贴与补贴")
    @ApiModelProperty(value = "津贴与补贴")
    private BigDecimal b200005510103;

    @LogField(tableName = "b200005", value = "b200005_510104", valueName = "股权激励")
    @ApiModelProperty(value = "股权激励")
    private BigDecimal b200005510104;

    @LogField(tableName = "b200005", value = "b200005_510105", valueName = "社会保险费")
    @ApiModelProperty(value = "社会保险费")
    private BigDecimal b200005510105;

    @LogField(tableName = "b200005", value = "b200005_510106", valueName = "住房公积金")
    @ApiModelProperty(value = "住房公积金")
    private BigDecimal b200005510106;

    @LogField(tableName = "b200005", value = "b200005_510107", valueName = "补充养老保险")
    @ApiModelProperty(value = "补充养老保险")
    private BigDecimal b200005510107;

    @LogField(tableName = "b200005", value = "b200005_510108", valueName = "补充医疗保险")
    @ApiModelProperty(value = "补充医疗保险")
    private BigDecimal b200005510108;

    @LogField(tableName = "b200005", value = "b200005_510109", valueName = "职工教育经费")
    @ApiModelProperty(value = "职工教育经费")
    private BigDecimal b200005510109;

    @LogField(tableName = "b200005", value = "b200005_510110", valueName = "职工培训费用")
    @ApiModelProperty(value = "职工培训费用")
    private BigDecimal b200005510110;

    @LogField(tableName = "b200005", value = "b200005_510111", valueName = "其他职工工资")
    @ApiModelProperty(value = "其他职工工资")
    private BigDecimal b200005510111;

    @LogField(tableName = "b200005", value = "b200005_510112", valueName = "职工福利费")
    @ApiModelProperty(value = "职工福利费")
    private BigDecimal b200005510112;

    @LogField(tableName = "b200005", value = "b200005_510113", valueName = "工会经费")
    @ApiModelProperty(value = "工会经费")
    private BigDecimal b200005510113;

    @LogField(tableName = "b200005", value = "b200005_510114", valueName = "商业保险费")
    @ApiModelProperty(value = "商业保险费")
    private BigDecimal b200005510114;

    @LogField(tableName = "b200005", value = "b200005_510115", valueName = "折旧费")
    @ApiModelProperty(value = "折旧费")
    private BigDecimal b200005510115;

    @LogField(tableName = "b200005", value = "b200005_510116", valueName = "修理费")
    @ApiModelProperty(value = "修理费")
    private BigDecimal b200005510116;

    @LogField(tableName = "b200005", value = "b200005_510117", valueName = "机物料消耗")
    @ApiModelProperty(value = "机物料消耗")
    private BigDecimal b200005510117;

    @LogField(tableName = "b200005", value = "b200005_510118", valueName = "低值易耗品摊销")
    @ApiModelProperty(value = "低值易耗品摊销")
    private BigDecimal b200005510118;

    @LogField(tableName = "b200005", value = "b200005_510119", valueName = "租赁费")
    @ApiModelProperty(value = "租赁费")
    private BigDecimal b200005510119;

    @LogField(tableName = "b200005", value = "b200005_510120", valueName = "运杂费")
    @ApiModelProperty(value = "运杂费")
    private BigDecimal b200005510120;

    @LogField(tableName = "b200005", value = "b200005_510121", valueName = "保险费")
    @ApiModelProperty(value = "保险费")
    private BigDecimal b200005510121;

    @LogField(tableName = "b200005", value = "b200005_510122", valueName = "差旅费")
    @ApiModelProperty(value = "差旅费")
    private BigDecimal b200005510122;

    @LogField(tableName = "b200005", value = "b200005_510123", valueName = "水电费")
    @ApiModelProperty(value = "水电费")
    private BigDecimal b200005510123;

    @LogField(tableName = "b200005", value = "b200005_510124", valueName = "外部加工费")
    @ApiModelProperty(value = "外部加工费")
    private BigDecimal b200005510124;

    @LogField(tableName = "b200005", value = "b200005_510125", valueName = "厂房租金")
    @ApiModelProperty(value = "厂房租金")
    private BigDecimal b200005510125;

    @LogField(tableName = "b200005", value = "b200005_510126", valueName = "装卸费")
    @ApiModelProperty(value = "装卸费")
    private BigDecimal b200005510126;

    @LogField(tableName = "b200005", value = "b200005_510127", valueName = "设计制图费")
    @ApiModelProperty(value = "设计制图费")
    private BigDecimal b200005510127;

    @LogField(tableName = "b200005", value = "b200005_510128", valueName = "劳动保护费")
    @ApiModelProperty(value = "劳动保护费")
    private BigDecimal b200005510128;

    @LogField(tableName = "b200005", value = "b200005_510129", valueName = "无形资产摊销费")
    @ApiModelProperty(value = "无形资产摊销费")
    private BigDecimal b200005510129;

    @LogField(tableName = "b200005", value = "b200005_510130", valueName = "长期待摊费用摊销费")
    @ApiModelProperty(value = "长期待摊费用摊销费")
    private BigDecimal b200005510130;

    @LogField(tableName = "b200005", value = "b200005_510131", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200005510131;

    @LogField(tableName = "b200005", value = "b200005_5201", valueName = "劳务成本")
    @ApiModelProperty(value = "劳务成本")
    private BigDecimal b2000055201;

    @LogField(tableName = "b200005", value = "b200005_520101", valueName = "职工工资")
    @ApiModelProperty(value = "职工工资")
    private BigDecimal b200005520101;

    @LogField(tableName = "b200005", value = "b200005_520102", valueName = "年终奖")
    @ApiModelProperty(value = "年终奖")
    private BigDecimal b200005520102;

    @LogField(tableName = "b200005", value = "b200005_520103", valueName = "津贴与补贴")
    @ApiModelProperty(value = "津贴与补贴")
    private BigDecimal b200005520103;

    @LogField(tableName = "b200005", value = "b200005_520104", valueName = "股权激励")
    @ApiModelProperty(value = "股权激励")
    private BigDecimal b200005520104;

    @LogField(tableName = "b200005", value = "b200005_520105", valueName = "职工教育经费")
    @ApiModelProperty(value = "职工教育经费")
    private BigDecimal b200005520105;

    @LogField(tableName = "b200005", value = "b200005_520106", valueName = "职工培训费用")
    @ApiModelProperty(value = "职工培训费用")
    private BigDecimal b200005520106;

    @LogField(tableName = "b200005", value = "b200005_520107", valueName = "工会经费")
    @ApiModelProperty(value = "工会经费")
    private BigDecimal b200005520107;

    @LogField(tableName = "b200005", value = "b200005_520108", valueName = "社会保险费")
    @ApiModelProperty(value = "社会保险费")
    private BigDecimal b200005520108;

    @LogField(tableName = "b200005", value = "b200005_520109", valueName = "住房公积金")
    @ApiModelProperty(value = "住房公积金")
    private BigDecimal b200005520109;

    @LogField(tableName = "b200005", value = "b200005_520110", valueName = "职工福利费")
    @ApiModelProperty(value = "职工福利费")
    private BigDecimal b200005520110;

    @LogField(tableName = "b200005", value = "b200005_520111", valueName = "补充养老保险")
    @ApiModelProperty(value = "补充养老保险")
    private BigDecimal b200005520111;

    @LogField(tableName = "b200005", value = "b200005_520112", valueName = "补充医疗保险")
    @ApiModelProperty(value = "补充医疗保险")
    private BigDecimal b200005520112;

    @LogField(tableName = "b200005", value = "b200005_520113", valueName = "其他职工工资")
    @ApiModelProperty(value = "其他职工工资")
    private BigDecimal b200005520113;

    @LogField(tableName = "b200005", value = "b200005_520114", valueName = "商业保险费")
    @ApiModelProperty(value = "商业保险费")
    private BigDecimal b200005520114;

    @LogField(tableName = "b200005", value = "b200005_520115", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200005520115;

    @LogField(tableName = "b200005", value = "b200005_5301", valueName = "研发支出")
    @ApiModelProperty(value = "研发支出")
    private BigDecimal b2000055301;

    @LogField(tableName = "b200005", value = "b200005_530101", valueName = "资本化支出")
    @ApiModelProperty(value = "资本化支出")
    private BigDecimal b200005530101;

    @LogField(tableName = "b200005", value = "b200005_53010101", valueName = "人工成本")
    @ApiModelProperty(value = "人工成本")
    private BigDecimal b20000553010101;

    @LogField(tableName = "b200005", value = "b200005_5301010101", valueName = "职工工资")
    @ApiModelProperty(value = "职工工资")
    private BigDecimal b2000055301010101;

    @LogField(tableName = "b200005", value = "b200005_5301010102", valueName = "年终奖")
    @ApiModelProperty(value = "年终奖")
    private BigDecimal b2000055301010102;

    @LogField(tableName = "b200005", value = "b200005_5301010103", valueName = "津贴与补贴")
    @ApiModelProperty(value = "津贴与补贴")
    private BigDecimal b2000055301010103;

    @LogField(tableName = "b200005", value = "b200005_5301010104", valueName = "股权激励")
    @ApiModelProperty(value = "股权激励")
    private BigDecimal b2000055301010104;

    @LogField(tableName = "b200005", value = "b200005_5301010105", valueName = "职工教育经费")
    @ApiModelProperty(value = "职工教育经费")
    private BigDecimal b2000055301010105;

    @LogField(tableName = "b200005", value = "b200005_5301010106", valueName = "工会经费")
    @ApiModelProperty(value = "工会经费")
    private BigDecimal b2000055301010106;

    @LogField(tableName = "b200005", value = "b200005_5301010107", valueName = "社会保险费")
    @ApiModelProperty(value = "社会保险费")
    private BigDecimal b2000055301010107;

    @LogField(tableName = "b200005", value = "b200005_5301010108", valueName = "商业保险费")
    @ApiModelProperty(value = "商业保险费")
    private BigDecimal b2000055301010108;

    @LogField(tableName = "b200005", value = "b200005_5301010109", valueName = "住房公积金")
    @ApiModelProperty(value = "住房公积金")
    private BigDecimal b2000055301010109;

    @LogField(tableName = "b200005", value = "b200005_5301010110", valueName = "职工福利费")
    @ApiModelProperty(value = "职工福利费")
    private BigDecimal b2000055301010110;

    @LogField(tableName = "b200005", value = "b200005_5301010111", valueName = "补充养老保险")
    @ApiModelProperty(value = "补充养老保险")
    private BigDecimal b2000055301010111;

    @LogField(tableName = "b200005", value = "b200005_5301010112", valueName = "补充医疗保险")
    @ApiModelProperty(value = "补充医疗保险")
    private BigDecimal b2000055301010112;

    @LogField(tableName = "b200005", value = "b200005_5301010113", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000055301010113;

    @LogField(tableName = "b200005", value = "b200005_53010102", valueName = "劳务费")
    @ApiModelProperty(value = "劳务费")
    private BigDecimal b20000553010102;

    @LogField(tableName = "b200005", value = "b200005_53010103", valueName = "直接投入")
    @ApiModelProperty(value = "直接投入")
    private BigDecimal b20000553010103;

    @LogField(tableName = "b200005", value = "b200005_5301010301", valueName = "材料费用")
    @ApiModelProperty(value = "材料费用")
    private BigDecimal b2000055301010301;

    @LogField(tableName = "b200005", value = "b200005_5301010302", valueName = "燃料费用")
    @ApiModelProperty(value = "燃料费用")
    private BigDecimal b2000055301010302;

    @LogField(tableName = "b200005", value = "b200005_5301010303", valueName = "动力费用")
    @ApiModelProperty(value = "动力费用")
    private BigDecimal b2000055301010303;

    @LogField(tableName = "b200005", value = "b200005_5301010304", valueName = "试用品开发及制造费")
    @ApiModelProperty(value = "试用品开发及制造费")
    private BigDecimal b2000055301010304;

    @LogField(tableName = "b200005", value = "b200005_5301010305", valueName = "样品测试购置费")
    @ApiModelProperty(value = "样品测试购置费")
    private BigDecimal b2000055301010305;

    @LogField(tableName = "b200005", value = "b200005_5301010306", valueName = "试制品检验费")
    @ApiModelProperty(value = "试制品检验费")
    private BigDecimal b2000055301010306;

    @LogField(tableName = "b200005", value = "b200005_5301010307", valueName = "运行维护维修费")
    @ApiModelProperty(value = "运行维护维修费")
    private BigDecimal b2000055301010307;

    @LogField(tableName = "b200005", value = "b200005_5301010308", valueName = "运行调整费")
    @ApiModelProperty(value = "运行调整费")
    private BigDecimal b2000055301010308;

    @LogField(tableName = "b200005", value = "b200005_5301010309", valueName = "运行检测费")
    @ApiModelProperty(value = "运行检测费")
    private BigDecimal b2000055301010309;

    @LogField(tableName = "b200005", value = "b200005_5301010310", valueName = "仪器租赁费")
    @ApiModelProperty(value = "仪器租赁费")
    private BigDecimal b2000055301010310;

    @LogField(tableName = "b200005", value = "b200005_5301010311", valueName = "设备租赁费")
    @ApiModelProperty(value = "设备租赁费")
    private BigDecimal b2000055301010311;

    @LogField(tableName = "b200005", value = "b200005_5301010312", valueName = "房屋租赁费")
    @ApiModelProperty(value = "房屋租赁费")
    private BigDecimal b2000055301010312;

    @LogField(tableName = "b200005", value = "b200005_5301010313", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000055301010313;

    @LogField(tableName = "b200005", value = "b200005_53010104", valueName = "低值易耗品摊销")
    @ApiModelProperty(value = "低值易耗品摊销")
    private BigDecimal b20000553010104;

    @LogField(tableName = "b200005", value = "b200005_5301010401", valueName = "直接消耗")
    @ApiModelProperty(value = "直接消耗")
    private BigDecimal b2000055301010401;

    @LogField(tableName = "b200005", value = "b200005_5301010402", valueName = "其他消耗")
    @ApiModelProperty(value = "其他消耗")
    private BigDecimal b2000055301010402;

    @LogField(tableName = "b200005", value = "b200005_53010105", valueName = "物料消耗")
    @ApiModelProperty(value = "物料消耗")
    private BigDecimal b20000553010105;

    @LogField(tableName = "b200005", value = "b200005_5301010501", valueName = "直接消耗")
    @ApiModelProperty(value = "直接消耗")
    private BigDecimal b2000055301010501;

    @LogField(tableName = "b200005", value = "b200005_5301010502", valueName = "其他消耗")
    @ApiModelProperty(value = "其他消耗")
    private BigDecimal b2000055301010502;

    @LogField(tableName = "b200005", value = "b200005_53010106", valueName = "折旧费用")
    @ApiModelProperty(value = "折旧费用")
    private BigDecimal b20000553010106;

    @LogField(tableName = "b200005", value = "b200005_5301010601", valueName = "仪器折旧费")
    @ApiModelProperty(value = "仪器折旧费")
    private BigDecimal b2000055301010601;

    @LogField(tableName = "b200005", value = "b200005_5301010602", valueName = "设备折旧费")
    @ApiModelProperty(value = "设备折旧费")
    private BigDecimal b2000055301010602;

    @LogField(tableName = "b200005", value = "b200005_5301010603", valueName = "房屋折旧费")
    @ApiModelProperty(value = "房屋折旧费")
    private BigDecimal b2000055301010603;

    @LogField(tableName = "b200005", value = "b200005_5301010604", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000055301010604;

    @LogField(tableName = "b200005", value = "b200005_53010107", valueName = "长期待摊费用")
    @ApiModelProperty(value = "长期待摊费用")
    private BigDecimal b20000553010107;

    @LogField(tableName = "b200005", value = "b200005_5301010701", valueName = "设施改建及改装费")
    @ApiModelProperty(value = "设施改建及改装费")
    private BigDecimal b2000055301010701;

    @LogField(tableName = "b200005", value = "b200005_5301010702", valueName = "设施装修及修理费")
    @ApiModelProperty(value = "设施装修及修理费")
    private BigDecimal b2000055301010702;

    @LogField(tableName = "b200005", value = "b200005_5301010703", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000055301010703;

    @LogField(tableName = "b200005", value = "b200005_53010108", valueName = "无形资产摊销")
    @ApiModelProperty(value = "无形资产摊销")
    private BigDecimal b20000553010108;

    @LogField(tableName = "b200005", value = "b200005_5301010801", valueName = "软件")
    @ApiModelProperty(value = "软件")
    private BigDecimal b2000055301010801;

    @LogField(tableName = "b200005", value = "b200005_5301010802", valueName = "专利权")
    @ApiModelProperty(value = "专利权")
    private BigDecimal b2000055301010802;

    @LogField(tableName = "b200005", value = "b200005_5301010803", valueName = "商标")
    @ApiModelProperty(value = "商标")
    private BigDecimal b2000055301010803;

    @LogField(tableName = "b200005", value = "b200005_5301010804", valueName = "非专利技术")
    @ApiModelProperty(value = "非专利技术")
    private BigDecimal b2000055301010804;

    @LogField(tableName = "b200005", value = "b200005_5301010805", valueName = "知识产权")
    @ApiModelProperty(value = "知识产权")
    private BigDecimal b2000055301010805;

    @LogField(tableName = "b200005", value = "b200005_5301010806", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000055301010806;

    @LogField(tableName = "b200005", value = "b200005_53010109", valueName = "设计费用")
    @ApiModelProperty(value = "设计费用")
    private BigDecimal b20000553010109;

    @LogField(tableName = "b200005", value = "b200005_5301010901", valueName = "新产品设计费")
    @ApiModelProperty(value = "新产品设计费")
    private BigDecimal b2000055301010901;

    @LogField(tableName = "b200005", value = "b200005_5301010902", valueName = "新工艺规程制定费")
    @ApiModelProperty(value = "新工艺规程制定费")
    private BigDecimal b2000055301010902;

    @LogField(tableName = "b200005", value = "b200005_5301010903", valueName = "新产品与新工艺工序设计费")
    @ApiModelProperty(value = "新产品与新工艺工序设计费")
    private BigDecimal b2000055301010903;

    @LogField(tableName = "b200005", value = "b200005_5301010904", valueName = "新产品与新工艺技术规范费")
    @ApiModelProperty(value = "新产品与新工艺技术规范费")
    private BigDecimal b2000055301010904;

    @LogField(tableName = "b200005", value = "b200005_5301010905", valueName = "新产品与新工艺操作设计费")
    @ApiModelProperty(value = "新产品与新工艺操作设计费")
    private BigDecimal b2000055301010905;

    @LogField(tableName = "b200005", value = "b200005_5301010906", valueName = "创意设计活动费")
    @ApiModelProperty(value = "创意设计活动费")
    private BigDecimal b2000055301010906;

    @LogField(tableName = "b200005", value = "b200005_5301010907", valueName = "其他设计费用")
    @ApiModelProperty(value = "其他设计费用")
    private BigDecimal b2000055301010907;

    @LogField(tableName = "b200005", value = "b200005_53010110", valueName = "装备调试费")
    @ApiModelProperty(value = "装备调试费")
    private BigDecimal b20000553010110;

    @LogField(tableName = "b200005", value = "b200005_53010111", valueName = "试验费")
    @ApiModelProperty(value = "试验费")
    private BigDecimal b20000553010111;

    @LogField(tableName = "b200005", value = "b200005_5301011101", valueName = "新药临床试验费")
    @ApiModelProperty(value = "新药临床试验费")
    private BigDecimal b2000055301011101;

    @LogField(tableName = "b200005", value = "b200005_5301011102", valueName = "勘探开发技术现场试验费")
    @ApiModelProperty(value = "勘探开发技术现场试验费")
    private BigDecimal b2000055301011102;

    @LogField(tableName = "b200005", value = "b200005_5301011103", valueName = "田间试验费")
    @ApiModelProperty(value = "田间试验费")
    private BigDecimal b2000055301011103;

    @LogField(tableName = "b200005", value = "b200005_5301011104", valueName = "其他试验费用")
    @ApiModelProperty(value = "其他试验费用")
    private BigDecimal b2000055301011104;

    @LogField(tableName = "b200005", value = "b200005_53010112", valueName = "图书资料费")
    @ApiModelProperty(value = "图书资料费")
    private BigDecimal b20000553010112;

    @LogField(tableName = "b200005", value = "b200005_53010113", valueName = "资料翻译费")
    @ApiModelProperty(value = "资料翻译费")
    private BigDecimal b20000553010113;

    @LogField(tableName = "b200005", value = "b200005_53010114", valueName = "专家咨询费")
    @ApiModelProperty(value = "专家咨询费")
    private BigDecimal b20000553010114;

    @LogField(tableName = "b200005", value = "b200005_53010115", valueName = "高新科技研发保险费")
    @ApiModelProperty(value = "高新科技研发保险费")
    private BigDecimal b20000553010115;

    @LogField(tableName = "b200005", value = "b200005_53010116", valueName = "成果费用")
    @ApiModelProperty(value = "成果费用")
    private BigDecimal b20000553010116;

    @LogField(tableName = "b200005", value = "b200005_5301011601", valueName = "检索费")
    @ApiModelProperty(value = "检索费")
    private BigDecimal b2000055301011601;

    @LogField(tableName = "b200005", value = "b200005_5301011602", valueName = "分析费")
    @ApiModelProperty(value = "分析费")
    private BigDecimal b2000055301011602;

    @LogField(tableName = "b200005", value = "b200005_5301011603", valueName = "评议费")
    @ApiModelProperty(value = "评议费")
    private BigDecimal b2000055301011603;

    @LogField(tableName = "b200005", value = "b200005_5301011604", valueName = "论证费")
    @ApiModelProperty(value = "论证费")
    private BigDecimal b2000055301011604;

    @LogField(tableName = "b200005", value = "b200005_5301011605", valueName = "鉴定费")
    @ApiModelProperty(value = "鉴定费")
    private BigDecimal b2000055301011605;

    @LogField(tableName = "b200005", value = "b200005_5301011606", valueName = "评审费")
    @ApiModelProperty(value = "评审费")
    private BigDecimal b2000055301011606;

    @LogField(tableName = "b200005", value = "b200005_5301011607", valueName = "评估费")
    @ApiModelProperty(value = "评估费")
    private BigDecimal b2000055301011607;

    @LogField(tableName = "b200005", value = "b200005_5301011608", valueName = "验收费")
    @ApiModelProperty(value = "验收费")
    private BigDecimal b2000055301011608;

    @LogField(tableName = "b200005", value = "b200005_5301011609", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000055301011609;

    @LogField(tableName = "b200005", value = "b200005_53010117", valueName = "知识产权费用")
    @ApiModelProperty(value = "知识产权费用")
    private BigDecimal b20000553010117;

    @LogField(tableName = "b200005", value = "b200005_5301011701", valueName = "申请费")
    @ApiModelProperty(value = "申请费")
    private BigDecimal b2000055301011701;

    @LogField(tableName = "b200005", value = "b200005_5301011702", valueName = "注册费")
    @ApiModelProperty(value = "注册费")
    private BigDecimal b2000055301011702;

    @LogField(tableName = "b200005", value = "b200005_5301011703", valueName = "代理费")
    @ApiModelProperty(value = "代理费")
    private BigDecimal b2000055301011703;

    @LogField(tableName = "b200005", value = "b200005_5301011704", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000055301011704;

    @LogField(tableName = "b200005", value = "b200005_53010118", valueName = "差旅费")
    @ApiModelProperty(value = "差旅费")
    private BigDecimal b20000553010118;

    @LogField(tableName = "b200005", value = "b200005_53010119", valueName = "会议费")
    @ApiModelProperty(value = "会议费")
    private BigDecimal b20000553010119;

    @LogField(tableName = "b200005", value = "b200005_53010120", valueName = "通讯费")
    @ApiModelProperty(value = "通讯费")
    private BigDecimal b20000553010120;

    @LogField(tableName = "b200005", value = "b200005_53010121", valueName = "办公费")
    @ApiModelProperty(value = "办公费")
    private BigDecimal b20000553010121;

    @LogField(tableName = "b200005", value = "b200005_53010122", valueName = "外事费")
    @ApiModelProperty(value = "外事费")
    private BigDecimal b20000553010122;

    @LogField(tableName = "b200005", value = "b200005_53010123", valueName = "研发人员培训费")
    @ApiModelProperty(value = "研发人员培训费")
    private BigDecimal b20000553010123;

    @LogField(tableName = "b200005", value = "b200005_53010124", valueName = "研发人员培养费")
    @ApiModelProperty(value = "研发人员培养费")
    private BigDecimal b20000553010124;

    @LogField(tableName = "b200005", value = "b200005_53010125", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b20000553010125;

    @LogField(tableName = "b200005", value = "b200005_53010126", valueName = "外包费用")
    @ApiModelProperty(value = "外包费用")
    private BigDecimal b20000553010126;

    @LogField(tableName = "b200005", value = "b200005_5301012601", valueName = "委托研发（境内进行）")
    @ApiModelProperty(value = "委托研发（境内进行）")
    private BigDecimal b2000055301012601;

    @LogField(tableName = "b200005", value = "b200005_5301012602", valueName = "委托研发（境外机构）")
    @ApiModelProperty(value = "委托研发（境外机构）")
    private BigDecimal b2000055301012602;

    @LogField(tableName = "b200005", value = "b200005_5301012603", valueName = "委托研发（境外个人）")
    @ApiModelProperty(value = "委托研发（境外个人）")
    private BigDecimal b2000055301012603;

    @LogField(tableName = "b200005", value = "b200005_5301012604", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000055301012604;

    @LogField(tableName = "b200005", value = "b200005_530102", valueName = "费用化支出")
    @ApiModelProperty(value = "费用化支出")
    private BigDecimal b200005530102;

    @LogField(tableName = "b200005", value = "b200005_53010201", valueName = "人工成本")
    @ApiModelProperty(value = "人工成本")
    private BigDecimal b20000553010201;

    @LogField(tableName = "b200005", value = "b200005_5301020101", valueName = "职工工资")
    @ApiModelProperty(value = "职工工资")
    private BigDecimal b2000055301020101;

    @LogField(tableName = "b200005", value = "b200005_5301020102", valueName = "年终奖")
    @ApiModelProperty(value = "年终奖")
    private BigDecimal b2000055301020102;

    @LogField(tableName = "b200005", value = "b200005_5301020103", valueName = "津贴与补贴")
    @ApiModelProperty(value = "津贴与补贴")
    private BigDecimal b2000055301020103;

    @LogField(tableName = "b200005", value = "b200005_5301020104", valueName = "股权激励")
    @ApiModelProperty(value = "股权激励")
    private BigDecimal b2000055301020104;

    @LogField(tableName = "b200005", value = "b200005_5301020105", valueName = "职工教育经费")
    @ApiModelProperty(value = "职工教育经费")
    private BigDecimal b2000055301020105;

    @LogField(tableName = "b200005", value = "b200005_5301020106", valueName = "工会经费")
    @ApiModelProperty(value = "工会经费")
    private BigDecimal b2000055301020106;

    @LogField(tableName = "b200005", value = "b200005_5301020107", valueName = "社会保险费")
    @ApiModelProperty(value = "社会保险费")
    private BigDecimal b2000055301020107;

    @LogField(tableName = "b200005", value = "b200005_5301020108", valueName = "商业保险费")
    @ApiModelProperty(value = "商业保险费")
    private BigDecimal b2000055301020108;

    @LogField(tableName = "b200005", value = "b200005_5301020109", valueName = "住房公积金")
    @ApiModelProperty(value = "住房公积金")
    private BigDecimal b2000055301020109;

    @LogField(tableName = "b200005", value = "b200005_5301020110", valueName = "职工福利费")
    @ApiModelProperty(value = "职工福利费")
    private BigDecimal b2000055301020110;

    @LogField(tableName = "b200005", value = "b200005_5301020111", valueName = "补充养老保险")
    @ApiModelProperty(value = "补充养老保险")
    private BigDecimal b2000055301020111;

    @LogField(tableName = "b200005", value = "b200005_5301020112", valueName = "补充养老保险")
    @ApiModelProperty(value = "补充养老保险")
    private BigDecimal b2000055301020112;

    @LogField(tableName = "b200005", value = "b200005_5301020113", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000055301020113;

    @LogField(tableName = "b200005", value = "b200005_53010202", valueName = "劳务费")
    @ApiModelProperty(value = "劳务费")
    private BigDecimal b20000553010202;

    @LogField(tableName = "b200005", value = "b200005_53010203", valueName = "直接投入")
    @ApiModelProperty(value = "直接投入")
    private BigDecimal b20000553010203;

    @LogField(tableName = "b200005", value = "b200005_5301020301", valueName = "材料费用")
    @ApiModelProperty(value = "材料费用")
    private BigDecimal b2000055301020301;

    @LogField(tableName = "b200005", value = "b200005_5301020302", valueName = "燃料费用")
    @ApiModelProperty(value = "燃料费用")
    private BigDecimal b2000055301020302;

    @LogField(tableName = "b200005", value = "b200005_5301020303", valueName = "动力费用")
    @ApiModelProperty(value = "动力费用")
    private BigDecimal b2000055301020303;

    @LogField(tableName = "b200005", value = "b200005_5301020304", valueName = "试用品开发及制造费")
    @ApiModelProperty(value = "试用品开发及制造费")
    private BigDecimal b2000055301020304;

    @LogField(tableName = "b200005", value = "b200005_5301020305", valueName = "样品测试购置费")
    @ApiModelProperty(value = "样品测试购置费")
    private BigDecimal b2000055301020305;

    @LogField(tableName = "b200005", value = "b200005_5301020306", valueName = "试制品检验费")
    @ApiModelProperty(value = "试制品检验费")
    private BigDecimal b2000055301020306;

    @LogField(tableName = "b200005", value = "b200005_5301020307", valueName = "运行维护维修费")
    @ApiModelProperty(value = "运行维护维修费")
    private BigDecimal b2000055301020307;

    @LogField(tableName = "b200005", value = "b200005_5301020308", valueName = "运行调整费")
    @ApiModelProperty(value = "运行调整费")
    private BigDecimal b2000055301020308;

    @LogField(tableName = "b200005", value = "b200005_5301020309", valueName = "运行检测费")
    @ApiModelProperty(value = "运行检测费")
    private BigDecimal b2000055301020309;

    @LogField(tableName = "b200005", value = "b200005_5301020310", valueName = "仪器租赁费")
    @ApiModelProperty(value = "仪器租赁费")
    private BigDecimal b2000055301020310;

    @LogField(tableName = "b200005", value = "b200005_5301020311", valueName = "设备租赁费")
    @ApiModelProperty(value = "设备租赁费")
    private BigDecimal b2000055301020311;

    @LogField(tableName = "b200005", value = "b200005_5301020312", valueName = "房屋租赁费")
    @ApiModelProperty(value = "房屋租赁费")
    private BigDecimal b2000055301020312;

    @LogField(tableName = "b200005", value = "b200005_5301020313", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000055301020313;

    @LogField(tableName = "b200005", value = "b200005_53010204", valueName = "低值易耗品摊销")
    @ApiModelProperty(value = "低值易耗品摊销")
    private BigDecimal b20000553010204;

    @LogField(tableName = "b200005", value = "b200005_5301020401", valueName = "直接消耗")
    @ApiModelProperty(value = "直接消耗")
    private BigDecimal b2000055301020401;

    @LogField(tableName = "b200005", value = "b200005_5301020402", valueName = "其他消耗")
    @ApiModelProperty(value = "其他消耗")
    private BigDecimal b2000055301020402;

    @LogField(tableName = "b200005", value = "b200005_53010205", valueName = "物料消耗")
    @ApiModelProperty(value = "物料消耗")
    private BigDecimal b20000553010205;

    @LogField(tableName = "b200005", value = "b200005_5301020501", valueName = "直接消耗")
    @ApiModelProperty(value = "直接消耗")
    private BigDecimal b2000055301020501;

    @LogField(tableName = "b200005", value = "b200005_5301020502", valueName = "其他消耗")
    @ApiModelProperty(value = "其他消耗")
    private BigDecimal b2000055301020502;

    @LogField(tableName = "b200005", value = "b200005_53010206", valueName = "折旧费用")
    @ApiModelProperty(value = "折旧费用")
    private BigDecimal b20000553010206;

    @LogField(tableName = "b200005", value = "b200005_5301020601", valueName = "仪器折旧费")
    @ApiModelProperty(value = "仪器折旧费")
    private BigDecimal b2000055301020601;

    @LogField(tableName = "b200005", value = "b200005_5301020602", valueName = "设备折旧费")
    @ApiModelProperty(value = "设备折旧费")
    private BigDecimal b2000055301020602;

    @LogField(tableName = "b200005", value = "b200005_5301020603", valueName = "房屋折旧费")
    @ApiModelProperty(value = "房屋折旧费")
    private BigDecimal b2000055301020603;

    @LogField(tableName = "b200005", value = "b200005_5301020604", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000055301020604;

    @LogField(tableName = "b200005", value = "b200005_53010207", valueName = "长期待摊费用")
    @ApiModelProperty(value = "长期待摊费用")
    private BigDecimal b20000553010207;

    @LogField(tableName = "b200005", value = "b200005_5301020701", valueName = "设施改建及改装费")
    @ApiModelProperty(value = "设施改建及改装费")
    private BigDecimal b2000055301020701;

    @LogField(tableName = "b200005", value = "b200005_5301020702", valueName = "设施装修及修理费")
    @ApiModelProperty(value = "设施装修及修理费")
    private BigDecimal b2000055301020702;

    @LogField(tableName = "b200005", value = "b200005_5301020703", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000055301020703;

    @LogField(tableName = "b200005", value = "b200005_53010208", valueName = "无形资产摊销")
    @ApiModelProperty(value = "无形资产摊销")
    private BigDecimal b20000553010208;

    @LogField(tableName = "b200005", value = "b200005_5301020801", valueName = "软件")
    @ApiModelProperty(value = "软件")
    private BigDecimal b2000055301020801;

    @LogField(tableName = "b200005", value = "b200005_5301020802", valueName = "专利权")
    @ApiModelProperty(value = "专利权")
    private BigDecimal b2000055301020802;

    @LogField(tableName = "b200005", value = "b200005_5301020803", valueName = "商标")
    @ApiModelProperty(value = "商标")
    private BigDecimal b2000055301020803;

    @LogField(tableName = "b200005", value = "b200005_5301020804", valueName = "非专利技术")
    @ApiModelProperty(value = "非专利技术")
    private BigDecimal b2000055301020804;

    @LogField(tableName = "b200005", value = "b200005_5301020805", valueName = "知识产权")
    @ApiModelProperty(value = "知识产权")
    private BigDecimal b2000055301020805;

    @LogField(tableName = "b200005", value = "b200005_5301020806", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000055301020806;

    @LogField(tableName = "b200005", value = "b200005_53010209", valueName = "设计费用")
    @ApiModelProperty(value = "设计费用")
    private BigDecimal b20000553010209;

    @LogField(tableName = "b200005", value = "b200005_5301020901", valueName = "新产品设计费")
    @ApiModelProperty(value = "新产品设计费")
    private BigDecimal b2000055301020901;

    @LogField(tableName = "b200005", value = "b200005_5301020902", valueName = "新工艺规程制定费")
    @ApiModelProperty(value = "新工艺规程制定费")
    private BigDecimal b2000055301020902;

    @LogField(tableName = "b200005", value = "b200005_5301020903", valueName = "新产品与新工艺工序设计费")
    @ApiModelProperty(value = "新产品与新工艺工序设计费")
    private BigDecimal b2000055301020903;

    @LogField(tableName = "b200005", value = "b200005_5301020904", valueName = "新产品与新工艺技术规范费")
    @ApiModelProperty(value = "新产品与新工艺技术规范费")
    private BigDecimal b2000055301020904;

    @LogField(tableName = "b200005", value = "b200005_5301020905", valueName = "新产品与新工艺操作设计费")
    @ApiModelProperty(value = "新产品与新工艺操作设计费")
    private BigDecimal b2000055301020905;

    @LogField(tableName = "b200005", value = "b200005_5301020906", valueName = "创意设计活动费")
    @ApiModelProperty(value = "创意设计活动费")
    private BigDecimal b2000055301020906;

    @LogField(tableName = "b200005", value = "b200005_5301020907", valueName = "其他设计费用")
    @ApiModelProperty(value = "其他设计费用")
    private BigDecimal b2000055301020907;

    @LogField(tableName = "b200005", value = "b200005_53010210", valueName = "装备调试费")
    @ApiModelProperty(value = "装备调试费")
    private BigDecimal b20000553010210;

    @LogField(tableName = "b200005", value = "b200005_53010211", valueName = "试验费")
    @ApiModelProperty(value = "试验费")
    private BigDecimal b20000553010211;

    @LogField(tableName = "b200005", value = "b200005_5301021101", valueName = "新药临床试验费")
    @ApiModelProperty(value = "新药临床试验费")
    private BigDecimal b2000055301021101;

    @LogField(tableName = "b200005", value = "b200005_5301021102", valueName = "勘探开发技术现场试验费")
    @ApiModelProperty(value = "勘探开发技术现场试验费")
    private BigDecimal b2000055301021102;

    @LogField(tableName = "b200005", value = "b200005_5301021103", valueName = "田间试验费")
    @ApiModelProperty(value = "田间试验费")
    private BigDecimal b2000055301021103;

    @LogField(tableName = "b200005", value = "b200005_5301021104", valueName = "其他试验费用")
    @ApiModelProperty(value = "其他试验费用")
    private BigDecimal b2000055301021104;

    @LogField(tableName = "b200005", value = "b200005_53010212", valueName = "图书资料费")
    @ApiModelProperty(value = "图书资料费")
    private BigDecimal b20000553010212;

    @LogField(tableName = "b200005", value = "b200005_53010213", valueName = "资料翻译费")
    @ApiModelProperty(value = "资料翻译费")
    private BigDecimal b20000553010213;

    @LogField(tableName = "b200005", value = "b200005_53010214", valueName = "专家咨询费")
    @ApiModelProperty(value = "专家咨询费")
    private BigDecimal b20000553010214;

    @LogField(tableName = "b200005", value = "b200005_53010215", valueName = "高新科技研发保险费")
    @ApiModelProperty(value = "高新科技研发保险费")
    private BigDecimal b20000553010215;

    @LogField(tableName = "b200005", value = "b200005_53010216", valueName = "成果费用")
    @ApiModelProperty(value = "成果费用")
    private BigDecimal b20000553010216;

    @LogField(tableName = "b200005", value = "b200005_5301021601", valueName = "检索费")
    @ApiModelProperty(value = "检索费")
    private BigDecimal b2000055301021601;

    @LogField(tableName = "b200005", value = "b200005_5301021602", valueName = "分析费")
    @ApiModelProperty(value = "分析费")
    private BigDecimal b2000055301021602;

    @LogField(tableName = "b200005", value = "b200005_5301021603", valueName = "评议费")
    @ApiModelProperty(value = "评议费")
    private BigDecimal b2000055301021603;

    @LogField(tableName = "b200005", value = "b200005_5301021604", valueName = "论证费")
    @ApiModelProperty(value = "论证费")
    private BigDecimal b2000055301021604;

    @LogField(tableName = "b200005", value = "b200005_5301021605", valueName = "鉴定费")
    @ApiModelProperty(value = "鉴定费")
    private BigDecimal b2000055301021605;

    @LogField(tableName = "b200005", value = "b200005_5301021606", valueName = "评审费")
    @ApiModelProperty(value = "评审费")
    private BigDecimal b2000055301021606;

    @LogField(tableName = "b200005", value = "b200005_5301021607", valueName = "评估费")
    @ApiModelProperty(value = "评估费")
    private BigDecimal b2000055301021607;

    @LogField(tableName = "b200005", value = "b200005_5301021608", valueName = "验收费")
    @ApiModelProperty(value = "验收费")
    private BigDecimal b2000055301021608;

    @LogField(tableName = "b200005", value = "b200005_5301021609", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000055301021609;

    @LogField(tableName = "b200005", value = "b200005_53010217", valueName = "知识产权费用")
    @ApiModelProperty(value = "知识产权费用")
    private BigDecimal b20000553010217;

    @LogField(tableName = "b200005", value = "b200005_5301021701", valueName = "申请费")
    @ApiModelProperty(value = "申请费")
    private BigDecimal b2000055301021701;

    @LogField(tableName = "b200005", value = "b200005_5301021702", valueName = "注册费")
    @ApiModelProperty(value = "注册费")
    private BigDecimal b2000055301021702;

    @LogField(tableName = "b200005", value = "b200005_5301021703", valueName = "代理费")
    @ApiModelProperty(value = "代理费")
    private BigDecimal b2000055301021703;

    @LogField(tableName = "b200005", value = "b200005_5301021704", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000055301021704;

    @LogField(tableName = "b200005", value = "b200005_53010218", valueName = "差旅费")
    @ApiModelProperty(value = "差旅费")
    private BigDecimal b20000553010218;

    @LogField(tableName = "b200005", value = "b200005_53010219", valueName = "会议费")
    @ApiModelProperty(value = "会议费")
    private BigDecimal b20000553010219;

    @LogField(tableName = "b200005", value = "b200005_53010220", valueName = "通讯费")
    @ApiModelProperty(value = "通讯费")
    private BigDecimal b20000553010220;

    @LogField(tableName = "b200005", value = "b200005_53010221", valueName = "办公费")
    @ApiModelProperty(value = "办公费")
    private BigDecimal b20000553010221;

    @LogField(tableName = "b200005", value = "b200005_53010222", valueName = "外事费")
    @ApiModelProperty(value = "外事费")
    private BigDecimal b20000553010222;

    @LogField(tableName = "b200005", value = "b200005_53010223", valueName = "研发人员培训费")
    @ApiModelProperty(value = "研发人员培训费")
    private BigDecimal b20000553010223;

    @LogField(tableName = "b200005", value = "b200005_53010224", valueName = "研发人员培养费")
    @ApiModelProperty(value = "研发人员培养费")
    private BigDecimal b20000553010224;

    @LogField(tableName = "b200005", value = "b200005_53010225", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b20000553010225;

    @LogField(tableName = "b200005", value = "b200005_53010226", valueName = "外包费用")
    @ApiModelProperty(value = "外包费用")
    private BigDecimal b20000553010226;

    @LogField(tableName = "b200005", value = "b200005_5301022601", valueName = "委托研发（境内进行）")
    @ApiModelProperty(value = "委托研发（境内进行）")
    private BigDecimal b2000055301022601;

    @LogField(tableName = "b200005", value = "b200005_5301022602", valueName = "委托研发（境外机构）")
    @ApiModelProperty(value = "委托研发（境外机构）")
    private BigDecimal b2000055301022602;

    @LogField(tableName = "b200005", value = "b200005_5301022603", valueName = "委托研发（境外个人）")
    @ApiModelProperty(value = "委托研发（境外个人）")
    private BigDecimal b2000055301022603;

    @LogField(tableName = "b200005", value = "b200005_5301022604", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000055301022604;

    @LogField(tableName = "b200005", value = "b200005_5401", valueName = "工程施工")
    @ApiModelProperty(value = "工程施工")
    private BigDecimal b2000055401;

    @LogField(tableName = "b200005", value = "b200005_5402", valueName = "工程结算")
    @ApiModelProperty(value = "工程结算")
    private BigDecimal b2000055402;

    @LogField(tableName = "b200005", value = "b200005_5403", valueName = "机械作业")
    @ApiModelProperty(value = "机械作业")
    private BigDecimal b2000055403;

    @LogField(tableName = "b200005", value = "b200005_5501", valueName = "合同履约成本")
    @ApiModelProperty(value = "合同履约成本")
    private BigDecimal b2000055501;

    @LogField(tableName = "b200005", value = "b200005_550101", valueName = "职工工资")
    @ApiModelProperty(value = "职工工资")
    private BigDecimal b200005550101;

    @LogField(tableName = "b200005", value = "b200005_550102", valueName = "年终奖")
    @ApiModelProperty(value = "年终奖")
    private BigDecimal b200005550102;

    @LogField(tableName = "b200005", value = "b200005_550103", valueName = "津贴与补贴")
    @ApiModelProperty(value = "津贴与补贴")
    private BigDecimal b200005550103;

    @LogField(tableName = "b200005", value = "b200005_550104", valueName = "股权激励")
    @ApiModelProperty(value = "股权激励")
    private BigDecimal b200005550104;

    @LogField(tableName = "b200005", value = "b200005_550105", valueName = "职工教育经费")
    @ApiModelProperty(value = "职工教育经费")
    private BigDecimal b200005550105;

    @LogField(tableName = "b200005", value = "b200005_550106", valueName = "职工培训费用")
    @ApiModelProperty(value = "职工培训费用")
    private BigDecimal b200005550106;

    @LogField(tableName = "b200005", value = "b200005_550107", valueName = "工会经费")
    @ApiModelProperty(value = "工会经费")
    private BigDecimal b200005550107;

    @LogField(tableName = "b200005", value = "b200005_550108", valueName = "社会保险费")
    @ApiModelProperty(value = "社会保险费")
    private BigDecimal b200005550108;

    @LogField(tableName = "b200005", value = "b200005_550109", valueName = "住房公积金")
    @ApiModelProperty(value = "住房公积金")
    private BigDecimal b200005550109;

    @LogField(tableName = "b200005", value = "b200005_550110", valueName = "职工福利费")
    @ApiModelProperty(value = "职工福利费")
    private BigDecimal b200005550110;

    @LogField(tableName = "b200005", value = "b200005_550111", valueName = "补充养老保险")
    @ApiModelProperty(value = "补充养老保险")
    private BigDecimal b200005550111;

    @LogField(tableName = "b200005", value = "b200005_550112", valueName = "补充医疗保险")
    @ApiModelProperty(value = "补充医疗保险")
    private BigDecimal b200005550112;

    @LogField(tableName = "b200005", value = "b200005_550113", valueName = "其他职工工资")
    @ApiModelProperty(value = "其他职工工资")
    private BigDecimal b200005550113;

    @LogField(tableName = "b200005", value = "b200005_550114", valueName = "商业保险费")
    @ApiModelProperty(value = "商业保险费")
    private BigDecimal b200005550114;

    @LogField(tableName = "b200005", value = "b200005_550115", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200005550115;

    @LogField(tableName = "b200005", value = "b200005_5502", valueName = "合同履约成本减值准备")
    @ApiModelProperty(value = "合同履约成本减值准备")
    private BigDecimal b2000055502;

    @LogField(tableName = "b200005", value = "b200005_5503", valueName = "合同取得成本")
    @ApiModelProperty(value = "合同取得成本")
    private BigDecimal b2000055503;

    @LogField(tableName = "b200005", value = "b200005_5504", valueName = "合同取得成本减值准备")
    @ApiModelProperty(value = "合同取得成本减值准备")
    private BigDecimal b2000055504;

    @LogField(tableName = "b200005", value = "b200005_6001", valueName = "主营业务收入")
    @ApiModelProperty(value = "主营业务收入")
    private BigDecimal b2000056001;

    @LogField(tableName = "b200005", value = "b200005_600101", valueName = "销售商品收入")
    @ApiModelProperty(value = "销售商品收入")
    private BigDecimal b200005600101;

    @LogField(tableName = "b200005", value = "b200005_600102", valueName = "提供劳务收入")
    @ApiModelProperty(value = "提供劳务收入")
    private BigDecimal b200005600102;

    @LogField(tableName = "b200005", value = "b200005_600103", valueName = "建造合同收入")
    @ApiModelProperty(value = "建造合同收入")
    private BigDecimal b200005600103;

    @LogField(tableName = "b200005", value = "b200005_600104", valueName = "让渡资产使用权收入")
    @ApiModelProperty(value = "让渡资产使用权收入")
    private BigDecimal b200005600104;

    @LogField(tableName = "b200005", value = "b200005_600105", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200005600105;

    @LogField(tableName = "b200005", value = "b200005_6011", valueName = "利息收入")
    @ApiModelProperty(value = "利息收入")
    private BigDecimal b2000056011;

    @LogField(tableName = "b200005", value = "b200005_6021", valueName = "手续费及佣金收入")
    @ApiModelProperty(value = "手续费及佣金收入")
    private BigDecimal b2000056021;

    @LogField(tableName = "b200005", value = "b200005_6031", valueName = "保费收入")
    @ApiModelProperty(value = "保费收入")
    private BigDecimal b2000056031;

    @LogField(tableName = "b200005", value = "b200005_6041", valueName = "租赁收入")
    @ApiModelProperty(value = "租赁收入")
    private BigDecimal b2000056041;

    @LogField(tableName = "b200005", value = "b200005_6051", valueName = "其他业务收入")
    @ApiModelProperty(value = "其他业务收入")
    private BigDecimal b2000056051;

    @LogField(tableName = "b200005", value = "b200005_605101", valueName = "销售材料收入")
    @ApiModelProperty(value = "销售材料收入")
    private BigDecimal b200005605101;

    @LogField(tableName = "b200005", value = "b200005_605102", valueName = "出租固定资产收入")
    @ApiModelProperty(value = "出租固定资产收入")
    private BigDecimal b200005605102;

    @LogField(tableName = "b200005", value = "b200005_605103", valueName = "出租无形资产收入")
    @ApiModelProperty(value = "出租无形资产收入")
    private BigDecimal b200005605103;

    @LogField(tableName = "b200005", value = "b200005_605104", valueName = "出租包装物和商品收入")
    @ApiModelProperty(value = "出租包装物和商品收入")
    private BigDecimal b200005605104;

    @LogField(tableName = "b200005", value = "b200005_605105", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200005605105;

    @LogField(tableName = "b200005", value = "b200005_6061", valueName = "汇兑损益")
    @ApiModelProperty(value = "汇兑损益")
    private BigDecimal b2000056061;

    @LogField(tableName = "b200005", value = "b200005_6101", valueName = "公允价值变动损益")
    @ApiModelProperty(value = "公允价值变动损益")
    private BigDecimal b2000056101;

    @LogField(tableName = "b200005", value = "b200005_6111", valueName = "投资收益")
    @ApiModelProperty(value = "投资收益")
    private BigDecimal b2000056111;

    @LogField(tableName = "b200005", value = "b200005_611101", valueName = "交易性金融资产")
    @ApiModelProperty(value = "交易性金融资产")
    private BigDecimal b200005611101;

    @LogField(tableName = "b200005", value = "b200005_61110101", valueName = "国债")
    @ApiModelProperty(value = "国债")
    private BigDecimal b20000561110101;

    @LogField(tableName = "b200005", value = "b200005_6111010101", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000056111010101;

    @LogField(tableName = "b200005", value = "b200005_6111010102", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000056111010102;

    @LogField(tableName = "b200005", value = "b200005_61110102", valueName = "证券投资基金")
    @ApiModelProperty(value = "证券投资基金")
    private BigDecimal b20000561110102;

    @LogField(tableName = "b200005", value = "b200005_6111010201", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000056111010201;

    @LogField(tableName = "b200005", value = "b200005_6111010202", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000056111010202;

    @LogField(tableName = "b200005", value = "b200005_61110103", valueName = "地方政府债券")
    @ApiModelProperty(value = "地方政府债券")
    private BigDecimal b20000561110103;

    @LogField(tableName = "b200005", value = "b200005_6111010301", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000056111010301;

    @LogField(tableName = "b200005", value = "b200005_6111010302", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000056111010302;

    @LogField(tableName = "b200005", value = "b200005_61110104", valueName = "铁路债券")
    @ApiModelProperty(value = "铁路债券")
    private BigDecimal b20000561110104;

    @LogField(tableName = "b200005", value = "b200005_6111010401", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000056111010401;

    @LogField(tableName = "b200005", value = "b200005_6111010402", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000056111010402;

    @LogField(tableName = "b200005", value = "b200005_61110105", valueName = "连续持有公开发行并上市流通12个月及以上的股票")
    @ApiModelProperty(value = "连续持有公开发行并上市流通12个月及以上的股票")
    private BigDecimal b20000561110105;

    @LogField(tableName = "b200005", value = "b200005_6111010501", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000056111010501;

    @LogField(tableName = "b200005", value = "b200005_6111010502", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000056111010502;

    @LogField(tableName = "b200005", value = "b200005_61110106", valueName = "永续债")
    @ApiModelProperty(value = "永续债")
    private BigDecimal b20000561110106;

    @LogField(tableName = "b200005", value = "b200005_6111010601", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000056111010601;

    @LogField(tableName = "b200005", value = "b200005_6111010602", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000056111010602;

    @LogField(tableName = "b200005", value = "b200005_61110107", valueName = "创新企业CDR")
    @ApiModelProperty(value = "创新企业CDR")
    private BigDecimal b20000561110107;

    @LogField(tableName = "b200005", value = "b200005_6111010701", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000056111010701;

    @LogField(tableName = "b200005", value = "b200005_6111010702", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000056111010702;

    @LogField(tableName = "b200005", value = "b200005_61110108", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b20000561110108;

    @LogField(tableName = "b200005", value = "b200005_6111010801", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000056111010801;

    @LogField(tableName = "b200005", value = "b200005_6111010802", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000056111010802;

    @LogField(tableName = "b200005", value = "b200005_611102", valueName = "可供出售金融资产")
    @ApiModelProperty(value = "可供出售金融资产")
    private BigDecimal b200005611102;

    @LogField(tableName = "b200005", value = "b200005_61110201", valueName = "国债")
    @ApiModelProperty(value = "国债")
    private BigDecimal b20000561110201;

    @LogField(tableName = "b200005", value = "b200005_6111020101", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000056111020101;

    @LogField(tableName = "b200005", value = "b200005_6111020102", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000056111020102;

    @LogField(tableName = "b200005", value = "b200005_61110202", valueName = "证券投资基金")
    @ApiModelProperty(value = "证券投资基金")
    private BigDecimal b20000561110202;

    @LogField(tableName = "b200005", value = "b200005_6111020201", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000056111020201;

    @LogField(tableName = "b200005", value = "b200005_6111020202", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000056111020202;

    @LogField(tableName = "b200005", value = "b200005_61110203", valueName = "地方政府债券")
    @ApiModelProperty(value = "地方政府债券")
    private BigDecimal b20000561110203;

    @LogField(tableName = "b200005", value = "b200005_6111020301", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000056111020301;

    @LogField(tableName = "b200005", value = "b200005_6111020302", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000056111020302;

    @LogField(tableName = "b200005", value = "b200005_61110204", valueName = "铁路债券")
    @ApiModelProperty(value = "铁路债券")
    private BigDecimal b20000561110204;

    @LogField(tableName = "b200005", value = "b200005_6111020401", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000056111020401;

    @LogField(tableName = "b200005", value = "b200005_6111020402", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000056111020402;

    @LogField(tableName = "b200005", value = "b200005_61110205", valueName = "连续持有公开发行并上市流通12个月及以上的股票")
    @ApiModelProperty(value = "连续持有公开发行并上市流通12个月及以上的股票")
    private BigDecimal b20000561110205;

    @LogField(tableName = "b200005", value = "b200005_6111020501", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000056111020501;

    @LogField(tableName = "b200005", value = "b200005_6111020502", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000056111020502;

    @LogField(tableName = "b200005", value = "b200005_61110206", valueName = "永续债")
    @ApiModelProperty(value = "永续债")
    private BigDecimal b20000561110206;

    @LogField(tableName = "b200005", value = "b200005_6111020601", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000056111020601;

    @LogField(tableName = "b200005", value = "b200005_6111020602", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000056111020602;

    @LogField(tableName = "b200005", value = "b200005_61110207", valueName = "创新企业CDR")
    @ApiModelProperty(value = "创新企业CDR")
    private BigDecimal b20000561110207;

    @LogField(tableName = "b200005", value = "b200005_6111020701", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000056111020701;

    @LogField(tableName = "b200005", value = "b200005_6111020702", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000056111020702;

    @LogField(tableName = "b200005", value = "b200005_61110208", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b20000561110208;

    @LogField(tableName = "b200005", value = "b200005_6111020801", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000056111020801;

    @LogField(tableName = "b200005", value = "b200005_6111020802", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000056111020802;

    @LogField(tableName = "b200005", value = "b200005_611103", valueName = "持有至到期投资")
    @ApiModelProperty(value = "持有至到期投资")
    private BigDecimal b200005611103;

    @LogField(tableName = "b200005", value = "b200005_61110301", valueName = "国债")
    @ApiModelProperty(value = "国债")
    private BigDecimal b20000561110301;

    @LogField(tableName = "b200005", value = "b200005_6111030101", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000056111030101;

    @LogField(tableName = "b200005", value = "b200005_6111030102", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000056111030102;

    @LogField(tableName = "b200005", value = "b200005_61110302", valueName = "地方政府债券")
    @ApiModelProperty(value = "地方政府债券")
    private BigDecimal b20000561110302;

    @LogField(tableName = "b200005", value = "b200005_6111030201", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000056111030201;

    @LogField(tableName = "b200005", value = "b200005_6111030202", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000056111030202;

    @LogField(tableName = "b200005", value = "b200005_61110303", valueName = "铁路债券")
    @ApiModelProperty(value = "铁路债券")
    private BigDecimal b20000561110303;

    @LogField(tableName = "b200005", value = "b200005_6111030301", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000056111030301;

    @LogField(tableName = "b200005", value = "b200005_6111030302", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000056111030302;

    @LogField(tableName = "b200005", value = "b200005_61110304", valueName = "永续债")
    @ApiModelProperty(value = "永续债")
    private BigDecimal b20000561110304;

    @LogField(tableName = "b200005", value = "b200005_6111030401", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000056111030401;

    @LogField(tableName = "b200005", value = "b200005_6111030402", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000056111030402;

    @LogField(tableName = "b200005", value = "b200005_61110305", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b20000561110305;

    @LogField(tableName = "b200005", value = "b200005_6111030501", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000056111030501;

    @LogField(tableName = "b200005", value = "b200005_6111030502", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000056111030502;

    @LogField(tableName = "b200005", value = "b200005_611104", valueName = "衍生工具")
    @ApiModelProperty(value = "衍生工具")
    private BigDecimal b200005611104;

    @LogField(tableName = "b200005", value = "b200005_61110401", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b20000561110401;

    @LogField(tableName = "b200005", value = "b200005_61110402", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b20000561110402;

    @LogField(tableName = "b200005", value = "b200005_611105", valueName = "交易性金融负债")
    @ApiModelProperty(value = "交易性金融负债")
    private BigDecimal b200005611105;

    @LogField(tableName = "b200005", value = "b200005_61110501", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b20000561110501;

    @LogField(tableName = "b200005", value = "b200005_61110502", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b20000561110502;

    @LogField(tableName = "b200005", value = "b200005_611106", valueName = "长期股权投资")
    @ApiModelProperty(value = "长期股权投资")
    private BigDecimal b200005611106;

    @LogField(tableName = "b200005", value = "b200005_61110601", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b20000561110601;

    @LogField(tableName = "b200005", value = "b200005_61110602", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b20000561110602;

    @LogField(tableName = "b200005", value = "b200005_611107", valueName = "长期债券投资")
    @ApiModelProperty(value = "长期债券投资")
    private BigDecimal b200005611107;

    @LogField(tableName = "b200005", value = "b200005_61110701", valueName = "国债")
    @ApiModelProperty(value = "国债")
    private BigDecimal b20000561110701;

    @LogField(tableName = "b200005", value = "b200005_6111070101", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000056111070101;

    @LogField(tableName = "b200005", value = "b200005_6111070102", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000056111070102;

    @LogField(tableName = "b200005", value = "b200005_61110702", valueName = "地方政府债券")
    @ApiModelProperty(value = "地方政府债券")
    private BigDecimal b20000561110702;

    @LogField(tableName = "b200005", value = "b200005_6111070201", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000056111070201;

    @LogField(tableName = "b200005", value = "b200005_6111070202", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000056111070202;

    @LogField(tableName = "b200005", value = "b200005_61110703", valueName = "铁路债券")
    @ApiModelProperty(value = "铁路债券")
    private BigDecimal b20000561110703;

    @LogField(tableName = "b200005", value = "b200005_6111070301", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000056111070301;

    @LogField(tableName = "b200005", value = "b200005_6111070302", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000056111070302;

    @LogField(tableName = "b200005", value = "b200005_61110704", valueName = "永续债")
    @ApiModelProperty(value = "永续债")
    private BigDecimal b20000561110704;

    @LogField(tableName = "b200005", value = "b200005_6111070401", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000056111070401;

    @LogField(tableName = "b200005", value = "b200005_6111070402", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000056111070402;

    @LogField(tableName = "b200005", value = "b200005_61110705", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b20000561110705;

    @LogField(tableName = "b200005", value = "b200005_6111070501", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000056111070501;

    @LogField(tableName = "b200005", value = "b200005_6111070502", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000056111070502;

    @LogField(tableName = "b200005", value = "b200005_611108", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200005611108;

    @LogField(tableName = "b200005", value = "b200005_61110801", valueName = "国债")
    @ApiModelProperty(value = "国债")
    private BigDecimal b20000561110801;

    @LogField(tableName = "b200005", value = "b200005_6111080101", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000056111080101;

    @LogField(tableName = "b200005", value = "b200005_6111080102", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000056111080102;

    @LogField(tableName = "b200005", value = "b200005_61110802", valueName = "证券投资基金")
    @ApiModelProperty(value = "证券投资基金")
    private BigDecimal b20000561110802;

    @LogField(tableName = "b200005", value = "b200005_6111080201", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000056111080201;

    @LogField(tableName = "b200005", value = "b200005_6111080202", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000056111080202;

    @LogField(tableName = "b200005", value = "b200005_61110803", valueName = "地方政府债券")
    @ApiModelProperty(value = "地方政府债券")
    private BigDecimal b20000561110803;

    @LogField(tableName = "b200005", value = "b200005_6111080301", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000056111080301;

    @LogField(tableName = "b200005", value = "b200005_6111080302", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000056111080302;

    @LogField(tableName = "b200005", value = "b200005_61110804", valueName = "铁路债券")
    @ApiModelProperty(value = "铁路债券")
    private BigDecimal b20000561110804;

    @LogField(tableName = "b200005", value = "b200005_6111080401", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000056111080401;

    @LogField(tableName = "b200005", value = "b200005_6111080402", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000056111080402;

    @LogField(tableName = "b200005", value = "b200005_61110805", valueName = "连续持有公开发行并上市流通12个月及以上的股票")
    @ApiModelProperty(value = "连续持有公开发行并上市流通12个月及以上的股票")
    private BigDecimal b20000561110805;

    @LogField(tableName = "b200005", value = "b200005_6111080501", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000056111080501;

    @LogField(tableName = "b200005", value = "b200005_6111080502", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000056111080502;

    @LogField(tableName = "b200005", value = "b200005_61110806", valueName = "永续债")
    @ApiModelProperty(value = "永续债")
    private BigDecimal b20000561110806;

    @LogField(tableName = "b200005", value = "b200005_6111080601", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000056111080601;

    @LogField(tableName = "b200005", value = "b200005_6111080602", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000056111080602;

    @LogField(tableName = "b200005", value = "b200005_61110807", valueName = "创新企业CDR")
    @ApiModelProperty(value = "创新企业CDR")
    private BigDecimal b20000561110807;

    @LogField(tableName = "b200005", value = "b200005_6111080701", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000056111080701;

    @LogField(tableName = "b200005", value = "b200005_6111080702", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000056111080702;

    @LogField(tableName = "b200005", value = "b200005_61110808", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b20000561110808;

    @LogField(tableName = "b200005", value = "b200005_6111080801", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000056111080801;

    @LogField(tableName = "b200005", value = "b200005_6111080802", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000056111080802;

    @LogField(tableName = "b200005", value = "b200005_6115", valueName = "资产处置损益")
    @ApiModelProperty(value = "资产处置损益")
    private BigDecimal b2000056115;

    @LogField(tableName = "b200005", value = "b200005_611501", valueName = "固定资产损失")
    @ApiModelProperty(value = "固定资产损失")
    private BigDecimal b200005611501;

    @LogField(tableName = "b200005", value = "b200005_611502", valueName = "无形资产损失")
    @ApiModelProperty(value = "无形资产损失")
    private BigDecimal b200005611502;

    @LogField(tableName = "b200005", value = "b200005_611503", valueName = "在建工程损失")
    @ApiModelProperty(value = "在建工程损失")
    private BigDecimal b200005611503;

    @LogField(tableName = "b200005", value = "b200005_611504", valueName = "生产性生物资产损失")
    @ApiModelProperty(value = "生产性生物资产损失")
    private BigDecimal b200005611504;

    @LogField(tableName = "b200005", value = "b200005_611505", valueName = "打包出售资产损失")
    @ApiModelProperty(value = "打包出售资产损失")
    private BigDecimal b200005611505;

    @LogField(tableName = "b200005", value = "b200005_611506", valueName = "其他资产损失")
    @ApiModelProperty(value = "其他资产损失")
    private BigDecimal b200005611506;

    @LogField(tableName = "b200005", value = "b200005_611507", valueName = "资产处置收益")
    @ApiModelProperty(value = "资产处置收益")
    private BigDecimal b200005611507;

    @LogField(tableName = "b200005", value = "b200005_6117", valueName = "其他收益")
    @ApiModelProperty(value = "其他收益")
    private BigDecimal b2000056117;

    @LogField(tableName = "b200005", value = "b200005_6201", valueName = "摊回保险责任准备金")
    @ApiModelProperty(value = "摊回保险责任准备金")
    private BigDecimal b2000056201;

    @LogField(tableName = "b200005", value = "b200005_6202", valueName = "摊回赔付支出")
    @ApiModelProperty(value = "摊回赔付支出")
    private BigDecimal b2000056202;

    @LogField(tableName = "b200005", value = "b200005_6203", valueName = "摊回分保费用")
    @ApiModelProperty(value = "摊回分保费用")
    private BigDecimal b2000056203;

    @LogField(tableName = "b200005", value = "b200005_6301", valueName = "营业外收入")
    @ApiModelProperty(value = "营业外收入")
    private BigDecimal b2000056301;

    @LogField(tableName = "b200005", value = "b200005_630101", valueName = "非流动资产处置利得")
    @ApiModelProperty(value = "非流动资产处置利得")
    private BigDecimal b200005630101;

    @LogField(tableName = "b200005", value = "b200005_630102", valueName = "非货币性资产交换利得")
    @ApiModelProperty(value = "非货币性资产交换利得")
    private BigDecimal b200005630102;

    @LogField(tableName = "b200005", value = "b200005_630103", valueName = "债务重组利得")
    @ApiModelProperty(value = "债务重组利得")
    private BigDecimal b200005630103;

    @LogField(tableName = "b200005", value = "b200005_630104", valueName = "政府补助利得")
    @ApiModelProperty(value = "政府补助利得")
    private BigDecimal b200005630104;

    @LogField(tableName = "b200005", value = "b200005_630105", valueName = "盘盈利得")
    @ApiModelProperty(value = "盘盈利得")
    private BigDecimal b200005630105;

    @LogField(tableName = "b200005", value = "b200005_630106", valueName = "捐赠利得")
    @ApiModelProperty(value = "捐赠利得")
    private BigDecimal b200005630106;

    @LogField(tableName = "b200005", value = "b200005_630107", valueName = "罚没利得")
    @ApiModelProperty(value = "罚没利得")
    private BigDecimal b200005630107;

    @LogField(tableName = "b200005", value = "b200005_630108", valueName = "确实无法偿付的应付款项")
    @ApiModelProperty(value = "确实无法偿付的应付款项")
    private BigDecimal b200005630108;

    @LogField(tableName = "b200005", value = "b200005_630109", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200005630109;

    @LogField(tableName = "b200005", value = "b200005_6401", valueName = "主营业务成本")
    @ApiModelProperty(value = "主营业务成本")
    private BigDecimal b2000056401;

    @LogField(tableName = "b200005", value = "b200005_640101", valueName = "销售商品成本")
    @ApiModelProperty(value = "销售商品成本")
    private BigDecimal b200005640101;

    @LogField(tableName = "b200005", value = "b200005_640102", valueName = "提供劳务成本")
    @ApiModelProperty(value = "提供劳务成本")
    private BigDecimal b200005640102;

    @LogField(tableName = "b200005", value = "b200005_640103", valueName = "职工工资")
    @ApiModelProperty(value = "职工工资")
    private BigDecimal b200005640103;

    @LogField(tableName = "b200005", value = "b200005_640104", valueName = "年终奖")
    @ApiModelProperty(value = "年终奖")
    private BigDecimal b200005640104;

    @LogField(tableName = "b200005", value = "b200005_640105", valueName = "津贴与补贴")
    @ApiModelProperty(value = "津贴与补贴")
    private BigDecimal b200005640105;

    @LogField(tableName = "b200005", value = "b200005_640106", valueName = "股权激励")
    @ApiModelProperty(value = "股权激励")
    private BigDecimal b200005640106;

    @LogField(tableName = "b200005", value = "b200005_640107", valueName = "社会保险费")
    @ApiModelProperty(value = "社会保险费")
    private BigDecimal b200005640107;

    @LogField(tableName = "b200005", value = "b200005_640108", valueName = "住房公积金")
    @ApiModelProperty(value = "住房公积金")
    private BigDecimal b200005640108;

    @LogField(tableName = "b200005", value = "b200005_640109", valueName = "补充养老保险")
    @ApiModelProperty(value = "补充养老保险")
    private BigDecimal b200005640109;

    @LogField(tableName = "b200005", value = "b200005_640110", valueName = "补充医疗保险")
    @ApiModelProperty(value = "补充医疗保险")
    private BigDecimal b200005640110;

    @LogField(tableName = "b200005", value = "b200005_640111", valueName = "职工福利费")
    @ApiModelProperty(value = "职工福利费")
    private BigDecimal b200005640111;

    @LogField(tableName = "b200005", value = "b200005_640112", valueName = "职工教育经费")
    @ApiModelProperty(value = "职工教育经费")
    private BigDecimal b200005640112;

    @LogField(tableName = "b200005", value = "b200005_640113", valueName = "职工培训费用")
    @ApiModelProperty(value = "职工培训费用")
    private BigDecimal b200005640113;

    @LogField(tableName = "b200005", value = "b200005_640114", valueName = "工会经费")
    @ApiModelProperty(value = "工会经费")
    private BigDecimal b200005640114;

    @LogField(tableName = "b200005", value = "b200005_640115", valueName = "其他职工工资")
    @ApiModelProperty(value = "其他职工工资")
    private BigDecimal b200005640115;

    @LogField(tableName = "b200005", value = "b200005_640116", valueName = "商业保险费")
    @ApiModelProperty(value = "商业保险费")
    private BigDecimal b200005640116;

    @LogField(tableName = "b200005", value = "b200005_640117", valueName = "建造合同成本")
    @ApiModelProperty(value = "建造合同成本")
    private BigDecimal b200005640117;

    @LogField(tableName = "b200005", value = "b200005_640118", valueName = "让渡资产使用权成本")
    @ApiModelProperty(value = "让渡资产使用权成本")
    private BigDecimal b200005640118;

    @LogField(tableName = "b200005", value = "b200005_640119", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200005640119;

    @LogField(tableName = "b200005", value = "b200005_6402", valueName = "其他业务成本")
    @ApiModelProperty(value = "其他业务成本")
    private BigDecimal b2000056402;

    @LogField(tableName = "b200005", value = "b200005_640201", valueName = "销售材料成本")
    @ApiModelProperty(value = "销售材料成本")
    private BigDecimal b200005640201;

    @LogField(tableName = "b200005", value = "b200005_640202", valueName = "出租固定资产成本")
    @ApiModelProperty(value = "出租固定资产成本")
    private BigDecimal b200005640202;

    @LogField(tableName = "b200005", value = "b200005_640203", valueName = "出租无形资产成本")
    @ApiModelProperty(value = "出租无形资产成本")
    private BigDecimal b200005640203;

    @LogField(tableName = "b200005", value = "b200005_640204", valueName = "出租包装物和商品成本")
    @ApiModelProperty(value = "出租包装物和商品成本")
    private BigDecimal b200005640204;

    @LogField(tableName = "b200005", value = "b200005_640205", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200005640205;

    @LogField(tableName = "b200005", value = "b200005_6403", valueName = "税金及附加")
    @ApiModelProperty(value = "税金及附加")
    private BigDecimal b2000056403;

    @LogField(tableName = "b200005", value = "b200005_6411", valueName = "利息支出")
    @ApiModelProperty(value = "利息支出")
    private BigDecimal b2000056411;

    @LogField(tableName = "b200005", value = "b200005_6421", valueName = "手续费及佣金支出")
    @ApiModelProperty(value = "手续费及佣金支出")
    private BigDecimal b2000056421;

    @LogField(tableName = "b200005", value = "b200005_6501", valueName = "提取未到期责任准备金")
    @ApiModelProperty(value = "提取未到期责任准备金")
    private BigDecimal b2000056501;

    @LogField(tableName = "b200005", value = "b200005_6502", valueName = "提取保险责任准备金")
    @ApiModelProperty(value = "提取保险责任准备金")
    private BigDecimal b2000056502;

    @LogField(tableName = "b200005", value = "b200005_6511", valueName = "赔付支出")
    @ApiModelProperty(value = "赔付支出")
    private BigDecimal b2000056511;

    @LogField(tableName = "b200005", value = "b200005_6521", valueName = "保单红利支出")
    @ApiModelProperty(value = "保单红利支出")
    private BigDecimal b2000056521;

    @LogField(tableName = "b200005", value = "b200005_6531", valueName = "退保金")
    @ApiModelProperty(value = "退保金")
    private BigDecimal b2000056531;

    @LogField(tableName = "b200005", value = "b200005_6541", valueName = "分出保费")
    @ApiModelProperty(value = "分出保费")
    private BigDecimal b2000056541;

    @LogField(tableName = "b200005", value = "b200005_6542", valueName = "分保费用")
    @ApiModelProperty(value = "分保费用")
    private BigDecimal b2000056542;

    @LogField(tableName = "b200005", value = "b200005_6601", valueName = "销售费用")
    @ApiModelProperty(value = "销售费用")
    private BigDecimal b2000056601;

    @LogField(tableName = "b200005", value = "b200005_660101", valueName = "职工工资")
    @ApiModelProperty(value = "职工工资")
    private BigDecimal b200005660101;

    @LogField(tableName = "b200005", value = "b200005_660102", valueName = "年终奖")
    @ApiModelProperty(value = "年终奖")
    private BigDecimal b200005660102;

    @LogField(tableName = "b200005", value = "b200005_660103", valueName = "津贴与补贴")
    @ApiModelProperty(value = "津贴与补贴")
    private BigDecimal b200005660103;

    @LogField(tableName = "b200005", value = "b200005_660104", valueName = "股权激励")
    @ApiModelProperty(value = "股权激励")
    private BigDecimal b200005660104;

    @LogField(tableName = "b200005", value = "b200005_660105", valueName = "社会保险费")
    @ApiModelProperty(value = "社会保险费")
    private BigDecimal b200005660105;

    @LogField(tableName = "b200005", value = "b200005_660106", valueName = "住房公积金")
    @ApiModelProperty(value = "住房公积金")
    private BigDecimal b200005660106;

    @LogField(tableName = "b200005", value = "b200005_660107", valueName = "补充医疗保险")
    @ApiModelProperty(value = "补充医疗保险")
    private BigDecimal b200005660107;

    @LogField(tableName = "b200005", value = "b200005_660108", valueName = "补充养老保险")
    @ApiModelProperty(value = "补充养老保险")
    private BigDecimal b200005660108;

    @LogField(tableName = "b200005", value = "b200005_660109", valueName = "职工福利费")
    @ApiModelProperty(value = "职工福利费")
    private BigDecimal b200005660109;

    @LogField(tableName = "b200005", value = "b200005_660110", valueName = "职工教育经费")
    @ApiModelProperty(value = "职工教育经费")
    private BigDecimal b200005660110;

    @LogField(tableName = "b200005", value = "b200005_660111", valueName = "职工培训费用")
    @ApiModelProperty(value = "职工培训费用")
    private BigDecimal b200005660111;

    @LogField(tableName = "b200005", value = "b200005_660112", valueName = "工会经费")
    @ApiModelProperty(value = "工会经费")
    private BigDecimal b200005660112;

    @LogField(tableName = "b200005", value = "b200005_660113", valueName = "其他职工工资")
    @ApiModelProperty(value = "其他职工工资")
    private BigDecimal b200005660113;

    @LogField(tableName = "b200005", value = "b200005_660114", valueName = "商业保险费")
    @ApiModelProperty(value = "商业保险费")
    private BigDecimal b200005660114;

    @LogField(tableName = "b200005", value = "b200005_660115", valueName = "折旧费")
    @ApiModelProperty(value = "折旧费")
    private BigDecimal b200005660115;

    @LogField(tableName = "b200005", value = "b200005_660116", valueName = "无形资产摊销费")
    @ApiModelProperty(value = "无形资产摊销费")
    private BigDecimal b200005660116;

    @LogField(tableName = "b200005", value = "b200005_660117", valueName = "长期待摊费用摊销费")
    @ApiModelProperty(value = "长期待摊费用摊销费")
    private BigDecimal b200005660117;

    @LogField(tableName = "b200005", value = "b200005_660118", valueName = "修理费")
    @ApiModelProperty(value = "修理费")
    private BigDecimal b200005660118;

    @LogField(tableName = "b200005", value = "b200005_660119", valueName = "物料消耗")
    @ApiModelProperty(value = "物料消耗")
    private BigDecimal b200005660119;

    @LogField(tableName = "b200005", value = "b200005_660120", valueName = "低值易耗品摊销")
    @ApiModelProperty(value = "低值易耗品摊销")
    private BigDecimal b200005660120;

    @LogField(tableName = "b200005", value = "b200005_660121", valueName = "办公费")
    @ApiModelProperty(value = "办公费")
    private BigDecimal b200005660121;

    @LogField(tableName = "b200005", value = "b200005_660122", valueName = "差旅费")
    @ApiModelProperty(value = "差旅费")
    private BigDecimal b200005660122;

    @LogField(tableName = "b200005", value = "b200005_660123", valueName = "业务招待费")
    @ApiModelProperty(value = "业务招待费")
    private BigDecimal b200005660123;

    @LogField(tableName = "b200005", value = "b200005_660124", valueName = "通信费")
    @ApiModelProperty(value = "通信费")
    private BigDecimal b200005660124;

    @LogField(tableName = "b200005", value = "b200005_660125", valueName = "车辆费")
    @ApiModelProperty(value = "车辆费")
    private BigDecimal b200005660125;

    @LogField(tableName = "b200005", value = "b200005_660126", valueName = "能源费")
    @ApiModelProperty(value = "能源费")
    private BigDecimal b200005660126;

    @LogField(tableName = "b200005", value = "b200005_660127", valueName = "运杂费")
    @ApiModelProperty(value = "运杂费")
    private BigDecimal b200005660127;

    @LogField(tableName = "b200005", value = "b200005_660128", valueName = "交通费")
    @ApiModelProperty(value = "交通费")
    private BigDecimal b200005660128;

    @LogField(tableName = "b200005", value = "b200005_660129", valueName = "房租")
    @ApiModelProperty(value = "房租")
    private BigDecimal b200005660129;

    @LogField(tableName = "b200005", value = "b200005_660130", valueName = "物业管理费")
    @ApiModelProperty(value = "物业管理费")
    private BigDecimal b200005660130;

    @LogField(tableName = "b200005", value = "b200005_660131", valueName = "水电费")
    @ApiModelProperty(value = "水电费")
    private BigDecimal b200005660131;

    @LogField(tableName = "b200005", value = "b200005_660132", valueName = "中介服务费")
    @ApiModelProperty(value = "中介服务费")
    private BigDecimal b200005660132;

    @LogField(tableName = "b200005", value = "b200005_660133", valueName = "快递费")
    @ApiModelProperty(value = "快递费")
    private BigDecimal b200005660133;

    @LogField(tableName = "b200005", value = "b200005_660134", valueName = "保险费")
    @ApiModelProperty(value = "保险费")
    private BigDecimal b200005660134;

    @LogField(tableName = "b200005", value = "b200005_660135", valueName = "租赁费")
    @ApiModelProperty(value = "租赁费")
    private BigDecimal b200005660135;

    @LogField(tableName = "b200005", value = "b200005_660136", valueName = "装卸费")
    @ApiModelProperty(value = "装卸费")
    private BigDecimal b200005660136;

    @LogField(tableName = "b200005", value = "b200005_660137", valueName = "包装费")
    @ApiModelProperty(value = "包装费")
    private BigDecimal b200005660137;

    @LogField(tableName = "b200005", value = "b200005_660138", valueName = "通关费用")
    @ApiModelProperty(value = "通关费用")
    private BigDecimal b200005660138;

    @LogField(tableName = "b200005", value = "b200005_660139", valueName = "宣传展览费")
    @ApiModelProperty(value = "宣传展览费")
    private BigDecimal b200005660139;

    @LogField(tableName = "b200005", value = "b200005_660140", valueName = "仓储费")
    @ApiModelProperty(value = "仓储费")
    private BigDecimal b200005660140;

    @LogField(tableName = "b200005", value = "b200005_660141", valueName = "调试费")
    @ApiModelProperty(value = "调试费")
    private BigDecimal b200005660141;

    @LogField(tableName = "b200005", value = "b200005_660142", valueName = "广告费")
    @ApiModelProperty(value = "广告费")
    private BigDecimal b200005660142;

    @LogField(tableName = "b200005", value = "b200005_660143", valueName = "业务提成/佣金（销售服务费）")
    @ApiModelProperty(value = "业务提成/佣金（销售服务费）")
    private BigDecimal b200005660143;

    @LogField(tableName = "b200005", value = "b200005_660144", valueName = "投标费")
    @ApiModelProperty(value = "投标费")
    private BigDecimal b200005660144;

    @LogField(tableName = "b200005", value = "b200005_660145", valueName = "售后服务费")
    @ApiModelProperty(value = "售后服务费")
    private BigDecimal b200005660145;

    @LogField(tableName = "b200005", value = "b200005_660146", valueName = "其他经营费用")
    @ApiModelProperty(value = "其他经营费用")
    private BigDecimal b200005660146;

    @LogField(tableName = "b200005", value = "b200005_660147", valueName = "劳务费")
    @ApiModelProperty(value = "劳务费")
    private BigDecimal b200005660147;

    @LogField(tableName = "b200005", value = "b200005_660148", valueName = "董事会费")
    @ApiModelProperty(value = "董事会费")
    private BigDecimal b200005660148;

    @LogField(tableName = "b200005", value = "b200005_660149", valueName = "诉讼费")
    @ApiModelProperty(value = "诉讼费")
    private BigDecimal b200005660149;

    @LogField(tableName = "b200005", value = "b200005_660150", valueName = "技术转让费")
    @ApiModelProperty(value = "技术转让费")
    private BigDecimal b200005660150;

    @LogField(tableName = "b200005", value = "b200005_660151", valueName = "研究费用")
    @ApiModelProperty(value = "研究费用")
    private BigDecimal b200005660151;

    @LogField(tableName = "b200005", value = "b200005_660152", valueName = "税金")
    @ApiModelProperty(value = "税金")
    private BigDecimal b200005660152;

    @LogField(tableName = "b200005", value = "b200005_660153", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200005660153;

    @LogField(tableName = "b200005", value = "b200005_6602", valueName = "管理费用")
    @ApiModelProperty(value = "管理费用")
    private BigDecimal b2000056602;

    @LogField(tableName = "b200005", value = "b200005_660201", valueName = "职工工资")
    @ApiModelProperty(value = "职工工资")
    private BigDecimal b200005660201;

    @LogField(tableName = "b200005", value = "b200005_660202", valueName = "年终奖")
    @ApiModelProperty(value = "年终奖")
    private BigDecimal b200005660202;

    @LogField(tableName = "b200005", value = "b200005_660203", valueName = "津贴与补贴")
    @ApiModelProperty(value = "津贴与补贴")
    private BigDecimal b200005660203;

    @LogField(tableName = "b200005", value = "b200005_660204", valueName = "股权激励")
    @ApiModelProperty(value = "股权激励")
    private BigDecimal b200005660204;

    @LogField(tableName = "b200005", value = "b200005_660205", valueName = "社会保险费")
    @ApiModelProperty(value = "社会保险费")
    private BigDecimal b200005660205;

    @LogField(tableName = "b200005", value = "b200005_660206", valueName = "住房公积金")
    @ApiModelProperty(value = "住房公积金")
    private BigDecimal b200005660206;

    @LogField(tableName = "b200005", value = "b200005_660207", valueName = "补充医疗保险")
    @ApiModelProperty(value = "补充医疗保险")
    private BigDecimal b200005660207;

    @LogField(tableName = "b200005", value = "b200005_660208", valueName = "补充养老保险")
    @ApiModelProperty(value = "补充养老保险")
    private BigDecimal b200005660208;

    @LogField(tableName = "b200005", value = "b200005_660209", valueName = "职工福利费")
    @ApiModelProperty(value = "职工福利费")
    private BigDecimal b200005660209;

    @LogField(tableName = "b200005", value = "b200005_660210", valueName = "职工教育经费")
    @ApiModelProperty(value = "职工教育经费")
    private BigDecimal b200005660210;

    @LogField(tableName = "b200005", value = "b200005_660211", valueName = "职工培训费用")
    @ApiModelProperty(value = "职工培训费用")
    private BigDecimal b200005660211;

    @LogField(tableName = "b200005", value = "b200005_660212", valueName = "工会经费")
    @ApiModelProperty(value = "工会经费")
    private BigDecimal b200005660212;

    @LogField(tableName = "b200005", value = "b200005_660213", valueName = "其他职工工资")
    @ApiModelProperty(value = "其他职工工资")
    private BigDecimal b200005660213;

    @LogField(tableName = "b200005", value = "b200005_660214", valueName = "商业保险费")
    @ApiModelProperty(value = "商业保险费")
    private BigDecimal b200005660214;

    @LogField(tableName = "b200005", value = "b200005_660215", valueName = "差旅费")
    @ApiModelProperty(value = "差旅费")
    private BigDecimal b200005660215;

    @LogField(tableName = "b200005", value = "b200005_660216", valueName = "办公费")
    @ApiModelProperty(value = "办公费")
    private BigDecimal b200005660216;

    @LogField(tableName = "b200005", value = "b200005_660217", valueName = "折旧费")
    @ApiModelProperty(value = "折旧费")
    private BigDecimal b200005660217;

    @LogField(tableName = "b200005", value = "b200005_660218", valueName = "修理费")
    @ApiModelProperty(value = "修理费")
    private BigDecimal b200005660218;

    @LogField(tableName = "b200005", value = "b200005_660219", valueName = "物料消耗")
    @ApiModelProperty(value = "物料消耗")
    private BigDecimal b200005660219;

    @LogField(tableName = "b200005", value = "b200005_660220", valueName = "低值易耗品摊销")
    @ApiModelProperty(value = "低值易耗品摊销")
    private BigDecimal b200005660220;

    @LogField(tableName = "b200005", value = "b200005_660221", valueName = "会议费")
    @ApiModelProperty(value = "会议费")
    private BigDecimal b200005660221;

    @LogField(tableName = "b200005", value = "b200005_660222", valueName = "咨询费")
    @ApiModelProperty(value = "咨询费")
    private BigDecimal b200005660222;

    @LogField(tableName = "b200005", value = "b200005_660223", valueName = "软件使用费")
    @ApiModelProperty(value = "软件使用费")
    private BigDecimal b200005660223;

    @LogField(tableName = "b200005", value = "b200005_660224", valueName = "招聘费")
    @ApiModelProperty(value = "招聘费")
    private BigDecimal b200005660224;

    @LogField(tableName = "b200005", value = "b200005_660225", valueName = "专业服务费")
    @ApiModelProperty(value = "专业服务费")
    private BigDecimal b200005660225;

    @LogField(tableName = "b200005", value = "b200005_660226", valueName = "税金")
    @ApiModelProperty(value = "税金")
    private BigDecimal b200005660226;

    @LogField(tableName = "b200005", value = "b200005_660227", valueName = "技术开发费")
    @ApiModelProperty(value = "技术开发费")
    private BigDecimal b200005660227;

    @LogField(tableName = "b200005", value = "b200005_660228", valueName = "技术转让费")
    @ApiModelProperty(value = "技术转让费")
    private BigDecimal b200005660228;

    @LogField(tableName = "b200005", value = "b200005_660229", valueName = "无形资产摊销费")
    @ApiModelProperty(value = "无形资产摊销费")
    private BigDecimal b200005660229;

    @LogField(tableName = "b200005", value = "b200005_660230", valueName = "业务招待费")
    @ApiModelProperty(value = "业务招待费")
    private BigDecimal b200005660230;

    @LogField(tableName = "b200005", value = "b200005_660231", valueName = "研发费用")
    @ApiModelProperty(value = "研发费用")
    private BigDecimal b200005660231;

    @LogField(tableName = "b200005", value = "b200005_660232", valueName = "仓储费用")
    @ApiModelProperty(value = "仓储费用")
    private BigDecimal b200005660232;

    @LogField(tableName = "b200005", value = "b200005_660233", valueName = "保险费")
    @ApiModelProperty(value = "保险费")
    private BigDecimal b200005660233;

    @LogField(tableName = "b200005", value = "b200005_660234", valueName = "房租")
    @ApiModelProperty(value = "房租")
    private BigDecimal b200005660234;

    @LogField(tableName = "b200005", value = "b200005_660235", valueName = "物业管理费")
    @ApiModelProperty(value = "物业管理费")
    private BigDecimal b200005660235;

    @LogField(tableName = "b200005", value = "b200005_660236", valueName = "快递费")
    @ApiModelProperty(value = "快递费")
    private BigDecimal b200005660236;

    @LogField(tableName = "b200005", value = "b200005_660237", valueName = "中介服务费")
    @ApiModelProperty(value = "中介服务费")
    private BigDecimal b200005660237;

    @LogField(tableName = "b200005", value = "b200005_660238", valueName = "开办费")
    @ApiModelProperty(value = "开办费")
    private BigDecimal b200005660238;

    @LogField(tableName = "b200005", value = "b200005_660239", valueName = "水电费")
    @ApiModelProperty(value = "水电费")
    private BigDecimal b200005660239;

    @LogField(tableName = "b200005", value = "b200005_660240", valueName = "运杂费")
    @ApiModelProperty(value = "运杂费")
    private BigDecimal b200005660240;

    @LogField(tableName = "b200005", value = "b200005_660241", valueName = "劳务费")
    @ApiModelProperty(value = "劳务费")
    private BigDecimal b200005660241;

    @LogField(tableName = "b200005", value = "b200005_660242", valueName = "广告费")
    @ApiModelProperty(value = "广告费")
    private BigDecimal b200005660242;

    @LogField(tableName = "b200005", value = "b200005_660243", valueName = "佣金和手续费")
    @ApiModelProperty(value = "佣金和手续费")
    private BigDecimal b200005660243;

    @LogField(tableName = "b200005", value = "b200005_660244", valueName = "董事会费")
    @ApiModelProperty(value = "董事会费")
    private BigDecimal b200005660244;

    @LogField(tableName = "b200005", value = "b200005_660245", valueName = "诉讼费")
    @ApiModelProperty(value = "诉讼费")
    private BigDecimal b200005660245;

    @LogField(tableName = "b200005", value = "b200005_660246", valueName = "包装费")
    @ApiModelProperty(value = "包装费")
    private BigDecimal b200005660246;

    @LogField(tableName = "b200005", value = "b200005_660247", valueName = "党组织工作经费")
    @ApiModelProperty(value = "党组织工作经费")
    private BigDecimal b200005660247;

    @LogField(tableName = "b200005", value = "b200005_660248", valueName = "长期待摊费用摊销费")
    @ApiModelProperty(value = "长期待摊费用摊销费")
    private BigDecimal b200005660248;

    @LogField(tableName = "b200005", value = "b200005_660249", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200005660249;

    @LogField(tableName = "b200005", value = "b200005_660250", valueName = "现金及银行存款损失")
    @ApiModelProperty(value = "现金及银行存款损失")
    private BigDecimal b200005660250;

    @LogField(tableName = "b200005", value = "b200005_660251", valueName = "应收及预付款项坏账损失")
    @ApiModelProperty(value = "应收及预付款项坏账损失")
    private BigDecimal b200005660251;

    @LogField(tableName = "b200005", value = "b200005_660252", valueName = "存货损失")
    @ApiModelProperty(value = "存货损失")
    private BigDecimal b200005660252;

    @LogField(tableName = "b200005", value = "b200005_660253", valueName = "固定资产损失")
    @ApiModelProperty(value = "固定资产损失")
    private BigDecimal b200005660253;

    @LogField(tableName = "b200005", value = "b200005_660254", valueName = "无形资产损失")
    @ApiModelProperty(value = "无形资产损失")
    private BigDecimal b200005660254;

    @LogField(tableName = "b200005", value = "b200005_660255", valueName = "在建工程损失")
    @ApiModelProperty(value = "在建工程损失")
    private BigDecimal b200005660255;

    @LogField(tableName = "b200005", value = "b200005_660256", valueName = "生产性生物资产损失")
    @ApiModelProperty(value = "生产性生物资产损失")
    private BigDecimal b200005660256;

    @LogField(tableName = "b200005", value = "b200005_660257", valueName = "非金融企业债权性投资损失")
    @ApiModelProperty(value = "非金融企业债权性投资损失")
    private BigDecimal b200005660257;

    @LogField(tableName = "b200005", value = "b200005_660258", valueName = "股权（权益）性投资损失")
    @ApiModelProperty(value = "股权（权益）性投资损失")
    private BigDecimal b200005660258;

    @LogField(tableName = "b200005", value = "b200005_660259", valueName = "通过各种交易场所、市场买卖债券、股票、期货、基金以及金融衍生产品等发生的损失")
    @ApiModelProperty(value = "通过各种交易场所、市场买卖债券、股票、期货、基金以及金融衍生产品等发生的损失")
    private BigDecimal b200005660259;

    @LogField(tableName = "b200005", value = "b200005_660260", valueName = "打包出售资产损失")
    @ApiModelProperty(value = "打包出售资产损失")
    private BigDecimal b200005660260;

    @LogField(tableName = "b200005", value = "b200005_660261", valueName = "其他资产损失")
    @ApiModelProperty(value = "其他资产损失")
    private BigDecimal b200005660261;

    @LogField(tableName = "b200005", value = "b200005_6603", valueName = "财务费用")
    @ApiModelProperty(value = "财务费用")
    private BigDecimal b2000056603;

    @LogField(tableName = "b200005", value = "b200005_660301", valueName = "汇兑损益")
    @ApiModelProperty(value = "汇兑损益")
    private BigDecimal b200005660301;

    @LogField(tableName = "b200005", value = "b200005_660302", valueName = "利息费用")
    @ApiModelProperty(value = "利息费用")
    private BigDecimal b200005660302;

    @LogField(tableName = "b200005", value = "b200005_660303", valueName = "佣金和手续费")
    @ApiModelProperty(value = "佣金和手续费")
    private BigDecimal b200005660303;

    @LogField(tableName = "b200005", value = "b200005_660304", valueName = "账户管理费")
    @ApiModelProperty(value = "账户管理费")
    private BigDecimal b200005660304;

    @LogField(tableName = "b200005", value = "b200005_660305", valueName = "利息收入")
    @ApiModelProperty(value = "利息收入")
    private BigDecimal b200005660305;

    @LogField(tableName = "b200005", value = "b200005_66030501", valueName = "因未实现融资收益确认的利息收入")
    @ApiModelProperty(value = "因未实现融资收益确认的利息收入")
    private BigDecimal b20000566030501;

    @LogField(tableName = "b200005", value = "b200005_66030502", valueName = "其他利息收入")
    @ApiModelProperty(value = "其他利息收入")
    private BigDecimal b20000566030502;

    @LogField(tableName = "b200005", value = "b200005_660306", valueName = "现金折扣")
    @ApiModelProperty(value = "现金折扣")
    private BigDecimal b200005660306;

    @LogField(tableName = "b200005", value = "b200005_660307", valueName = "银行手续费")
    @ApiModelProperty(value = "银行手续费")
    private BigDecimal b200005660307;

    @LogField(tableName = "b200005", value = "b200005_660308", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200005660308;

    @LogField(tableName = "b200005", value = "b200005_6604", valueName = "勘探费用")
    @ApiModelProperty(value = "勘探费用")
    private BigDecimal b2000056604;

    @LogField(tableName = "b200005", value = "b200005_6701", valueName = "资产减值损失")
    @ApiModelProperty(value = "资产减值损失")
    private BigDecimal b2000056701;

    @LogField(tableName = "b200005", value = "b200005_6702", valueName = "信用减值损失")
    @ApiModelProperty(value = "信用减值损失")
    private BigDecimal b2000056702;

    @LogField(tableName = "b200005", value = "b200005_6711", valueName = "营业外支出")
    @ApiModelProperty(value = "营业外支出")
    private BigDecimal b2000056711;

    @LogField(tableName = "b200005", value = "b200005_671101", valueName = "非流动资产处置净损失")
    @ApiModelProperty(value = "非流动资产处置净损失")
    private BigDecimal b200005671101;

    @LogField(tableName = "b200005", value = "b200005_67110101", valueName = "存货损失")
    @ApiModelProperty(value = "存货损失")
    private BigDecimal b20000567110101;

    @LogField(tableName = "b200005", value = "b200005_67110102", valueName = "固定资产损失")
    @ApiModelProperty(value = "固定资产损失")
    private BigDecimal b20000567110102;

    @LogField(tableName = "b200005", value = "b200005_67110103", valueName = "无形资产损失")
    @ApiModelProperty(value = "无形资产损失")
    private BigDecimal b20000567110103;

    @LogField(tableName = "b200005", value = "b200005_67110104", valueName = "在建工程损失")
    @ApiModelProperty(value = "在建工程损失")
    private BigDecimal b20000567110104;

    @LogField(tableName = "b200005", value = "b200005_67110105", valueName = "生产性生物资产损失")
    @ApiModelProperty(value = "生产性生物资产损失")
    private BigDecimal b20000567110105;

    @LogField(tableName = "b200005", value = "b200005_67110106", valueName = "打包出售资产损失")
    @ApiModelProperty(value = "打包出售资产损失")
    private BigDecimal b20000567110106;

    @LogField(tableName = "b200005", value = "b200005_67110107", valueName = "其他资产损失")
    @ApiModelProperty(value = "其他资产损失")
    private BigDecimal b20000567110107;

    @LogField(tableName = "b200005", value = "b200005_671102", valueName = "非货币性资产交换损失")
    @ApiModelProperty(value = "非货币性资产交换损失")
    private BigDecimal b200005671102;

    @LogField(tableName = "b200005", value = "b200005_67110201", valueName = "存货损失")
    @ApiModelProperty(value = "存货损失")
    private BigDecimal b20000567110201;

    @LogField(tableName = "b200005", value = "b200005_67110202", valueName = "固定资产损失")
    @ApiModelProperty(value = "固定资产损失")
    private BigDecimal b20000567110202;

    @LogField(tableName = "b200005", value = "b200005_67110203", valueName = "无形资产损失")
    @ApiModelProperty(value = "无形资产损失")
    private BigDecimal b20000567110203;

    @LogField(tableName = "b200005", value = "b200005_67110204", valueName = "在建工程损失")
    @ApiModelProperty(value = "在建工程损失")
    private BigDecimal b20000567110204;

    @LogField(tableName = "b200005", value = "b200005_67110205", valueName = "生产性生物资产损失")
    @ApiModelProperty(value = "生产性生物资产损失")
    private BigDecimal b20000567110205;

    @LogField(tableName = "b200005", value = "b200005_67110206", valueName = "打包出售资产损失")
    @ApiModelProperty(value = "打包出售资产损失")
    private BigDecimal b20000567110206;

    @LogField(tableName = "b200005", value = "b200005_67110207", valueName = "其他资产损失")
    @ApiModelProperty(value = "其他资产损失")
    private BigDecimal b20000567110207;

    @LogField(tableName = "b200005", value = "b200005_671103", valueName = "债务重组损失")
    @ApiModelProperty(value = "债务重组损失")
    private BigDecimal b200005671103;

    @LogField(tableName = "b200005", value = "b200005_671104", valueName = "非常损失")
    @ApiModelProperty(value = "非常损失")
    private BigDecimal b200005671104;

    @LogField(tableName = "b200005", value = "b200005_67110401", valueName = "存货损失")
    @ApiModelProperty(value = "存货损失")
    private BigDecimal b20000567110401;

    @LogField(tableName = "b200005", value = "b200005_67110402", valueName = "固定资产损失")
    @ApiModelProperty(value = "固定资产损失")
    private BigDecimal b20000567110402;

    @LogField(tableName = "b200005", value = "b200005_67110403", valueName = "无形资产损失")
    @ApiModelProperty(value = "无形资产损失")
    private BigDecimal b20000567110403;

    @LogField(tableName = "b200005", value = "b200005_67110404", valueName = "在建工程损失")
    @ApiModelProperty(value = "在建工程损失")
    private BigDecimal b20000567110404;

    @LogField(tableName = "b200005", value = "b200005_67110405", valueName = "生产性生物资产损失")
    @ApiModelProperty(value = "生产性生物资产损失")
    private BigDecimal b20000567110405;

    @LogField(tableName = "b200005", value = "b200005_67110406", valueName = "打包出售资产损失")
    @ApiModelProperty(value = "打包出售资产损失")
    private BigDecimal b20000567110406;

    @LogField(tableName = "b200005", value = "b200005_67110407", valueName = "其他资产损失")
    @ApiModelProperty(value = "其他资产损失")
    private BigDecimal b20000567110407;

    @LogField(tableName = "b200005", value = "b200005_671105", valueName = "捐赠支出")
    @ApiModelProperty(value = "捐赠支出")
    private BigDecimal b200005671105;

    @LogField(tableName = "b200005", value = "b200005_671106", valueName = "赞助支出")
    @ApiModelProperty(value = "赞助支出")
    private BigDecimal b200005671106;

    @LogField(tableName = "b200005", value = "b200005_671107", valueName = "罚没支出")
    @ApiModelProperty(value = "罚没支出")
    private BigDecimal b200005671107;

    @LogField(tableName = "b200005", value = "b200005_67110701", valueName = "经营性处罚")
    @ApiModelProperty(value = "经营性处罚")
    private BigDecimal b20000567110701;

    @LogField(tableName = "b200005", value = "b200005_67110702", valueName = "税收滞纳金")
    @ApiModelProperty(value = "税收滞纳金")
    private BigDecimal b20000567110702;

    @LogField(tableName = "b200005", value = "b200005_67110703", valueName = "行政性处罚")
    @ApiModelProperty(value = "行政性处罚")
    private BigDecimal b20000567110703;

    @LogField(tableName = "b200005", value = "b200005_671108", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200005671108;

    @LogField(tableName = "b200005", value = "b200005_67110801", valueName = "现金及银行存款损失")
    @ApiModelProperty(value = "现金及银行存款损失")
    private BigDecimal b20000567110801;

    @LogField(tableName = "b200005", value = "b200005_67110802", valueName = "应收及预付款项坏账损失")
    @ApiModelProperty(value = "应收及预付款项坏账损失")
    private BigDecimal b20000567110802;

    @LogField(tableName = "b200005", value = "b200005_67110803", valueName = "存货损失")
    @ApiModelProperty(value = "存货损失")
    private BigDecimal b20000567110803;

    @LogField(tableName = "b200005", value = "b200005_67110804", valueName = "固定资产损失")
    @ApiModelProperty(value = "固定资产损失")
    private BigDecimal b20000567110804;

    @LogField(tableName = "b200005", value = "b200005_67110805", valueName = "无形资产损失")
    @ApiModelProperty(value = "无形资产损失")
    private BigDecimal b20000567110805;

    @LogField(tableName = "b200005", value = "b200005_67110806", valueName = "在建工程损失")
    @ApiModelProperty(value = "在建工程损失")
    private BigDecimal b20000567110806;

    @LogField(tableName = "b200005", value = "b200005_67110807", valueName = "生产性生物资产损失")
    @ApiModelProperty(value = "生产性生物资产损失")
    private BigDecimal b20000567110807;

    @LogField(tableName = "b200005", value = "b200005_67110808", valueName = "非金融企业债权性投资损失")
    @ApiModelProperty(value = "非金融企业债权性投资损失")
    private BigDecimal b20000567110808;

    @LogField(tableName = "b200005", value = "b200005_67110809", valueName = "股权（权益）性投资损失")
    @ApiModelProperty(value = "股权（权益）性投资损失")
    private BigDecimal b20000567110809;

    @LogField(tableName = "b200005", value = "b200005_67110810", valueName = "通过各种交易场所、市场买卖债券、股票、期货、基金以及金融衍生产品等发生的损失")
    @ApiModelProperty(value = "通过各种交易场所、市场买卖债券、股票、期货、基金以及金融衍生产品等发生的损失")
    private BigDecimal b20000567110810;

    @LogField(tableName = "b200005", value = "b200005_67110811", valueName = "打包出售资产损失")
    @ApiModelProperty(value = "打包出售资产损失")
    private BigDecimal b20000567110811;

    @LogField(tableName = "b200005", value = "b200005_67110812", valueName = "其他资产损失")
    @ApiModelProperty(value = "其他资产损失")
    private BigDecimal b20000567110812;

    @LogField(tableName = "b200005", value = "b200005_67110813", valueName = "其他支出")
    @ApiModelProperty(value = "其他支出")
    private BigDecimal b20000567110813;

    @LogField(tableName = "b200005", value = "b200005_6801", valueName = "所得税费用")
    @ApiModelProperty(value = "所得税费用")
    private BigDecimal b2000056801;

    @LogField(tableName = "b200005", value = "b200005_6901", valueName = "以前年度损益调整")
    @ApiModelProperty(value = "以前年度损益调整")
    private BigDecimal b2000056901;

    @LogField(tableName = "b200005", value = "b200005_222124", valueName = "应交税费-纳税检查调整")
    @ApiModelProperty(value = "应交税费-纳税检查调整")
    private BigDecimal b200005222124;

    @LogField(tableName = "b200005", value = "b200005_222125", valueName = "应交税费-加计抵减进项税额")
    @ApiModelProperty(value = "应交税费-加计抵减进项税额")
    private BigDecimal b200005222125;

    @LogField(tableName = "b200005", value = "b200005_67110501", valueName = "营业外支出—捐赠支出—公益性捐赠")
    @ApiModelProperty(value = "营业外支出—捐赠支出—公益性捐赠")
    private BigDecimal b20000567110501;

    @LogField(tableName = "b200005", value = "b200005_67110502", valueName = "营业外支出—捐赠支出—非公益性捐赠")
    @ApiModelProperty(value = "营业外支出—捐赠支出—非公益性捐赠")
    private BigDecimal b20000567110502;

    @ApiModelProperty(value = "创建时间")
    private String createTime;

    @ApiModelProperty(value = "更新时间")
    private String updateTime;

    public String getEntId() {
        return entId;
    }

    public void setEntId(String entId) {
        this.entId = entId;
    }

    public String getTaxYear() {
        return taxYear;
    }

    public void setTaxYear(String taxYear) {
        this.taxYear = taxYear;
    }

    public BigDecimal getB2000051001() {
        return b2000051001;
    }

    public void setB2000051001(BigDecimal b2000051001) {
        this.b2000051001 = b2000051001;
    }

    public BigDecimal getB2000051002() {
        return b2000051002;
    }

    public void setB2000051002(BigDecimal b2000051002) {
        this.b2000051002 = b2000051002;
    }

    public BigDecimal getB2000051003() {
        return b2000051003;
    }

    public void setB2000051003(BigDecimal b2000051003) {
        this.b2000051003 = b2000051003;
    }

    public BigDecimal getB2000051011() {
        return b2000051011;
    }

    public void setB2000051011(BigDecimal b2000051011) {
        this.b2000051011 = b2000051011;
    }

    public BigDecimal getB2000051012() {
        return b2000051012;
    }

    public void setB2000051012(BigDecimal b2000051012) {
        this.b2000051012 = b2000051012;
    }

    public BigDecimal getB2000051021() {
        return b2000051021;
    }

    public void setB2000051021(BigDecimal b2000051021) {
        this.b2000051021 = b2000051021;
    }

    public BigDecimal getB2000051031() {
        return b2000051031;
    }

    public void setB2000051031(BigDecimal b2000051031) {
        this.b2000051031 = b2000051031;
    }

    public BigDecimal getB2000051101() {
        return b2000051101;
    }

    public void setB2000051101(BigDecimal b2000051101) {
        this.b2000051101 = b2000051101;
    }

    public BigDecimal getB2000051111() {
        return b2000051111;
    }

    public void setB2000051111(BigDecimal b2000051111) {
        this.b2000051111 = b2000051111;
    }

    public BigDecimal getB2000051121() {
        return b2000051121;
    }

    public void setB2000051121(BigDecimal b2000051121) {
        this.b2000051121 = b2000051121;
    }

    public BigDecimal getB2000051122() {
        return b2000051122;
    }

    public void setB2000051122(BigDecimal b2000051122) {
        this.b2000051122 = b2000051122;
    }

    public BigDecimal getB2000051123() {
        return b2000051123;
    }

    public void setB2000051123(BigDecimal b2000051123) {
        this.b2000051123 = b2000051123;
    }

    public BigDecimal getB2000051124() {
        return b2000051124;
    }

    public void setB2000051124(BigDecimal b2000051124) {
        this.b2000051124 = b2000051124;
    }

    public BigDecimal getB2000051125() {
        return b2000051125;
    }

    public void setB2000051125(BigDecimal b2000051125) {
        this.b2000051125 = b2000051125;
    }

    public BigDecimal getB2000051131() {
        return b2000051131;
    }

    public void setB2000051131(BigDecimal b2000051131) {
        this.b2000051131 = b2000051131;
    }

    public BigDecimal getB2000051132() {
        return b2000051132;
    }

    public void setB2000051132(BigDecimal b2000051132) {
        this.b2000051132 = b2000051132;
    }

    public BigDecimal getB2000051201() {
        return b2000051201;
    }

    public void setB2000051201(BigDecimal b2000051201) {
        this.b2000051201 = b2000051201;
    }

    public BigDecimal getB2000051211() {
        return b2000051211;
    }

    public void setB2000051211(BigDecimal b2000051211) {
        this.b2000051211 = b2000051211;
    }

    public BigDecimal getB2000051212() {
        return b2000051212;
    }

    public void setB2000051212(BigDecimal b2000051212) {
        this.b2000051212 = b2000051212;
    }

    public BigDecimal getB2000051221() {
        return b2000051221;
    }

    public void setB2000051221(BigDecimal b2000051221) {
        this.b2000051221 = b2000051221;
    }

    public BigDecimal getB2000051231() {
        return b2000051231;
    }

    public void setB2000051231(BigDecimal b2000051231) {
        this.b2000051231 = b2000051231;
    }

    public BigDecimal getB2000051301() {
        return b2000051301;
    }

    public void setB2000051301(BigDecimal b2000051301) {
        this.b2000051301 = b2000051301;
    }

    public BigDecimal getB2000051302() {
        return b2000051302;
    }

    public void setB2000051302(BigDecimal b2000051302) {
        this.b2000051302 = b2000051302;
    }

    public BigDecimal getB2000051303() {
        return b2000051303;
    }

    public void setB2000051303(BigDecimal b2000051303) {
        this.b2000051303 = b2000051303;
    }

    public BigDecimal getB2000051304() {
        return b2000051304;
    }

    public void setB2000051304(BigDecimal b2000051304) {
        this.b2000051304 = b2000051304;
    }

    public BigDecimal getB2000051311() {
        return b2000051311;
    }

    public void setB2000051311(BigDecimal b2000051311) {
        this.b2000051311 = b2000051311;
    }

    public BigDecimal getB2000051321() {
        return b2000051321;
    }

    public void setB2000051321(BigDecimal b2000051321) {
        this.b2000051321 = b2000051321;
    }

    public BigDecimal getB2000051401() {
        return b2000051401;
    }

    public void setB2000051401(BigDecimal b2000051401) {
        this.b2000051401 = b2000051401;
    }

    public BigDecimal getB2000051402() {
        return b2000051402;
    }

    public void setB2000051402(BigDecimal b2000051402) {
        this.b2000051402 = b2000051402;
    }

    public BigDecimal getB2000051403() {
        return b2000051403;
    }

    public void setB2000051403(BigDecimal b2000051403) {
        this.b2000051403 = b2000051403;
    }

    public BigDecimal getB2000051404() {
        return b2000051404;
    }

    public void setB2000051404(BigDecimal b2000051404) {
        this.b2000051404 = b2000051404;
    }

    public BigDecimal getB2000051405() {
        return b2000051405;
    }

    public void setB2000051405(BigDecimal b2000051405) {
        this.b2000051405 = b2000051405;
    }

    public BigDecimal getB2000051406() {
        return b2000051406;
    }

    public void setB2000051406(BigDecimal b2000051406) {
        this.b2000051406 = b2000051406;
    }

    public BigDecimal getB2000051407() {
        return b2000051407;
    }

    public void setB2000051407(BigDecimal b2000051407) {
        this.b2000051407 = b2000051407;
    }

    public BigDecimal getB2000051408() {
        return b2000051408;
    }

    public void setB2000051408(BigDecimal b2000051408) {
        this.b2000051408 = b2000051408;
    }

    public BigDecimal getB2000051411() {
        return b2000051411;
    }

    public void setB2000051411(BigDecimal b2000051411) {
        this.b2000051411 = b2000051411;
    }

    public BigDecimal getB2000051421() {
        return b2000051421;
    }

    public void setB2000051421(BigDecimal b2000051421) {
        this.b2000051421 = b2000051421;
    }

    public BigDecimal getB2000051431() {
        return b2000051431;
    }

    public void setB2000051431(BigDecimal b2000051431) {
        this.b2000051431 = b2000051431;
    }

    public BigDecimal getB2000051441() {
        return b2000051441;
    }

    public void setB2000051441(BigDecimal b2000051441) {
        this.b2000051441 = b2000051441;
    }

    public BigDecimal getB2000051451() {
        return b2000051451;
    }

    public void setB2000051451(BigDecimal b2000051451) {
        this.b2000051451 = b2000051451;
    }

    public BigDecimal getB2000051461() {
        return b2000051461;
    }

    public void setB2000051461(BigDecimal b2000051461) {
        this.b2000051461 = b2000051461;
    }

    public BigDecimal getB2000051471() {
        return b2000051471;
    }

    public void setB2000051471(BigDecimal b2000051471) {
        this.b2000051471 = b2000051471;
    }

    public BigDecimal getB2000051481() {
        return b2000051481;
    }

    public void setB2000051481(BigDecimal b2000051481) {
        this.b2000051481 = b2000051481;
    }

    public BigDecimal getB2000051482() {
        return b2000051482;
    }

    public void setB2000051482(BigDecimal b2000051482) {
        this.b2000051482 = b2000051482;
    }

    public BigDecimal getB2000051501() {
        return b2000051501;
    }

    public void setB2000051501(BigDecimal b2000051501) {
        this.b2000051501 = b2000051501;
    }

    public BigDecimal getB2000051502() {
        return b2000051502;
    }

    public void setB2000051502(BigDecimal b2000051502) {
        this.b2000051502 = b2000051502;
    }

    public BigDecimal getB2000051503() {
        return b2000051503;
    }

    public void setB2000051503(BigDecimal b2000051503) {
        this.b2000051503 = b2000051503;
    }

    public BigDecimal getB2000051511() {
        return b2000051511;
    }

    public void setB2000051511(BigDecimal b2000051511) {
        this.b2000051511 = b2000051511;
    }

    public BigDecimal getB2000051512() {
        return b2000051512;
    }

    public void setB2000051512(BigDecimal b2000051512) {
        this.b2000051512 = b2000051512;
    }

    public BigDecimal getB2000051521() {
        return b2000051521;
    }

    public void setB2000051521(BigDecimal b2000051521) {
        this.b2000051521 = b2000051521;
    }

    public BigDecimal getB2000051531() {
        return b2000051531;
    }

    public void setB2000051531(BigDecimal b2000051531) {
        this.b2000051531 = b2000051531;
    }

    public BigDecimal getB2000051532() {
        return b2000051532;
    }

    public void setB2000051532(BigDecimal b2000051532) {
        this.b2000051532 = b2000051532;
    }

    public BigDecimal getB2000051541() {
        return b2000051541;
    }

    public void setB2000051541(BigDecimal b2000051541) {
        this.b2000051541 = b2000051541;
    }

    public BigDecimal getB2000051601() {
        return b2000051601;
    }

    public void setB2000051601(BigDecimal b2000051601) {
        this.b2000051601 = b2000051601;
    }

    public BigDecimal getB2000051602() {
        return b2000051602;
    }

    public void setB2000051602(BigDecimal b2000051602) {
        this.b2000051602 = b2000051602;
    }

    public BigDecimal getB2000051603() {
        return b2000051603;
    }

    public void setB2000051603(BigDecimal b2000051603) {
        this.b2000051603 = b2000051603;
    }

    public BigDecimal getB2000051604() {
        return b2000051604;
    }

    public void setB2000051604(BigDecimal b2000051604) {
        this.b2000051604 = b2000051604;
    }

    public BigDecimal getB2000051605() {
        return b2000051605;
    }

    public void setB2000051605(BigDecimal b2000051605) {
        this.b2000051605 = b2000051605;
    }

    public BigDecimal getB2000051606() {
        return b2000051606;
    }

    public void setB2000051606(BigDecimal b2000051606) {
        this.b2000051606 = b2000051606;
    }

    public BigDecimal getB2000051611() {
        return b2000051611;
    }

    public void setB2000051611(BigDecimal b2000051611) {
        this.b2000051611 = b2000051611;
    }

    public BigDecimal getB2000051621() {
        return b2000051621;
    }

    public void setB2000051621(BigDecimal b2000051621) {
        this.b2000051621 = b2000051621;
    }

    public BigDecimal getB2000051622() {
        return b2000051622;
    }

    public void setB2000051622(BigDecimal b2000051622) {
        this.b2000051622 = b2000051622;
    }

    public BigDecimal getB2000051623() {
        return b2000051623;
    }

    public void setB2000051623(BigDecimal b2000051623) {
        this.b2000051623 = b2000051623;
    }

    public BigDecimal getB2000051631() {
        return b2000051631;
    }

    public void setB2000051631(BigDecimal b2000051631) {
        this.b2000051631 = b2000051631;
    }

    public BigDecimal getB2000051632() {
        return b2000051632;
    }

    public void setB2000051632(BigDecimal b2000051632) {
        this.b2000051632 = b2000051632;
    }

    public BigDecimal getB2000051701() {
        return b2000051701;
    }

    public void setB2000051701(BigDecimal b2000051701) {
        this.b2000051701 = b2000051701;
    }

    public BigDecimal getB2000051702() {
        return b2000051702;
    }

    public void setB2000051702(BigDecimal b2000051702) {
        this.b2000051702 = b2000051702;
    }

    public BigDecimal getB2000051703() {
        return b2000051703;
    }

    public void setB2000051703(BigDecimal b2000051703) {
        this.b2000051703 = b2000051703;
    }

    public BigDecimal getB2000051711() {
        return b2000051711;
    }

    public void setB2000051711(BigDecimal b2000051711) {
        this.b2000051711 = b2000051711;
    }

    public BigDecimal getB2000051801() {
        return b2000051801;
    }

    public void setB2000051801(BigDecimal b2000051801) {
        this.b2000051801 = b2000051801;
    }

    public BigDecimal getB2000051811() {
        return b2000051811;
    }

    public void setB2000051811(BigDecimal b2000051811) {
        this.b2000051811 = b2000051811;
    }

    public BigDecimal getB2000051821() {
        return b2000051821;
    }

    public void setB2000051821(BigDecimal b2000051821) {
        this.b2000051821 = b2000051821;
    }

    public BigDecimal getB2000051901() {
        return b2000051901;
    }

    public void setB2000051901(BigDecimal b2000051901) {
        this.b2000051901 = b2000051901;
    }

    public BigDecimal getB2000052001() {
        return b2000052001;
    }

    public void setB2000052001(BigDecimal b2000052001) {
        this.b2000052001 = b2000052001;
    }

    public BigDecimal getB2000052002() {
        return b2000052002;
    }

    public void setB2000052002(BigDecimal b2000052002) {
        this.b2000052002 = b2000052002;
    }

    public BigDecimal getB2000052003() {
        return b2000052003;
    }

    public void setB2000052003(BigDecimal b2000052003) {
        this.b2000052003 = b2000052003;
    }

    public BigDecimal getB2000052004() {
        return b2000052004;
    }

    public void setB2000052004(BigDecimal b2000052004) {
        this.b2000052004 = b2000052004;
    }

    public BigDecimal getB2000052011() {
        return b2000052011;
    }

    public void setB2000052011(BigDecimal b2000052011) {
        this.b2000052011 = b2000052011;
    }

    public BigDecimal getB2000052012() {
        return b2000052012;
    }

    public void setB2000052012(BigDecimal b2000052012) {
        this.b2000052012 = b2000052012;
    }

    public BigDecimal getB2000052021() {
        return b2000052021;
    }

    public void setB2000052021(BigDecimal b2000052021) {
        this.b2000052021 = b2000052021;
    }

    public BigDecimal getB2000052101() {
        return b2000052101;
    }

    public void setB2000052101(BigDecimal b2000052101) {
        this.b2000052101 = b2000052101;
    }

    public BigDecimal getB2000052111() {
        return b2000052111;
    }

    public void setB2000052111(BigDecimal b2000052111) {
        this.b2000052111 = b2000052111;
    }

    public BigDecimal getB2000052201() {
        return b2000052201;
    }

    public void setB2000052201(BigDecimal b2000052201) {
        this.b2000052201 = b2000052201;
    }

    public BigDecimal getB2000052202() {
        return b2000052202;
    }

    public void setB2000052202(BigDecimal b2000052202) {
        this.b2000052202 = b2000052202;
    }

    public BigDecimal getB2000052203() {
        return b2000052203;
    }

    public void setB2000052203(BigDecimal b2000052203) {
        this.b2000052203 = b2000052203;
    }

    public BigDecimal getB2000052204() {
        return b2000052204;
    }

    public void setB2000052204(BigDecimal b2000052204) {
        this.b2000052204 = b2000052204;
    }

    public BigDecimal getB2000052211() {
        return b2000052211;
    }

    public void setB2000052211(BigDecimal b2000052211) {
        this.b2000052211 = b2000052211;
    }

    public BigDecimal getB200005221101() {
        return b200005221101;
    }

    public void setB200005221101(BigDecimal b200005221101) {
        this.b200005221101 = b200005221101;
    }

    public BigDecimal getB200005221102() {
        return b200005221102;
    }

    public void setB200005221102(BigDecimal b200005221102) {
        this.b200005221102 = b200005221102;
    }

    public BigDecimal getB200005221103() {
        return b200005221103;
    }

    public void setB200005221103(BigDecimal b200005221103) {
        this.b200005221103 = b200005221103;
    }

    public BigDecimal getB200005221104() {
        return b200005221104;
    }

    public void setB200005221104(BigDecimal b200005221104) {
        this.b200005221104 = b200005221104;
    }

    public BigDecimal getB200005221105() {
        return b200005221105;
    }

    public void setB200005221105(BigDecimal b200005221105) {
        this.b200005221105 = b200005221105;
    }

    public BigDecimal getB200005221106() {
        return b200005221106;
    }

    public void setB200005221106(BigDecimal b200005221106) {
        this.b200005221106 = b200005221106;
    }

    public BigDecimal getB200005221107() {
        return b200005221107;
    }

    public void setB200005221107(BigDecimal b200005221107) {
        this.b200005221107 = b200005221107;
    }

    public BigDecimal getB200005221108() {
        return b200005221108;
    }

    public void setB200005221108(BigDecimal b200005221108) {
        this.b200005221108 = b200005221108;
    }

    public BigDecimal getB200005221109() {
        return b200005221109;
    }

    public void setB200005221109(BigDecimal b200005221109) {
        this.b200005221109 = b200005221109;
    }

    public BigDecimal getB200005221110() {
        return b200005221110;
    }

    public void setB200005221110(BigDecimal b200005221110) {
        this.b200005221110 = b200005221110;
    }

    public BigDecimal getB200005221111() {
        return b200005221111;
    }

    public void setB200005221111(BigDecimal b200005221111) {
        this.b200005221111 = b200005221111;
    }

    public BigDecimal getB200005221112() {
        return b200005221112;
    }

    public void setB200005221112(BigDecimal b200005221112) {
        this.b200005221112 = b200005221112;
    }

    public BigDecimal getB200005221113() {
        return b200005221113;
    }

    public void setB200005221113(BigDecimal b200005221113) {
        this.b200005221113 = b200005221113;
    }

    public BigDecimal getB2000052221() {
        return b2000052221;
    }

    public void setB2000052221(BigDecimal b2000052221) {
        this.b2000052221 = b2000052221;
    }

    public BigDecimal getB200005222101() {
        return b200005222101;
    }

    public void setB200005222101(BigDecimal b200005222101) {
        this.b200005222101 = b200005222101;
    }

    public BigDecimal getB20000522210101() {
        return b20000522210101;
    }

    public void setB20000522210101(BigDecimal b20000522210101) {
        this.b20000522210101 = b20000522210101;
    }

    public BigDecimal getB20000522210102() {
        return b20000522210102;
    }

    public void setB20000522210102(BigDecimal b20000522210102) {
        this.b20000522210102 = b20000522210102;
    }

    public BigDecimal getB20000522210103() {
        return b20000522210103;
    }

    public void setB20000522210103(BigDecimal b20000522210103) {
        this.b20000522210103 = b20000522210103;
    }

    public BigDecimal getB20000522210104() {
        return b20000522210104;
    }

    public void setB20000522210104(BigDecimal b20000522210104) {
        this.b20000522210104 = b20000522210104;
    }

    public BigDecimal getB20000522210105() {
        return b20000522210105;
    }

    public void setB20000522210105(BigDecimal b20000522210105) {
        this.b20000522210105 = b20000522210105;
    }

    public BigDecimal getB20000522210106() {
        return b20000522210106;
    }

    public void setB20000522210106(BigDecimal b20000522210106) {
        this.b20000522210106 = b20000522210106;
    }

    public BigDecimal getB20000522210107() {
        return b20000522210107;
    }

    public void setB20000522210107(BigDecimal b20000522210107) {
        this.b20000522210107 = b20000522210107;
    }

    public BigDecimal getB20000522210108() {
        return b20000522210108;
    }

    public void setB20000522210108(BigDecimal b20000522210108) {
        this.b20000522210108 = b20000522210108;
    }

    public BigDecimal getB20000522210109() {
        return b20000522210109;
    }

    public void setB20000522210109(BigDecimal b20000522210109) {
        this.b20000522210109 = b20000522210109;
    }

    public BigDecimal getB20000522210110() {
        return b20000522210110;
    }

    public void setB20000522210110(BigDecimal b20000522210110) {
        this.b20000522210110 = b20000522210110;
    }

    public BigDecimal getB200005222102() {
        return b200005222102;
    }

    public void setB200005222102(BigDecimal b200005222102) {
        this.b200005222102 = b200005222102;
    }

    public BigDecimal getB200005222103() {
        return b200005222103;
    }

    public void setB200005222103(BigDecimal b200005222103) {
        this.b200005222103 = b200005222103;
    }

    public BigDecimal getB200005222104() {
        return b200005222104;
    }

    public void setB200005222104(BigDecimal b200005222104) {
        this.b200005222104 = b200005222104;
    }

    public BigDecimal getB200005222105() {
        return b200005222105;
    }

    public void setB200005222105(BigDecimal b200005222105) {
        this.b200005222105 = b200005222105;
    }

    public BigDecimal getB200005222106() {
        return b200005222106;
    }

    public void setB200005222106(BigDecimal b200005222106) {
        this.b200005222106 = b200005222106;
    }

    public BigDecimal getB200005222107() {
        return b200005222107;
    }

    public void setB200005222107(BigDecimal b200005222107) {
        this.b200005222107 = b200005222107;
    }

    public BigDecimal getB200005222108() {
        return b200005222108;
    }

    public void setB200005222108(BigDecimal b200005222108) {
        this.b200005222108 = b200005222108;
    }

    public BigDecimal getB200005222109() {
        return b200005222109;
    }

    public void setB200005222109(BigDecimal b200005222109) {
        this.b200005222109 = b200005222109;
    }

    public BigDecimal getB200005222110() {
        return b200005222110;
    }

    public void setB200005222110(BigDecimal b200005222110) {
        this.b200005222110 = b200005222110;
    }

    public BigDecimal getB200005222111() {
        return b200005222111;
    }

    public void setB200005222111(BigDecimal b200005222111) {
        this.b200005222111 = b200005222111;
    }

    public BigDecimal getB200005222112() {
        return b200005222112;
    }

    public void setB200005222112(BigDecimal b200005222112) {
        this.b200005222112 = b200005222112;
    }

    public BigDecimal getB200005222113() {
        return b200005222113;
    }

    public void setB200005222113(BigDecimal b200005222113) {
        this.b200005222113 = b200005222113;
    }

    public BigDecimal getB200005222114() {
        return b200005222114;
    }

    public void setB200005222114(BigDecimal b200005222114) {
        this.b200005222114 = b200005222114;
    }

    public BigDecimal getB200005222115() {
        return b200005222115;
    }

    public void setB200005222115(BigDecimal b200005222115) {
        this.b200005222115 = b200005222115;
    }

    public BigDecimal getB200005222116() {
        return b200005222116;
    }

    public void setB200005222116(BigDecimal b200005222116) {
        this.b200005222116 = b200005222116;
    }

    public BigDecimal getB200005222117() {
        return b200005222117;
    }

    public void setB200005222117(BigDecimal b200005222117) {
        this.b200005222117 = b200005222117;
    }

    public BigDecimal getB200005222118() {
        return b200005222118;
    }

    public void setB200005222118(BigDecimal b200005222118) {
        this.b200005222118 = b200005222118;
    }

    public BigDecimal getB200005222119() {
        return b200005222119;
    }

    public void setB200005222119(BigDecimal b200005222119) {
        this.b200005222119 = b200005222119;
    }

    public BigDecimal getB200005222120() {
        return b200005222120;
    }

    public void setB200005222120(BigDecimal b200005222120) {
        this.b200005222120 = b200005222120;
    }

    public BigDecimal getB200005222121() {
        return b200005222121;
    }

    public void setB200005222121(BigDecimal b200005222121) {
        this.b200005222121 = b200005222121;
    }

    public BigDecimal getB200005222122() {
        return b200005222122;
    }

    public void setB200005222122(BigDecimal b200005222122) {
        this.b200005222122 = b200005222122;
    }

    public BigDecimal getB200005222123() {
        return b200005222123;
    }

    public void setB200005222123(BigDecimal b200005222123) {
        this.b200005222123 = b200005222123;
    }

    public BigDecimal getB2000052231() {
        return b2000052231;
    }

    public void setB2000052231(BigDecimal b2000052231) {
        this.b2000052231 = b2000052231;
    }

    public BigDecimal getB2000052232() {
        return b2000052232;
    }

    public void setB2000052232(BigDecimal b2000052232) {
        this.b2000052232 = b2000052232;
    }

    public BigDecimal getB2000052241() {
        return b2000052241;
    }

    public void setB2000052241(BigDecimal b2000052241) {
        this.b2000052241 = b2000052241;
    }

    public BigDecimal getB2000052251() {
        return b2000052251;
    }

    public void setB2000052251(BigDecimal b2000052251) {
        this.b2000052251 = b2000052251;
    }

    public BigDecimal getB2000052261() {
        return b2000052261;
    }

    public void setB2000052261(BigDecimal b2000052261) {
        this.b2000052261 = b2000052261;
    }

    public BigDecimal getB2000052311() {
        return b2000052311;
    }

    public void setB2000052311(BigDecimal b2000052311) {
        this.b2000052311 = b2000052311;
    }

    public BigDecimal getB2000052312() {
        return b2000052312;
    }

    public void setB2000052312(BigDecimal b2000052312) {
        this.b2000052312 = b2000052312;
    }

    public BigDecimal getB2000052313() {
        return b2000052313;
    }

    public void setB2000052313(BigDecimal b2000052313) {
        this.b2000052313 = b2000052313;
    }

    public BigDecimal getB2000052314() {
        return b2000052314;
    }

    public void setB2000052314(BigDecimal b2000052314) {
        this.b2000052314 = b2000052314;
    }

    public BigDecimal getB2000052401() {
        return b2000052401;
    }

    public void setB2000052401(BigDecimal b2000052401) {
        this.b2000052401 = b2000052401;
    }

    public BigDecimal getB2000052245() {
        return b2000052245;
    }

    public void setB2000052245(BigDecimal b2000052245) {
        this.b2000052245 = b2000052245;
    }

    public BigDecimal getB2000052501() {
        return b2000052501;
    }

    public void setB2000052501(BigDecimal b2000052501) {
        this.b2000052501 = b2000052501;
    }

    public BigDecimal getB2000052502() {
        return b2000052502;
    }

    public void setB2000052502(BigDecimal b2000052502) {
        this.b2000052502 = b2000052502;
    }

    public BigDecimal getB2000052601() {
        return b2000052601;
    }

    public void setB2000052601(BigDecimal b2000052601) {
        this.b2000052601 = b2000052601;
    }

    public BigDecimal getB2000052602() {
        return b2000052602;
    }

    public void setB2000052602(BigDecimal b2000052602) {
        this.b2000052602 = b2000052602;
    }

    public BigDecimal getB2000052611() {
        return b2000052611;
    }

    public void setB2000052611(BigDecimal b2000052611) {
        this.b2000052611 = b2000052611;
    }

    public BigDecimal getB2000052621() {
        return b2000052621;
    }

    public void setB2000052621(BigDecimal b2000052621) {
        this.b2000052621 = b2000052621;
    }

    public BigDecimal getB2000052701() {
        return b2000052701;
    }

    public void setB2000052701(BigDecimal b2000052701) {
        this.b2000052701 = b2000052701;
    }

    public BigDecimal getB2000052702() {
        return b2000052702;
    }

    public void setB2000052702(BigDecimal b2000052702) {
        this.b2000052702 = b2000052702;
    }

    public BigDecimal getB2000052711() {
        return b2000052711;
    }

    public void setB2000052711(BigDecimal b2000052711) {
        this.b2000052711 = b2000052711;
    }

    public BigDecimal getB2000052801() {
        return b2000052801;
    }

    public void setB2000052801(BigDecimal b2000052801) {
        this.b2000052801 = b2000052801;
    }

    public BigDecimal getB2000052901() {
        return b2000052901;
    }

    public void setB2000052901(BigDecimal b2000052901) {
        this.b2000052901 = b2000052901;
    }

    public BigDecimal getB2000053001() {
        return b2000053001;
    }

    public void setB2000053001(BigDecimal b2000053001) {
        this.b2000053001 = b2000053001;
    }

    public BigDecimal getB2000053002() {
        return b2000053002;
    }

    public void setB2000053002(BigDecimal b2000053002) {
        this.b2000053002 = b2000053002;
    }

    public BigDecimal getB2000053101() {
        return b2000053101;
    }

    public void setB2000053101(BigDecimal b2000053101) {
        this.b2000053101 = b2000053101;
    }

    public BigDecimal getB2000053201() {
        return b2000053201;
    }

    public void setB2000053201(BigDecimal b2000053201) {
        this.b2000053201 = b2000053201;
    }

    public BigDecimal getB2000053202() {
        return b2000053202;
    }

    public void setB2000053202(BigDecimal b2000053202) {
        this.b2000053202 = b2000053202;
    }

    public BigDecimal getB2000054001() {
        return b2000054001;
    }

    public void setB2000054001(BigDecimal b2000054001) {
        this.b2000054001 = b2000054001;
    }

    public BigDecimal getB2000054002() {
        return b2000054002;
    }

    public void setB2000054002(BigDecimal b2000054002) {
        this.b2000054002 = b2000054002;
    }

    public BigDecimal getB2000054003() {
        return b2000054003;
    }

    public void setB2000054003(BigDecimal b2000054003) {
        this.b2000054003 = b2000054003;
    }

    public BigDecimal getB2000054101() {
        return b2000054101;
    }

    public void setB2000054101(BigDecimal b2000054101) {
        this.b2000054101 = b2000054101;
    }

    public BigDecimal getB2000054102() {
        return b2000054102;
    }

    public void setB2000054102(BigDecimal b2000054102) {
        this.b2000054102 = b2000054102;
    }

    public BigDecimal getB2000054103() {
        return b2000054103;
    }

    public void setB2000054103(BigDecimal b2000054103) {
        this.b2000054103 = b2000054103;
    }

    public BigDecimal getB2000054104() {
        return b2000054104;
    }

    public void setB2000054104(BigDecimal b2000054104) {
        this.b2000054104 = b2000054104;
    }

    public BigDecimal getB2000054201() {
        return b2000054201;
    }

    public void setB2000054201(BigDecimal b2000054201) {
        this.b2000054201 = b2000054201;
    }

    public BigDecimal getB2000054301() {
        return b2000054301;
    }

    public void setB2000054301(BigDecimal b2000054301) {
        this.b2000054301 = b2000054301;
    }

    public BigDecimal getB2000055001() {
        return b2000055001;
    }

    public void setB2000055001(BigDecimal b2000055001) {
        this.b2000055001 = b2000055001;
    }

    public BigDecimal getB200005500101() {
        return b200005500101;
    }

    public void setB200005500101(BigDecimal b200005500101) {
        this.b200005500101 = b200005500101;
    }

    public BigDecimal getB200005500102() {
        return b200005500102;
    }

    public void setB200005500102(BigDecimal b200005500102) {
        this.b200005500102 = b200005500102;
    }

    public BigDecimal getB200005500103() {
        return b200005500103;
    }

    public void setB200005500103(BigDecimal b200005500103) {
        this.b200005500103 = b200005500103;
    }

    public BigDecimal getB200005500104() {
        return b200005500104;
    }

    public void setB200005500104(BigDecimal b200005500104) {
        this.b200005500104 = b200005500104;
    }

    public BigDecimal getB200005500105() {
        return b200005500105;
    }

    public void setB200005500105(BigDecimal b200005500105) {
        this.b200005500105 = b200005500105;
    }

    public BigDecimal getB200005500106() {
        return b200005500106;
    }

    public void setB200005500106(BigDecimal b200005500106) {
        this.b200005500106 = b200005500106;
    }

    public BigDecimal getB200005500107() {
        return b200005500107;
    }

    public void setB200005500107(BigDecimal b200005500107) {
        this.b200005500107 = b200005500107;
    }

    public BigDecimal getB200005500108() {
        return b200005500108;
    }

    public void setB200005500108(BigDecimal b200005500108) {
        this.b200005500108 = b200005500108;
    }

    public BigDecimal getB200005500109() {
        return b200005500109;
    }

    public void setB200005500109(BigDecimal b200005500109) {
        this.b200005500109 = b200005500109;
    }

    public BigDecimal getB200005500110() {
        return b200005500110;
    }

    public void setB200005500110(BigDecimal b200005500110) {
        this.b200005500110 = b200005500110;
    }

    public BigDecimal getB200005500111() {
        return b200005500111;
    }

    public void setB200005500111(BigDecimal b200005500111) {
        this.b200005500111 = b200005500111;
    }

    public BigDecimal getB200005500112() {
        return b200005500112;
    }

    public void setB200005500112(BigDecimal b200005500112) {
        this.b200005500112 = b200005500112;
    }

    public BigDecimal getB200005500113() {
        return b200005500113;
    }

    public void setB200005500113(BigDecimal b200005500113) {
        this.b200005500113 = b200005500113;
    }

    public BigDecimal getB200005500114() {
        return b200005500114;
    }

    public void setB200005500114(BigDecimal b200005500114) {
        this.b200005500114 = b200005500114;
    }

    public BigDecimal getB200005500115() {
        return b200005500115;
    }

    public void setB200005500115(BigDecimal b200005500115) {
        this.b200005500115 = b200005500115;
    }

    public BigDecimal getB200005500116() {
        return b200005500116;
    }

    public void setB200005500116(BigDecimal b200005500116) {
        this.b200005500116 = b200005500116;
    }

    public BigDecimal getB200005500117() {
        return b200005500117;
    }

    public void setB200005500117(BigDecimal b200005500117) {
        this.b200005500117 = b200005500117;
    }

    public BigDecimal getB200005500118() {
        return b200005500118;
    }

    public void setB200005500118(BigDecimal b200005500118) {
        this.b200005500118 = b200005500118;
    }

    public BigDecimal getB2000055101() {
        return b2000055101;
    }

    public void setB2000055101(BigDecimal b2000055101) {
        this.b2000055101 = b2000055101;
    }

    public BigDecimal getB200005510101() {
        return b200005510101;
    }

    public void setB200005510101(BigDecimal b200005510101) {
        this.b200005510101 = b200005510101;
    }

    public BigDecimal getB200005510102() {
        return b200005510102;
    }

    public void setB200005510102(BigDecimal b200005510102) {
        this.b200005510102 = b200005510102;
    }

    public BigDecimal getB200005510103() {
        return b200005510103;
    }

    public void setB200005510103(BigDecimal b200005510103) {
        this.b200005510103 = b200005510103;
    }

    public BigDecimal getB200005510104() {
        return b200005510104;
    }

    public void setB200005510104(BigDecimal b200005510104) {
        this.b200005510104 = b200005510104;
    }

    public BigDecimal getB200005510105() {
        return b200005510105;
    }

    public void setB200005510105(BigDecimal b200005510105) {
        this.b200005510105 = b200005510105;
    }

    public BigDecimal getB200005510106() {
        return b200005510106;
    }

    public void setB200005510106(BigDecimal b200005510106) {
        this.b200005510106 = b200005510106;
    }

    public BigDecimal getB200005510107() {
        return b200005510107;
    }

    public void setB200005510107(BigDecimal b200005510107) {
        this.b200005510107 = b200005510107;
    }

    public BigDecimal getB200005510108() {
        return b200005510108;
    }

    public void setB200005510108(BigDecimal b200005510108) {
        this.b200005510108 = b200005510108;
    }

    public BigDecimal getB200005510109() {
        return b200005510109;
    }

    public void setB200005510109(BigDecimal b200005510109) {
        this.b200005510109 = b200005510109;
    }

    public BigDecimal getB200005510110() {
        return b200005510110;
    }

    public void setB200005510110(BigDecimal b200005510110) {
        this.b200005510110 = b200005510110;
    }

    public BigDecimal getB200005510111() {
        return b200005510111;
    }

    public void setB200005510111(BigDecimal b200005510111) {
        this.b200005510111 = b200005510111;
    }

    public BigDecimal getB200005510112() {
        return b200005510112;
    }

    public void setB200005510112(BigDecimal b200005510112) {
        this.b200005510112 = b200005510112;
    }

    public BigDecimal getB200005510113() {
        return b200005510113;
    }

    public void setB200005510113(BigDecimal b200005510113) {
        this.b200005510113 = b200005510113;
    }

    public BigDecimal getB200005510114() {
        return b200005510114;
    }

    public void setB200005510114(BigDecimal b200005510114) {
        this.b200005510114 = b200005510114;
    }

    public BigDecimal getB200005510115() {
        return b200005510115;
    }

    public void setB200005510115(BigDecimal b200005510115) {
        this.b200005510115 = b200005510115;
    }

    public BigDecimal getB200005510116() {
        return b200005510116;
    }

    public void setB200005510116(BigDecimal b200005510116) {
        this.b200005510116 = b200005510116;
    }

    public BigDecimal getB200005510117() {
        return b200005510117;
    }

    public void setB200005510117(BigDecimal b200005510117) {
        this.b200005510117 = b200005510117;
    }

    public BigDecimal getB200005510118() {
        return b200005510118;
    }

    public void setB200005510118(BigDecimal b200005510118) {
        this.b200005510118 = b200005510118;
    }

    public BigDecimal getB200005510119() {
        return b200005510119;
    }

    public void setB200005510119(BigDecimal b200005510119) {
        this.b200005510119 = b200005510119;
    }

    public BigDecimal getB200005510120() {
        return b200005510120;
    }

    public void setB200005510120(BigDecimal b200005510120) {
        this.b200005510120 = b200005510120;
    }

    public BigDecimal getB200005510121() {
        return b200005510121;
    }

    public void setB200005510121(BigDecimal b200005510121) {
        this.b200005510121 = b200005510121;
    }

    public BigDecimal getB200005510122() {
        return b200005510122;
    }

    public void setB200005510122(BigDecimal b200005510122) {
        this.b200005510122 = b200005510122;
    }

    public BigDecimal getB200005510123() {
        return b200005510123;
    }

    public void setB200005510123(BigDecimal b200005510123) {
        this.b200005510123 = b200005510123;
    }

    public BigDecimal getB200005510124() {
        return b200005510124;
    }

    public void setB200005510124(BigDecimal b200005510124) {
        this.b200005510124 = b200005510124;
    }

    public BigDecimal getB200005510125() {
        return b200005510125;
    }

    public void setB200005510125(BigDecimal b200005510125) {
        this.b200005510125 = b200005510125;
    }

    public BigDecimal getB200005510126() {
        return b200005510126;
    }

    public void setB200005510126(BigDecimal b200005510126) {
        this.b200005510126 = b200005510126;
    }

    public BigDecimal getB200005510127() {
        return b200005510127;
    }

    public void setB200005510127(BigDecimal b200005510127) {
        this.b200005510127 = b200005510127;
    }

    public BigDecimal getB200005510128() {
        return b200005510128;
    }

    public void setB200005510128(BigDecimal b200005510128) {
        this.b200005510128 = b200005510128;
    }

    public BigDecimal getB200005510129() {
        return b200005510129;
    }

    public void setB200005510129(BigDecimal b200005510129) {
        this.b200005510129 = b200005510129;
    }

    public BigDecimal getB200005510130() {
        return b200005510130;
    }

    public void setB200005510130(BigDecimal b200005510130) {
        this.b200005510130 = b200005510130;
    }

    public BigDecimal getB200005510131() {
        return b200005510131;
    }

    public void setB200005510131(BigDecimal b200005510131) {
        this.b200005510131 = b200005510131;
    }

    public BigDecimal getB2000055201() {
        return b2000055201;
    }

    public void setB2000055201(BigDecimal b2000055201) {
        this.b2000055201 = b2000055201;
    }

    public BigDecimal getB200005520101() {
        return b200005520101;
    }

    public void setB200005520101(BigDecimal b200005520101) {
        this.b200005520101 = b200005520101;
    }

    public BigDecimal getB200005520102() {
        return b200005520102;
    }

    public void setB200005520102(BigDecimal b200005520102) {
        this.b200005520102 = b200005520102;
    }

    public BigDecimal getB200005520103() {
        return b200005520103;
    }

    public void setB200005520103(BigDecimal b200005520103) {
        this.b200005520103 = b200005520103;
    }

    public BigDecimal getB200005520104() {
        return b200005520104;
    }

    public void setB200005520104(BigDecimal b200005520104) {
        this.b200005520104 = b200005520104;
    }

    public BigDecimal getB200005520105() {
        return b200005520105;
    }

    public void setB200005520105(BigDecimal b200005520105) {
        this.b200005520105 = b200005520105;
    }

    public BigDecimal getB200005520106() {
        return b200005520106;
    }

    public void setB200005520106(BigDecimal b200005520106) {
        this.b200005520106 = b200005520106;
    }

    public BigDecimal getB200005520107() {
        return b200005520107;
    }

    public void setB200005520107(BigDecimal b200005520107) {
        this.b200005520107 = b200005520107;
    }

    public BigDecimal getB200005520108() {
        return b200005520108;
    }

    public void setB200005520108(BigDecimal b200005520108) {
        this.b200005520108 = b200005520108;
    }

    public BigDecimal getB200005520109() {
        return b200005520109;
    }

    public void setB200005520109(BigDecimal b200005520109) {
        this.b200005520109 = b200005520109;
    }

    public BigDecimal getB200005520110() {
        return b200005520110;
    }

    public void setB200005520110(BigDecimal b200005520110) {
        this.b200005520110 = b200005520110;
    }

    public BigDecimal getB200005520111() {
        return b200005520111;
    }

    public void setB200005520111(BigDecimal b200005520111) {
        this.b200005520111 = b200005520111;
    }

    public BigDecimal getB200005520112() {
        return b200005520112;
    }

    public void setB200005520112(BigDecimal b200005520112) {
        this.b200005520112 = b200005520112;
    }

    public BigDecimal getB200005520113() {
        return b200005520113;
    }

    public void setB200005520113(BigDecimal b200005520113) {
        this.b200005520113 = b200005520113;
    }

    public BigDecimal getB200005520114() {
        return b200005520114;
    }

    public void setB200005520114(BigDecimal b200005520114) {
        this.b200005520114 = b200005520114;
    }

    public BigDecimal getB200005520115() {
        return b200005520115;
    }

    public void setB200005520115(BigDecimal b200005520115) {
        this.b200005520115 = b200005520115;
    }

    public BigDecimal getB2000055301() {
        return b2000055301;
    }

    public void setB2000055301(BigDecimal b2000055301) {
        this.b2000055301 = b2000055301;
    }

    public BigDecimal getB200005530101() {
        return b200005530101;
    }

    public void setB200005530101(BigDecimal b200005530101) {
        this.b200005530101 = b200005530101;
    }

    public BigDecimal getB20000553010101() {
        return b20000553010101;
    }

    public void setB20000553010101(BigDecimal b20000553010101) {
        this.b20000553010101 = b20000553010101;
    }

    public BigDecimal getB2000055301010101() {
        return b2000055301010101;
    }

    public void setB2000055301010101(BigDecimal b2000055301010101) {
        this.b2000055301010101 = b2000055301010101;
    }

    public BigDecimal getB2000055301010102() {
        return b2000055301010102;
    }

    public void setB2000055301010102(BigDecimal b2000055301010102) {
        this.b2000055301010102 = b2000055301010102;
    }

    public BigDecimal getB2000055301010103() {
        return b2000055301010103;
    }

    public void setB2000055301010103(BigDecimal b2000055301010103) {
        this.b2000055301010103 = b2000055301010103;
    }

    public BigDecimal getB2000055301010104() {
        return b2000055301010104;
    }

    public void setB2000055301010104(BigDecimal b2000055301010104) {
        this.b2000055301010104 = b2000055301010104;
    }

    public BigDecimal getB2000055301010105() {
        return b2000055301010105;
    }

    public void setB2000055301010105(BigDecimal b2000055301010105) {
        this.b2000055301010105 = b2000055301010105;
    }

    public BigDecimal getB2000055301010106() {
        return b2000055301010106;
    }

    public void setB2000055301010106(BigDecimal b2000055301010106) {
        this.b2000055301010106 = b2000055301010106;
    }

    public BigDecimal getB2000055301010107() {
        return b2000055301010107;
    }

    public void setB2000055301010107(BigDecimal b2000055301010107) {
        this.b2000055301010107 = b2000055301010107;
    }

    public BigDecimal getB2000055301010108() {
        return b2000055301010108;
    }

    public void setB2000055301010108(BigDecimal b2000055301010108) {
        this.b2000055301010108 = b2000055301010108;
    }

    public BigDecimal getB2000055301010109() {
        return b2000055301010109;
    }

    public void setB2000055301010109(BigDecimal b2000055301010109) {
        this.b2000055301010109 = b2000055301010109;
    }

    public BigDecimal getB2000055301010110() {
        return b2000055301010110;
    }

    public void setB2000055301010110(BigDecimal b2000055301010110) {
        this.b2000055301010110 = b2000055301010110;
    }

    public BigDecimal getB2000055301010111() {
        return b2000055301010111;
    }

    public void setB2000055301010111(BigDecimal b2000055301010111) {
        this.b2000055301010111 = b2000055301010111;
    }

    public BigDecimal getB2000055301010112() {
        return b2000055301010112;
    }

    public void setB2000055301010112(BigDecimal b2000055301010112) {
        this.b2000055301010112 = b2000055301010112;
    }

    public BigDecimal getB2000055301010113() {
        return b2000055301010113;
    }

    public void setB2000055301010113(BigDecimal b2000055301010113) {
        this.b2000055301010113 = b2000055301010113;
    }

    public BigDecimal getB20000553010102() {
        return b20000553010102;
    }

    public void setB20000553010102(BigDecimal b20000553010102) {
        this.b20000553010102 = b20000553010102;
    }

    public BigDecimal getB20000553010103() {
        return b20000553010103;
    }

    public void setB20000553010103(BigDecimal b20000553010103) {
        this.b20000553010103 = b20000553010103;
    }

    public BigDecimal getB2000055301010301() {
        return b2000055301010301;
    }

    public void setB2000055301010301(BigDecimal b2000055301010301) {
        this.b2000055301010301 = b2000055301010301;
    }

    public BigDecimal getB2000055301010302() {
        return b2000055301010302;
    }

    public void setB2000055301010302(BigDecimal b2000055301010302) {
        this.b2000055301010302 = b2000055301010302;
    }

    public BigDecimal getB2000055301010303() {
        return b2000055301010303;
    }

    public void setB2000055301010303(BigDecimal b2000055301010303) {
        this.b2000055301010303 = b2000055301010303;
    }

    public BigDecimal getB2000055301010304() {
        return b2000055301010304;
    }

    public void setB2000055301010304(BigDecimal b2000055301010304) {
        this.b2000055301010304 = b2000055301010304;
    }

    public BigDecimal getB2000055301010305() {
        return b2000055301010305;
    }

    public void setB2000055301010305(BigDecimal b2000055301010305) {
        this.b2000055301010305 = b2000055301010305;
    }

    public BigDecimal getB2000055301010306() {
        return b2000055301010306;
    }

    public void setB2000055301010306(BigDecimal b2000055301010306) {
        this.b2000055301010306 = b2000055301010306;
    }

    public BigDecimal getB2000055301010307() {
        return b2000055301010307;
    }

    public void setB2000055301010307(BigDecimal b2000055301010307) {
        this.b2000055301010307 = b2000055301010307;
    }

    public BigDecimal getB2000055301010308() {
        return b2000055301010308;
    }

    public void setB2000055301010308(BigDecimal b2000055301010308) {
        this.b2000055301010308 = b2000055301010308;
    }

    public BigDecimal getB2000055301010309() {
        return b2000055301010309;
    }

    public void setB2000055301010309(BigDecimal b2000055301010309) {
        this.b2000055301010309 = b2000055301010309;
    }

    public BigDecimal getB2000055301010310() {
        return b2000055301010310;
    }

    public void setB2000055301010310(BigDecimal b2000055301010310) {
        this.b2000055301010310 = b2000055301010310;
    }

    public BigDecimal getB2000055301010311() {
        return b2000055301010311;
    }

    public void setB2000055301010311(BigDecimal b2000055301010311) {
        this.b2000055301010311 = b2000055301010311;
    }

    public BigDecimal getB2000055301010312() {
        return b2000055301010312;
    }

    public void setB2000055301010312(BigDecimal b2000055301010312) {
        this.b2000055301010312 = b2000055301010312;
    }

    public BigDecimal getB2000055301010313() {
        return b2000055301010313;
    }

    public void setB2000055301010313(BigDecimal b2000055301010313) {
        this.b2000055301010313 = b2000055301010313;
    }

    public BigDecimal getB20000553010104() {
        return b20000553010104;
    }

    public void setB20000553010104(BigDecimal b20000553010104) {
        this.b20000553010104 = b20000553010104;
    }

    public BigDecimal getB2000055301010401() {
        return b2000055301010401;
    }

    public void setB2000055301010401(BigDecimal b2000055301010401) {
        this.b2000055301010401 = b2000055301010401;
    }

    public BigDecimal getB2000055301010402() {
        return b2000055301010402;
    }

    public void setB2000055301010402(BigDecimal b2000055301010402) {
        this.b2000055301010402 = b2000055301010402;
    }

    public BigDecimal getB20000553010105() {
        return b20000553010105;
    }

    public void setB20000553010105(BigDecimal b20000553010105) {
        this.b20000553010105 = b20000553010105;
    }

    public BigDecimal getB2000055301010501() {
        return b2000055301010501;
    }

    public void setB2000055301010501(BigDecimal b2000055301010501) {
        this.b2000055301010501 = b2000055301010501;
    }

    public BigDecimal getB2000055301010502() {
        return b2000055301010502;
    }

    public void setB2000055301010502(BigDecimal b2000055301010502) {
        this.b2000055301010502 = b2000055301010502;
    }

    public BigDecimal getB20000553010106() {
        return b20000553010106;
    }

    public void setB20000553010106(BigDecimal b20000553010106) {
        this.b20000553010106 = b20000553010106;
    }

    public BigDecimal getB2000055301010601() {
        return b2000055301010601;
    }

    public void setB2000055301010601(BigDecimal b2000055301010601) {
        this.b2000055301010601 = b2000055301010601;
    }

    public BigDecimal getB2000055301010602() {
        return b2000055301010602;
    }

    public void setB2000055301010602(BigDecimal b2000055301010602) {
        this.b2000055301010602 = b2000055301010602;
    }

    public BigDecimal getB2000055301010603() {
        return b2000055301010603;
    }

    public void setB2000055301010603(BigDecimal b2000055301010603) {
        this.b2000055301010603 = b2000055301010603;
    }

    public BigDecimal getB2000055301010604() {
        return b2000055301010604;
    }

    public void setB2000055301010604(BigDecimal b2000055301010604) {
        this.b2000055301010604 = b2000055301010604;
    }

    public BigDecimal getB20000553010107() {
        return b20000553010107;
    }

    public void setB20000553010107(BigDecimal b20000553010107) {
        this.b20000553010107 = b20000553010107;
    }

    public BigDecimal getB2000055301010701() {
        return b2000055301010701;
    }

    public void setB2000055301010701(BigDecimal b2000055301010701) {
        this.b2000055301010701 = b2000055301010701;
    }

    public BigDecimal getB2000055301010702() {
        return b2000055301010702;
    }

    public void setB2000055301010702(BigDecimal b2000055301010702) {
        this.b2000055301010702 = b2000055301010702;
    }

    public BigDecimal getB2000055301010703() {
        return b2000055301010703;
    }

    public void setB2000055301010703(BigDecimal b2000055301010703) {
        this.b2000055301010703 = b2000055301010703;
    }

    public BigDecimal getB20000553010108() {
        return b20000553010108;
    }

    public void setB20000553010108(BigDecimal b20000553010108) {
        this.b20000553010108 = b20000553010108;
    }

    public BigDecimal getB2000055301010801() {
        return b2000055301010801;
    }

    public void setB2000055301010801(BigDecimal b2000055301010801) {
        this.b2000055301010801 = b2000055301010801;
    }

    public BigDecimal getB2000055301010802() {
        return b2000055301010802;
    }

    public void setB2000055301010802(BigDecimal b2000055301010802) {
        this.b2000055301010802 = b2000055301010802;
    }

    public BigDecimal getB2000055301010803() {
        return b2000055301010803;
    }

    public void setB2000055301010803(BigDecimal b2000055301010803) {
        this.b2000055301010803 = b2000055301010803;
    }

    public BigDecimal getB2000055301010804() {
        return b2000055301010804;
    }

    public void setB2000055301010804(BigDecimal b2000055301010804) {
        this.b2000055301010804 = b2000055301010804;
    }

    public BigDecimal getB2000055301010805() {
        return b2000055301010805;
    }

    public void setB2000055301010805(BigDecimal b2000055301010805) {
        this.b2000055301010805 = b2000055301010805;
    }

    public BigDecimal getB2000055301010806() {
        return b2000055301010806;
    }

    public void setB2000055301010806(BigDecimal b2000055301010806) {
        this.b2000055301010806 = b2000055301010806;
    }

    public BigDecimal getB20000553010109() {
        return b20000553010109;
    }

    public void setB20000553010109(BigDecimal b20000553010109) {
        this.b20000553010109 = b20000553010109;
    }

    public BigDecimal getB2000055301010901() {
        return b2000055301010901;
    }

    public void setB2000055301010901(BigDecimal b2000055301010901) {
        this.b2000055301010901 = b2000055301010901;
    }

    public BigDecimal getB2000055301010902() {
        return b2000055301010902;
    }

    public void setB2000055301010902(BigDecimal b2000055301010902) {
        this.b2000055301010902 = b2000055301010902;
    }

    public BigDecimal getB2000055301010903() {
        return b2000055301010903;
    }

    public void setB2000055301010903(BigDecimal b2000055301010903) {
        this.b2000055301010903 = b2000055301010903;
    }

    public BigDecimal getB2000055301010904() {
        return b2000055301010904;
    }

    public void setB2000055301010904(BigDecimal b2000055301010904) {
        this.b2000055301010904 = b2000055301010904;
    }

    public BigDecimal getB2000055301010905() {
        return b2000055301010905;
    }

    public void setB2000055301010905(BigDecimal b2000055301010905) {
        this.b2000055301010905 = b2000055301010905;
    }

    public BigDecimal getB2000055301010906() {
        return b2000055301010906;
    }

    public void setB2000055301010906(BigDecimal b2000055301010906) {
        this.b2000055301010906 = b2000055301010906;
    }

    public BigDecimal getB2000055301010907() {
        return b2000055301010907;
    }

    public void setB2000055301010907(BigDecimal b2000055301010907) {
        this.b2000055301010907 = b2000055301010907;
    }

    public BigDecimal getB20000553010110() {
        return b20000553010110;
    }

    public void setB20000553010110(BigDecimal b20000553010110) {
        this.b20000553010110 = b20000553010110;
    }

    public BigDecimal getB20000553010111() {
        return b20000553010111;
    }

    public void setB20000553010111(BigDecimal b20000553010111) {
        this.b20000553010111 = b20000553010111;
    }

    public BigDecimal getB2000055301011101() {
        return b2000055301011101;
    }

    public void setB2000055301011101(BigDecimal b2000055301011101) {
        this.b2000055301011101 = b2000055301011101;
    }

    public BigDecimal getB2000055301011102() {
        return b2000055301011102;
    }

    public void setB2000055301011102(BigDecimal b2000055301011102) {
        this.b2000055301011102 = b2000055301011102;
    }

    public BigDecimal getB2000055301011103() {
        return b2000055301011103;
    }

    public void setB2000055301011103(BigDecimal b2000055301011103) {
        this.b2000055301011103 = b2000055301011103;
    }

    public BigDecimal getB2000055301011104() {
        return b2000055301011104;
    }

    public void setB2000055301011104(BigDecimal b2000055301011104) {
        this.b2000055301011104 = b2000055301011104;
    }

    public BigDecimal getB20000553010112() {
        return b20000553010112;
    }

    public void setB20000553010112(BigDecimal b20000553010112) {
        this.b20000553010112 = b20000553010112;
    }

    public BigDecimal getB20000553010113() {
        return b20000553010113;
    }

    public void setB20000553010113(BigDecimal b20000553010113) {
        this.b20000553010113 = b20000553010113;
    }

    public BigDecimal getB20000553010114() {
        return b20000553010114;
    }

    public void setB20000553010114(BigDecimal b20000553010114) {
        this.b20000553010114 = b20000553010114;
    }

    public BigDecimal getB20000553010115() {
        return b20000553010115;
    }

    public void setB20000553010115(BigDecimal b20000553010115) {
        this.b20000553010115 = b20000553010115;
    }

    public BigDecimal getB20000553010116() {
        return b20000553010116;
    }

    public void setB20000553010116(BigDecimal b20000553010116) {
        this.b20000553010116 = b20000553010116;
    }

    public BigDecimal getB2000055301011601() {
        return b2000055301011601;
    }

    public void setB2000055301011601(BigDecimal b2000055301011601) {
        this.b2000055301011601 = b2000055301011601;
    }

    public BigDecimal getB2000055301011602() {
        return b2000055301011602;
    }

    public void setB2000055301011602(BigDecimal b2000055301011602) {
        this.b2000055301011602 = b2000055301011602;
    }

    public BigDecimal getB2000055301011603() {
        return b2000055301011603;
    }

    public void setB2000055301011603(BigDecimal b2000055301011603) {
        this.b2000055301011603 = b2000055301011603;
    }

    public BigDecimal getB2000055301011604() {
        return b2000055301011604;
    }

    public void setB2000055301011604(BigDecimal b2000055301011604) {
        this.b2000055301011604 = b2000055301011604;
    }

    public BigDecimal getB2000055301011605() {
        return b2000055301011605;
    }

    public void setB2000055301011605(BigDecimal b2000055301011605) {
        this.b2000055301011605 = b2000055301011605;
    }

    public BigDecimal getB2000055301011606() {
        return b2000055301011606;
    }

    public void setB2000055301011606(BigDecimal b2000055301011606) {
        this.b2000055301011606 = b2000055301011606;
    }

    public BigDecimal getB2000055301011607() {
        return b2000055301011607;
    }

    public void setB2000055301011607(BigDecimal b2000055301011607) {
        this.b2000055301011607 = b2000055301011607;
    }

    public BigDecimal getB2000055301011608() {
        return b2000055301011608;
    }

    public void setB2000055301011608(BigDecimal b2000055301011608) {
        this.b2000055301011608 = b2000055301011608;
    }

    public BigDecimal getB2000055301011609() {
        return b2000055301011609;
    }

    public void setB2000055301011609(BigDecimal b2000055301011609) {
        this.b2000055301011609 = b2000055301011609;
    }

    public BigDecimal getB20000553010117() {
        return b20000553010117;
    }

    public void setB20000553010117(BigDecimal b20000553010117) {
        this.b20000553010117 = b20000553010117;
    }

    public BigDecimal getB2000055301011701() {
        return b2000055301011701;
    }

    public void setB2000055301011701(BigDecimal b2000055301011701) {
        this.b2000055301011701 = b2000055301011701;
    }

    public BigDecimal getB2000055301011702() {
        return b2000055301011702;
    }

    public void setB2000055301011702(BigDecimal b2000055301011702) {
        this.b2000055301011702 = b2000055301011702;
    }

    public BigDecimal getB2000055301011703() {
        return b2000055301011703;
    }

    public void setB2000055301011703(BigDecimal b2000055301011703) {
        this.b2000055301011703 = b2000055301011703;
    }

    public BigDecimal getB2000055301011704() {
        return b2000055301011704;
    }

    public void setB2000055301011704(BigDecimal b2000055301011704) {
        this.b2000055301011704 = b2000055301011704;
    }

    public BigDecimal getB20000553010118() {
        return b20000553010118;
    }

    public void setB20000553010118(BigDecimal b20000553010118) {
        this.b20000553010118 = b20000553010118;
    }

    public BigDecimal getB20000553010119() {
        return b20000553010119;
    }

    public void setB20000553010119(BigDecimal b20000553010119) {
        this.b20000553010119 = b20000553010119;
    }

    public BigDecimal getB20000553010120() {
        return b20000553010120;
    }

    public void setB20000553010120(BigDecimal b20000553010120) {
        this.b20000553010120 = b20000553010120;
    }

    public BigDecimal getB20000553010121() {
        return b20000553010121;
    }

    public void setB20000553010121(BigDecimal b20000553010121) {
        this.b20000553010121 = b20000553010121;
    }

    public BigDecimal getB20000553010122() {
        return b20000553010122;
    }

    public void setB20000553010122(BigDecimal b20000553010122) {
        this.b20000553010122 = b20000553010122;
    }

    public BigDecimal getB20000553010123() {
        return b20000553010123;
    }

    public void setB20000553010123(BigDecimal b20000553010123) {
        this.b20000553010123 = b20000553010123;
    }

    public BigDecimal getB20000553010124() {
        return b20000553010124;
    }

    public void setB20000553010124(BigDecimal b20000553010124) {
        this.b20000553010124 = b20000553010124;
    }

    public BigDecimal getB20000553010125() {
        return b20000553010125;
    }

    public void setB20000553010125(BigDecimal b20000553010125) {
        this.b20000553010125 = b20000553010125;
    }

    public BigDecimal getB20000553010126() {
        return b20000553010126;
    }

    public void setB20000553010126(BigDecimal b20000553010126) {
        this.b20000553010126 = b20000553010126;
    }

    public BigDecimal getB2000055301012601() {
        return b2000055301012601;
    }

    public void setB2000055301012601(BigDecimal b2000055301012601) {
        this.b2000055301012601 = b2000055301012601;
    }

    public BigDecimal getB2000055301012602() {
        return b2000055301012602;
    }

    public void setB2000055301012602(BigDecimal b2000055301012602) {
        this.b2000055301012602 = b2000055301012602;
    }

    public BigDecimal getB2000055301012603() {
        return b2000055301012603;
    }

    public void setB2000055301012603(BigDecimal b2000055301012603) {
        this.b2000055301012603 = b2000055301012603;
    }

    public BigDecimal getB2000055301012604() {
        return b2000055301012604;
    }

    public void setB2000055301012604(BigDecimal b2000055301012604) {
        this.b2000055301012604 = b2000055301012604;
    }

    public BigDecimal getB200005530102() {
        return b200005530102;
    }

    public void setB200005530102(BigDecimal b200005530102) {
        this.b200005530102 = b200005530102;
    }

    public BigDecimal getB20000553010201() {
        return b20000553010201;
    }

    public void setB20000553010201(BigDecimal b20000553010201) {
        this.b20000553010201 = b20000553010201;
    }

    public BigDecimal getB2000055301020101() {
        return b2000055301020101;
    }

    public void setB2000055301020101(BigDecimal b2000055301020101) {
        this.b2000055301020101 = b2000055301020101;
    }

    public BigDecimal getB2000055301020102() {
        return b2000055301020102;
    }

    public void setB2000055301020102(BigDecimal b2000055301020102) {
        this.b2000055301020102 = b2000055301020102;
    }

    public BigDecimal getB2000055301020103() {
        return b2000055301020103;
    }

    public void setB2000055301020103(BigDecimal b2000055301020103) {
        this.b2000055301020103 = b2000055301020103;
    }

    public BigDecimal getB2000055301020104() {
        return b2000055301020104;
    }

    public void setB2000055301020104(BigDecimal b2000055301020104) {
        this.b2000055301020104 = b2000055301020104;
    }

    public BigDecimal getB2000055301020105() {
        return b2000055301020105;
    }

    public void setB2000055301020105(BigDecimal b2000055301020105) {
        this.b2000055301020105 = b2000055301020105;
    }

    public BigDecimal getB2000055301020106() {
        return b2000055301020106;
    }

    public void setB2000055301020106(BigDecimal b2000055301020106) {
        this.b2000055301020106 = b2000055301020106;
    }

    public BigDecimal getB2000055301020107() {
        return b2000055301020107;
    }

    public void setB2000055301020107(BigDecimal b2000055301020107) {
        this.b2000055301020107 = b2000055301020107;
    }

    public BigDecimal getB2000055301020108() {
        return b2000055301020108;
    }

    public void setB2000055301020108(BigDecimal b2000055301020108) {
        this.b2000055301020108 = b2000055301020108;
    }

    public BigDecimal getB2000055301020109() {
        return b2000055301020109;
    }

    public void setB2000055301020109(BigDecimal b2000055301020109) {
        this.b2000055301020109 = b2000055301020109;
    }

    public BigDecimal getB2000055301020110() {
        return b2000055301020110;
    }

    public void setB2000055301020110(BigDecimal b2000055301020110) {
        this.b2000055301020110 = b2000055301020110;
    }

    public BigDecimal getB2000055301020111() {
        return b2000055301020111;
    }

    public void setB2000055301020111(BigDecimal b2000055301020111) {
        this.b2000055301020111 = b2000055301020111;
    }

    public BigDecimal getB2000055301020112() {
        return b2000055301020112;
    }

    public void setB2000055301020112(BigDecimal b2000055301020112) {
        this.b2000055301020112 = b2000055301020112;
    }

    public BigDecimal getB2000055301020113() {
        return b2000055301020113;
    }

    public void setB2000055301020113(BigDecimal b2000055301020113) {
        this.b2000055301020113 = b2000055301020113;
    }

    public BigDecimal getB20000553010202() {
        return b20000553010202;
    }

    public void setB20000553010202(BigDecimal b20000553010202) {
        this.b20000553010202 = b20000553010202;
    }

    public BigDecimal getB20000553010203() {
        return b20000553010203;
    }

    public void setB20000553010203(BigDecimal b20000553010203) {
        this.b20000553010203 = b20000553010203;
    }

    public BigDecimal getB2000055301020301() {
        return b2000055301020301;
    }

    public void setB2000055301020301(BigDecimal b2000055301020301) {
        this.b2000055301020301 = b2000055301020301;
    }

    public BigDecimal getB2000055301020302() {
        return b2000055301020302;
    }

    public void setB2000055301020302(BigDecimal b2000055301020302) {
        this.b2000055301020302 = b2000055301020302;
    }

    public BigDecimal getB2000055301020303() {
        return b2000055301020303;
    }

    public void setB2000055301020303(BigDecimal b2000055301020303) {
        this.b2000055301020303 = b2000055301020303;
    }

    public BigDecimal getB2000055301020304() {
        return b2000055301020304;
    }

    public void setB2000055301020304(BigDecimal b2000055301020304) {
        this.b2000055301020304 = b2000055301020304;
    }

    public BigDecimal getB2000055301020305() {
        return b2000055301020305;
    }

    public void setB2000055301020305(BigDecimal b2000055301020305) {
        this.b2000055301020305 = b2000055301020305;
    }

    public BigDecimal getB2000055301020306() {
        return b2000055301020306;
    }

    public void setB2000055301020306(BigDecimal b2000055301020306) {
        this.b2000055301020306 = b2000055301020306;
    }

    public BigDecimal getB2000055301020307() {
        return b2000055301020307;
    }

    public void setB2000055301020307(BigDecimal b2000055301020307) {
        this.b2000055301020307 = b2000055301020307;
    }

    public BigDecimal getB2000055301020308() {
        return b2000055301020308;
    }

    public void setB2000055301020308(BigDecimal b2000055301020308) {
        this.b2000055301020308 = b2000055301020308;
    }

    public BigDecimal getB2000055301020309() {
        return b2000055301020309;
    }

    public void setB2000055301020309(BigDecimal b2000055301020309) {
        this.b2000055301020309 = b2000055301020309;
    }

    public BigDecimal getB2000055301020310() {
        return b2000055301020310;
    }

    public void setB2000055301020310(BigDecimal b2000055301020310) {
        this.b2000055301020310 = b2000055301020310;
    }

    public BigDecimal getB2000055301020311() {
        return b2000055301020311;
    }

    public void setB2000055301020311(BigDecimal b2000055301020311) {
        this.b2000055301020311 = b2000055301020311;
    }

    public BigDecimal getB2000055301020312() {
        return b2000055301020312;
    }

    public void setB2000055301020312(BigDecimal b2000055301020312) {
        this.b2000055301020312 = b2000055301020312;
    }

    public BigDecimal getB2000055301020313() {
        return b2000055301020313;
    }

    public void setB2000055301020313(BigDecimal b2000055301020313) {
        this.b2000055301020313 = b2000055301020313;
    }

    public BigDecimal getB20000553010204() {
        return b20000553010204;
    }

    public void setB20000553010204(BigDecimal b20000553010204) {
        this.b20000553010204 = b20000553010204;
    }

    public BigDecimal getB2000055301020401() {
        return b2000055301020401;
    }

    public void setB2000055301020401(BigDecimal b2000055301020401) {
        this.b2000055301020401 = b2000055301020401;
    }

    public BigDecimal getB2000055301020402() {
        return b2000055301020402;
    }

    public void setB2000055301020402(BigDecimal b2000055301020402) {
        this.b2000055301020402 = b2000055301020402;
    }

    public BigDecimal getB20000553010205() {
        return b20000553010205;
    }

    public void setB20000553010205(BigDecimal b20000553010205) {
        this.b20000553010205 = b20000553010205;
    }

    public BigDecimal getB2000055301020501() {
        return b2000055301020501;
    }

    public void setB2000055301020501(BigDecimal b2000055301020501) {
        this.b2000055301020501 = b2000055301020501;
    }

    public BigDecimal getB2000055301020502() {
        return b2000055301020502;
    }

    public void setB2000055301020502(BigDecimal b2000055301020502) {
        this.b2000055301020502 = b2000055301020502;
    }

    public BigDecimal getB20000553010206() {
        return b20000553010206;
    }

    public void setB20000553010206(BigDecimal b20000553010206) {
        this.b20000553010206 = b20000553010206;
    }

    public BigDecimal getB2000055301020601() {
        return b2000055301020601;
    }

    public void setB2000055301020601(BigDecimal b2000055301020601) {
        this.b2000055301020601 = b2000055301020601;
    }

    public BigDecimal getB2000055301020602() {
        return b2000055301020602;
    }

    public void setB2000055301020602(BigDecimal b2000055301020602) {
        this.b2000055301020602 = b2000055301020602;
    }

    public BigDecimal getB2000055301020603() {
        return b2000055301020603;
    }

    public void setB2000055301020603(BigDecimal b2000055301020603) {
        this.b2000055301020603 = b2000055301020603;
    }

    public BigDecimal getB2000055301020604() {
        return b2000055301020604;
    }

    public void setB2000055301020604(BigDecimal b2000055301020604) {
        this.b2000055301020604 = b2000055301020604;
    }

    public BigDecimal getB20000553010207() {
        return b20000553010207;
    }

    public void setB20000553010207(BigDecimal b20000553010207) {
        this.b20000553010207 = b20000553010207;
    }

    public BigDecimal getB2000055301020701() {
        return b2000055301020701;
    }

    public void setB2000055301020701(BigDecimal b2000055301020701) {
        this.b2000055301020701 = b2000055301020701;
    }

    public BigDecimal getB2000055301020702() {
        return b2000055301020702;
    }

    public void setB2000055301020702(BigDecimal b2000055301020702) {
        this.b2000055301020702 = b2000055301020702;
    }

    public BigDecimal getB2000055301020703() {
        return b2000055301020703;
    }

    public void setB2000055301020703(BigDecimal b2000055301020703) {
        this.b2000055301020703 = b2000055301020703;
    }

    public BigDecimal getB20000553010208() {
        return b20000553010208;
    }

    public void setB20000553010208(BigDecimal b20000553010208) {
        this.b20000553010208 = b20000553010208;
    }

    public BigDecimal getB2000055301020801() {
        return b2000055301020801;
    }

    public void setB2000055301020801(BigDecimal b2000055301020801) {
        this.b2000055301020801 = b2000055301020801;
    }

    public BigDecimal getB2000055301020802() {
        return b2000055301020802;
    }

    public void setB2000055301020802(BigDecimal b2000055301020802) {
        this.b2000055301020802 = b2000055301020802;
    }

    public BigDecimal getB2000055301020803() {
        return b2000055301020803;
    }

    public void setB2000055301020803(BigDecimal b2000055301020803) {
        this.b2000055301020803 = b2000055301020803;
    }

    public BigDecimal getB2000055301020804() {
        return b2000055301020804;
    }

    public void setB2000055301020804(BigDecimal b2000055301020804) {
        this.b2000055301020804 = b2000055301020804;
    }

    public BigDecimal getB2000055301020805() {
        return b2000055301020805;
    }

    public void setB2000055301020805(BigDecimal b2000055301020805) {
        this.b2000055301020805 = b2000055301020805;
    }

    public BigDecimal getB2000055301020806() {
        return b2000055301020806;
    }

    public void setB2000055301020806(BigDecimal b2000055301020806) {
        this.b2000055301020806 = b2000055301020806;
    }

    public BigDecimal getB20000553010209() {
        return b20000553010209;
    }

    public void setB20000553010209(BigDecimal b20000553010209) {
        this.b20000553010209 = b20000553010209;
    }

    public BigDecimal getB2000055301020901() {
        return b2000055301020901;
    }

    public void setB2000055301020901(BigDecimal b2000055301020901) {
        this.b2000055301020901 = b2000055301020901;
    }

    public BigDecimal getB2000055301020902() {
        return b2000055301020902;
    }

    public void setB2000055301020902(BigDecimal b2000055301020902) {
        this.b2000055301020902 = b2000055301020902;
    }

    public BigDecimal getB2000055301020903() {
        return b2000055301020903;
    }

    public void setB2000055301020903(BigDecimal b2000055301020903) {
        this.b2000055301020903 = b2000055301020903;
    }

    public BigDecimal getB2000055301020904() {
        return b2000055301020904;
    }

    public void setB2000055301020904(BigDecimal b2000055301020904) {
        this.b2000055301020904 = b2000055301020904;
    }

    public BigDecimal getB2000055301020905() {
        return b2000055301020905;
    }

    public void setB2000055301020905(BigDecimal b2000055301020905) {
        this.b2000055301020905 = b2000055301020905;
    }

    public BigDecimal getB2000055301020906() {
        return b2000055301020906;
    }

    public void setB2000055301020906(BigDecimal b2000055301020906) {
        this.b2000055301020906 = b2000055301020906;
    }

    public BigDecimal getB2000055301020907() {
        return b2000055301020907;
    }

    public void setB2000055301020907(BigDecimal b2000055301020907) {
        this.b2000055301020907 = b2000055301020907;
    }

    public BigDecimal getB20000553010210() {
        return b20000553010210;
    }

    public void setB20000553010210(BigDecimal b20000553010210) {
        this.b20000553010210 = b20000553010210;
    }

    public BigDecimal getB20000553010211() {
        return b20000553010211;
    }

    public void setB20000553010211(BigDecimal b20000553010211) {
        this.b20000553010211 = b20000553010211;
    }

    public BigDecimal getB2000055301021101() {
        return b2000055301021101;
    }

    public void setB2000055301021101(BigDecimal b2000055301021101) {
        this.b2000055301021101 = b2000055301021101;
    }

    public BigDecimal getB2000055301021102() {
        return b2000055301021102;
    }

    public void setB2000055301021102(BigDecimal b2000055301021102) {
        this.b2000055301021102 = b2000055301021102;
    }

    public BigDecimal getB2000055301021103() {
        return b2000055301021103;
    }

    public void setB2000055301021103(BigDecimal b2000055301021103) {
        this.b2000055301021103 = b2000055301021103;
    }

    public BigDecimal getB2000055301021104() {
        return b2000055301021104;
    }

    public void setB2000055301021104(BigDecimal b2000055301021104) {
        this.b2000055301021104 = b2000055301021104;
    }

    public BigDecimal getB20000553010212() {
        return b20000553010212;
    }

    public void setB20000553010212(BigDecimal b20000553010212) {
        this.b20000553010212 = b20000553010212;
    }

    public BigDecimal getB20000553010213() {
        return b20000553010213;
    }

    public void setB20000553010213(BigDecimal b20000553010213) {
        this.b20000553010213 = b20000553010213;
    }

    public BigDecimal getB20000553010214() {
        return b20000553010214;
    }

    public void setB20000553010214(BigDecimal b20000553010214) {
        this.b20000553010214 = b20000553010214;
    }

    public BigDecimal getB20000553010215() {
        return b20000553010215;
    }

    public void setB20000553010215(BigDecimal b20000553010215) {
        this.b20000553010215 = b20000553010215;
    }

    public BigDecimal getB20000553010216() {
        return b20000553010216;
    }

    public void setB20000553010216(BigDecimal b20000553010216) {
        this.b20000553010216 = b20000553010216;
    }

    public BigDecimal getB2000055301021601() {
        return b2000055301021601;
    }

    public void setB2000055301021601(BigDecimal b2000055301021601) {
        this.b2000055301021601 = b2000055301021601;
    }

    public BigDecimal getB2000055301021602() {
        return b2000055301021602;
    }

    public void setB2000055301021602(BigDecimal b2000055301021602) {
        this.b2000055301021602 = b2000055301021602;
    }

    public BigDecimal getB2000055301021603() {
        return b2000055301021603;
    }

    public void setB2000055301021603(BigDecimal b2000055301021603) {
        this.b2000055301021603 = b2000055301021603;
    }

    public BigDecimal getB2000055301021604() {
        return b2000055301021604;
    }

    public void setB2000055301021604(BigDecimal b2000055301021604) {
        this.b2000055301021604 = b2000055301021604;
    }

    public BigDecimal getB2000055301021605() {
        return b2000055301021605;
    }

    public void setB2000055301021605(BigDecimal b2000055301021605) {
        this.b2000055301021605 = b2000055301021605;
    }

    public BigDecimal getB2000055301021606() {
        return b2000055301021606;
    }

    public void setB2000055301021606(BigDecimal b2000055301021606) {
        this.b2000055301021606 = b2000055301021606;
    }

    public BigDecimal getB2000055301021607() {
        return b2000055301021607;
    }

    public void setB2000055301021607(BigDecimal b2000055301021607) {
        this.b2000055301021607 = b2000055301021607;
    }

    public BigDecimal getB2000055301021608() {
        return b2000055301021608;
    }

    public void setB2000055301021608(BigDecimal b2000055301021608) {
        this.b2000055301021608 = b2000055301021608;
    }

    public BigDecimal getB2000055301021609() {
        return b2000055301021609;
    }

    public void setB2000055301021609(BigDecimal b2000055301021609) {
        this.b2000055301021609 = b2000055301021609;
    }

    public BigDecimal getB20000553010217() {
        return b20000553010217;
    }

    public void setB20000553010217(BigDecimal b20000553010217) {
        this.b20000553010217 = b20000553010217;
    }

    public BigDecimal getB2000055301021701() {
        return b2000055301021701;
    }

    public void setB2000055301021701(BigDecimal b2000055301021701) {
        this.b2000055301021701 = b2000055301021701;
    }

    public BigDecimal getB2000055301021702() {
        return b2000055301021702;
    }

    public void setB2000055301021702(BigDecimal b2000055301021702) {
        this.b2000055301021702 = b2000055301021702;
    }

    public BigDecimal getB2000055301021703() {
        return b2000055301021703;
    }

    public void setB2000055301021703(BigDecimal b2000055301021703) {
        this.b2000055301021703 = b2000055301021703;
    }

    public BigDecimal getB2000055301021704() {
        return b2000055301021704;
    }

    public void setB2000055301021704(BigDecimal b2000055301021704) {
        this.b2000055301021704 = b2000055301021704;
    }

    public BigDecimal getB20000553010218() {
        return b20000553010218;
    }

    public void setB20000553010218(BigDecimal b20000553010218) {
        this.b20000553010218 = b20000553010218;
    }

    public BigDecimal getB20000553010219() {
        return b20000553010219;
    }

    public void setB20000553010219(BigDecimal b20000553010219) {
        this.b20000553010219 = b20000553010219;
    }

    public BigDecimal getB20000553010220() {
        return b20000553010220;
    }

    public void setB20000553010220(BigDecimal b20000553010220) {
        this.b20000553010220 = b20000553010220;
    }

    public BigDecimal getB20000553010221() {
        return b20000553010221;
    }

    public void setB20000553010221(BigDecimal b20000553010221) {
        this.b20000553010221 = b20000553010221;
    }

    public BigDecimal getB20000553010222() {
        return b20000553010222;
    }

    public void setB20000553010222(BigDecimal b20000553010222) {
        this.b20000553010222 = b20000553010222;
    }

    public BigDecimal getB20000553010223() {
        return b20000553010223;
    }

    public void setB20000553010223(BigDecimal b20000553010223) {
        this.b20000553010223 = b20000553010223;
    }

    public BigDecimal getB20000553010224() {
        return b20000553010224;
    }

    public void setB20000553010224(BigDecimal b20000553010224) {
        this.b20000553010224 = b20000553010224;
    }

    public BigDecimal getB20000553010225() {
        return b20000553010225;
    }

    public void setB20000553010225(BigDecimal b20000553010225) {
        this.b20000553010225 = b20000553010225;
    }

    public BigDecimal getB20000553010226() {
        return b20000553010226;
    }

    public void setB20000553010226(BigDecimal b20000553010226) {
        this.b20000553010226 = b20000553010226;
    }

    public BigDecimal getB2000055301022601() {
        return b2000055301022601;
    }

    public void setB2000055301022601(BigDecimal b2000055301022601) {
        this.b2000055301022601 = b2000055301022601;
    }

    public BigDecimal getB2000055301022602() {
        return b2000055301022602;
    }

    public void setB2000055301022602(BigDecimal b2000055301022602) {
        this.b2000055301022602 = b2000055301022602;
    }

    public BigDecimal getB2000055301022603() {
        return b2000055301022603;
    }

    public void setB2000055301022603(BigDecimal b2000055301022603) {
        this.b2000055301022603 = b2000055301022603;
    }

    public BigDecimal getB2000055301022604() {
        return b2000055301022604;
    }

    public void setB2000055301022604(BigDecimal b2000055301022604) {
        this.b2000055301022604 = b2000055301022604;
    }

    public BigDecimal getB2000055401() {
        return b2000055401;
    }

    public void setB2000055401(BigDecimal b2000055401) {
        this.b2000055401 = b2000055401;
    }

    public BigDecimal getB2000055402() {
        return b2000055402;
    }

    public void setB2000055402(BigDecimal b2000055402) {
        this.b2000055402 = b2000055402;
    }

    public BigDecimal getB2000055403() {
        return b2000055403;
    }

    public void setB2000055403(BigDecimal b2000055403) {
        this.b2000055403 = b2000055403;
    }

    public BigDecimal getB2000055501() {
        return b2000055501;
    }

    public void setB2000055501(BigDecimal b2000055501) {
        this.b2000055501 = b2000055501;
    }

    public BigDecimal getB200005550101() {
        return b200005550101;
    }

    public void setB200005550101(BigDecimal b200005550101) {
        this.b200005550101 = b200005550101;
    }

    public BigDecimal getB200005550102() {
        return b200005550102;
    }

    public void setB200005550102(BigDecimal b200005550102) {
        this.b200005550102 = b200005550102;
    }

    public BigDecimal getB200005550103() {
        return b200005550103;
    }

    public void setB200005550103(BigDecimal b200005550103) {
        this.b200005550103 = b200005550103;
    }

    public BigDecimal getB200005550104() {
        return b200005550104;
    }

    public void setB200005550104(BigDecimal b200005550104) {
        this.b200005550104 = b200005550104;
    }

    public BigDecimal getB200005550105() {
        return b200005550105;
    }

    public void setB200005550105(BigDecimal b200005550105) {
        this.b200005550105 = b200005550105;
    }

    public BigDecimal getB200005550106() {
        return b200005550106;
    }

    public void setB200005550106(BigDecimal b200005550106) {
        this.b200005550106 = b200005550106;
    }

    public BigDecimal getB200005550107() {
        return b200005550107;
    }

    public void setB200005550107(BigDecimal b200005550107) {
        this.b200005550107 = b200005550107;
    }

    public BigDecimal getB200005550108() {
        return b200005550108;
    }

    public void setB200005550108(BigDecimal b200005550108) {
        this.b200005550108 = b200005550108;
    }

    public BigDecimal getB200005550109() {
        return b200005550109;
    }

    public void setB200005550109(BigDecimal b200005550109) {
        this.b200005550109 = b200005550109;
    }

    public BigDecimal getB200005550110() {
        return b200005550110;
    }

    public void setB200005550110(BigDecimal b200005550110) {
        this.b200005550110 = b200005550110;
    }

    public BigDecimal getB200005550111() {
        return b200005550111;
    }

    public void setB200005550111(BigDecimal b200005550111) {
        this.b200005550111 = b200005550111;
    }

    public BigDecimal getB200005550112() {
        return b200005550112;
    }

    public void setB200005550112(BigDecimal b200005550112) {
        this.b200005550112 = b200005550112;
    }

    public BigDecimal getB200005550113() {
        return b200005550113;
    }

    public void setB200005550113(BigDecimal b200005550113) {
        this.b200005550113 = b200005550113;
    }

    public BigDecimal getB200005550114() {
        return b200005550114;
    }

    public void setB200005550114(BigDecimal b200005550114) {
        this.b200005550114 = b200005550114;
    }

    public BigDecimal getB200005550115() {
        return b200005550115;
    }

    public void setB200005550115(BigDecimal b200005550115) {
        this.b200005550115 = b200005550115;
    }

    public BigDecimal getB2000055502() {
        return b2000055502;
    }

    public void setB2000055502(BigDecimal b2000055502) {
        this.b2000055502 = b2000055502;
    }

    public BigDecimal getB2000055503() {
        return b2000055503;
    }

    public void setB2000055503(BigDecimal b2000055503) {
        this.b2000055503 = b2000055503;
    }

    public BigDecimal getB2000055504() {
        return b2000055504;
    }

    public void setB2000055504(BigDecimal b2000055504) {
        this.b2000055504 = b2000055504;
    }

    public BigDecimal getB2000056001() {
        return b2000056001;
    }

    public void setB2000056001(BigDecimal b2000056001) {
        this.b2000056001 = b2000056001;
    }

    public BigDecimal getB200005600101() {
        return b200005600101;
    }

    public void setB200005600101(BigDecimal b200005600101) {
        this.b200005600101 = b200005600101;
    }

    public BigDecimal getB200005600102() {
        return b200005600102;
    }

    public void setB200005600102(BigDecimal b200005600102) {
        this.b200005600102 = b200005600102;
    }

    public BigDecimal getB200005600103() {
        return b200005600103;
    }

    public void setB200005600103(BigDecimal b200005600103) {
        this.b200005600103 = b200005600103;
    }

    public BigDecimal getB200005600104() {
        return b200005600104;
    }

    public void setB200005600104(BigDecimal b200005600104) {
        this.b200005600104 = b200005600104;
    }

    public BigDecimal getB200005600105() {
        return b200005600105;
    }

    public void setB200005600105(BigDecimal b200005600105) {
        this.b200005600105 = b200005600105;
    }

    public BigDecimal getB2000056011() {
        return b2000056011;
    }

    public void setB2000056011(BigDecimal b2000056011) {
        this.b2000056011 = b2000056011;
    }

    public BigDecimal getB2000056021() {
        return b2000056021;
    }

    public void setB2000056021(BigDecimal b2000056021) {
        this.b2000056021 = b2000056021;
    }

    public BigDecimal getB2000056031() {
        return b2000056031;
    }

    public void setB2000056031(BigDecimal b2000056031) {
        this.b2000056031 = b2000056031;
    }

    public BigDecimal getB2000056041() {
        return b2000056041;
    }

    public void setB2000056041(BigDecimal b2000056041) {
        this.b2000056041 = b2000056041;
    }

    public BigDecimal getB2000056051() {
        return b2000056051;
    }

    public void setB2000056051(BigDecimal b2000056051) {
        this.b2000056051 = b2000056051;
    }

    public BigDecimal getB200005605101() {
        return b200005605101;
    }

    public void setB200005605101(BigDecimal b200005605101) {
        this.b200005605101 = b200005605101;
    }

    public BigDecimal getB200005605102() {
        return b200005605102;
    }

    public void setB200005605102(BigDecimal b200005605102) {
        this.b200005605102 = b200005605102;
    }

    public BigDecimal getB200005605103() {
        return b200005605103;
    }

    public void setB200005605103(BigDecimal b200005605103) {
        this.b200005605103 = b200005605103;
    }

    public BigDecimal getB200005605104() {
        return b200005605104;
    }

    public void setB200005605104(BigDecimal b200005605104) {
        this.b200005605104 = b200005605104;
    }

    public BigDecimal getB200005605105() {
        return b200005605105;
    }

    public void setB200005605105(BigDecimal b200005605105) {
        this.b200005605105 = b200005605105;
    }

    public BigDecimal getB2000056061() {
        return b2000056061;
    }

    public void setB2000056061(BigDecimal b2000056061) {
        this.b2000056061 = b2000056061;
    }

    public BigDecimal getB2000056101() {
        return b2000056101;
    }

    public void setB2000056101(BigDecimal b2000056101) {
        this.b2000056101 = b2000056101;
    }

    public BigDecimal getB2000056111() {
        return b2000056111;
    }

    public void setB2000056111(BigDecimal b2000056111) {
        this.b2000056111 = b2000056111;
    }

    public BigDecimal getB200005611101() {
        return b200005611101;
    }

    public void setB200005611101(BigDecimal b200005611101) {
        this.b200005611101 = b200005611101;
    }

    public BigDecimal getB20000561110101() {
        return b20000561110101;
    }

    public void setB20000561110101(BigDecimal b20000561110101) {
        this.b20000561110101 = b20000561110101;
    }

    public BigDecimal getB2000056111010101() {
        return b2000056111010101;
    }

    public void setB2000056111010101(BigDecimal b2000056111010101) {
        this.b2000056111010101 = b2000056111010101;
    }

    public BigDecimal getB2000056111010102() {
        return b2000056111010102;
    }

    public void setB2000056111010102(BigDecimal b2000056111010102) {
        this.b2000056111010102 = b2000056111010102;
    }

    public BigDecimal getB20000561110102() {
        return b20000561110102;
    }

    public void setB20000561110102(BigDecimal b20000561110102) {
        this.b20000561110102 = b20000561110102;
    }

    public BigDecimal getB2000056111010201() {
        return b2000056111010201;
    }

    public void setB2000056111010201(BigDecimal b2000056111010201) {
        this.b2000056111010201 = b2000056111010201;
    }

    public BigDecimal getB2000056111010202() {
        return b2000056111010202;
    }

    public void setB2000056111010202(BigDecimal b2000056111010202) {
        this.b2000056111010202 = b2000056111010202;
    }

    public BigDecimal getB20000561110103() {
        return b20000561110103;
    }

    public void setB20000561110103(BigDecimal b20000561110103) {
        this.b20000561110103 = b20000561110103;
    }

    public BigDecimal getB2000056111010301() {
        return b2000056111010301;
    }

    public void setB2000056111010301(BigDecimal b2000056111010301) {
        this.b2000056111010301 = b2000056111010301;
    }

    public BigDecimal getB2000056111010302() {
        return b2000056111010302;
    }

    public void setB2000056111010302(BigDecimal b2000056111010302) {
        this.b2000056111010302 = b2000056111010302;
    }

    public BigDecimal getB20000561110104() {
        return b20000561110104;
    }

    public void setB20000561110104(BigDecimal b20000561110104) {
        this.b20000561110104 = b20000561110104;
    }

    public BigDecimal getB2000056111010401() {
        return b2000056111010401;
    }

    public void setB2000056111010401(BigDecimal b2000056111010401) {
        this.b2000056111010401 = b2000056111010401;
    }

    public BigDecimal getB2000056111010402() {
        return b2000056111010402;
    }

    public void setB2000056111010402(BigDecimal b2000056111010402) {
        this.b2000056111010402 = b2000056111010402;
    }

    public BigDecimal getB20000561110105() {
        return b20000561110105;
    }

    public void setB20000561110105(BigDecimal b20000561110105) {
        this.b20000561110105 = b20000561110105;
    }

    public BigDecimal getB2000056111010501() {
        return b2000056111010501;
    }

    public void setB2000056111010501(BigDecimal b2000056111010501) {
        this.b2000056111010501 = b2000056111010501;
    }

    public BigDecimal getB2000056111010502() {
        return b2000056111010502;
    }

    public void setB2000056111010502(BigDecimal b2000056111010502) {
        this.b2000056111010502 = b2000056111010502;
    }

    public BigDecimal getB20000561110106() {
        return b20000561110106;
    }

    public void setB20000561110106(BigDecimal b20000561110106) {
        this.b20000561110106 = b20000561110106;
    }

    public BigDecimal getB2000056111010601() {
        return b2000056111010601;
    }

    public void setB2000056111010601(BigDecimal b2000056111010601) {
        this.b2000056111010601 = b2000056111010601;
    }

    public BigDecimal getB2000056111010602() {
        return b2000056111010602;
    }

    public void setB2000056111010602(BigDecimal b2000056111010602) {
        this.b2000056111010602 = b2000056111010602;
    }

    public BigDecimal getB20000561110107() {
        return b20000561110107;
    }

    public void setB20000561110107(BigDecimal b20000561110107) {
        this.b20000561110107 = b20000561110107;
    }

    public BigDecimal getB2000056111010701() {
        return b2000056111010701;
    }

    public void setB2000056111010701(BigDecimal b2000056111010701) {
        this.b2000056111010701 = b2000056111010701;
    }

    public BigDecimal getB2000056111010702() {
        return b2000056111010702;
    }

    public void setB2000056111010702(BigDecimal b2000056111010702) {
        this.b2000056111010702 = b2000056111010702;
    }

    public BigDecimal getB20000561110108() {
        return b20000561110108;
    }

    public void setB20000561110108(BigDecimal b20000561110108) {
        this.b20000561110108 = b20000561110108;
    }

    public BigDecimal getB2000056111010801() {
        return b2000056111010801;
    }

    public void setB2000056111010801(BigDecimal b2000056111010801) {
        this.b2000056111010801 = b2000056111010801;
    }

    public BigDecimal getB2000056111010802() {
        return b2000056111010802;
    }

    public void setB2000056111010802(BigDecimal b2000056111010802) {
        this.b2000056111010802 = b2000056111010802;
    }

    public BigDecimal getB200005611102() {
        return b200005611102;
    }

    public void setB200005611102(BigDecimal b200005611102) {
        this.b200005611102 = b200005611102;
    }

    public BigDecimal getB20000561110201() {
        return b20000561110201;
    }

    public void setB20000561110201(BigDecimal b20000561110201) {
        this.b20000561110201 = b20000561110201;
    }

    public BigDecimal getB2000056111020101() {
        return b2000056111020101;
    }

    public void setB2000056111020101(BigDecimal b2000056111020101) {
        this.b2000056111020101 = b2000056111020101;
    }

    public BigDecimal getB2000056111020102() {
        return b2000056111020102;
    }

    public void setB2000056111020102(BigDecimal b2000056111020102) {
        this.b2000056111020102 = b2000056111020102;
    }

    public BigDecimal getB20000561110202() {
        return b20000561110202;
    }

    public void setB20000561110202(BigDecimal b20000561110202) {
        this.b20000561110202 = b20000561110202;
    }

    public BigDecimal getB2000056111020201() {
        return b2000056111020201;
    }

    public void setB2000056111020201(BigDecimal b2000056111020201) {
        this.b2000056111020201 = b2000056111020201;
    }

    public BigDecimal getB2000056111020202() {
        return b2000056111020202;
    }

    public void setB2000056111020202(BigDecimal b2000056111020202) {
        this.b2000056111020202 = b2000056111020202;
    }

    public BigDecimal getB20000561110203() {
        return b20000561110203;
    }

    public void setB20000561110203(BigDecimal b20000561110203) {
        this.b20000561110203 = b20000561110203;
    }

    public BigDecimal getB2000056111020301() {
        return b2000056111020301;
    }

    public void setB2000056111020301(BigDecimal b2000056111020301) {
        this.b2000056111020301 = b2000056111020301;
    }

    public BigDecimal getB2000056111020302() {
        return b2000056111020302;
    }

    public void setB2000056111020302(BigDecimal b2000056111020302) {
        this.b2000056111020302 = b2000056111020302;
    }

    public BigDecimal getB20000561110204() {
        return b20000561110204;
    }

    public void setB20000561110204(BigDecimal b20000561110204) {
        this.b20000561110204 = b20000561110204;
    }

    public BigDecimal getB2000056111020401() {
        return b2000056111020401;
    }

    public void setB2000056111020401(BigDecimal b2000056111020401) {
        this.b2000056111020401 = b2000056111020401;
    }

    public BigDecimal getB2000056111020402() {
        return b2000056111020402;
    }

    public void setB2000056111020402(BigDecimal b2000056111020402) {
        this.b2000056111020402 = b2000056111020402;
    }

    public BigDecimal getB20000561110205() {
        return b20000561110205;
    }

    public void setB20000561110205(BigDecimal b20000561110205) {
        this.b20000561110205 = b20000561110205;
    }

    public BigDecimal getB2000056111020501() {
        return b2000056111020501;
    }

    public void setB2000056111020501(BigDecimal b2000056111020501) {
        this.b2000056111020501 = b2000056111020501;
    }

    public BigDecimal getB2000056111020502() {
        return b2000056111020502;
    }

    public void setB2000056111020502(BigDecimal b2000056111020502) {
        this.b2000056111020502 = b2000056111020502;
    }

    public BigDecimal getB20000561110206() {
        return b20000561110206;
    }

    public void setB20000561110206(BigDecimal b20000561110206) {
        this.b20000561110206 = b20000561110206;
    }

    public BigDecimal getB2000056111020601() {
        return b2000056111020601;
    }

    public void setB2000056111020601(BigDecimal b2000056111020601) {
        this.b2000056111020601 = b2000056111020601;
    }

    public BigDecimal getB2000056111020602() {
        return b2000056111020602;
    }

    public void setB2000056111020602(BigDecimal b2000056111020602) {
        this.b2000056111020602 = b2000056111020602;
    }

    public BigDecimal getB20000561110207() {
        return b20000561110207;
    }

    public void setB20000561110207(BigDecimal b20000561110207) {
        this.b20000561110207 = b20000561110207;
    }

    public BigDecimal getB2000056111020701() {
        return b2000056111020701;
    }

    public void setB2000056111020701(BigDecimal b2000056111020701) {
        this.b2000056111020701 = b2000056111020701;
    }

    public BigDecimal getB2000056111020702() {
        return b2000056111020702;
    }

    public void setB2000056111020702(BigDecimal b2000056111020702) {
        this.b2000056111020702 = b2000056111020702;
    }

    public BigDecimal getB20000561110208() {
        return b20000561110208;
    }

    public void setB20000561110208(BigDecimal b20000561110208) {
        this.b20000561110208 = b20000561110208;
    }

    public BigDecimal getB2000056111020801() {
        return b2000056111020801;
    }

    public void setB2000056111020801(BigDecimal b2000056111020801) {
        this.b2000056111020801 = b2000056111020801;
    }

    public BigDecimal getB2000056111020802() {
        return b2000056111020802;
    }

    public void setB2000056111020802(BigDecimal b2000056111020802) {
        this.b2000056111020802 = b2000056111020802;
    }

    public BigDecimal getB200005611103() {
        return b200005611103;
    }

    public void setB200005611103(BigDecimal b200005611103) {
        this.b200005611103 = b200005611103;
    }

    public BigDecimal getB20000561110301() {
        return b20000561110301;
    }

    public void setB20000561110301(BigDecimal b20000561110301) {
        this.b20000561110301 = b20000561110301;
    }

    public BigDecimal getB2000056111030101() {
        return b2000056111030101;
    }

    public void setB2000056111030101(BigDecimal b2000056111030101) {
        this.b2000056111030101 = b2000056111030101;
    }

    public BigDecimal getB2000056111030102() {
        return b2000056111030102;
    }

    public void setB2000056111030102(BigDecimal b2000056111030102) {
        this.b2000056111030102 = b2000056111030102;
    }

    public BigDecimal getB20000561110302() {
        return b20000561110302;
    }

    public void setB20000561110302(BigDecimal b20000561110302) {
        this.b20000561110302 = b20000561110302;
    }

    public BigDecimal getB2000056111030201() {
        return b2000056111030201;
    }

    public void setB2000056111030201(BigDecimal b2000056111030201) {
        this.b2000056111030201 = b2000056111030201;
    }

    public BigDecimal getB2000056111030202() {
        return b2000056111030202;
    }

    public void setB2000056111030202(BigDecimal b2000056111030202) {
        this.b2000056111030202 = b2000056111030202;
    }

    public BigDecimal getB20000561110303() {
        return b20000561110303;
    }

    public void setB20000561110303(BigDecimal b20000561110303) {
        this.b20000561110303 = b20000561110303;
    }

    public BigDecimal getB2000056111030301() {
        return b2000056111030301;
    }

    public void setB2000056111030301(BigDecimal b2000056111030301) {
        this.b2000056111030301 = b2000056111030301;
    }

    public BigDecimal getB2000056111030302() {
        return b2000056111030302;
    }

    public void setB2000056111030302(BigDecimal b2000056111030302) {
        this.b2000056111030302 = b2000056111030302;
    }

    public BigDecimal getB20000561110304() {
        return b20000561110304;
    }

    public void setB20000561110304(BigDecimal b20000561110304) {
        this.b20000561110304 = b20000561110304;
    }

    public BigDecimal getB2000056111030401() {
        return b2000056111030401;
    }

    public void setB2000056111030401(BigDecimal b2000056111030401) {
        this.b2000056111030401 = b2000056111030401;
    }

    public BigDecimal getB2000056111030402() {
        return b2000056111030402;
    }

    public void setB2000056111030402(BigDecimal b2000056111030402) {
        this.b2000056111030402 = b2000056111030402;
    }

    public BigDecimal getB20000561110305() {
        return b20000561110305;
    }

    public void setB20000561110305(BigDecimal b20000561110305) {
        this.b20000561110305 = b20000561110305;
    }

    public BigDecimal getB2000056111030501() {
        return b2000056111030501;
    }

    public void setB2000056111030501(BigDecimal b2000056111030501) {
        this.b2000056111030501 = b2000056111030501;
    }

    public BigDecimal getB2000056111030502() {
        return b2000056111030502;
    }

    public void setB2000056111030502(BigDecimal b2000056111030502) {
        this.b2000056111030502 = b2000056111030502;
    }

    public BigDecimal getB200005611104() {
        return b200005611104;
    }

    public void setB200005611104(BigDecimal b200005611104) {
        this.b200005611104 = b200005611104;
    }

    public BigDecimal getB20000561110401() {
        return b20000561110401;
    }

    public void setB20000561110401(BigDecimal b20000561110401) {
        this.b20000561110401 = b20000561110401;
    }

    public BigDecimal getB20000561110402() {
        return b20000561110402;
    }

    public void setB20000561110402(BigDecimal b20000561110402) {
        this.b20000561110402 = b20000561110402;
    }

    public BigDecimal getB200005611105() {
        return b200005611105;
    }

    public void setB200005611105(BigDecimal b200005611105) {
        this.b200005611105 = b200005611105;
    }

    public BigDecimal getB20000561110501() {
        return b20000561110501;
    }

    public void setB20000561110501(BigDecimal b20000561110501) {
        this.b20000561110501 = b20000561110501;
    }

    public BigDecimal getB20000561110502() {
        return b20000561110502;
    }

    public void setB20000561110502(BigDecimal b20000561110502) {
        this.b20000561110502 = b20000561110502;
    }

    public BigDecimal getB200005611106() {
        return b200005611106;
    }

    public void setB200005611106(BigDecimal b200005611106) {
        this.b200005611106 = b200005611106;
    }

    public BigDecimal getB20000561110601() {
        return b20000561110601;
    }

    public void setB20000561110601(BigDecimal b20000561110601) {
        this.b20000561110601 = b20000561110601;
    }

    public BigDecimal getB20000561110602() {
        return b20000561110602;
    }

    public void setB20000561110602(BigDecimal b20000561110602) {
        this.b20000561110602 = b20000561110602;
    }

    public BigDecimal getB200005611107() {
        return b200005611107;
    }

    public void setB200005611107(BigDecimal b200005611107) {
        this.b200005611107 = b200005611107;
    }

    public BigDecimal getB20000561110701() {
        return b20000561110701;
    }

    public void setB20000561110701(BigDecimal b20000561110701) {
        this.b20000561110701 = b20000561110701;
    }

    public BigDecimal getB2000056111070101() {
        return b2000056111070101;
    }

    public void setB2000056111070101(BigDecimal b2000056111070101) {
        this.b2000056111070101 = b2000056111070101;
    }

    public BigDecimal getB2000056111070102() {
        return b2000056111070102;
    }

    public void setB2000056111070102(BigDecimal b2000056111070102) {
        this.b2000056111070102 = b2000056111070102;
    }

    public BigDecimal getB20000561110702() {
        return b20000561110702;
    }

    public void setB20000561110702(BigDecimal b20000561110702) {
        this.b20000561110702 = b20000561110702;
    }

    public BigDecimal getB2000056111070201() {
        return b2000056111070201;
    }

    public void setB2000056111070201(BigDecimal b2000056111070201) {
        this.b2000056111070201 = b2000056111070201;
    }

    public BigDecimal getB2000056111070202() {
        return b2000056111070202;
    }

    public void setB2000056111070202(BigDecimal b2000056111070202) {
        this.b2000056111070202 = b2000056111070202;
    }

    public BigDecimal getB20000561110703() {
        return b20000561110703;
    }

    public void setB20000561110703(BigDecimal b20000561110703) {
        this.b20000561110703 = b20000561110703;
    }

    public BigDecimal getB2000056111070301() {
        return b2000056111070301;
    }

    public void setB2000056111070301(BigDecimal b2000056111070301) {
        this.b2000056111070301 = b2000056111070301;
    }

    public BigDecimal getB2000056111070302() {
        return b2000056111070302;
    }

    public void setB2000056111070302(BigDecimal b2000056111070302) {
        this.b2000056111070302 = b2000056111070302;
    }

    public BigDecimal getB20000561110704() {
        return b20000561110704;
    }

    public void setB20000561110704(BigDecimal b20000561110704) {
        this.b20000561110704 = b20000561110704;
    }

    public BigDecimal getB2000056111070401() {
        return b2000056111070401;
    }

    public void setB2000056111070401(BigDecimal b2000056111070401) {
        this.b2000056111070401 = b2000056111070401;
    }

    public BigDecimal getB2000056111070402() {
        return b2000056111070402;
    }

    public void setB2000056111070402(BigDecimal b2000056111070402) {
        this.b2000056111070402 = b2000056111070402;
    }

    public BigDecimal getB20000561110705() {
        return b20000561110705;
    }

    public void setB20000561110705(BigDecimal b20000561110705) {
        this.b20000561110705 = b20000561110705;
    }

    public BigDecimal getB2000056111070501() {
        return b2000056111070501;
    }

    public void setB2000056111070501(BigDecimal b2000056111070501) {
        this.b2000056111070501 = b2000056111070501;
    }

    public BigDecimal getB2000056111070502() {
        return b2000056111070502;
    }

    public void setB2000056111070502(BigDecimal b2000056111070502) {
        this.b2000056111070502 = b2000056111070502;
    }

    public BigDecimal getB200005611108() {
        return b200005611108;
    }

    public void setB200005611108(BigDecimal b200005611108) {
        this.b200005611108 = b200005611108;
    }

    public BigDecimal getB20000561110801() {
        return b20000561110801;
    }

    public void setB20000561110801(BigDecimal b20000561110801) {
        this.b20000561110801 = b20000561110801;
    }

    public BigDecimal getB2000056111080101() {
        return b2000056111080101;
    }

    public void setB2000056111080101(BigDecimal b2000056111080101) {
        this.b2000056111080101 = b2000056111080101;
    }

    public BigDecimal getB2000056111080102() {
        return b2000056111080102;
    }

    public void setB2000056111080102(BigDecimal b2000056111080102) {
        this.b2000056111080102 = b2000056111080102;
    }

    public BigDecimal getB20000561110802() {
        return b20000561110802;
    }

    public void setB20000561110802(BigDecimal b20000561110802) {
        this.b20000561110802 = b20000561110802;
    }

    public BigDecimal getB2000056111080201() {
        return b2000056111080201;
    }

    public void setB2000056111080201(BigDecimal b2000056111080201) {
        this.b2000056111080201 = b2000056111080201;
    }

    public BigDecimal getB2000056111080202() {
        return b2000056111080202;
    }

    public void setB2000056111080202(BigDecimal b2000056111080202) {
        this.b2000056111080202 = b2000056111080202;
    }

    public BigDecimal getB20000561110803() {
        return b20000561110803;
    }

    public void setB20000561110803(BigDecimal b20000561110803) {
        this.b20000561110803 = b20000561110803;
    }

    public BigDecimal getB2000056111080301() {
        return b2000056111080301;
    }

    public void setB2000056111080301(BigDecimal b2000056111080301) {
        this.b2000056111080301 = b2000056111080301;
    }

    public BigDecimal getB2000056111080302() {
        return b2000056111080302;
    }

    public void setB2000056111080302(BigDecimal b2000056111080302) {
        this.b2000056111080302 = b2000056111080302;
    }

    public BigDecimal getB20000561110804() {
        return b20000561110804;
    }

    public void setB20000561110804(BigDecimal b20000561110804) {
        this.b20000561110804 = b20000561110804;
    }

    public BigDecimal getB2000056111080401() {
        return b2000056111080401;
    }

    public void setB2000056111080401(BigDecimal b2000056111080401) {
        this.b2000056111080401 = b2000056111080401;
    }

    public BigDecimal getB2000056111080402() {
        return b2000056111080402;
    }

    public void setB2000056111080402(BigDecimal b2000056111080402) {
        this.b2000056111080402 = b2000056111080402;
    }

    public BigDecimal getB20000561110805() {
        return b20000561110805;
    }

    public void setB20000561110805(BigDecimal b20000561110805) {
        this.b20000561110805 = b20000561110805;
    }

    public BigDecimal getB2000056111080501() {
        return b2000056111080501;
    }

    public void setB2000056111080501(BigDecimal b2000056111080501) {
        this.b2000056111080501 = b2000056111080501;
    }

    public BigDecimal getB2000056111080502() {
        return b2000056111080502;
    }

    public void setB2000056111080502(BigDecimal b2000056111080502) {
        this.b2000056111080502 = b2000056111080502;
    }

    public BigDecimal getB20000561110806() {
        return b20000561110806;
    }

    public void setB20000561110806(BigDecimal b20000561110806) {
        this.b20000561110806 = b20000561110806;
    }

    public BigDecimal getB2000056111080601() {
        return b2000056111080601;
    }

    public void setB2000056111080601(BigDecimal b2000056111080601) {
        this.b2000056111080601 = b2000056111080601;
    }

    public BigDecimal getB2000056111080602() {
        return b2000056111080602;
    }

    public void setB2000056111080602(BigDecimal b2000056111080602) {
        this.b2000056111080602 = b2000056111080602;
    }

    public BigDecimal getB20000561110807() {
        return b20000561110807;
    }

    public void setB20000561110807(BigDecimal b20000561110807) {
        this.b20000561110807 = b20000561110807;
    }

    public BigDecimal getB2000056111080701() {
        return b2000056111080701;
    }

    public void setB2000056111080701(BigDecimal b2000056111080701) {
        this.b2000056111080701 = b2000056111080701;
    }

    public BigDecimal getB2000056111080702() {
        return b2000056111080702;
    }

    public void setB2000056111080702(BigDecimal b2000056111080702) {
        this.b2000056111080702 = b2000056111080702;
    }

    public BigDecimal getB20000561110808() {
        return b20000561110808;
    }

    public void setB20000561110808(BigDecimal b20000561110808) {
        this.b20000561110808 = b20000561110808;
    }

    public BigDecimal getB2000056111080801() {
        return b2000056111080801;
    }

    public void setB2000056111080801(BigDecimal b2000056111080801) {
        this.b2000056111080801 = b2000056111080801;
    }

    public BigDecimal getB2000056111080802() {
        return b2000056111080802;
    }

    public void setB2000056111080802(BigDecimal b2000056111080802) {
        this.b2000056111080802 = b2000056111080802;
    }

    public BigDecimal getB2000056115() {
        return b2000056115;
    }

    public void setB2000056115(BigDecimal b2000056115) {
        this.b2000056115 = b2000056115;
    }

    public BigDecimal getB200005611501() {
        return b200005611501;
    }

    public void setB200005611501(BigDecimal b200005611501) {
        this.b200005611501 = b200005611501;
    }

    public BigDecimal getB200005611502() {
        return b200005611502;
    }

    public void setB200005611502(BigDecimal b200005611502) {
        this.b200005611502 = b200005611502;
    }

    public BigDecimal getB200005611503() {
        return b200005611503;
    }

    public void setB200005611503(BigDecimal b200005611503) {
        this.b200005611503 = b200005611503;
    }

    public BigDecimal getB200005611504() {
        return b200005611504;
    }

    public void setB200005611504(BigDecimal b200005611504) {
        this.b200005611504 = b200005611504;
    }

    public BigDecimal getB200005611505() {
        return b200005611505;
    }

    public void setB200005611505(BigDecimal b200005611505) {
        this.b200005611505 = b200005611505;
    }

    public BigDecimal getB200005611506() {
        return b200005611506;
    }

    public void setB200005611506(BigDecimal b200005611506) {
        this.b200005611506 = b200005611506;
    }

    public BigDecimal getB200005611507() {
        return b200005611507;
    }

    public void setB200005611507(BigDecimal b200005611507) {
        this.b200005611507 = b200005611507;
    }

    public BigDecimal getB2000056117() {
        return b2000056117;
    }

    public void setB2000056117(BigDecimal b2000056117) {
        this.b2000056117 = b2000056117;
    }

    public BigDecimal getB2000056201() {
        return b2000056201;
    }

    public void setB2000056201(BigDecimal b2000056201) {
        this.b2000056201 = b2000056201;
    }

    public BigDecimal getB2000056202() {
        return b2000056202;
    }

    public void setB2000056202(BigDecimal b2000056202) {
        this.b2000056202 = b2000056202;
    }

    public BigDecimal getB2000056203() {
        return b2000056203;
    }

    public void setB2000056203(BigDecimal b2000056203) {
        this.b2000056203 = b2000056203;
    }

    public BigDecimal getB2000056301() {
        return b2000056301;
    }

    public void setB2000056301(BigDecimal b2000056301) {
        this.b2000056301 = b2000056301;
    }

    public BigDecimal getB200005630101() {
        return b200005630101;
    }

    public void setB200005630101(BigDecimal b200005630101) {
        this.b200005630101 = b200005630101;
    }

    public BigDecimal getB200005630102() {
        return b200005630102;
    }

    public void setB200005630102(BigDecimal b200005630102) {
        this.b200005630102 = b200005630102;
    }

    public BigDecimal getB200005630103() {
        return b200005630103;
    }

    public void setB200005630103(BigDecimal b200005630103) {
        this.b200005630103 = b200005630103;
    }

    public BigDecimal getB200005630104() {
        return b200005630104;
    }

    public void setB200005630104(BigDecimal b200005630104) {
        this.b200005630104 = b200005630104;
    }

    public BigDecimal getB200005630105() {
        return b200005630105;
    }

    public void setB200005630105(BigDecimal b200005630105) {
        this.b200005630105 = b200005630105;
    }

    public BigDecimal getB200005630106() {
        return b200005630106;
    }

    public void setB200005630106(BigDecimal b200005630106) {
        this.b200005630106 = b200005630106;
    }

    public BigDecimal getB200005630107() {
        return b200005630107;
    }

    public void setB200005630107(BigDecimal b200005630107) {
        this.b200005630107 = b200005630107;
    }

    public BigDecimal getB200005630108() {
        return b200005630108;
    }

    public void setB200005630108(BigDecimal b200005630108) {
        this.b200005630108 = b200005630108;
    }

    public BigDecimal getB200005630109() {
        return b200005630109;
    }

    public void setB200005630109(BigDecimal b200005630109) {
        this.b200005630109 = b200005630109;
    }

    public BigDecimal getB2000056401() {
        return b2000056401;
    }

    public void setB2000056401(BigDecimal b2000056401) {
        this.b2000056401 = b2000056401;
    }

    public BigDecimal getB200005640101() {
        return b200005640101;
    }

    public void setB200005640101(BigDecimal b200005640101) {
        this.b200005640101 = b200005640101;
    }

    public BigDecimal getB200005640102() {
        return b200005640102;
    }

    public void setB200005640102(BigDecimal b200005640102) {
        this.b200005640102 = b200005640102;
    }

    public BigDecimal getB200005640103() {
        return b200005640103;
    }

    public void setB200005640103(BigDecimal b200005640103) {
        this.b200005640103 = b200005640103;
    }

    public BigDecimal getB200005640104() {
        return b200005640104;
    }

    public void setB200005640104(BigDecimal b200005640104) {
        this.b200005640104 = b200005640104;
    }

    public BigDecimal getB200005640105() {
        return b200005640105;
    }

    public void setB200005640105(BigDecimal b200005640105) {
        this.b200005640105 = b200005640105;
    }

    public BigDecimal getB200005640106() {
        return b200005640106;
    }

    public void setB200005640106(BigDecimal b200005640106) {
        this.b200005640106 = b200005640106;
    }

    public BigDecimal getB200005640107() {
        return b200005640107;
    }

    public void setB200005640107(BigDecimal b200005640107) {
        this.b200005640107 = b200005640107;
    }

    public BigDecimal getB200005640108() {
        return b200005640108;
    }

    public void setB200005640108(BigDecimal b200005640108) {
        this.b200005640108 = b200005640108;
    }

    public BigDecimal getB200005640109() {
        return b200005640109;
    }

    public void setB200005640109(BigDecimal b200005640109) {
        this.b200005640109 = b200005640109;
    }

    public BigDecimal getB200005640110() {
        return b200005640110;
    }

    public void setB200005640110(BigDecimal b200005640110) {
        this.b200005640110 = b200005640110;
    }

    public BigDecimal getB200005640111() {
        return b200005640111;
    }

    public void setB200005640111(BigDecimal b200005640111) {
        this.b200005640111 = b200005640111;
    }

    public BigDecimal getB200005640112() {
        return b200005640112;
    }

    public void setB200005640112(BigDecimal b200005640112) {
        this.b200005640112 = b200005640112;
    }

    public BigDecimal getB200005640113() {
        return b200005640113;
    }

    public void setB200005640113(BigDecimal b200005640113) {
        this.b200005640113 = b200005640113;
    }

    public BigDecimal getB200005640114() {
        return b200005640114;
    }

    public void setB200005640114(BigDecimal b200005640114) {
        this.b200005640114 = b200005640114;
    }

    public BigDecimal getB200005640115() {
        return b200005640115;
    }

    public void setB200005640115(BigDecimal b200005640115) {
        this.b200005640115 = b200005640115;
    }

    public BigDecimal getB200005640116() {
        return b200005640116;
    }

    public void setB200005640116(BigDecimal b200005640116) {
        this.b200005640116 = b200005640116;
    }

    public BigDecimal getB200005640117() {
        return b200005640117;
    }

    public void setB200005640117(BigDecimal b200005640117) {
        this.b200005640117 = b200005640117;
    }

    public BigDecimal getB200005640118() {
        return b200005640118;
    }

    public void setB200005640118(BigDecimal b200005640118) {
        this.b200005640118 = b200005640118;
    }

    public BigDecimal getB200005640119() {
        return b200005640119;
    }

    public void setB200005640119(BigDecimal b200005640119) {
        this.b200005640119 = b200005640119;
    }

    public BigDecimal getB2000056402() {
        return b2000056402;
    }

    public void setB2000056402(BigDecimal b2000056402) {
        this.b2000056402 = b2000056402;
    }

    public BigDecimal getB200005640201() {
        return b200005640201;
    }

    public void setB200005640201(BigDecimal b200005640201) {
        this.b200005640201 = b200005640201;
    }

    public BigDecimal getB200005640202() {
        return b200005640202;
    }

    public void setB200005640202(BigDecimal b200005640202) {
        this.b200005640202 = b200005640202;
    }

    public BigDecimal getB200005640203() {
        return b200005640203;
    }

    public void setB200005640203(BigDecimal b200005640203) {
        this.b200005640203 = b200005640203;
    }

    public BigDecimal getB200005640204() {
        return b200005640204;
    }

    public void setB200005640204(BigDecimal b200005640204) {
        this.b200005640204 = b200005640204;
    }

    public BigDecimal getB200005640205() {
        return b200005640205;
    }

    public void setB200005640205(BigDecimal b200005640205) {
        this.b200005640205 = b200005640205;
    }

    public BigDecimal getB2000056403() {
        return b2000056403;
    }

    public void setB2000056403(BigDecimal b2000056403) {
        this.b2000056403 = b2000056403;
    }

    public BigDecimal getB2000056411() {
        return b2000056411;
    }

    public void setB2000056411(BigDecimal b2000056411) {
        this.b2000056411 = b2000056411;
    }

    public BigDecimal getB2000056421() {
        return b2000056421;
    }

    public void setB2000056421(BigDecimal b2000056421) {
        this.b2000056421 = b2000056421;
    }

    public BigDecimal getB2000056501() {
        return b2000056501;
    }

    public void setB2000056501(BigDecimal b2000056501) {
        this.b2000056501 = b2000056501;
    }

    public BigDecimal getB2000056502() {
        return b2000056502;
    }

    public void setB2000056502(BigDecimal b2000056502) {
        this.b2000056502 = b2000056502;
    }

    public BigDecimal getB2000056511() {
        return b2000056511;
    }

    public void setB2000056511(BigDecimal b2000056511) {
        this.b2000056511 = b2000056511;
    }

    public BigDecimal getB2000056521() {
        return b2000056521;
    }

    public void setB2000056521(BigDecimal b2000056521) {
        this.b2000056521 = b2000056521;
    }

    public BigDecimal getB2000056531() {
        return b2000056531;
    }

    public void setB2000056531(BigDecimal b2000056531) {
        this.b2000056531 = b2000056531;
    }

    public BigDecimal getB2000056541() {
        return b2000056541;
    }

    public void setB2000056541(BigDecimal b2000056541) {
        this.b2000056541 = b2000056541;
    }

    public BigDecimal getB2000056542() {
        return b2000056542;
    }

    public void setB2000056542(BigDecimal b2000056542) {
        this.b2000056542 = b2000056542;
    }

    public BigDecimal getB2000056601() {
        return b2000056601;
    }

    public void setB2000056601(BigDecimal b2000056601) {
        this.b2000056601 = b2000056601;
    }

    public BigDecimal getB200005660101() {
        return b200005660101;
    }

    public void setB200005660101(BigDecimal b200005660101) {
        this.b200005660101 = b200005660101;
    }

    public BigDecimal getB200005660102() {
        return b200005660102;
    }

    public void setB200005660102(BigDecimal b200005660102) {
        this.b200005660102 = b200005660102;
    }

    public BigDecimal getB200005660103() {
        return b200005660103;
    }

    public void setB200005660103(BigDecimal b200005660103) {
        this.b200005660103 = b200005660103;
    }

    public BigDecimal getB200005660104() {
        return b200005660104;
    }

    public void setB200005660104(BigDecimal b200005660104) {
        this.b200005660104 = b200005660104;
    }

    public BigDecimal getB200005660105() {
        return b200005660105;
    }

    public void setB200005660105(BigDecimal b200005660105) {
        this.b200005660105 = b200005660105;
    }

    public BigDecimal getB200005660106() {
        return b200005660106;
    }

    public void setB200005660106(BigDecimal b200005660106) {
        this.b200005660106 = b200005660106;
    }

    public BigDecimal getB200005660107() {
        return b200005660107;
    }

    public void setB200005660107(BigDecimal b200005660107) {
        this.b200005660107 = b200005660107;
    }

    public BigDecimal getB200005660108() {
        return b200005660108;
    }

    public void setB200005660108(BigDecimal b200005660108) {
        this.b200005660108 = b200005660108;
    }

    public BigDecimal getB200005660109() {
        return b200005660109;
    }

    public void setB200005660109(BigDecimal b200005660109) {
        this.b200005660109 = b200005660109;
    }

    public BigDecimal getB200005660110() {
        return b200005660110;
    }

    public void setB200005660110(BigDecimal b200005660110) {
        this.b200005660110 = b200005660110;
    }

    public BigDecimal getB200005660111() {
        return b200005660111;
    }

    public void setB200005660111(BigDecimal b200005660111) {
        this.b200005660111 = b200005660111;
    }

    public BigDecimal getB200005660112() {
        return b200005660112;
    }

    public void setB200005660112(BigDecimal b200005660112) {
        this.b200005660112 = b200005660112;
    }

    public BigDecimal getB200005660113() {
        return b200005660113;
    }

    public void setB200005660113(BigDecimal b200005660113) {
        this.b200005660113 = b200005660113;
    }

    public BigDecimal getB200005660114() {
        return b200005660114;
    }

    public void setB200005660114(BigDecimal b200005660114) {
        this.b200005660114 = b200005660114;
    }

    public BigDecimal getB200005660115() {
        return b200005660115;
    }

    public void setB200005660115(BigDecimal b200005660115) {
        this.b200005660115 = b200005660115;
    }

    public BigDecimal getB200005660116() {
        return b200005660116;
    }

    public void setB200005660116(BigDecimal b200005660116) {
        this.b200005660116 = b200005660116;
    }

    public BigDecimal getB200005660117() {
        return b200005660117;
    }

    public void setB200005660117(BigDecimal b200005660117) {
        this.b200005660117 = b200005660117;
    }

    public BigDecimal getB200005660118() {
        return b200005660118;
    }

    public void setB200005660118(BigDecimal b200005660118) {
        this.b200005660118 = b200005660118;
    }

    public BigDecimal getB200005660119() {
        return b200005660119;
    }

    public void setB200005660119(BigDecimal b200005660119) {
        this.b200005660119 = b200005660119;
    }

    public BigDecimal getB200005660120() {
        return b200005660120;
    }

    public void setB200005660120(BigDecimal b200005660120) {
        this.b200005660120 = b200005660120;
    }

    public BigDecimal getB200005660121() {
        return b200005660121;
    }

    public void setB200005660121(BigDecimal b200005660121) {
        this.b200005660121 = b200005660121;
    }

    public BigDecimal getB200005660122() {
        return b200005660122;
    }

    public void setB200005660122(BigDecimal b200005660122) {
        this.b200005660122 = b200005660122;
    }

    public BigDecimal getB200005660123() {
        return b200005660123;
    }

    public void setB200005660123(BigDecimal b200005660123) {
        this.b200005660123 = b200005660123;
    }

    public BigDecimal getB200005660124() {
        return b200005660124;
    }

    public void setB200005660124(BigDecimal b200005660124) {
        this.b200005660124 = b200005660124;
    }

    public BigDecimal getB200005660125() {
        return b200005660125;
    }

    public void setB200005660125(BigDecimal b200005660125) {
        this.b200005660125 = b200005660125;
    }

    public BigDecimal getB200005660126() {
        return b200005660126;
    }

    public void setB200005660126(BigDecimal b200005660126) {
        this.b200005660126 = b200005660126;
    }

    public BigDecimal getB200005660127() {
        return b200005660127;
    }

    public void setB200005660127(BigDecimal b200005660127) {
        this.b200005660127 = b200005660127;
    }

    public BigDecimal getB200005660128() {
        return b200005660128;
    }

    public void setB200005660128(BigDecimal b200005660128) {
        this.b200005660128 = b200005660128;
    }

    public BigDecimal getB200005660129() {
        return b200005660129;
    }

    public void setB200005660129(BigDecimal b200005660129) {
        this.b200005660129 = b200005660129;
    }

    public BigDecimal getB200005660130() {
        return b200005660130;
    }

    public void setB200005660130(BigDecimal b200005660130) {
        this.b200005660130 = b200005660130;
    }

    public BigDecimal getB200005660131() {
        return b200005660131;
    }

    public void setB200005660131(BigDecimal b200005660131) {
        this.b200005660131 = b200005660131;
    }

    public BigDecimal getB200005660132() {
        return b200005660132;
    }

    public void setB200005660132(BigDecimal b200005660132) {
        this.b200005660132 = b200005660132;
    }

    public BigDecimal getB200005660133() {
        return b200005660133;
    }

    public void setB200005660133(BigDecimal b200005660133) {
        this.b200005660133 = b200005660133;
    }

    public BigDecimal getB200005660134() {
        return b200005660134;
    }

    public void setB200005660134(BigDecimal b200005660134) {
        this.b200005660134 = b200005660134;
    }

    public BigDecimal getB200005660135() {
        return b200005660135;
    }

    public void setB200005660135(BigDecimal b200005660135) {
        this.b200005660135 = b200005660135;
    }

    public BigDecimal getB200005660136() {
        return b200005660136;
    }

    public void setB200005660136(BigDecimal b200005660136) {
        this.b200005660136 = b200005660136;
    }

    public BigDecimal getB200005660137() {
        return b200005660137;
    }

    public void setB200005660137(BigDecimal b200005660137) {
        this.b200005660137 = b200005660137;
    }

    public BigDecimal getB200005660138() {
        return b200005660138;
    }

    public void setB200005660138(BigDecimal b200005660138) {
        this.b200005660138 = b200005660138;
    }

    public BigDecimal getB200005660139() {
        return b200005660139;
    }

    public void setB200005660139(BigDecimal b200005660139) {
        this.b200005660139 = b200005660139;
    }

    public BigDecimal getB200005660140() {
        return b200005660140;
    }

    public void setB200005660140(BigDecimal b200005660140) {
        this.b200005660140 = b200005660140;
    }

    public BigDecimal getB200005660141() {
        return b200005660141;
    }

    public void setB200005660141(BigDecimal b200005660141) {
        this.b200005660141 = b200005660141;
    }

    public BigDecimal getB200005660142() {
        return b200005660142;
    }

    public void setB200005660142(BigDecimal b200005660142) {
        this.b200005660142 = b200005660142;
    }

    public BigDecimal getB200005660143() {
        return b200005660143;
    }

    public void setB200005660143(BigDecimal b200005660143) {
        this.b200005660143 = b200005660143;
    }

    public BigDecimal getB200005660144() {
        return b200005660144;
    }

    public void setB200005660144(BigDecimal b200005660144) {
        this.b200005660144 = b200005660144;
    }

    public BigDecimal getB200005660145() {
        return b200005660145;
    }

    public void setB200005660145(BigDecimal b200005660145) {
        this.b200005660145 = b200005660145;
    }

    public BigDecimal getB200005660146() {
        return b200005660146;
    }

    public void setB200005660146(BigDecimal b200005660146) {
        this.b200005660146 = b200005660146;
    }

    public BigDecimal getB200005660147() {
        return b200005660147;
    }

    public void setB200005660147(BigDecimal b200005660147) {
        this.b200005660147 = b200005660147;
    }

    public BigDecimal getB200005660148() {
        return b200005660148;
    }

    public void setB200005660148(BigDecimal b200005660148) {
        this.b200005660148 = b200005660148;
    }

    public BigDecimal getB200005660149() {
        return b200005660149;
    }

    public void setB200005660149(BigDecimal b200005660149) {
        this.b200005660149 = b200005660149;
    }

    public BigDecimal getB200005660150() {
        return b200005660150;
    }

    public void setB200005660150(BigDecimal b200005660150) {
        this.b200005660150 = b200005660150;
    }

    public BigDecimal getB200005660151() {
        return b200005660151;
    }

    public void setB200005660151(BigDecimal b200005660151) {
        this.b200005660151 = b200005660151;
    }

    public BigDecimal getB200005660152() {
        return b200005660152;
    }

    public void setB200005660152(BigDecimal b200005660152) {
        this.b200005660152 = b200005660152;
    }

    public BigDecimal getB200005660153() {
        return b200005660153;
    }

    public void setB200005660153(BigDecimal b200005660153) {
        this.b200005660153 = b200005660153;
    }

    public BigDecimal getB2000056602() {
        return b2000056602;
    }

    public void setB2000056602(BigDecimal b2000056602) {
        this.b2000056602 = b2000056602;
    }

    public BigDecimal getB200005660201() {
        return b200005660201;
    }

    public void setB200005660201(BigDecimal b200005660201) {
        this.b200005660201 = b200005660201;
    }

    public BigDecimal getB200005660202() {
        return b200005660202;
    }

    public void setB200005660202(BigDecimal b200005660202) {
        this.b200005660202 = b200005660202;
    }

    public BigDecimal getB200005660203() {
        return b200005660203;
    }

    public void setB200005660203(BigDecimal b200005660203) {
        this.b200005660203 = b200005660203;
    }

    public BigDecimal getB200005660204() {
        return b200005660204;
    }

    public void setB200005660204(BigDecimal b200005660204) {
        this.b200005660204 = b200005660204;
    }

    public BigDecimal getB200005660205() {
        return b200005660205;
    }

    public void setB200005660205(BigDecimal b200005660205) {
        this.b200005660205 = b200005660205;
    }

    public BigDecimal getB200005660206() {
        return b200005660206;
    }

    public void setB200005660206(BigDecimal b200005660206) {
        this.b200005660206 = b200005660206;
    }

    public BigDecimal getB200005660207() {
        return b200005660207;
    }

    public void setB200005660207(BigDecimal b200005660207) {
        this.b200005660207 = b200005660207;
    }

    public BigDecimal getB200005660208() {
        return b200005660208;
    }

    public void setB200005660208(BigDecimal b200005660208) {
        this.b200005660208 = b200005660208;
    }

    public BigDecimal getB200005660209() {
        return b200005660209;
    }

    public void setB200005660209(BigDecimal b200005660209) {
        this.b200005660209 = b200005660209;
    }

    public BigDecimal getB200005660210() {
        return b200005660210;
    }

    public void setB200005660210(BigDecimal b200005660210) {
        this.b200005660210 = b200005660210;
    }

    public BigDecimal getB200005660211() {
        return b200005660211;
    }

    public void setB200005660211(BigDecimal b200005660211) {
        this.b200005660211 = b200005660211;
    }

    public BigDecimal getB200005660212() {
        return b200005660212;
    }

    public void setB200005660212(BigDecimal b200005660212) {
        this.b200005660212 = b200005660212;
    }

    public BigDecimal getB200005660213() {
        return b200005660213;
    }

    public void setB200005660213(BigDecimal b200005660213) {
        this.b200005660213 = b200005660213;
    }

    public BigDecimal getB200005660214() {
        return b200005660214;
    }

    public void setB200005660214(BigDecimal b200005660214) {
        this.b200005660214 = b200005660214;
    }

    public BigDecimal getB200005660215() {
        return b200005660215;
    }

    public void setB200005660215(BigDecimal b200005660215) {
        this.b200005660215 = b200005660215;
    }

    public BigDecimal getB200005660216() {
        return b200005660216;
    }

    public void setB200005660216(BigDecimal b200005660216) {
        this.b200005660216 = b200005660216;
    }

    public BigDecimal getB200005660217() {
        return b200005660217;
    }

    public void setB200005660217(BigDecimal b200005660217) {
        this.b200005660217 = b200005660217;
    }

    public BigDecimal getB200005660218() {
        return b200005660218;
    }

    public void setB200005660218(BigDecimal b200005660218) {
        this.b200005660218 = b200005660218;
    }

    public BigDecimal getB200005660219() {
        return b200005660219;
    }

    public void setB200005660219(BigDecimal b200005660219) {
        this.b200005660219 = b200005660219;
    }

    public BigDecimal getB200005660220() {
        return b200005660220;
    }

    public void setB200005660220(BigDecimal b200005660220) {
        this.b200005660220 = b200005660220;
    }

    public BigDecimal getB200005660221() {
        return b200005660221;
    }

    public void setB200005660221(BigDecimal b200005660221) {
        this.b200005660221 = b200005660221;
    }

    public BigDecimal getB200005660222() {
        return b200005660222;
    }

    public void setB200005660222(BigDecimal b200005660222) {
        this.b200005660222 = b200005660222;
    }

    public BigDecimal getB200005660223() {
        return b200005660223;
    }

    public void setB200005660223(BigDecimal b200005660223) {
        this.b200005660223 = b200005660223;
    }

    public BigDecimal getB200005660224() {
        return b200005660224;
    }

    public void setB200005660224(BigDecimal b200005660224) {
        this.b200005660224 = b200005660224;
    }

    public BigDecimal getB200005660225() {
        return b200005660225;
    }

    public void setB200005660225(BigDecimal b200005660225) {
        this.b200005660225 = b200005660225;
    }

    public BigDecimal getB200005660226() {
        return b200005660226;
    }

    public void setB200005660226(BigDecimal b200005660226) {
        this.b200005660226 = b200005660226;
    }

    public BigDecimal getB200005660227() {
        return b200005660227;
    }

    public void setB200005660227(BigDecimal b200005660227) {
        this.b200005660227 = b200005660227;
    }

    public BigDecimal getB200005660228() {
        return b200005660228;
    }

    public void setB200005660228(BigDecimal b200005660228) {
        this.b200005660228 = b200005660228;
    }

    public BigDecimal getB200005660229() {
        return b200005660229;
    }

    public void setB200005660229(BigDecimal b200005660229) {
        this.b200005660229 = b200005660229;
    }

    public BigDecimal getB200005660230() {
        return b200005660230;
    }

    public void setB200005660230(BigDecimal b200005660230) {
        this.b200005660230 = b200005660230;
    }

    public BigDecimal getB200005660231() {
        return b200005660231;
    }

    public void setB200005660231(BigDecimal b200005660231) {
        this.b200005660231 = b200005660231;
    }

    public BigDecimal getB200005660232() {
        return b200005660232;
    }

    public void setB200005660232(BigDecimal b200005660232) {
        this.b200005660232 = b200005660232;
    }

    public BigDecimal getB200005660233() {
        return b200005660233;
    }

    public void setB200005660233(BigDecimal b200005660233) {
        this.b200005660233 = b200005660233;
    }

    public BigDecimal getB200005660234() {
        return b200005660234;
    }

    public void setB200005660234(BigDecimal b200005660234) {
        this.b200005660234 = b200005660234;
    }

    public BigDecimal getB200005660235() {
        return b200005660235;
    }

    public void setB200005660235(BigDecimal b200005660235) {
        this.b200005660235 = b200005660235;
    }

    public BigDecimal getB200005660236() {
        return b200005660236;
    }

    public void setB200005660236(BigDecimal b200005660236) {
        this.b200005660236 = b200005660236;
    }

    public BigDecimal getB200005660237() {
        return b200005660237;
    }

    public void setB200005660237(BigDecimal b200005660237) {
        this.b200005660237 = b200005660237;
    }

    public BigDecimal getB200005660238() {
        return b200005660238;
    }

    public void setB200005660238(BigDecimal b200005660238) {
        this.b200005660238 = b200005660238;
    }

    public BigDecimal getB200005660239() {
        return b200005660239;
    }

    public void setB200005660239(BigDecimal b200005660239) {
        this.b200005660239 = b200005660239;
    }

    public BigDecimal getB200005660240() {
        return b200005660240;
    }

    public void setB200005660240(BigDecimal b200005660240) {
        this.b200005660240 = b200005660240;
    }

    public BigDecimal getB200005660241() {
        return b200005660241;
    }

    public void setB200005660241(BigDecimal b200005660241) {
        this.b200005660241 = b200005660241;
    }

    public BigDecimal getB200005660242() {
        return b200005660242;
    }

    public void setB200005660242(BigDecimal b200005660242) {
        this.b200005660242 = b200005660242;
    }

    public BigDecimal getB200005660243() {
        return b200005660243;
    }

    public void setB200005660243(BigDecimal b200005660243) {
        this.b200005660243 = b200005660243;
    }

    public BigDecimal getB200005660244() {
        return b200005660244;
    }

    public void setB200005660244(BigDecimal b200005660244) {
        this.b200005660244 = b200005660244;
    }

    public BigDecimal getB200005660245() {
        return b200005660245;
    }

    public void setB200005660245(BigDecimal b200005660245) {
        this.b200005660245 = b200005660245;
    }

    public BigDecimal getB200005660246() {
        return b200005660246;
    }

    public void setB200005660246(BigDecimal b200005660246) {
        this.b200005660246 = b200005660246;
    }

    public BigDecimal getB200005660247() {
        return b200005660247;
    }

    public void setB200005660247(BigDecimal b200005660247) {
        this.b200005660247 = b200005660247;
    }

    public BigDecimal getB200005660248() {
        return b200005660248;
    }

    public void setB200005660248(BigDecimal b200005660248) {
        this.b200005660248 = b200005660248;
    }

    public BigDecimal getB200005660249() {
        return b200005660249;
    }

    public void setB200005660249(BigDecimal b200005660249) {
        this.b200005660249 = b200005660249;
    }

    public BigDecimal getB200005660250() {
        return b200005660250;
    }

    public void setB200005660250(BigDecimal b200005660250) {
        this.b200005660250 = b200005660250;
    }

    public BigDecimal getB200005660251() {
        return b200005660251;
    }

    public void setB200005660251(BigDecimal b200005660251) {
        this.b200005660251 = b200005660251;
    }

    public BigDecimal getB200005660252() {
        return b200005660252;
    }

    public void setB200005660252(BigDecimal b200005660252) {
        this.b200005660252 = b200005660252;
    }

    public BigDecimal getB200005660253() {
        return b200005660253;
    }

    public void setB200005660253(BigDecimal b200005660253) {
        this.b200005660253 = b200005660253;
    }

    public BigDecimal getB200005660254() {
        return b200005660254;
    }

    public void setB200005660254(BigDecimal b200005660254) {
        this.b200005660254 = b200005660254;
    }

    public BigDecimal getB200005660255() {
        return b200005660255;
    }

    public void setB200005660255(BigDecimal b200005660255) {
        this.b200005660255 = b200005660255;
    }

    public BigDecimal getB200005660256() {
        return b200005660256;
    }

    public void setB200005660256(BigDecimal b200005660256) {
        this.b200005660256 = b200005660256;
    }

    public BigDecimal getB200005660257() {
        return b200005660257;
    }

    public void setB200005660257(BigDecimal b200005660257) {
        this.b200005660257 = b200005660257;
    }

    public BigDecimal getB200005660258() {
        return b200005660258;
    }

    public void setB200005660258(BigDecimal b200005660258) {
        this.b200005660258 = b200005660258;
    }

    public BigDecimal getB200005660259() {
        return b200005660259;
    }

    public void setB200005660259(BigDecimal b200005660259) {
        this.b200005660259 = b200005660259;
    }

    public BigDecimal getB200005660260() {
        return b200005660260;
    }

    public void setB200005660260(BigDecimal b200005660260) {
        this.b200005660260 = b200005660260;
    }

    public BigDecimal getB200005660261() {
        return b200005660261;
    }

    public void setB200005660261(BigDecimal b200005660261) {
        this.b200005660261 = b200005660261;
    }

    public BigDecimal getB2000056603() {
        return b2000056603;
    }

    public void setB2000056603(BigDecimal b2000056603) {
        this.b2000056603 = b2000056603;
    }

    public BigDecimal getB200005660301() {
        return b200005660301;
    }

    public void setB200005660301(BigDecimal b200005660301) {
        this.b200005660301 = b200005660301;
    }

    public BigDecimal getB200005660302() {
        return b200005660302;
    }

    public void setB200005660302(BigDecimal b200005660302) {
        this.b200005660302 = b200005660302;
    }

    public BigDecimal getB200005660303() {
        return b200005660303;
    }

    public void setB200005660303(BigDecimal b200005660303) {
        this.b200005660303 = b200005660303;
    }

    public BigDecimal getB200005660304() {
        return b200005660304;
    }

    public void setB200005660304(BigDecimal b200005660304) {
        this.b200005660304 = b200005660304;
    }

    public BigDecimal getB200005660305() {
        return b200005660305;
    }

    public void setB200005660305(BigDecimal b200005660305) {
        this.b200005660305 = b200005660305;
    }

    public BigDecimal getB20000566030501() {
        return b20000566030501;
    }

    public void setB20000566030501(BigDecimal b20000566030501) {
        this.b20000566030501 = b20000566030501;
    }

    public BigDecimal getB20000566030502() {
        return b20000566030502;
    }

    public void setB20000566030502(BigDecimal b20000566030502) {
        this.b20000566030502 = b20000566030502;
    }

    public BigDecimal getB200005660306() {
        return b200005660306;
    }

    public void setB200005660306(BigDecimal b200005660306) {
        this.b200005660306 = b200005660306;
    }

    public BigDecimal getB200005660307() {
        return b200005660307;
    }

    public void setB200005660307(BigDecimal b200005660307) {
        this.b200005660307 = b200005660307;
    }

    public BigDecimal getB200005660308() {
        return b200005660308;
    }

    public void setB200005660308(BigDecimal b200005660308) {
        this.b200005660308 = b200005660308;
    }

    public BigDecimal getB2000056604() {
        return b2000056604;
    }

    public void setB2000056604(BigDecimal b2000056604) {
        this.b2000056604 = b2000056604;
    }

    public BigDecimal getB2000056701() {
        return b2000056701;
    }

    public void setB2000056701(BigDecimal b2000056701) {
        this.b2000056701 = b2000056701;
    }

    public BigDecimal getB2000056702() {
        return b2000056702;
    }

    public void setB2000056702(BigDecimal b2000056702) {
        this.b2000056702 = b2000056702;
    }

    public BigDecimal getB2000056711() {
        return b2000056711;
    }

    public void setB2000056711(BigDecimal b2000056711) {
        this.b2000056711 = b2000056711;
    }

    public BigDecimal getB200005671101() {
        return b200005671101;
    }

    public void setB200005671101(BigDecimal b200005671101) {
        this.b200005671101 = b200005671101;
    }

    public BigDecimal getB20000567110101() {
        return b20000567110101;
    }

    public void setB20000567110101(BigDecimal b20000567110101) {
        this.b20000567110101 = b20000567110101;
    }

    public BigDecimal getB20000567110102() {
        return b20000567110102;
    }

    public void setB20000567110102(BigDecimal b20000567110102) {
        this.b20000567110102 = b20000567110102;
    }

    public BigDecimal getB20000567110103() {
        return b20000567110103;
    }

    public void setB20000567110103(BigDecimal b20000567110103) {
        this.b20000567110103 = b20000567110103;
    }

    public BigDecimal getB20000567110104() {
        return b20000567110104;
    }

    public void setB20000567110104(BigDecimal b20000567110104) {
        this.b20000567110104 = b20000567110104;
    }

    public BigDecimal getB20000567110105() {
        return b20000567110105;
    }

    public void setB20000567110105(BigDecimal b20000567110105) {
        this.b20000567110105 = b20000567110105;
    }

    public BigDecimal getB20000567110106() {
        return b20000567110106;
    }

    public void setB20000567110106(BigDecimal b20000567110106) {
        this.b20000567110106 = b20000567110106;
    }

    public BigDecimal getB20000567110107() {
        return b20000567110107;
    }

    public void setB20000567110107(BigDecimal b20000567110107) {
        this.b20000567110107 = b20000567110107;
    }

    public BigDecimal getB200005671102() {
        return b200005671102;
    }

    public void setB200005671102(BigDecimal b200005671102) {
        this.b200005671102 = b200005671102;
    }

    public BigDecimal getB20000567110201() {
        return b20000567110201;
    }

    public void setB20000567110201(BigDecimal b20000567110201) {
        this.b20000567110201 = b20000567110201;
    }

    public BigDecimal getB20000567110202() {
        return b20000567110202;
    }

    public void setB20000567110202(BigDecimal b20000567110202) {
        this.b20000567110202 = b20000567110202;
    }

    public BigDecimal getB20000567110203() {
        return b20000567110203;
    }

    public void setB20000567110203(BigDecimal b20000567110203) {
        this.b20000567110203 = b20000567110203;
    }

    public BigDecimal getB20000567110204() {
        return b20000567110204;
    }

    public void setB20000567110204(BigDecimal b20000567110204) {
        this.b20000567110204 = b20000567110204;
    }

    public BigDecimal getB20000567110205() {
        return b20000567110205;
    }

    public void setB20000567110205(BigDecimal b20000567110205) {
        this.b20000567110205 = b20000567110205;
    }

    public BigDecimal getB20000567110206() {
        return b20000567110206;
    }

    public void setB20000567110206(BigDecimal b20000567110206) {
        this.b20000567110206 = b20000567110206;
    }

    public BigDecimal getB20000567110207() {
        return b20000567110207;
    }

    public void setB20000567110207(BigDecimal b20000567110207) {
        this.b20000567110207 = b20000567110207;
    }

    public BigDecimal getB200005671103() {
        return b200005671103;
    }

    public void setB200005671103(BigDecimal b200005671103) {
        this.b200005671103 = b200005671103;
    }

    public BigDecimal getB200005671104() {
        return b200005671104;
    }

    public void setB200005671104(BigDecimal b200005671104) {
        this.b200005671104 = b200005671104;
    }

    public BigDecimal getB20000567110401() {
        return b20000567110401;
    }

    public void setB20000567110401(BigDecimal b20000567110401) {
        this.b20000567110401 = b20000567110401;
    }

    public BigDecimal getB20000567110402() {
        return b20000567110402;
    }

    public void setB20000567110402(BigDecimal b20000567110402) {
        this.b20000567110402 = b20000567110402;
    }

    public BigDecimal getB20000567110403() {
        return b20000567110403;
    }

    public void setB20000567110403(BigDecimal b20000567110403) {
        this.b20000567110403 = b20000567110403;
    }

    public BigDecimal getB20000567110404() {
        return b20000567110404;
    }

    public void setB20000567110404(BigDecimal b20000567110404) {
        this.b20000567110404 = b20000567110404;
    }

    public BigDecimal getB20000567110405() {
        return b20000567110405;
    }

    public void setB20000567110405(BigDecimal b20000567110405) {
        this.b20000567110405 = b20000567110405;
    }

    public BigDecimal getB20000567110406() {
        return b20000567110406;
    }

    public void setB20000567110406(BigDecimal b20000567110406) {
        this.b20000567110406 = b20000567110406;
    }

    public BigDecimal getB20000567110407() {
        return b20000567110407;
    }

    public void setB20000567110407(BigDecimal b20000567110407) {
        this.b20000567110407 = b20000567110407;
    }

    public BigDecimal getB200005671105() {
        return b200005671105;
    }

    public void setB200005671105(BigDecimal b200005671105) {
        this.b200005671105 = b200005671105;
    }

    public BigDecimal getB200005671106() {
        return b200005671106;
    }

    public void setB200005671106(BigDecimal b200005671106) {
        this.b200005671106 = b200005671106;
    }

    public BigDecimal getB200005671107() {
        return b200005671107;
    }

    public void setB200005671107(BigDecimal b200005671107) {
        this.b200005671107 = b200005671107;
    }

    public BigDecimal getB20000567110701() {
        return b20000567110701;
    }

    public void setB20000567110701(BigDecimal b20000567110701) {
        this.b20000567110701 = b20000567110701;
    }

    public BigDecimal getB20000567110702() {
        return b20000567110702;
    }

    public void setB20000567110702(BigDecimal b20000567110702) {
        this.b20000567110702 = b20000567110702;
    }

    public BigDecimal getB20000567110703() {
        return b20000567110703;
    }

    public void setB20000567110703(BigDecimal b20000567110703) {
        this.b20000567110703 = b20000567110703;
    }

    public BigDecimal getB200005671108() {
        return b200005671108;
    }

    public void setB200005671108(BigDecimal b200005671108) {
        this.b200005671108 = b200005671108;
    }

    public BigDecimal getB20000567110801() {
        return b20000567110801;
    }

    public void setB20000567110801(BigDecimal b20000567110801) {
        this.b20000567110801 = b20000567110801;
    }

    public BigDecimal getB20000567110802() {
        return b20000567110802;
    }

    public void setB20000567110802(BigDecimal b20000567110802) {
        this.b20000567110802 = b20000567110802;
    }

    public BigDecimal getB20000567110803() {
        return b20000567110803;
    }

    public void setB20000567110803(BigDecimal b20000567110803) {
        this.b20000567110803 = b20000567110803;
    }

    public BigDecimal getB20000567110804() {
        return b20000567110804;
    }

    public void setB20000567110804(BigDecimal b20000567110804) {
        this.b20000567110804 = b20000567110804;
    }

    public BigDecimal getB20000567110805() {
        return b20000567110805;
    }

    public void setB20000567110805(BigDecimal b20000567110805) {
        this.b20000567110805 = b20000567110805;
    }

    public BigDecimal getB20000567110806() {
        return b20000567110806;
    }

    public void setB20000567110806(BigDecimal b20000567110806) {
        this.b20000567110806 = b20000567110806;
    }

    public BigDecimal getB20000567110807() {
        return b20000567110807;
    }

    public void setB20000567110807(BigDecimal b20000567110807) {
        this.b20000567110807 = b20000567110807;
    }

    public BigDecimal getB20000567110808() {
        return b20000567110808;
    }

    public void setB20000567110808(BigDecimal b20000567110808) {
        this.b20000567110808 = b20000567110808;
    }

    public BigDecimal getB20000567110809() {
        return b20000567110809;
    }

    public void setB20000567110809(BigDecimal b20000567110809) {
        this.b20000567110809 = b20000567110809;
    }

    public BigDecimal getB20000567110810() {
        return b20000567110810;
    }

    public void setB20000567110810(BigDecimal b20000567110810) {
        this.b20000567110810 = b20000567110810;
    }

    public BigDecimal getB20000567110811() {
        return b20000567110811;
    }

    public void setB20000567110811(BigDecimal b20000567110811) {
        this.b20000567110811 = b20000567110811;
    }

    public BigDecimal getB20000567110812() {
        return b20000567110812;
    }

    public void setB20000567110812(BigDecimal b20000567110812) {
        this.b20000567110812 = b20000567110812;
    }

    public BigDecimal getB20000567110813() {
        return b20000567110813;
    }

    public void setB20000567110813(BigDecimal b20000567110813) {
        this.b20000567110813 = b20000567110813;
    }

    public BigDecimal getB2000056801() {
        return b2000056801;
    }

    public void setB2000056801(BigDecimal b2000056801) {
        this.b2000056801 = b2000056801;
    }

    public BigDecimal getB2000056901() {
        return b2000056901;
    }

    public void setB2000056901(BigDecimal b2000056901) {
        this.b2000056901 = b2000056901;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getSubjectMonth() {
        return subjectMonth;
    }

    public void setSubjectMonth(Integer subjectMonth) {
        this.subjectMonth = subjectMonth;
    }

    public BigDecimal getB200005222124() {
        return b200005222124;
    }

    public void setB200005222124(BigDecimal b200005222124) {
        this.b200005222124 = b200005222124;
    }

    public BigDecimal getB200005222125() {
        return b200005222125;
    }

    public void setB200005222125(BigDecimal b200005222125) {
        this.b200005222125 = b200005222125;
    }

    public BigDecimal getB20000567110501() {
        return b20000567110501;
    }

    public void setB20000567110501(BigDecimal b20000567110501) {
        this.b20000567110501 = b20000567110501;
    }

    public BigDecimal getB20000567110502() {
        return b20000567110502;
    }

    public void setB20000567110502(BigDecimal b20000567110502) {
        this.b20000567110502 = b20000567110502;
    }
}
