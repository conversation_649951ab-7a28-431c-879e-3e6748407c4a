package com.hm.api.consumer.feign.base.dto;

import com.hm.api.common.aop.LogField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 企业端明细账初始化映射实体
 * <AUTHOR>
@ApiModel("企业端明细账初始化映射实体")
public class EntDetailedAccountMap implements Serializable {
    private static final long serialVersionUID = 1L;



    /**
     * 企业id
     */
    @LogField(tableName = "ent_detailed_account_map", value = "ent_id", valueName = "企业id")
    @ApiModelProperty("企业id")
    @NotBlank(message = "企业id不能为空")
    private String entId;

    /**
     * 纳税年度
     */
    @LogField(tableName = "ent_detailed_account_map", value = "tax_year", valueName = "纳税年度")
    @ApiModelProperty("纳税年度")
    @NotBlank(message = "纳税年度")
    private String taxYear;


    /**
     * 科目
     */
    @LogField(tableName = "ent_detailed_account_map", value = "subject", valueName = "科目")
    @ApiModelProperty("科目")
    private String subject;

    @LogField(tableName = "ent_detailed_account_map", value = "subject_no", valueName = "科目编码")
    @ApiModelProperty("科目编码")
    private String subjectNo;

    @LogField(tableName = "ent_detailed_account_map", value = "subject_name", valueName = "科目名称")
    @ApiModelProperty("科目名称")
    private String subjectName;

    @LogField(tableName = "ent_detailed_account_map", value = "split_char", valueName = "分割符")
    @ApiModelProperty("分割符")
    private String splitChar;

    @LogField(tableName = "ent_detailed_account_map", value = "is_subjectno_before", valueName = "是否分割符在前")
    @ApiModelProperty("是否分割符在前 1 在前 2 在后")
    private String isSubjectnoBefore;

    /**
     * 日期
     */
    @LogField(tableName = "ent_detailed_account_map", value = "date", valueName = "日期")
    @ApiModelProperty("日期")
    private String date;

    /**
     * 凭证字号
     */
    @LogField(tableName = "ent_detailed_account_map", value = "voucherNo", valueName = "凭证字号")
    @ApiModelProperty("凭证字号")
    private String voucherNo;

    /**
     * 摘要
     */
    @LogField(tableName = "ent_detailed_account_map", value = "keyword", valueName = "摘要")
    @ApiModelProperty("摘要")
    private String keyword;

    /**
     * 借方
     */
    @LogField(tableName = "ent_detailed_account_map", value = "debit", valueName = "借方")
    @ApiModelProperty("借方")
    private String debit;

    /**
     * 贷方
     */
    @LogField(tableName = "ent_detailed_account_map", value = "credit", valueName = "贷方")
    @ApiModelProperty("贷方")
    private String credit;

    /**
     * 表头读取起始行
     */
    @LogField(tableName = "ent_detailed_account_map", value = "headRow", valueName = "表头读取起始行")
    @ApiModelProperty("表头读取起始行")
    @NotNull(message = "表头读取起始行不能为空")
    private Integer headRow;

    /**
     * 表头读取起始列
     */
    @LogField(tableName = "ent_detailed_account_map", value = "headColumn", valueName = "表头读取起始列")
    @ApiModelProperty("表头读取起始列")
    private Integer headColumn;

    /**
     * 数据读取起始行
     */
    @LogField(tableName = "ent_detailed_account_map", value = "dataRow", valueName = "数据读取起始行")
    @ApiModelProperty("数据读取起始行")
    @NotNull(message = "数据读取起始行不能为空")
    private Integer dataRow;

    /**
     *  上传excel文件url地址
     */
    @ApiModelProperty("上传excel文件url地址")
    private String  excelFileUrl;

    /**
     * 创建日期
     */
    @LogField(tableName = "ent_detailed_account_map", value = "主键", valueName = "主键")
    @ApiModelProperty("创建日期")
    private String createTime;

    /**
     * 更新日期
     */
    @LogField(tableName = "ent_detailed_account_map", value = "主键", valueName = "主键")
    @ApiModelProperty("更新日期")
    private String updateTime;

    public String getEntId() {
        return entId;
    }

    public void setEntId(String entId) {
        this.entId = entId;
    }

    public String getTaxYear() {
        return taxYear;
    }

    public void setTaxYear(String taxYear) {
        this.taxYear = taxYear;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getSubjectNo() {
        return subjectNo;
    }

    public void setSubjectNo(String subjectNo) {
        this.subjectNo = subjectNo;
    }

    public String getSubjectName() {
        return subjectName;
    }

    public void setSubjectName(String subjectName) {
        this.subjectName = subjectName;
    }

    public String getSplitChar() {
        return splitChar;
    }

    public void setSplitChar(String splitChar) {
        this.splitChar = splitChar;
    }

    public String getIsSubjectnoBefore() {
        return isSubjectnoBefore;
    }

    public void setIsSubjectnoBefore(String isSubjectnoBefore) {
        this.isSubjectnoBefore = isSubjectnoBefore;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public String getVoucherNo() {
        return voucherNo;
    }

    public void setVoucherNo(String voucherNo) {
        this.voucherNo = voucherNo;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public String getDebit() {
        return debit;
    }

    public void setDebit(String debit) {
        this.debit = debit;
    }

    public String getCredit() {
        return credit;
    }

    public void setCredit(String credit) {
        this.credit = credit;
    }

    public Integer getHeadRow() {
        return headRow;
    }

    public void setHeadRow(Integer headRow) {
        this.headRow = headRow;
    }

    public Integer getHeadColumn() {
        return headColumn;
    }

    public void setHeadColumn(Integer headColumn) {
        this.headColumn = headColumn;
    }

    public Integer getDataRow() {
        return dataRow;
    }

    public String getExcelFileUrl() {
        return excelFileUrl;
    }

    public void setExcelFileUrl(String excelFileUrl) {
        this.excelFileUrl = excelFileUrl;
    }

    public void setDataRow(Integer dataRow) {
        this.dataRow = dataRow;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }


}