package com.hm.base.activiti.controller;

import com.hm.api.common.exception.BizException;
import com.hm.api.common.response.Result;
import com.hm.base.activiti.service.ProcessDefinitionService;
import com.hm.base.activiti.vo.DeploymentVo;
import com.hm.base.activiti.vo.UserTaskVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.RepositoryService;
import org.activiti.engine.repository.ProcessDefinition;
import org.activiti.engine.repository.ProcessDefinitionQuery;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;

@Slf4j
@Api(tags = "流程定义相关")
@RestController
@RequestMapping("/process/definition")
public class ProcessDefinitionController {
    @Autowired
    private RepositoryService repositoryService;

    @Autowired
    private ProcessDefinitionService processDefinitionService;

    @ApiOperation("流程定义列表")
    @GetMapping("/list")
    public List<com.hm.base.activiti.pojo.ProcessDefinition> list(com.hm.base.activiti.pojo.ProcessDefinition processDefinition) {
        return processDefinitionService.listProcessDefinition(processDefinition);
    }

    @ApiOperation("流程定义文件上传")
    @PostMapping("/upload")
    public Result<DeploymentVo> deployProcessDefinition(@RequestPart("file") MultipartFile file) {
        DeploymentVo entity = processDefinitionService.deployProcessDefinition(file);
        return Result.success(entity);
    }

    @ApiOperation("删除流程部署")
    @PostMapping("/remove")
    public Result<?> remove(@ApiParam(value = "流程部署id集合") @RequestParam List<String> ids) {
        processDefinitionService.deleteProcessDeploymentByIds(ids);
        return Result.success();
    }


    @ApiOperation("流程定义激活或挂起")
    @PostMapping("/suspendOrActiveApply")
    public Result<?> suspendOrActiveApply(@ApiParam(value = "流程定义id") @RequestParam String processDefinitionId,
                                          @ApiParam(value = "1：挂起，2：激活") @RequestParam String suspendState) {
        processDefinitionService.suspendOrActiveApply(processDefinitionId, suspendState);
        return Result.success();
    }

    @ApiOperation("读取流程定义资源文件")
    @GetMapping(value = "/readResource")
    public void readResource(@ApiParam(value = "流程定义id") @RequestParam String processDefinitionId,
                             @ApiParam(value = "资源名称") @RequestParam String resourceName, HttpServletResponse response) {
        ProcessDefinitionQuery pdq = repositoryService.createProcessDefinitionQuery();
        ProcessDefinition pd = pdq.processDefinitionId(processDefinitionId).singleResult();
        if (pd == null) {
            throw new BizException("流程定义id不存在");
        }
        try (ServletOutputStream outputStream = response.getOutputStream();
             InputStream resourceAsStream = repositoryService.getResourceAsStream(pd.getDeploymentId(), resourceName);) {
            IOUtils.copy(resourceAsStream, outputStream);
        } catch (IOException exception) {
            log.error(exception.getMessage(), exception);
        }
    }

    @ApiOperation("获取所有用户任务节点")
    @GetMapping("/user-task")
    public Result<?> userTask(@ApiParam(value = "流程定义id") @RequestParam String processDefinitionId) {
        List<UserTaskVo> taskVos = processDefinitionService.selectUserTask(processDefinitionId);
        return Result.success(taskVos);
    }
}
