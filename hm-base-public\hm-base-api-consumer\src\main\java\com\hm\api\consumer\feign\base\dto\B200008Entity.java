package com.hm.api.consumer.feign.base.dto;

import com.hm.api.common.aop.LogField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @author: curry.wu
 * @create: 2020/10/25 14:26
 **/
@ApiModel(value = "本年期末贷方余额")
public class B200008Entity implements Serializable {

    private static final long serialVersionUID = 7159516178412616517L;

    @LogField(tableName = "b200008", value = "ent_id", valueName = "企业ID")
    @ApiModelProperty(value = "企业ID")
    private String entId;

    @LogField(tableName = "b200008", value = "tax_year", valueName = "所属时间")
    @ApiModelProperty(value = "所属时间")
    private String taxYear;

    @LogField(tableName = "b200008", value = "subject_month", valueName = "月份(年度等于12)")
    @ApiModelProperty(value = "月份(年度等于12)")
    private Integer subjectMonth;

    @LogField(tableName = "b200008", value = "b200008_1001", valueName = "库存现金")
    @ApiModelProperty(value = "库存现金")
    private BigDecimal b2000081001;

    @LogField(tableName = "b200008", value = "b200008_1002", valueName = "银行存款")
    @ApiModelProperty(value = "银行存款")
    private BigDecimal b2000081002;

    @LogField(tableName = "b200008", value = "b200008_1003", valueName = "存放中央银行款项")
    @ApiModelProperty(value = "存放中央银行款项")
    private BigDecimal b2000081003;

    @LogField(tableName = "b200008", value = "b200008_1011", valueName = "存放同业")
    @ApiModelProperty(value = "存放同业")
    private BigDecimal b2000081011;

    @LogField(tableName = "b200008", value = "b200008_1012", valueName = "其他货币资金")
    @ApiModelProperty(value = "其他货币资金")
    private BigDecimal b2000081012;

    @LogField(tableName = "b200008", value = "b200008_1021", valueName = "结算备付金")
    @ApiModelProperty(value = "结算备付金")
    private BigDecimal b2000081021;

    @LogField(tableName = "b200008", value = "b200008_1031", valueName = "存出保证金")
    @ApiModelProperty(value = "存出保证金")
    private BigDecimal b2000081031;

    @LogField(tableName = "b200008", value = "b200008_1101", valueName = "交易性金融资产")
    @ApiModelProperty(value = "交易性金融资产")
    private BigDecimal b2000081101;

    @LogField(tableName = "b200008", value = "b200008_1111", valueName = "买入返售金融资产")
    @ApiModelProperty(value = "买入返售金融资产")
    private BigDecimal b2000081111;

    @LogField(tableName = "b200008", value = "b200008_1121", valueName = "应收票据")
    @ApiModelProperty(value = "应收票据")
    private BigDecimal b2000081121;

    @LogField(tableName = "b200008", value = "b200008_1122", valueName = "应收账款")
    @ApiModelProperty(value = "应收账款")
    private BigDecimal b2000081122;

    @LogField(tableName = "b200008", value = "b200008_1123", valueName = "预付账款")
    @ApiModelProperty(value = "预付账款")
    private BigDecimal b2000081123;

    @LogField(tableName = "b200008", value = "b200008_1124", valueName = "合同资产")
    @ApiModelProperty(value = "合同资产")
    private BigDecimal b2000081124;

    @LogField(tableName = "b200008", value = "b200008_1125", valueName = "合同资产减值准备")
    @ApiModelProperty(value = "合同资产减值准备")
    private BigDecimal b2000081125;

    @LogField(tableName = "b200008", value = "b200008_1131", valueName = "应收股利")
    @ApiModelProperty(value = "应收股利")
    private BigDecimal b2000081131;

    @LogField(tableName = "b200008", value = "b200008_1132", valueName = "应收利息")
    @ApiModelProperty(value = "应收利息")
    private BigDecimal b2000081132;

    @LogField(tableName = "b200008", value = "b200008_1201", valueName = "应收代位追偿款")
    @ApiModelProperty(value = "应收代位追偿款")
    private BigDecimal b2000081201;

    @LogField(tableName = "b200008", value = "b200008_1211", valueName = "应收分保账款")
    @ApiModelProperty(value = "应收分保账款")
    private BigDecimal b2000081211;

    @LogField(tableName = "b200008", value = "b200008_1212", valueName = "应收分保合同准备金")
    @ApiModelProperty(value = "应收分保合同准备金")
    private BigDecimal b2000081212;

    @LogField(tableName = "b200008", value = "b200008_1221", valueName = "其他应收款")
    @ApiModelProperty(value = "其他应收款")
    private BigDecimal b2000081221;

    @LogField(tableName = "b200008", value = "b200008_1231", valueName = "坏账准备")
    @ApiModelProperty(value = "坏账准备")
    private BigDecimal b2000081231;

    @LogField(tableName = "b200008", value = "b200008_1301", valueName = "贴现资产")
    @ApiModelProperty(value = "贴现资产")
    private BigDecimal b2000081301;

    @LogField(tableName = "b200008", value = "b200008_1302", valueName = "拆出资金")
    @ApiModelProperty(value = "拆出资金")
    private BigDecimal b2000081302;

    @LogField(tableName = "b200008", value = "b200008_1303", valueName = "贷款")
    @ApiModelProperty(value = "贷款")
    private BigDecimal b2000081303;

    @LogField(tableName = "b200008", value = "b200008_1304", valueName = "贷款损失准备")
    @ApiModelProperty(value = "贷款损失准备")
    private BigDecimal b2000081304;

    @LogField(tableName = "b200008", value = "b200008_1311", valueName = "代理兑付证券")
    @ApiModelProperty(value = "代理兑付证券")
    private BigDecimal b2000081311;

    @LogField(tableName = "b200008", value = "b200008_1321", valueName = "代理业务资产")
    @ApiModelProperty(value = "代理业务资产")
    private BigDecimal b2000081321;

    @LogField(tableName = "b200008", value = "b200008_1401", valueName = "材料采购")
    @ApiModelProperty(value = "材料采购")
    private BigDecimal b2000081401;

    @LogField(tableName = "b200008", value = "b200008_1402", valueName = "在途物资")
    @ApiModelProperty(value = "在途物资")
    private BigDecimal b2000081402;

    @LogField(tableName = "b200008", value = "b200008_1403", valueName = "原材料")
    @ApiModelProperty(value = "原材料")
    private BigDecimal b2000081403;

    @LogField(tableName = "b200008", value = "b200008_1404", valueName = "材料成本差异")
    @ApiModelProperty(value = "材料成本差异")
    private BigDecimal b2000081404;

    @LogField(tableName = "b200008", value = "b200008_1405", valueName = "库存商品")
    @ApiModelProperty(value = "库存商品")
    private BigDecimal b2000081405;

    @LogField(tableName = "b200008", value = "b200008_1406", valueName = "发出商品")
    @ApiModelProperty(value = "发出商品")
    private BigDecimal b2000081406;

    @LogField(tableName = "b200008", value = "b200008_1407", valueName = "商品进销差价")
    @ApiModelProperty(value = "商品进销差价")
    private BigDecimal b2000081407;

    @LogField(tableName = "b200008", value = "b200008_1408", valueName = "委托加工物资")
    @ApiModelProperty(value = "委托加工物资")
    private BigDecimal b2000081408;

    @LogField(tableName = "b200008", value = "b200008_1411", valueName = "周转材料")
    @ApiModelProperty(value = "周转材料")
    private BigDecimal b2000081411;

    @LogField(tableName = "b200008", value = "b200008_1421", valueName = "消耗性生物资产")
    @ApiModelProperty(value = "消耗性生物资产")
    private BigDecimal b2000081421;

    @LogField(tableName = "b200008", value = "b200008_1431", valueName = "贵金属")
    @ApiModelProperty(value = "贵金属")
    private BigDecimal b2000081431;

    @LogField(tableName = "b200008", value = "b200008_1441", valueName = "抵债资产")
    @ApiModelProperty(value = "抵债资产")
    private BigDecimal b2000081441;

    @LogField(tableName = "b200008", value = "b200008_1451", valueName = "损余物资")
    @ApiModelProperty(value = "损余物资")
    private BigDecimal b2000081451;

    @LogField(tableName = "b200008", value = "b200008_1461", valueName = "融资租赁资产")
    @ApiModelProperty(value = "融资租赁资产")
    private BigDecimal b2000081461;

    @LogField(tableName = "b200008", value = "b200008_1471", valueName = "存货跌价准备")
    @ApiModelProperty(value = "存货跌价准备")
    private BigDecimal b2000081471;

    @LogField(tableName = "b200008", value = "b200008_1481", valueName = "持有待售资产")
    @ApiModelProperty(value = "持有待售资产")
    private BigDecimal b2000081481;

    @LogField(tableName = "b200008", value = "b200008_1482", valueName = "持有待售资产减值准备")
    @ApiModelProperty(value = "持有待售资产减值准备")
    private BigDecimal b2000081482;

    @LogField(tableName = "b200008", value = "b200008_1501", valueName = "持有至到期投资")
    @ApiModelProperty(value = "持有至到期投资")
    private BigDecimal b2000081501;

    @LogField(tableName = "b200008", value = "b200008_1502", valueName = "持有至到期投资减值准备")
    @ApiModelProperty(value = "持有至到期投资减值准备")
    private BigDecimal b2000081502;

    @LogField(tableName = "b200008", value = "b200008_1503", valueName = "可供出售金融资产")
    @ApiModelProperty(value = "可供出售金融资产")
    private BigDecimal b2000081503;

    @LogField(tableName = "b200008", value = "b200008_1511", valueName = "长期股权投资")
    @ApiModelProperty(value = "长期股权投资")
    private BigDecimal b2000081511;

    @LogField(tableName = "b200008", value = "b200008_1512", valueName = "长期股权投资减值准备")
    @ApiModelProperty(value = "长期股权投资减值准备")
    private BigDecimal b2000081512;

    @LogField(tableName = "b200008", value = "b200008_1521", valueName = "投资性房地产")
    @ApiModelProperty(value = "投资性房地产")
    private BigDecimal b2000081521;

    @LogField(tableName = "b200008", value = "b200008_1531", valueName = "长期应收款")
    @ApiModelProperty(value = "长期应收款")
    private BigDecimal b2000081531;

    @LogField(tableName = "b200008", value = "b200008_1532", valueName = "未实现融资收益")
    @ApiModelProperty(value = "未实现融资收益")
    private BigDecimal b2000081532;

    @LogField(tableName = "b200008", value = "b200008_1541", valueName = "存出资本保证金")
    @ApiModelProperty(value = "存出资本保证金")
    private BigDecimal b2000081541;

    @LogField(tableName = "b200008", value = "b200008_1601", valueName = "固定资产")
    @ApiModelProperty(value = "固定资产")
    private BigDecimal b2000081601;

    @LogField(tableName = "b200008", value = "b200008_1602", valueName = "累计折旧")
    @ApiModelProperty(value = "累计折旧")
    private BigDecimal b2000081602;

    @LogField(tableName = "b200008", value = "b200008_1603", valueName = "固定资产减值准备")
    @ApiModelProperty(value = "固定资产减值准备")
    private BigDecimal b2000081603;

    @LogField(tableName = "b200008", value = "b200008_1604", valueName = "在建工程")
    @ApiModelProperty(value = "在建工程")
    private BigDecimal b2000081604;

    @LogField(tableName = "b200008", value = "b200008_1605", valueName = "工程物资")
    @ApiModelProperty(value = "工程物资")
    private BigDecimal b2000081605;

    @LogField(tableName = "b200008", value = "b200008_1606", valueName = "固定资产清理")
    @ApiModelProperty(value = "固定资产清理")
    private BigDecimal b2000081606;

    @LogField(tableName = "b200008", value = "b200008_1611", valueName = "未担保余值")
    @ApiModelProperty(value = "未担保余值")
    private BigDecimal b2000081611;

    @LogField(tableName = "b200008", value = "b200008_1621", valueName = "生产性生物资产")
    @ApiModelProperty(value = "生产性生物资产")
    private BigDecimal b2000081621;

    @LogField(tableName = "b200008", value = "b200008_1622", valueName = "生产性生物资产累计折旧")
    @ApiModelProperty(value = "生产性生物资产累计折旧")
    private BigDecimal b2000081622;

    @LogField(tableName = "b200008", value = "b200008_1623", valueName = "公益性生物资产")
    @ApiModelProperty(value = "公益性生物资产")
    private BigDecimal b2000081623;

    @LogField(tableName = "b200008", value = "b200008_1631", valueName = "油气资产")
    @ApiModelProperty(value = "油气资产")
    private BigDecimal b2000081631;

    @LogField(tableName = "b200008", value = "b200008_1632", valueName = "累计折耗")
    @ApiModelProperty(value = "累计折耗")
    private BigDecimal b2000081632;

    @LogField(tableName = "b200008", value = "b200008_1701", valueName = "无形资产")
    @ApiModelProperty(value = "无形资产")
    private BigDecimal b2000081701;

    @LogField(tableName = "b200008", value = "b200008_1702", valueName = "累计摊销")
    @ApiModelProperty(value = "累计摊销")
    private BigDecimal b2000081702;

    @LogField(tableName = "b200008", value = "b200008_1703", valueName = "无形资产减值准备")
    @ApiModelProperty(value = "无形资产减值准备")
    private BigDecimal b2000081703;

    @LogField(tableName = "b200008", value = "b200008_1711", valueName = "商誉")
    @ApiModelProperty(value = "商誉")
    private BigDecimal b2000081711;

    @LogField(tableName = "b200008", value = "b200008_1801", valueName = "长期待摊费用")
    @ApiModelProperty(value = "长期待摊费用")
    private BigDecimal b2000081801;

    @LogField(tableName = "b200008", value = "b200008_1811", valueName = "递延所得税资产")
    @ApiModelProperty(value = "递延所得税资产")
    private BigDecimal b2000081811;

    @LogField(tableName = "b200008", value = "b200008_1821", valueName = "独立账户资产")
    @ApiModelProperty(value = "独立账户资产")
    private BigDecimal b2000081821;

    @LogField(tableName = "b200008", value = "b200008_1901", valueName = "待处理财产损溢")
    @ApiModelProperty(value = "待处理财产损溢")
    private BigDecimal b2000081901;

    @LogField(tableName = "b200008", value = "b200008_2001", valueName = "短期借款")
    @ApiModelProperty(value = "短期借款")
    private BigDecimal b2000082001;

    @LogField(tableName = "b200008", value = "b200008_2002", valueName = "存入保证金")
    @ApiModelProperty(value = "存入保证金")
    private BigDecimal b2000082002;

    @LogField(tableName = "b200008", value = "b200008_2003", valueName = "拆入资金")
    @ApiModelProperty(value = "拆入资金")
    private BigDecimal b2000082003;

    @LogField(tableName = "b200008", value = "b200008_2004", valueName = "向中央银行借款")
    @ApiModelProperty(value = "向中央银行借款")
    private BigDecimal b2000082004;

    @LogField(tableName = "b200008", value = "b200008_2011", valueName = "吸收存款")
    @ApiModelProperty(value = "吸收存款")
    private BigDecimal b2000082011;

    @LogField(tableName = "b200008", value = "b200008_2012", valueName = "同业存放")
    @ApiModelProperty(value = "同业存放")
    private BigDecimal b2000082012;

    @LogField(tableName = "b200008", value = "b200008_2021", valueName = "贴现负债")
    @ApiModelProperty(value = "贴现负债")
    private BigDecimal b2000082021;

    @LogField(tableName = "b200008", value = "b200008_2101", valueName = "交易性金融负债")
    @ApiModelProperty(value = "交易性金融负债")
    private BigDecimal b2000082101;

    @LogField(tableName = "b200008", value = "b200008_2111", valueName = "卖出回购金融资产款")
    @ApiModelProperty(value = "卖出回购金融资产款")
    private BigDecimal b2000082111;

    @LogField(tableName = "b200008", value = "b200008_2201", valueName = "应付票据")
    @ApiModelProperty(value = "应付票据")
    private BigDecimal b2000082201;

    @LogField(tableName = "b200008", value = "b200008_2202", valueName = "应付账款")
    @ApiModelProperty(value = "应付账款")
    private BigDecimal b2000082202;

    @LogField(tableName = "b200008", value = "b200008_2203", valueName = "预收账款")
    @ApiModelProperty(value = "预收账款")
    private BigDecimal b2000082203;

    @LogField(tableName = "b200008", value = "b200008_2204", valueName = "合同负债")
    @ApiModelProperty(value = "合同负债")
    private BigDecimal b2000082204;

    @LogField(tableName = "b200008", value = "b200008_2211", valueName = "应付职工薪酬")
    @ApiModelProperty(value = "应付职工薪酬")
    private BigDecimal b2000082211;

    @LogField(tableName = "b200008", value = "b200008_221101", valueName = "职工工资")
    @ApiModelProperty(value = "职工工资")
    private BigDecimal b200008221101;

    @LogField(tableName = "b200008", value = "b200008_221102", valueName = "职工福利费")
    @ApiModelProperty(value = "职工福利费")
    private BigDecimal b200008221102;

    @LogField(tableName = "b200008", value = "b200008_221103", valueName = "社会保险费")
    @ApiModelProperty(value = "社会保险费")
    private BigDecimal b200008221103;

    @LogField(tableName = "b200008", value = "b200008_221104", valueName = "住房公积金")
    @ApiModelProperty(value = "住房公积金")
    private BigDecimal b200008221104;

    @LogField(tableName = "b200008", value = "b200008_221105", valueName = "工会经费")
    @ApiModelProperty(value = "工会经费")
    private BigDecimal b200008221105;

    @LogField(tableName = "b200008", value = "b200008_221106", valueName = "职工教育经费")
    @ApiModelProperty(value = "职工教育经费")
    private BigDecimal b200008221106;

    @LogField(tableName = "b200008", value = "b200008_221107", valueName = "年终奖")
    @ApiModelProperty(value = "年终奖")
    private BigDecimal b200008221107;

    @LogField(tableName = "b200008", value = "b200008_221108", valueName = "职工培训费用")
    @ApiModelProperty(value = "职工培训费用")
    private BigDecimal b200008221108;

    @LogField(tableName = "b200008", value = "b200008_221109", valueName = "补充养老保险")
    @ApiModelProperty(value = "补充养老保险")
    private BigDecimal b200008221109;

    @LogField(tableName = "b200008", value = "b200008_221110", valueName = "补充医疗保险")
    @ApiModelProperty(value = "补充医疗保险")
    private BigDecimal b200008221110;

    @LogField(tableName = "b200008", value = "b200008_221111", valueName = "股权激励")
    @ApiModelProperty(value = "股权激励")
    private BigDecimal b200008221111;

    @LogField(tableName = "b200008", value = "b200008_221112", valueName = "商业保险费")
    @ApiModelProperty(value = "商业保险费")
    private BigDecimal b200008221112;

    @LogField(tableName = "b200008", value = "b200008_221113", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200008221113;

    @LogField(tableName = "b200008", value = "b200008_2221", valueName = "应交税费")
    @ApiModelProperty(value = "应交税费")
    private BigDecimal b2000082221;

    @LogField(tableName = "b200008", value = "b200008_222101", valueName = "应交增值税")
    @ApiModelProperty(value = "应交增值税")
    private BigDecimal b200008222101;

    @LogField(tableName = "b200008", value = "b200008_22210101", valueName = "进项税额")
    @ApiModelProperty(value = "进项税额")
    private BigDecimal b20000822210101;

    @LogField(tableName = "b200008", value = "b200008_22210102", valueName = "销项税额")
    @ApiModelProperty(value = "销项税额")
    private BigDecimal b20000822210102;

    @LogField(tableName = "b200008", value = "b200008_22210103", valueName = "已交税金")
    @ApiModelProperty(value = "已交税金")
    private BigDecimal b20000822210103;

    @LogField(tableName = "b200008", value = "b200008_22210104", valueName = "出口抵减内销产品应纳税额")
    @ApiModelProperty(value = "出口抵减内销产品应纳税额")
    private BigDecimal b20000822210104;

    @LogField(tableName = "b200008", value = "b200008_22210105", valueName = "转出未交增值税")
    @ApiModelProperty(value = "转出未交增值税")
    private BigDecimal b20000822210105;

    @LogField(tableName = "b200008", value = "b200008_22210106", valueName = "进项税额转出")
    @ApiModelProperty(value = "进项税额转出")
    private BigDecimal b20000822210106;

    @LogField(tableName = "b200008", value = "b200008_22210107", valueName = "减免税款")
    @ApiModelProperty(value = "减免税款")
    private BigDecimal b20000822210107;

    @LogField(tableName = "b200008", value = "b200008_22210108", valueName = "出口退税")
    @ApiModelProperty(value = "出口退税")
    private BigDecimal b20000822210108;

    @LogField(tableName = "b200008", value = "b200008_22210109", valueName = "转出多交增值税")
    @ApiModelProperty(value = "转出多交增值税")
    private BigDecimal b20000822210109;

    @LogField(tableName = "b200008", value = "b200008_22210110", valueName = "销项税额抵减")
    @ApiModelProperty(value = "销项税额抵减")
    private BigDecimal b20000822210110;

    @LogField(tableName = "b200008", value = "b200008_222102", valueName = "未交增值税")
    @ApiModelProperty(value = "未交增值税")
    private BigDecimal b200008222102;

    @LogField(tableName = "b200008", value = "b200008_222103", valueName = "应交营业税")
    @ApiModelProperty(value = "应交营业税")
    private BigDecimal b200008222103;

    @LogField(tableName = "b200008", value = "b200008_222104", valueName = "应交消费税")
    @ApiModelProperty(value = "应交消费税")
    private BigDecimal b200008222104;

    @LogField(tableName = "b200008", value = "b200008_222105", valueName = "应交资源税")
    @ApiModelProperty(value = "应交资源税")
    private BigDecimal b200008222105;

    @LogField(tableName = "b200008", value = "b200008_222106", valueName = "应交所得税")
    @ApiModelProperty(value = "应交所得税")
    private BigDecimal b200008222106;

    @LogField(tableName = "b200008", value = "b200008_222107", valueName = "应交土地增值税")
    @ApiModelProperty(value = "应交土地增值税")
    private BigDecimal b200008222107;

    @LogField(tableName = "b200008", value = "b200008_222108", valueName = "应交城市维护建设税")
    @ApiModelProperty(value = "应交城市维护建设税")
    private BigDecimal b200008222108;

    @LogField(tableName = "b200008", value = "b200008_222109", valueName = "应交房产税")
    @ApiModelProperty(value = "应交房产税")
    private BigDecimal b200008222109;

    @LogField(tableName = "b200008", value = "b200008_222110", valueName = "应交土地使用税")
    @ApiModelProperty(value = "应交土地使用税")
    private BigDecimal b200008222110;

    @LogField(tableName = "b200008", value = "b200008_222111", valueName = "应交车船税")
    @ApiModelProperty(value = "应交车船税")
    private BigDecimal b200008222111;

    @LogField(tableName = "b200008", value = "b200008_222112", valueName = "应交个人所得税")
    @ApiModelProperty(value = "应交个人所得税")
    private BigDecimal b200008222112;

    @LogField(tableName = "b200008", value = "b200008_222113", valueName = "教育费附加")
    @ApiModelProperty(value = "教育费附加")
    private BigDecimal b200008222113;

    @LogField(tableName = "b200008", value = "b200008_222114", valueName = "地方教育费附加")
    @ApiModelProperty(value = "地方教育费附加")
    private BigDecimal b200008222114;

    @LogField(tableName = "b200008", value = "b200008_222115", valueName = "印花税")
    @ApiModelProperty(value = "印花税")
    private BigDecimal b200008222115;

    @LogField(tableName = "b200008", value = "b200008_222116", valueName = "待抵扣进项税额")
    @ApiModelProperty(value = "待抵扣进项税额")
    private BigDecimal b200008222116;

    @LogField(tableName = "b200008", value = "b200008_222117", valueName = "待认证进项税额")
    @ApiModelProperty(value = "待认证进项税额")
    private BigDecimal b200008222117;

    @LogField(tableName = "b200008", value = "b200008_222118", valueName = "预交增值税")
    @ApiModelProperty(value = "预交增值税")
    private BigDecimal b200008222118;

    @LogField(tableName = "b200008", value = "b200008_222119", valueName = "待转销项税额")
    @ApiModelProperty(value = "待转销项税额")
    private BigDecimal b200008222119;

    @LogField(tableName = "b200008", value = "b200008_222120", valueName = "增值税留抵税额")
    @ApiModelProperty(value = "增值税留抵税额")
    private BigDecimal b200008222120;

    @LogField(tableName = "b200008", value = "b200008_222121", valueName = "简易计税")
    @ApiModelProperty(value = "简易计税")
    private BigDecimal b200008222121;

    @LogField(tableName = "b200008", value = "b200008_222122", valueName = "转让金融商品应交增值税")
    @ApiModelProperty(value = "转让金融商品应交增值税")
    private BigDecimal b200008222122;

    @LogField(tableName = "b200008", value = "b200008_222123", valueName = "代扣代缴增值税")
    @ApiModelProperty(value = "代扣代缴增值税")
    private BigDecimal b200008222123;

    @LogField(tableName = "b200008", value = "b200008_2231", valueName = "应付利息")
    @ApiModelProperty(value = "应付利息")
    private BigDecimal b2000082231;

    @LogField(tableName = "b200008", value = "b200008_2232", valueName = "应付股利")
    @ApiModelProperty(value = "应付股利")
    private BigDecimal b2000082232;

    @LogField(tableName = "b200008", value = "b200008_2241", valueName = "其他应付款")
    @ApiModelProperty(value = "其他应付款")
    private BigDecimal b2000082241;

    @LogField(tableName = "b200008", value = "b200008_2251", valueName = "应付保单红利")
    @ApiModelProperty(value = "应付保单红利")
    private BigDecimal b2000082251;

    @LogField(tableName = "b200008", value = "b200008_2261", valueName = "应付分保账款")
    @ApiModelProperty(value = "应付分保账款")
    private BigDecimal b2000082261;

    @LogField(tableName = "b200008", value = "b200008_2311", valueName = "代理买卖证券款")
    @ApiModelProperty(value = "代理买卖证券款")
    private BigDecimal b2000082311;

    @LogField(tableName = "b200008", value = "b200008_2312", valueName = "代理承销证券款")
    @ApiModelProperty(value = "代理承销证券款")
    private BigDecimal b2000082312;

    @LogField(tableName = "b200008", value = "b200008_2313", valueName = "代理兑付证券款")
    @ApiModelProperty(value = "代理兑付证券款")
    private BigDecimal b2000082313;

    @LogField(tableName = "b200008", value = "b200008_2314", valueName = "代理业务负债")
    @ApiModelProperty(value = "代理业务负债")
    private BigDecimal b2000082314;

    @LogField(tableName = "b200008", value = "b200008_2401", valueName = "递延收益")
    @ApiModelProperty(value = "递延收益")
    private BigDecimal b2000082401;

    @LogField(tableName = "b200008", value = "b200008_2245", valueName = "持有待售负债")
    @ApiModelProperty(value = "持有待售负债")
    private BigDecimal b2000082245;

    @LogField(tableName = "b200008", value = "b200008_2501", valueName = "长期借款")
    @ApiModelProperty(value = "长期借款")
    private BigDecimal b2000082501;

    @LogField(tableName = "b200008", value = "b200008_2502", valueName = "应付债券")
    @ApiModelProperty(value = "应付债券")
    private BigDecimal b2000082502;

    @LogField(tableName = "b200008", value = "b200008_2601", valueName = "未到期责任准备金")
    @ApiModelProperty(value = "未到期责任准备金")
    private BigDecimal b2000082601;

    @LogField(tableName = "b200008", value = "b200008_2602", valueName = "保险责任准备金")
    @ApiModelProperty(value = "保险责任准备金")
    private BigDecimal b2000082602;

    @LogField(tableName = "b200008", value = "b200008_2611", valueName = "保户储金")
    @ApiModelProperty(value = "保户储金")
    private BigDecimal b2000082611;

    @LogField(tableName = "b200008", value = "b200008_2621", valueName = "独立账户负债")
    @ApiModelProperty(value = "独立账户负债")
    private BigDecimal b2000082621;

    @LogField(tableName = "b200008", value = "b200008_2701", valueName = "长期应付款")
    @ApiModelProperty(value = "长期应付款")
    private BigDecimal b2000082701;

    @LogField(tableName = "b200008", value = "b200008_2702", valueName = "未确认融资费用")
    @ApiModelProperty(value = "未确认融资费用")
    private BigDecimal b2000082702;

    @LogField(tableName = "b200008", value = "b200008_2711", valueName = "专项应付款")
    @ApiModelProperty(value = "专项应付款")
    private BigDecimal b2000082711;

    @LogField(tableName = "b200008", value = "b200008_2801", valueName = "预计负债")
    @ApiModelProperty(value = "预计负债")
    private BigDecimal b2000082801;

    @LogField(tableName = "b200008", value = "b200008_2901", valueName = "递延所得税负债")
    @ApiModelProperty(value = "递延所得税负债")
    private BigDecimal b2000082901;

    @LogField(tableName = "b200008", value = "b200008_3001", valueName = "清算资金往来")
    @ApiModelProperty(value = "清算资金往来")
    private BigDecimal b2000083001;

    @LogField(tableName = "b200008", value = "b200008_3002", valueName = "货币兑换")
    @ApiModelProperty(value = "货币兑换")
    private BigDecimal b2000083002;

    @LogField(tableName = "b200008", value = "b200008_3101", valueName = "衍生工具")
    @ApiModelProperty(value = "衍生工具")
    private BigDecimal b2000083101;

    @LogField(tableName = "b200008", value = "b200008_3201", valueName = "套期工具")
    @ApiModelProperty(value = "套期工具")
    private BigDecimal b2000083201;

    @LogField(tableName = "b200008", value = "b200008_3202", valueName = "被套期项目")
    @ApiModelProperty(value = "被套期项目")
    private BigDecimal b2000083202;

    @LogField(tableName = "b200008", value = "b200008_4001", valueName = "实收资本")
    @ApiModelProperty(value = "实收资本")
    private BigDecimal b2000084001;

    @LogField(tableName = "b200008", value = "b200008_4002", valueName = "资本公积")
    @ApiModelProperty(value = "资本公积")
    private BigDecimal b2000084002;

    @LogField(tableName = "b200008", value = "b200008_4003", valueName = "其他综合收益")
    @ApiModelProperty(value = "其他综合收益")
    private BigDecimal b2000084003;

    @LogField(tableName = "b200008", value = "b200008_4101", valueName = "盈余公积")
    @ApiModelProperty(value = "盈余公积")
    private BigDecimal b2000084101;

    @LogField(tableName = "b200008", value = "b200008_4102", valueName = "一般风险准备")
    @ApiModelProperty(value = "一般风险准备")
    private BigDecimal b2000084102;

    @LogField(tableName = "b200008", value = "b200008_4103", valueName = "本年利润")
    @ApiModelProperty(value = "本年利润")
    private BigDecimal b2000084103;

    @LogField(tableName = "b200008", value = "b200008_4104", valueName = "利润分配")
    @ApiModelProperty(value = "利润分配")
    private BigDecimal b2000084104;

    @LogField(tableName = "b200008", value = "b200008_4201", valueName = "库存股")
    @ApiModelProperty(value = "库存股")
    private BigDecimal b2000084201;

    @LogField(tableName = "b200008", value = "b200008_4301", valueName = "专项储备")
    @ApiModelProperty(value = "专项储备")
    private BigDecimal b2000084301;

    @LogField(tableName = "b200008", value = "b200008_5001", valueName = "生产成本")
    @ApiModelProperty(value = "生产成本")
    private BigDecimal b2000085001;

    @LogField(tableName = "b200008", value = "b200008_500101", valueName = "直接人工")
    @ApiModelProperty(value = "直接人工")
    private BigDecimal b200008500101;

    @LogField(tableName = "b200008", value = "b200008_500102", valueName = "直接材料")
    @ApiModelProperty(value = "直接材料")
    private BigDecimal b200008500102;

    @LogField(tableName = "b200008", value = "b200008_500103", valueName = "制造费用")
    @ApiModelProperty(value = "制造费用")
    private BigDecimal b200008500103;

    @LogField(tableName = "b200008", value = "b200008_500104", valueName = "职工工资")
    @ApiModelProperty(value = "职工工资")
    private BigDecimal b200008500104;

    @LogField(tableName = "b200008", value = "b200008_500105", valueName = "年终奖")
    @ApiModelProperty(value = "年终奖")
    private BigDecimal b200008500105;

    @LogField(tableName = "b200008", value = "b200008_500106", valueName = "津贴与补贴")
    @ApiModelProperty(value = "津贴与补贴")
    private BigDecimal b200008500106;

    @LogField(tableName = "b200008", value = "b200008_500107", valueName = "股权激励")
    @ApiModelProperty(value = "股权激励")
    private BigDecimal b200008500107;

    @LogField(tableName = "b200008", value = "b200008_500108", valueName = "职工教育经费")
    @ApiModelProperty(value = "职工教育经费")
    private BigDecimal b200008500108;

    @LogField(tableName = "b200008", value = "b200008_500109", valueName = "职工培训费用")
    @ApiModelProperty(value = "职工培训费用")
    private BigDecimal b200008500109;

    @LogField(tableName = "b200008", value = "b200008_500110", valueName = "工会经费")
    @ApiModelProperty(value = "工会经费")
    private BigDecimal b200008500110;

    @LogField(tableName = "b200008", value = "b200008_500111", valueName = "社会保险费")
    @ApiModelProperty(value = "社会保险费")
    private BigDecimal b200008500111;

    @LogField(tableName = "b200008", value = "b200008_500112", valueName = "住房公积金")
    @ApiModelProperty(value = "住房公积金")
    private BigDecimal b200008500112;

    @LogField(tableName = "b200008", value = "b200008_500113", valueName = "职工福利费")
    @ApiModelProperty(value = "职工福利费")
    private BigDecimal b200008500113;

    @LogField(tableName = "b200008", value = "b200008_500114", valueName = "补充养老保险")
    @ApiModelProperty(value = "补充养老保险")
    private BigDecimal b200008500114;

    @LogField(tableName = "b200008", value = "b200008_500115", valueName = "补充医疗保险")
    @ApiModelProperty(value = "补充医疗保险")
    private BigDecimal b200008500115;

    @LogField(tableName = "b200008", value = "b200008_500116", valueName = "其他职工工资")
    @ApiModelProperty(value = "其他职工工资")
    private BigDecimal b200008500116;

    @LogField(tableName = "b200008", value = "b200008_500117", valueName = "商业保险费")
    @ApiModelProperty(value = "商业保险费")
    private BigDecimal b200008500117;

    @LogField(tableName = "b200008", value = "b200008_500118", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200008500118;

    @LogField(tableName = "b200008", value = "b200008_5101", valueName = "制造费用")
    @ApiModelProperty(value = "制造费用")
    private BigDecimal b2000085101;

    @LogField(tableName = "b200008", value = "b200008_510101", valueName = "职工工资")
    @ApiModelProperty(value = "职工工资")
    private BigDecimal b200008510101;

    @LogField(tableName = "b200008", value = "b200008_510102", valueName = "年终奖")
    @ApiModelProperty(value = "年终奖")
    private BigDecimal b200008510102;

    @LogField(tableName = "b200008", value = "b200008_510103", valueName = "津贴与补贴")
    @ApiModelProperty(value = "津贴与补贴")
    private BigDecimal b200008510103;

    @LogField(tableName = "b200008", value = "b200008_510104", valueName = "股权激励")
    @ApiModelProperty(value = "股权激励")
    private BigDecimal b200008510104;

    @LogField(tableName = "b200008", value = "b200008_510105", valueName = "社会保险费")
    @ApiModelProperty(value = "社会保险费")
    private BigDecimal b200008510105;

    @LogField(tableName = "b200008", value = "b200008_510106", valueName = "住房公积金")
    @ApiModelProperty(value = "住房公积金")
    private BigDecimal b200008510106;

    @LogField(tableName = "b200008", value = "b200008_510107", valueName = "补充养老保险")
    @ApiModelProperty(value = "补充养老保险")
    private BigDecimal b200008510107;

    @LogField(tableName = "b200008", value = "b200008_510108", valueName = "补充医疗保险")
    @ApiModelProperty(value = "补充医疗保险")
    private BigDecimal b200008510108;

    @LogField(tableName = "b200008", value = "b200008_510109", valueName = "职工教育经费")
    @ApiModelProperty(value = "职工教育经费")
    private BigDecimal b200008510109;

    @LogField(tableName = "b200008", value = "b200008_510110", valueName = "职工培训费用")
    @ApiModelProperty(value = "职工培训费用")
    private BigDecimal b200008510110;

    @LogField(tableName = "b200008", value = "b200008_510111", valueName = "其他职工工资")
    @ApiModelProperty(value = "其他职工工资")
    private BigDecimal b200008510111;

    @LogField(tableName = "b200008", value = "b200008_510112", valueName = "职工福利费")
    @ApiModelProperty(value = "职工福利费")
    private BigDecimal b200008510112;

    @LogField(tableName = "b200008", value = "b200008_510113", valueName = "工会经费")
    @ApiModelProperty(value = "工会经费")
    private BigDecimal b200008510113;

    @LogField(tableName = "b200008", value = "b200008_510114", valueName = "商业保险费")
    @ApiModelProperty(value = "商业保险费")
    private BigDecimal b200008510114;

    @LogField(tableName = "b200008", value = "b200008_510115", valueName = "折旧费")
    @ApiModelProperty(value = "折旧费")
    private BigDecimal b200008510115;

    @LogField(tableName = "b200008", value = "b200008_510116", valueName = "修理费")
    @ApiModelProperty(value = "修理费")
    private BigDecimal b200008510116;

    @LogField(tableName = "b200008", value = "b200008_510117", valueName = "机物料消耗")
    @ApiModelProperty(value = "机物料消耗")
    private BigDecimal b200008510117;

    @LogField(tableName = "b200008", value = "b200008_510118", valueName = "低值易耗品摊销")
    @ApiModelProperty(value = "低值易耗品摊销")
    private BigDecimal b200008510118;

    @LogField(tableName = "b200008", value = "b200008_510119", valueName = "租赁费")
    @ApiModelProperty(value = "租赁费")
    private BigDecimal b200008510119;

    @LogField(tableName = "b200008", value = "b200008_510120", valueName = "运杂费")
    @ApiModelProperty(value = "运杂费")
    private BigDecimal b200008510120;

    @LogField(tableName = "b200008", value = "b200008_510121", valueName = "保险费")
    @ApiModelProperty(value = "保险费")
    private BigDecimal b200008510121;

    @LogField(tableName = "b200008", value = "b200008_510122", valueName = "差旅费")
    @ApiModelProperty(value = "差旅费")
    private BigDecimal b200008510122;

    @LogField(tableName = "b200008", value = "b200008_510123", valueName = "水电费")
    @ApiModelProperty(value = "水电费")
    private BigDecimal b200008510123;

    @LogField(tableName = "b200008", value = "b200008_510124", valueName = "外部加工费")
    @ApiModelProperty(value = "外部加工费")
    private BigDecimal b200008510124;

    @LogField(tableName = "b200008", value = "b200008_510125", valueName = "厂房租金")
    @ApiModelProperty(value = "厂房租金")
    private BigDecimal b200008510125;

    @LogField(tableName = "b200008", value = "b200008_510126", valueName = "装卸费")
    @ApiModelProperty(value = "装卸费")
    private BigDecimal b200008510126;

    @LogField(tableName = "b200008", value = "b200008_510127", valueName = "设计制图费")
    @ApiModelProperty(value = "设计制图费")
    private BigDecimal b200008510127;

    @LogField(tableName = "b200008", value = "b200008_510128", valueName = "劳动保护费")
    @ApiModelProperty(value = "劳动保护费")
    private BigDecimal b200008510128;

    @LogField(tableName = "b200008", value = "b200008_510129", valueName = "无形资产摊销费")
    @ApiModelProperty(value = "无形资产摊销费")
    private BigDecimal b200008510129;

    @LogField(tableName = "b200008", value = "b200008_510130", valueName = "长期待摊费用摊销费")
    @ApiModelProperty(value = "长期待摊费用摊销费")
    private BigDecimal b200008510130;

    @LogField(tableName = "b200008", value = "b200008_510131", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200008510131;

    @LogField(tableName = "b200008", value = "b200008_5201", valueName = "劳务成本")
    @ApiModelProperty(value = "劳务成本")
    private BigDecimal b2000085201;

    @LogField(tableName = "b200008", value = "b200008_520101", valueName = "职工工资")
    @ApiModelProperty(value = "职工工资")
    private BigDecimal b200008520101;

    @LogField(tableName = "b200008", value = "b200008_520102", valueName = "年终奖")
    @ApiModelProperty(value = "年终奖")
    private BigDecimal b200008520102;

    @LogField(tableName = "b200008", value = "b200008_520103", valueName = "津贴与补贴")
    @ApiModelProperty(value = "津贴与补贴")
    private BigDecimal b200008520103;

    @LogField(tableName = "b200008", value = "b200008_520104", valueName = "股权激励")
    @ApiModelProperty(value = "股权激励")
    private BigDecimal b200008520104;

    @LogField(tableName = "b200008", value = "b200008_520105", valueName = "职工教育经费")
    @ApiModelProperty(value = "职工教育经费")
    private BigDecimal b200008520105;

    @LogField(tableName = "b200008", value = "b200008_520106", valueName = "职工培训费用")
    @ApiModelProperty(value = "职工培训费用")
    private BigDecimal b200008520106;

    @LogField(tableName = "b200008", value = "b200008_520107", valueName = "工会经费")
    @ApiModelProperty(value = "工会经费")
    private BigDecimal b200008520107;

    @LogField(tableName = "b200008", value = "b200008_520108", valueName = "社会保险费")
    @ApiModelProperty(value = "社会保险费")
    private BigDecimal b200008520108;

    @LogField(tableName = "b200008", value = "b200008_520109", valueName = "住房公积金")
    @ApiModelProperty(value = "住房公积金")
    private BigDecimal b200008520109;

    @LogField(tableName = "b200008", value = "b200008_520110", valueName = "职工福利费")
    @ApiModelProperty(value = "职工福利费")
    private BigDecimal b200008520110;

    @LogField(tableName = "b200008", value = "b200008_520111", valueName = "补充养老保险")
    @ApiModelProperty(value = "补充养老保险")
    private BigDecimal b200008520111;

    @LogField(tableName = "b200008", value = "b200008_520112", valueName = "补充医疗保险")
    @ApiModelProperty(value = "补充医疗保险")
    private BigDecimal b200008520112;

    @LogField(tableName = "b200008", value = "b200008_520113", valueName = "其他职工工资")
    @ApiModelProperty(value = "其他职工工资")
    private BigDecimal b200008520113;

    @LogField(tableName = "b200008", value = "b200008_520114", valueName = "商业保险费")
    @ApiModelProperty(value = "商业保险费")
    private BigDecimal b200008520114;

    @LogField(tableName = "b200008", value = "b200008_520115", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200008520115;

    @LogField(tableName = "b200008", value = "b200008_5301", valueName = "研发支出")
    @ApiModelProperty(value = "研发支出")
    private BigDecimal b2000085301;

    @LogField(tableName = "b200008", value = "b200008_530101", valueName = "资本化支出")
    @ApiModelProperty(value = "资本化支出")
    private BigDecimal b200008530101;

    @LogField(tableName = "b200008", value = "b200008_53010101", valueName = "人工成本")
    @ApiModelProperty(value = "人工成本")
    private BigDecimal b20000853010101;

    @LogField(tableName = "b200008", value = "b200008_5301010101", valueName = "职工工资")
    @ApiModelProperty(value = "职工工资")
    private BigDecimal b2000085301010101;

    @LogField(tableName = "b200008", value = "b200008_5301010102", valueName = "年终奖")
    @ApiModelProperty(value = "年终奖")
    private BigDecimal b2000085301010102;

    @LogField(tableName = "b200008", value = "b200008_5301010103", valueName = "津贴与补贴")
    @ApiModelProperty(value = "津贴与补贴")
    private BigDecimal b2000085301010103;

    @LogField(tableName = "b200008", value = "b200008_5301010104", valueName = "股权激励")
    @ApiModelProperty(value = "股权激励")
    private BigDecimal b2000085301010104;

    @LogField(tableName = "b200008", value = "b200008_5301010105", valueName = "职工教育经费")
    @ApiModelProperty(value = "职工教育经费")
    private BigDecimal b2000085301010105;

    @LogField(tableName = "b200008", value = "b200008_5301010106", valueName = "工会经费")
    @ApiModelProperty(value = "工会经费")
    private BigDecimal b2000085301010106;

    @LogField(tableName = "b200008", value = "b200008_5301010107", valueName = "社会保险费")
    @ApiModelProperty(value = "社会保险费")
    private BigDecimal b2000085301010107;

    @LogField(tableName = "b200008", value = "b200008_5301010108", valueName = "商业保险费")
    @ApiModelProperty(value = "商业保险费")
    private BigDecimal b2000085301010108;

    @LogField(tableName = "b200008", value = "b200008_5301010109", valueName = "住房公积金")
    @ApiModelProperty(value = "住房公积金")
    private BigDecimal b2000085301010109;

    @LogField(tableName = "b200008", value = "b200008_5301010110", valueName = "职工福利费")
    @ApiModelProperty(value = "职工福利费")
    private BigDecimal b2000085301010110;

    @LogField(tableName = "b200008", value = "b200008_5301010111", valueName = "补充养老保险")
    @ApiModelProperty(value = "补充养老保险")
    private BigDecimal b2000085301010111;

    @LogField(tableName = "b200008", value = "b200008_5301010112", valueName = "补充医疗保险")
    @ApiModelProperty(value = "补充医疗保险")
    private BigDecimal b2000085301010112;

    @LogField(tableName = "b200008", value = "b200008_5301010113", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000085301010113;

    @LogField(tableName = "b200008", value = "b200008_53010102", valueName = "劳务费")
    @ApiModelProperty(value = "劳务费")
    private BigDecimal b20000853010102;

    @LogField(tableName = "b200008", value = "b200008_53010103", valueName = "直接投入")
    @ApiModelProperty(value = "直接投入")
    private BigDecimal b20000853010103;

    @LogField(tableName = "b200008", value = "b200008_5301010301", valueName = "材料费用")
    @ApiModelProperty(value = "材料费用")
    private BigDecimal b2000085301010301;

    @LogField(tableName = "b200008", value = "b200008_5301010302", valueName = "燃料费用")
    @ApiModelProperty(value = "燃料费用")
    private BigDecimal b2000085301010302;

    @LogField(tableName = "b200008", value = "b200008_5301010303", valueName = "动力费用")
    @ApiModelProperty(value = "动力费用")
    private BigDecimal b2000085301010303;

    @LogField(tableName = "b200008", value = "b200008_5301010304", valueName = "试用品开发及制造费")
    @ApiModelProperty(value = "试用品开发及制造费")
    private BigDecimal b2000085301010304;

    @LogField(tableName = "b200008", value = "b200008_5301010305", valueName = "样品测试购置费")
    @ApiModelProperty(value = "样品测试购置费")
    private BigDecimal b2000085301010305;

    @LogField(tableName = "b200008", value = "b200008_5301010306", valueName = "试制品检验费")
    @ApiModelProperty(value = "试制品检验费")
    private BigDecimal b2000085301010306;

    @LogField(tableName = "b200008", value = "b200008_5301010307", valueName = "运行维护维修费")
    @ApiModelProperty(value = "运行维护维修费")
    private BigDecimal b2000085301010307;

    @LogField(tableName = "b200008", value = "b200008_5301010308", valueName = "运行调整费")
    @ApiModelProperty(value = "运行调整费")
    private BigDecimal b2000085301010308;

    @LogField(tableName = "b200008", value = "b200008_5301010309", valueName = "运行检测费")
    @ApiModelProperty(value = "运行检测费")
    private BigDecimal b2000085301010309;

    @LogField(tableName = "b200008", value = "b200008_5301010310", valueName = "仪器租赁费")
    @ApiModelProperty(value = "仪器租赁费")
    private BigDecimal b2000085301010310;

    @LogField(tableName = "b200008", value = "b200008_5301010311", valueName = "设备租赁费")
    @ApiModelProperty(value = "设备租赁费")
    private BigDecimal b2000085301010311;

    @LogField(tableName = "b200008", value = "b200008_5301010312", valueName = "房屋租赁费")
    @ApiModelProperty(value = "房屋租赁费")
    private BigDecimal b2000085301010312;

    @LogField(tableName = "b200008", value = "b200008_5301010313", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000085301010313;

    @LogField(tableName = "b200008", value = "b200008_53010104", valueName = "低值易耗品摊销")
    @ApiModelProperty(value = "低值易耗品摊销")
    private BigDecimal b20000853010104;

    @LogField(tableName = "b200008", value = "b200008_5301010401", valueName = "直接消耗")
    @ApiModelProperty(value = "直接消耗")
    private BigDecimal b2000085301010401;

    @LogField(tableName = "b200008", value = "b200008_5301010402", valueName = "其他消耗")
    @ApiModelProperty(value = "其他消耗")
    private BigDecimal b2000085301010402;

    @LogField(tableName = "b200008", value = "b200008_53010105", valueName = "物料消耗")
    @ApiModelProperty(value = "物料消耗")
    private BigDecimal b20000853010105;

    @LogField(tableName = "b200008", value = "b200008_5301010501", valueName = "直接消耗")
    @ApiModelProperty(value = "直接消耗")
    private BigDecimal b2000085301010501;

    @LogField(tableName = "b200008", value = "b200008_5301010502", valueName = "其他消耗")
    @ApiModelProperty(value = "其他消耗")
    private BigDecimal b2000085301010502;

    @LogField(tableName = "b200008", value = "b200008_53010106", valueName = "折旧费用")
    @ApiModelProperty(value = "折旧费用")
    private BigDecimal b20000853010106;

    @LogField(tableName = "b200008", value = "b200008_5301010601", valueName = "仪器折旧费")
    @ApiModelProperty(value = "仪器折旧费")
    private BigDecimal b2000085301010601;

    @LogField(tableName = "b200008", value = "b200008_5301010602", valueName = "设备折旧费")
    @ApiModelProperty(value = "设备折旧费")
    private BigDecimal b2000085301010602;

    @LogField(tableName = "b200008", value = "b200008_5301010603", valueName = "房屋折旧费")
    @ApiModelProperty(value = "房屋折旧费")
    private BigDecimal b2000085301010603;

    @LogField(tableName = "b200008", value = "b200008_5301010604", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000085301010604;

    @LogField(tableName = "b200008", value = "b200008_53010107", valueName = "长期待摊费用")
    @ApiModelProperty(value = "长期待摊费用")
    private BigDecimal b20000853010107;

    @LogField(tableName = "b200008", value = "b200008_5301010701", valueName = "设施改建及改装费")
    @ApiModelProperty(value = "设施改建及改装费")
    private BigDecimal b2000085301010701;

    @LogField(tableName = "b200008", value = "b200008_5301010702", valueName = "设施装修及修理费")
    @ApiModelProperty(value = "设施装修及修理费")
    private BigDecimal b2000085301010702;

    @LogField(tableName = "b200008", value = "b200008_5301010703", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000085301010703;

    @LogField(tableName = "b200008", value = "b200008_53010108", valueName = "无形资产摊销")
    @ApiModelProperty(value = "无形资产摊销")
    private BigDecimal b20000853010108;

    @LogField(tableName = "b200008", value = "b200008_5301010801", valueName = "软件")
    @ApiModelProperty(value = "软件")
    private BigDecimal b2000085301010801;

    @LogField(tableName = "b200008", value = "b200008_5301010802", valueName = "专利权")
    @ApiModelProperty(value = "专利权")
    private BigDecimal b2000085301010802;

    @LogField(tableName = "b200008", value = "b200008_5301010803", valueName = "商标")
    @ApiModelProperty(value = "商标")
    private BigDecimal b2000085301010803;

    @LogField(tableName = "b200008", value = "b200008_5301010804", valueName = "非专利技术")
    @ApiModelProperty(value = "非专利技术")
    private BigDecimal b2000085301010804;

    @LogField(tableName = "b200008", value = "b200008_5301010805", valueName = "知识产权")
    @ApiModelProperty(value = "知识产权")
    private BigDecimal b2000085301010805;

    @LogField(tableName = "b200008", value = "b200008_5301010806", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000085301010806;

    @LogField(tableName = "b200008", value = "b200008_53010109", valueName = "设计费用")
    @ApiModelProperty(value = "设计费用")
    private BigDecimal b20000853010109;

    @LogField(tableName = "b200008", value = "b200008_5301010901", valueName = "新产品设计费")
    @ApiModelProperty(value = "新产品设计费")
    private BigDecimal b2000085301010901;

    @LogField(tableName = "b200008", value = "b200008_5301010902", valueName = "新工艺规程制定费")
    @ApiModelProperty(value = "新工艺规程制定费")
    private BigDecimal b2000085301010902;

    @LogField(tableName = "b200008", value = "b200008_5301010903", valueName = "新产品与新工艺工序设计费")
    @ApiModelProperty(value = "新产品与新工艺工序设计费")
    private BigDecimal b2000085301010903;

    @LogField(tableName = "b200008", value = "b200008_5301010904", valueName = "新产品与新工艺技术规范费")
    @ApiModelProperty(value = "新产品与新工艺技术规范费")
    private BigDecimal b2000085301010904;

    @LogField(tableName = "b200008", value = "b200008_5301010905", valueName = "新产品与新工艺操作设计费")
    @ApiModelProperty(value = "新产品与新工艺操作设计费")
    private BigDecimal b2000085301010905;

    @LogField(tableName = "b200008", value = "b200008_5301010906", valueName = "创意设计活动费")
    @ApiModelProperty(value = "创意设计活动费")
    private BigDecimal b2000085301010906;

    @LogField(tableName = "b200008", value = "b200008_5301010907", valueName = "其他设计费用")
    @ApiModelProperty(value = "其他设计费用")
    private BigDecimal b2000085301010907;

    @LogField(tableName = "b200008", value = "b200008_53010110", valueName = "装备调试费")
    @ApiModelProperty(value = "装备调试费")
    private BigDecimal b20000853010110;

    @LogField(tableName = "b200008", value = "b200008_53010111", valueName = "试验费")
    @ApiModelProperty(value = "试验费")
    private BigDecimal b20000853010111;

    @LogField(tableName = "b200008", value = "b200008_5301011101", valueName = "新药临床试验费")
    @ApiModelProperty(value = "新药临床试验费")
    private BigDecimal b2000085301011101;

    @LogField(tableName = "b200008", value = "b200008_5301011102", valueName = "勘探开发技术现场试验费")
    @ApiModelProperty(value = "勘探开发技术现场试验费")
    private BigDecimal b2000085301011102;

    @LogField(tableName = "b200008", value = "b200008_5301011103", valueName = "田间试验费")
    @ApiModelProperty(value = "田间试验费")
    private BigDecimal b2000085301011103;

    @LogField(tableName = "b200008", value = "b200008_5301011104", valueName = "其他试验费用")
    @ApiModelProperty(value = "其他试验费用")
    private BigDecimal b2000085301011104;

    @LogField(tableName = "b200008", value = "b200008_53010112", valueName = "图书资料费")
    @ApiModelProperty(value = "图书资料费")
    private BigDecimal b20000853010112;

    @LogField(tableName = "b200008", value = "b200008_53010113", valueName = "资料翻译费")
    @ApiModelProperty(value = "资料翻译费")
    private BigDecimal b20000853010113;

    @LogField(tableName = "b200008", value = "b200008_53010114", valueName = "专家咨询费")
    @ApiModelProperty(value = "专家咨询费")
    private BigDecimal b20000853010114;

    @LogField(tableName = "b200008", value = "b200008_53010115", valueName = "高新科技研发保险费")
    @ApiModelProperty(value = "高新科技研发保险费")
    private BigDecimal b20000853010115;

    @LogField(tableName = "b200008", value = "b200008_53010116", valueName = "成果费用")
    @ApiModelProperty(value = "成果费用")
    private BigDecimal b20000853010116;

    @LogField(tableName = "b200008", value = "b200008_5301011601", valueName = "检索费")
    @ApiModelProperty(value = "检索费")
    private BigDecimal b2000085301011601;

    @LogField(tableName = "b200008", value = "b200008_5301011602", valueName = "分析费")
    @ApiModelProperty(value = "分析费")
    private BigDecimal b2000085301011602;

    @LogField(tableName = "b200008", value = "b200008_5301011603", valueName = "评议费")
    @ApiModelProperty(value = "评议费")
    private BigDecimal b2000085301011603;

    @LogField(tableName = "b200008", value = "b200008_5301011604", valueName = "论证费")
    @ApiModelProperty(value = "论证费")
    private BigDecimal b2000085301011604;

    @LogField(tableName = "b200008", value = "b200008_5301011605", valueName = "鉴定费")
    @ApiModelProperty(value = "鉴定费")
    private BigDecimal b2000085301011605;

    @LogField(tableName = "b200008", value = "b200008_5301011606", valueName = "评审费")
    @ApiModelProperty(value = "评审费")
    private BigDecimal b2000085301011606;

    @LogField(tableName = "b200008", value = "b200008_5301011607", valueName = "评估费")
    @ApiModelProperty(value = "评估费")
    private BigDecimal b2000085301011607;

    @LogField(tableName = "b200008", value = "b200008_5301011608", valueName = "验收费")
    @ApiModelProperty(value = "验收费")
    private BigDecimal b2000085301011608;

    @LogField(tableName = "b200008", value = "b200008_5301011609", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000085301011609;

    @LogField(tableName = "b200008", value = "b200008_53010117", valueName = "知识产权费用")
    @ApiModelProperty(value = "知识产权费用")
    private BigDecimal b20000853010117;

    @LogField(tableName = "b200008", value = "b200008_5301011701", valueName = "申请费")
    @ApiModelProperty(value = "申请费")
    private BigDecimal b2000085301011701;

    @LogField(tableName = "b200008", value = "b200008_5301011702", valueName = "注册费")
    @ApiModelProperty(value = "注册费")
    private BigDecimal b2000085301011702;

    @LogField(tableName = "b200008", value = "b200008_5301011703", valueName = "代理费")
    @ApiModelProperty(value = "代理费")
    private BigDecimal b2000085301011703;

    @LogField(tableName = "b200008", value = "b200008_5301011704", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000085301011704;

    @LogField(tableName = "b200008", value = "b200008_53010118", valueName = "差旅费")
    @ApiModelProperty(value = "差旅费")
    private BigDecimal b20000853010118;

    @LogField(tableName = "b200008", value = "b200008_53010119", valueName = "会议费")
    @ApiModelProperty(value = "会议费")
    private BigDecimal b20000853010119;

    @LogField(tableName = "b200008", value = "b200008_53010120", valueName = "通讯费")
    @ApiModelProperty(value = "通讯费")
    private BigDecimal b20000853010120;

    @LogField(tableName = "b200008", value = "b200008_53010121", valueName = "办公费")
    @ApiModelProperty(value = "办公费")
    private BigDecimal b20000853010121;

    @LogField(tableName = "b200008", value = "b200008_53010122", valueName = "外事费")
    @ApiModelProperty(value = "外事费")
    private BigDecimal b20000853010122;

    @LogField(tableName = "b200008", value = "b200008_53010123", valueName = "研发人员培训费")
    @ApiModelProperty(value = "研发人员培训费")
    private BigDecimal b20000853010123;

    @LogField(tableName = "b200008", value = "b200008_53010124", valueName = "研发人员培养费")
    @ApiModelProperty(value = "研发人员培养费")
    private BigDecimal b20000853010124;

    @LogField(tableName = "b200008", value = "b200008_53010125", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b20000853010125;

    @LogField(tableName = "b200008", value = "b200008_53010126", valueName = "外包费用")
    @ApiModelProperty(value = "外包费用")
    private BigDecimal b20000853010126;

    @LogField(tableName = "b200008", value = "b200008_5301012601", valueName = "委托研发（境内进行）")
    @ApiModelProperty(value = "委托研发（境内进行）")
    private BigDecimal b2000085301012601;

    @LogField(tableName = "b200008", value = "b200008_5301012602", valueName = "委托研发（境外机构）")
    @ApiModelProperty(value = "委托研发（境外机构）")
    private BigDecimal b2000085301012602;

    @LogField(tableName = "b200008", value = "b200008_5301012603", valueName = "委托研发（境外个人）")
    @ApiModelProperty(value = "委托研发（境外个人）")
    private BigDecimal b2000085301012603;

    @LogField(tableName = "b200008", value = "b200008_5301012604", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000085301012604;

    @LogField(tableName = "b200008", value = "b200008_530102", valueName = "费用化支出")
    @ApiModelProperty(value = "费用化支出")
    private BigDecimal b200008530102;

    @LogField(tableName = "b200008", value = "b200008_53010201", valueName = "人工成本")
    @ApiModelProperty(value = "人工成本")
    private BigDecimal b20000853010201;

    @LogField(tableName = "b200008", value = "b200008_5301020101", valueName = "职工工资")
    @ApiModelProperty(value = "职工工资")
    private BigDecimal b2000085301020101;

    @LogField(tableName = "b200008", value = "b200008_5301020102", valueName = "年终奖")
    @ApiModelProperty(value = "年终奖")
    private BigDecimal b2000085301020102;

    @LogField(tableName = "b200008", value = "b200008_5301020103", valueName = "津贴与补贴")
    @ApiModelProperty(value = "津贴与补贴")
    private BigDecimal b2000085301020103;

    @LogField(tableName = "b200008", value = "b200008_5301020104", valueName = "股权激励")
    @ApiModelProperty(value = "股权激励")
    private BigDecimal b2000085301020104;

    @LogField(tableName = "b200008", value = "b200008_5301020105", valueName = "职工教育经费")
    @ApiModelProperty(value = "职工教育经费")
    private BigDecimal b2000085301020105;

    @LogField(tableName = "b200008", value = "b200008_5301020106", valueName = "工会经费")
    @ApiModelProperty(value = "工会经费")
    private BigDecimal b2000085301020106;

    @LogField(tableName = "b200008", value = "b200008_5301020107", valueName = "社会保险费")
    @ApiModelProperty(value = "社会保险费")
    private BigDecimal b2000085301020107;

    @LogField(tableName = "b200008", value = "b200008_5301020108", valueName = "商业保险费")
    @ApiModelProperty(value = "商业保险费")
    private BigDecimal b2000085301020108;

    @LogField(tableName = "b200008", value = "b200008_5301020109", valueName = "住房公积金")
    @ApiModelProperty(value = "住房公积金")
    private BigDecimal b2000085301020109;

    @LogField(tableName = "b200008", value = "b200008_5301020110", valueName = "职工福利费")
    @ApiModelProperty(value = "职工福利费")
    private BigDecimal b2000085301020110;

    @LogField(tableName = "b200008", value = "b200008_5301020111", valueName = "补充养老保险")
    @ApiModelProperty(value = "补充养老保险")
    private BigDecimal b2000085301020111;

    @LogField(tableName = "b200008", value = "b200008_5301020112", valueName = "补充养老保险")
    @ApiModelProperty(value = "补充养老保险")
    private BigDecimal b2000085301020112;

    @LogField(tableName = "b200008", value = "b200008_5301020113", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000085301020113;

    @LogField(tableName = "b200008", value = "b200008_53010202", valueName = "劳务费")
    @ApiModelProperty(value = "劳务费")
    private BigDecimal b20000853010202;

    @LogField(tableName = "b200008", value = "b200008_53010203", valueName = "直接投入")
    @ApiModelProperty(value = "直接投入")
    private BigDecimal b20000853010203;

    @LogField(tableName = "b200008", value = "b200008_5301020301", valueName = "材料费用")
    @ApiModelProperty(value = "材料费用")
    private BigDecimal b2000085301020301;

    @LogField(tableName = "b200008", value = "b200008_5301020302", valueName = "燃料费用")
    @ApiModelProperty(value = "燃料费用")
    private BigDecimal b2000085301020302;

    @LogField(tableName = "b200008", value = "b200008_5301020303", valueName = "动力费用")
    @ApiModelProperty(value = "动力费用")
    private BigDecimal b2000085301020303;

    @LogField(tableName = "b200008", value = "b200008_5301020304", valueName = "试用品开发及制造费")
    @ApiModelProperty(value = "试用品开发及制造费")
    private BigDecimal b2000085301020304;

    @LogField(tableName = "b200008", value = "b200008_5301020305", valueName = "样品测试购置费")
    @ApiModelProperty(value = "样品测试购置费")
    private BigDecimal b2000085301020305;

    @LogField(tableName = "b200008", value = "b200008_5301020306", valueName = "试制品检验费")
    @ApiModelProperty(value = "试制品检验费")
    private BigDecimal b2000085301020306;

    @LogField(tableName = "b200008", value = "b200008_5301020307", valueName = "运行维护维修费")
    @ApiModelProperty(value = "运行维护维修费")
    private BigDecimal b2000085301020307;

    @LogField(tableName = "b200008", value = "b200008_5301020308", valueName = "运行调整费")
    @ApiModelProperty(value = "运行调整费")
    private BigDecimal b2000085301020308;

    @LogField(tableName = "b200008", value = "b200008_5301020309", valueName = "运行检测费")
    @ApiModelProperty(value = "运行检测费")
    private BigDecimal b2000085301020309;

    @LogField(tableName = "b200008", value = "b200008_5301020310", valueName = "仪器租赁费")
    @ApiModelProperty(value = "仪器租赁费")
    private BigDecimal b2000085301020310;

    @LogField(tableName = "b200008", value = "b200008_5301020311", valueName = "设备租赁费")
    @ApiModelProperty(value = "设备租赁费")
    private BigDecimal b2000085301020311;

    @LogField(tableName = "b200008", value = "b200008_5301020312", valueName = "房屋租赁费")
    @ApiModelProperty(value = "房屋租赁费")
    private BigDecimal b2000085301020312;

    @LogField(tableName = "b200008", value = "b200008_5301020313", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000085301020313;

    @LogField(tableName = "b200008", value = "b200008_53010204", valueName = "低值易耗品摊销")
    @ApiModelProperty(value = "低值易耗品摊销")
    private BigDecimal b20000853010204;

    @LogField(tableName = "b200008", value = "b200008_5301020401", valueName = "直接消耗")
    @ApiModelProperty(value = "直接消耗")
    private BigDecimal b2000085301020401;

    @LogField(tableName = "b200008", value = "b200008_5301020402", valueName = "其他消耗")
    @ApiModelProperty(value = "其他消耗")
    private BigDecimal b2000085301020402;

    @LogField(tableName = "b200008", value = "b200008_53010205", valueName = "物料消耗")
    @ApiModelProperty(value = "物料消耗")
    private BigDecimal b20000853010205;

    @LogField(tableName = "b200008", value = "b200008_5301020501", valueName = "直接消耗")
    @ApiModelProperty(value = "直接消耗")
    private BigDecimal b2000085301020501;

    @LogField(tableName = "b200008", value = "b200008_5301020502", valueName = "其他消耗")
    @ApiModelProperty(value = "其他消耗")
    private BigDecimal b2000085301020502;

    @LogField(tableName = "b200008", value = "b200008_53010206", valueName = "折旧费用")
    @ApiModelProperty(value = "折旧费用")
    private BigDecimal b20000853010206;

    @LogField(tableName = "b200008", value = "b200008_5301020601", valueName = "仪器折旧费")
    @ApiModelProperty(value = "仪器折旧费")
    private BigDecimal b2000085301020601;

    @LogField(tableName = "b200008", value = "b200008_5301020602", valueName = "设备折旧费")
    @ApiModelProperty(value = "设备折旧费")
    private BigDecimal b2000085301020602;

    @LogField(tableName = "b200008", value = "b200008_5301020603", valueName = "房屋折旧费")
    @ApiModelProperty(value = "房屋折旧费")
    private BigDecimal b2000085301020603;

    @LogField(tableName = "b200008", value = "b200008_5301020604", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000085301020604;

    @LogField(tableName = "b200008", value = "b200008_53010207", valueName = "长期待摊费用")
    @ApiModelProperty(value = "长期待摊费用")
    private BigDecimal b20000853010207;

    @LogField(tableName = "b200008", value = "b200008_5301020701", valueName = "设施改建及改装费")
    @ApiModelProperty(value = "设施改建及改装费")
    private BigDecimal b2000085301020701;

    @LogField(tableName = "b200008", value = "b200008_5301020702", valueName = "设施装修及修理费")
    @ApiModelProperty(value = "设施装修及修理费")
    private BigDecimal b2000085301020702;

    @LogField(tableName = "b200008", value = "b200008_5301020703", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000085301020703;

    @LogField(tableName = "b200008", value = "b200008_53010208", valueName = "无形资产摊销")
    @ApiModelProperty(value = "无形资产摊销")
    private BigDecimal b20000853010208;

    @LogField(tableName = "b200008", value = "b200008_5301020801", valueName = "软件")
    @ApiModelProperty(value = "软件")
    private BigDecimal b2000085301020801;

    @LogField(tableName = "b200008", value = "b200008_5301020802", valueName = "专利权")
    @ApiModelProperty(value = "专利权")
    private BigDecimal b2000085301020802;

    @LogField(tableName = "b200008", value = "b200008_5301020803", valueName = "商标")
    @ApiModelProperty(value = "商标")
    private BigDecimal b2000085301020803;

    @LogField(tableName = "b200008", value = "b200008_5301020804", valueName = "非专利技术")
    @ApiModelProperty(value = "非专利技术")
    private BigDecimal b2000085301020804;

    @LogField(tableName = "b200008", value = "b200008_5301020805", valueName = "知识产权")
    @ApiModelProperty(value = "知识产权")
    private BigDecimal b2000085301020805;

    @LogField(tableName = "b200008", value = "b200008_5301020806", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000085301020806;

    @LogField(tableName = "b200008", value = "b200008_53010209", valueName = "设计费用")
    @ApiModelProperty(value = "设计费用")
    private BigDecimal b20000853010209;

    @LogField(tableName = "b200008", value = "b200008_5301020901", valueName = "新产品设计费")
    @ApiModelProperty(value = "新产品设计费")
    private BigDecimal b2000085301020901;

    @LogField(tableName = "b200008", value = "b200008_5301020902", valueName = "新工艺规程制定费")
    @ApiModelProperty(value = "新工艺规程制定费")
    private BigDecimal b2000085301020902;

    @LogField(tableName = "b200008", value = "b200008_5301020903", valueName = "新产品与新工艺工序设计费")
    @ApiModelProperty(value = "新产品与新工艺工序设计费")
    private BigDecimal b2000085301020903;

    @LogField(tableName = "b200008", value = "b200008_5301020904", valueName = "新产品与新工艺技术规范费")
    @ApiModelProperty(value = "新产品与新工艺技术规范费")
    private BigDecimal b2000085301020904;

    @LogField(tableName = "b200008", value = "b200008_5301020905", valueName = "新产品与新工艺操作设计费")
    @ApiModelProperty(value = "新产品与新工艺操作设计费")
    private BigDecimal b2000085301020905;

    @LogField(tableName = "b200008", value = "b200008_5301020906", valueName = "创意设计活动费")
    @ApiModelProperty(value = "创意设计活动费")
    private BigDecimal b2000085301020906;

    @LogField(tableName = "b200008", value = "b200008_5301020907", valueName = "其他设计费用")
    @ApiModelProperty(value = "其他设计费用")
    private BigDecimal b2000085301020907;

    @LogField(tableName = "b200008", value = "b200008_53010210", valueName = "装备调试费")
    @ApiModelProperty(value = "装备调试费")
    private BigDecimal b20000853010210;

    @LogField(tableName = "b200008", value = "b200008_53010211", valueName = "试验费")
    @ApiModelProperty(value = "试验费")
    private BigDecimal b20000853010211;

    @LogField(tableName = "b200008", value = "b200008_5301021101", valueName = "新药临床试验费")
    @ApiModelProperty(value = "新药临床试验费")
    private BigDecimal b2000085301021101;

    @LogField(tableName = "b200008", value = "b200008_5301021102", valueName = "勘探开发技术现场试验费")
    @ApiModelProperty(value = "勘探开发技术现场试验费")
    private BigDecimal b2000085301021102;

    @LogField(tableName = "b200008", value = "b200008_5301021103", valueName = "田间试验费")
    @ApiModelProperty(value = "田间试验费")
    private BigDecimal b2000085301021103;

    @LogField(tableName = "b200008", value = "b200008_5301021104", valueName = "其他试验费用")
    @ApiModelProperty(value = "其他试验费用")
    private BigDecimal b2000085301021104;

    @LogField(tableName = "b200008", value = "b200008_53010212", valueName = "图书资料费")
    @ApiModelProperty(value = "图书资料费")
    private BigDecimal b20000853010212;

    @LogField(tableName = "b200008", value = "b200008_53010213", valueName = "资料翻译费")
    @ApiModelProperty(value = "资料翻译费")
    private BigDecimal b20000853010213;

    @LogField(tableName = "b200008", value = "b200008_53010214", valueName = "专家咨询费")
    @ApiModelProperty(value = "专家咨询费")
    private BigDecimal b20000853010214;

    @LogField(tableName = "b200008", value = "b200008_53010215", valueName = "高新科技研发保险费")
    @ApiModelProperty(value = "高新科技研发保险费")
    private BigDecimal b20000853010215;

    @LogField(tableName = "b200008", value = "b200008_53010216", valueName = "成果费用")
    @ApiModelProperty(value = "成果费用")
    private BigDecimal b20000853010216;

    @LogField(tableName = "b200008", value = "b200008_5301021601", valueName = "检索费")
    @ApiModelProperty(value = "检索费")
    private BigDecimal b2000085301021601;

    @LogField(tableName = "b200008", value = "b200008_5301021602", valueName = "分析费")
    @ApiModelProperty(value = "分析费")
    private BigDecimal b2000085301021602;

    @LogField(tableName = "b200008", value = "b200008_5301021603", valueName = "评议费")
    @ApiModelProperty(value = "评议费")
    private BigDecimal b2000085301021603;

    @LogField(tableName = "b200008", value = "b200008_5301021604", valueName = "论证费")
    @ApiModelProperty(value = "论证费")
    private BigDecimal b2000085301021604;

    @LogField(tableName = "b200008", value = "b200008_5301021605", valueName = "鉴定费")
    @ApiModelProperty(value = "鉴定费")
    private BigDecimal b2000085301021605;

    @LogField(tableName = "b200008", value = "b200008_5301021606", valueName = "评审费")
    @ApiModelProperty(value = "评审费")
    private BigDecimal b2000085301021606;

    @LogField(tableName = "b200008", value = "b200008_5301021607", valueName = "评估费")
    @ApiModelProperty(value = "评估费")
    private BigDecimal b2000085301021607;

    @LogField(tableName = "b200008", value = "b200008_5301021608", valueName = "验收费")
    @ApiModelProperty(value = "验收费")
    private BigDecimal b2000085301021608;

    @LogField(tableName = "b200008", value = "b200008_5301021609", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000085301021609;

    @LogField(tableName = "b200008", value = "b200008_53010217", valueName = "知识产权费用")
    @ApiModelProperty(value = "知识产权费用")
    private BigDecimal b20000853010217;

    @LogField(tableName = "b200008", value = "b200008_5301021701", valueName = "申请费")
    @ApiModelProperty(value = "申请费")
    private BigDecimal b2000085301021701;

    @LogField(tableName = "b200008", value = "b200008_5301021702", valueName = "注册费")
    @ApiModelProperty(value = "注册费")
    private BigDecimal b2000085301021702;

    @LogField(tableName = "b200008", value = "b200008_5301021703", valueName = "代理费")
    @ApiModelProperty(value = "代理费")
    private BigDecimal b2000085301021703;

    @LogField(tableName = "b200008", value = "b200008_5301021704", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000085301021704;

    @LogField(tableName = "b200008", value = "b200008_53010218", valueName = "差旅费")
    @ApiModelProperty(value = "差旅费")
    private BigDecimal b20000853010218;

    @LogField(tableName = "b200008", value = "b200008_53010219", valueName = "会议费")
    @ApiModelProperty(value = "会议费")
    private BigDecimal b20000853010219;

    @LogField(tableName = "b200008", value = "b200008_53010220", valueName = "通讯费")
    @ApiModelProperty(value = "通讯费")
    private BigDecimal b20000853010220;

    @LogField(tableName = "b200008", value = "b200008_53010221", valueName = "办公费")
    @ApiModelProperty(value = "办公费")
    private BigDecimal b20000853010221;

    @LogField(tableName = "b200008", value = "b200008_53010222", valueName = "外事费")
    @ApiModelProperty(value = "外事费")
    private BigDecimal b20000853010222;

    @LogField(tableName = "b200008", value = "b200008_53010223", valueName = "研发人员培训费")
    @ApiModelProperty(value = "研发人员培训费")
    private BigDecimal b20000853010223;

    @LogField(tableName = "b200008", value = "b200008_53010224", valueName = "研发人员培养费")
    @ApiModelProperty(value = "研发人员培养费")
    private BigDecimal b20000853010224;

    @LogField(tableName = "b200008", value = "b200008_53010225", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b20000853010225;

    @LogField(tableName = "b200008", value = "b200008_53010226", valueName = "外包费用")
    @ApiModelProperty(value = "外包费用")
    private BigDecimal b20000853010226;

    @LogField(tableName = "b200008", value = "b200008_5301022601", valueName = "委托研发（境内进行）")
    @ApiModelProperty(value = "委托研发（境内进行）")
    private BigDecimal b2000085301022601;

    @LogField(tableName = "b200008", value = "b200008_5301022602", valueName = "委托研发（境外机构）")
    @ApiModelProperty(value = "委托研发（境外机构）")
    private BigDecimal b2000085301022602;

    @LogField(tableName = "b200008", value = "b200008_5301022603", valueName = "委托研发（境外个人）")
    @ApiModelProperty(value = "委托研发（境外个人）")
    private BigDecimal b2000085301022603;

    @LogField(tableName = "b200008", value = "b200008_5301022604", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000085301022604;

    @LogField(tableName = "b200008", value = "b200008_5401", valueName = "工程施工")
    @ApiModelProperty(value = "工程施工")
    private BigDecimal b2000085401;

    @LogField(tableName = "b200008", value = "b200008_5402", valueName = "工程结算")
    @ApiModelProperty(value = "工程结算")
    private BigDecimal b2000085402;

    @LogField(tableName = "b200008", value = "b200008_5403", valueName = "机械作业")
    @ApiModelProperty(value = "机械作业")
    private BigDecimal b2000085403;

    @LogField(tableName = "b200008", value = "b200008_5501", valueName = "合同履约成本")
    @ApiModelProperty(value = "合同履约成本")
    private BigDecimal b2000085501;

    @LogField(tableName = "b200008", value = "b200008_550101", valueName = "职工工资")
    @ApiModelProperty(value = "职工工资")
    private BigDecimal b200008550101;

    @LogField(tableName = "b200008", value = "b200008_550102", valueName = "年终奖")
    @ApiModelProperty(value = "年终奖")
    private BigDecimal b200008550102;

    @LogField(tableName = "b200008", value = "b200008_550103", valueName = "津贴与补贴")
    @ApiModelProperty(value = "津贴与补贴")
    private BigDecimal b200008550103;

    @LogField(tableName = "b200008", value = "b200008_550104", valueName = "股权激励")
    @ApiModelProperty(value = "股权激励")
    private BigDecimal b200008550104;

    @LogField(tableName = "b200008", value = "b200008_550105", valueName = "职工教育经费")
    @ApiModelProperty(value = "职工教育经费")
    private BigDecimal b200008550105;

    @LogField(tableName = "b200008", value = "b200008_550106", valueName = "职工培训费用")
    @ApiModelProperty(value = "职工培训费用")
    private BigDecimal b200008550106;

    @LogField(tableName = "b200008", value = "b200008_550107", valueName = "工会经费")
    @ApiModelProperty(value = "工会经费")
    private BigDecimal b200008550107;

    @LogField(tableName = "b200008", value = "b200008_550108", valueName = "社会保险费")
    @ApiModelProperty(value = "社会保险费")
    private BigDecimal b200008550108;

    @LogField(tableName = "b200008", value = "b200008_550109", valueName = "住房公积金")
    @ApiModelProperty(value = "住房公积金")
    private BigDecimal b200008550109;

    @LogField(tableName = "b200008", value = "b200008_550110", valueName = "职工福利费")
    @ApiModelProperty(value = "职工福利费")
    private BigDecimal b200008550110;

    @LogField(tableName = "b200008", value = "b200008_550111", valueName = "补充养老保险")
    @ApiModelProperty(value = "补充养老保险")
    private BigDecimal b200008550111;

    @LogField(tableName = "b200008", value = "b200008_550112", valueName = "补充医疗保险")
    @ApiModelProperty(value = "补充医疗保险")
    private BigDecimal b200008550112;

    @LogField(tableName = "b200008", value = "b200008_550113", valueName = "其他职工工资")
    @ApiModelProperty(value = "其他职工工资")
    private BigDecimal b200008550113;

    @LogField(tableName = "b200008", value = "b200008_550114", valueName = "商业保险费")
    @ApiModelProperty(value = "商业保险费")
    private BigDecimal b200008550114;

    @LogField(tableName = "b200008", value = "b200008_550115", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200008550115;

    @LogField(tableName = "b200008", value = "b200008_5502", valueName = "合同履约成本减值准备")
    @ApiModelProperty(value = "合同履约成本减值准备")
    private BigDecimal b2000085502;

    @LogField(tableName = "b200008", value = "b200008_5503", valueName = "合同取得成本")
    @ApiModelProperty(value = "合同取得成本")
    private BigDecimal b2000085503;

    @LogField(tableName = "b200008", value = "b200008_5504", valueName = "合同取得成本减值准备")
    @ApiModelProperty(value = "合同取得成本减值准备")
    private BigDecimal b2000085504;

    @LogField(tableName = "b200008", value = "b200008_6001", valueName = "主营业务收入")
    @ApiModelProperty(value = "主营业务收入")
    private BigDecimal b2000086001;

    @LogField(tableName = "b200008", value = "b200008_600101", valueName = "销售商品收入")
    @ApiModelProperty(value = "销售商品收入")
    private BigDecimal b200008600101;

    @LogField(tableName = "b200008", value = "b200008_600102", valueName = "提供劳务收入")
    @ApiModelProperty(value = "提供劳务收入")
    private BigDecimal b200008600102;

    @LogField(tableName = "b200008", value = "b200008_600103", valueName = "建造合同收入")
    @ApiModelProperty(value = "建造合同收入")
    private BigDecimal b200008600103;

    @LogField(tableName = "b200008", value = "b200008_600104", valueName = "让渡资产使用权收入")
    @ApiModelProperty(value = "让渡资产使用权收入")
    private BigDecimal b200008600104;

    @LogField(tableName = "b200008", value = "b200008_600105", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200008600105;

    @LogField(tableName = "b200008", value = "b200008_6011", valueName = "利息收入")
    @ApiModelProperty(value = "利息收入")
    private BigDecimal b2000086011;

    @LogField(tableName = "b200008", value = "b200008_6021", valueName = "手续费及佣金收入")
    @ApiModelProperty(value = "手续费及佣金收入")
    private BigDecimal b2000086021;

    @LogField(tableName = "b200008", value = "b200008_6031", valueName = "保费收入")
    @ApiModelProperty(value = "保费收入")
    private BigDecimal b2000086031;

    @LogField(tableName = "b200008", value = "b200008_6041", valueName = "租赁收入")
    @ApiModelProperty(value = "租赁收入")
    private BigDecimal b2000086041;

    @LogField(tableName = "b200008", value = "b200008_6051", valueName = "其他业务收入")
    @ApiModelProperty(value = "其他业务收入")
    private BigDecimal b2000086051;

    @LogField(tableName = "b200008", value = "b200008_605101", valueName = "销售材料收入")
    @ApiModelProperty(value = "销售材料收入")
    private BigDecimal b200008605101;

    @LogField(tableName = "b200008", value = "b200008_605102", valueName = "出租固定资产收入")
    @ApiModelProperty(value = "出租固定资产收入")
    private BigDecimal b200008605102;

    @LogField(tableName = "b200008", value = "b200008_605103", valueName = "出租无形资产收入")
    @ApiModelProperty(value = "出租无形资产收入")
    private BigDecimal b200008605103;

    @LogField(tableName = "b200008", value = "b200008_605104", valueName = "出租包装物和商品收入")
    @ApiModelProperty(value = "出租包装物和商品收入")
    private BigDecimal b200008605104;

    @LogField(tableName = "b200008", value = "b200008_605105", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200008605105;

    @LogField(tableName = "b200008", value = "b200008_6061", valueName = "汇兑损益")
    @ApiModelProperty(value = "汇兑损益")
    private BigDecimal b2000086061;

    @LogField(tableName = "b200008", value = "b200008_6101", valueName = "公允价值变动损益")
    @ApiModelProperty(value = "公允价值变动损益")
    private BigDecimal b2000086101;

    @LogField(tableName = "b200008", value = "b200008_6111", valueName = "投资收益")
    @ApiModelProperty(value = "投资收益")
    private BigDecimal b2000086111;

    @LogField(tableName = "b200008", value = "b200008_611101", valueName = "交易性金融资产")
    @ApiModelProperty(value = "交易性金融资产")
    private BigDecimal b200008611101;

    @LogField(tableName = "b200008", value = "b200008_61110101", valueName = "国债")
    @ApiModelProperty(value = "国债")
    private BigDecimal b20000861110101;

    @LogField(tableName = "b200008", value = "b200008_6111010101", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000086111010101;

    @LogField(tableName = "b200008", value = "b200008_6111010102", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000086111010102;

    @LogField(tableName = "b200008", value = "b200008_61110102", valueName = "证券投资基金")
    @ApiModelProperty(value = "证券投资基金")
    private BigDecimal b20000861110102;

    @LogField(tableName = "b200008", value = "b200008_6111010201", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000086111010201;

    @LogField(tableName = "b200008", value = "b200008_6111010202", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000086111010202;

    @LogField(tableName = "b200008", value = "b200008_61110103", valueName = "地方政府债券")
    @ApiModelProperty(value = "地方政府债券")
    private BigDecimal b20000861110103;

    @LogField(tableName = "b200008", value = "b200008_6111010301", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000086111010301;

    @LogField(tableName = "b200008", value = "b200008_6111010302", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000086111010302;

    @LogField(tableName = "b200008", value = "b200008_61110104", valueName = "铁路债券")
    @ApiModelProperty(value = "铁路债券")
    private BigDecimal b20000861110104;

    @LogField(tableName = "b200008", value = "b200008_6111010401", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000086111010401;

    @LogField(tableName = "b200008", value = "b200008_6111010402", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000086111010402;

    @LogField(tableName = "b200008", value = "b200008_61110105", valueName = "连续持有公开发行并上市流通12个月及以上的股票")
    @ApiModelProperty(value = "连续持有公开发行并上市流通12个月及以上的股票")
    private BigDecimal b20000861110105;

    @LogField(tableName = "b200008", value = "b200008_6111010501", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000086111010501;

    @LogField(tableName = "b200008", value = "b200008_6111010502", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000086111010502;

    @LogField(tableName = "b200008", value = "b200008_61110106", valueName = "永续债")
    @ApiModelProperty(value = "永续债")
    private BigDecimal b20000861110106;

    @LogField(tableName = "b200008", value = "b200008_6111010601", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000086111010601;

    @LogField(tableName = "b200008", value = "b200008_6111010602", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000086111010602;

    @LogField(tableName = "b200008", value = "b200008_61110107", valueName = "创新企业CDR")
    @ApiModelProperty(value = "创新企业CDR")
    private BigDecimal b20000861110107;

    @LogField(tableName = "b200008", value = "b200008_6111010701", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000086111010701;

    @LogField(tableName = "b200008", value = "b200008_6111010702", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000086111010702;

    @LogField(tableName = "b200008", value = "b200008_61110108", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b20000861110108;

    @LogField(tableName = "b200008", value = "b200008_6111010801", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000086111010801;

    @LogField(tableName = "b200008", value = "b200008_6111010802", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000086111010802;

    @LogField(tableName = "b200008", value = "b200008_611102", valueName = "可供出售金融资产")
    @ApiModelProperty(value = "可供出售金融资产")
    private BigDecimal b200008611102;

    @LogField(tableName = "b200008", value = "b200008_61110201", valueName = "国债")
    @ApiModelProperty(value = "国债")
    private BigDecimal b20000861110201;

    @LogField(tableName = "b200008", value = "b200008_6111020101", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000086111020101;

    @LogField(tableName = "b200008", value = "b200008_6111020102", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000086111020102;

    @LogField(tableName = "b200008", value = "b200008_61110202", valueName = "证券投资基金")
    @ApiModelProperty(value = "证券投资基金")
    private BigDecimal b20000861110202;

    @LogField(tableName = "b200008", value = "b200008_6111020201", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000086111020201;

    @LogField(tableName = "b200008", value = "b200008_6111020202", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000086111020202;

    @LogField(tableName = "b200008", value = "b200008_61110203", valueName = "地方政府债券")
    @ApiModelProperty(value = "地方政府债券")
    private BigDecimal b20000861110203;

    @LogField(tableName = "b200008", value = "b200008_6111020301", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000086111020301;

    @LogField(tableName = "b200008", value = "b200008_6111020302", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000086111020302;

    @LogField(tableName = "b200008", value = "b200008_61110204", valueName = "铁路债券")
    @ApiModelProperty(value = "铁路债券")
    private BigDecimal b20000861110204;

    @LogField(tableName = "b200008", value = "b200008_6111020401", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000086111020401;

    @LogField(tableName = "b200008", value = "b200008_6111020402", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000086111020402;

    @LogField(tableName = "b200008", value = "b200008_61110205", valueName = "连续持有公开发行并上市流通12个月及以上的股票")
    @ApiModelProperty(value = "连续持有公开发行并上市流通12个月及以上的股票")
    private BigDecimal b20000861110205;

    @LogField(tableName = "b200008", value = "b200008_6111020501", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000086111020501;

    @LogField(tableName = "b200008", value = "b200008_6111020502", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000086111020502;

    @LogField(tableName = "b200008", value = "b200008_61110206", valueName = "永续债")
    @ApiModelProperty(value = "永续债")
    private BigDecimal b20000861110206;

    @LogField(tableName = "b200008", value = "b200008_6111020601", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000086111020601;

    @LogField(tableName = "b200008", value = "b200008_6111020602", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000086111020602;

    @LogField(tableName = "b200008", value = "b200008_61110207", valueName = "创新企业CDR")
    @ApiModelProperty(value = "创新企业CDR")
    private BigDecimal b20000861110207;

    @LogField(tableName = "b200008", value = "b200008_6111020701", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000086111020701;

    @LogField(tableName = "b200008", value = "b200008_6111020702", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000086111020702;

    @LogField(tableName = "b200008", value = "b200008_61110208", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b20000861110208;

    @LogField(tableName = "b200008", value = "b200008_6111020801", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000086111020801;

    @LogField(tableName = "b200008", value = "b200008_6111020802", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000086111020802;

    @LogField(tableName = "b200008", value = "b200008_611103", valueName = "持有至到期投资")
    @ApiModelProperty(value = "持有至到期投资")
    private BigDecimal b200008611103;

    @LogField(tableName = "b200008", value = "b200008_61110301", valueName = "国债")
    @ApiModelProperty(value = "国债")
    private BigDecimal b20000861110301;

    @LogField(tableName = "b200008", value = "b200008_6111030101", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000086111030101;

    @LogField(tableName = "b200008", value = "b200008_6111030102", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000086111030102;

    @LogField(tableName = "b200008", value = "b200008_61110302", valueName = "地方政府债券")
    @ApiModelProperty(value = "地方政府债券")
    private BigDecimal b20000861110302;

    @LogField(tableName = "b200008", value = "b200008_6111030201", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000086111030201;

    @LogField(tableName = "b200008", value = "b200008_6111030202", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000086111030202;

    @LogField(tableName = "b200008", value = "b200008_61110303", valueName = "铁路债券")
    @ApiModelProperty(value = "铁路债券")
    private BigDecimal b20000861110303;

    @LogField(tableName = "b200008", value = "b200008_6111030301", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000086111030301;

    @LogField(tableName = "b200008", value = "b200008_6111030302", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000086111030302;

    @LogField(tableName = "b200008", value = "b200008_61110304", valueName = "永续债")
    @ApiModelProperty(value = "永续债")
    private BigDecimal b20000861110304;

    @LogField(tableName = "b200008", value = "b200008_6111030401", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000086111030401;

    @LogField(tableName = "b200008", value = "b200008_6111030402", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000086111030402;

    @LogField(tableName = "b200008", value = "b200008_61110305", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b20000861110305;

    @LogField(tableName = "b200008", value = "b200008_6111030501", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000086111030501;

    @LogField(tableName = "b200008", value = "b200008_6111030502", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000086111030502;

    @LogField(tableName = "b200008", value = "b200008_611104", valueName = "衍生工具")
    @ApiModelProperty(value = "衍生工具")
    private BigDecimal b200008611104;

    @LogField(tableName = "b200008", value = "b200008_61110401", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b20000861110401;

    @LogField(tableName = "b200008", value = "b200008_61110402", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b20000861110402;

    @LogField(tableName = "b200008", value = "b200008_611105", valueName = "交易性金融负债")
    @ApiModelProperty(value = "交易性金融负债")
    private BigDecimal b200008611105;

    @LogField(tableName = "b200008", value = "b200008_61110501", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b20000861110501;

    @LogField(tableName = "b200008", value = "b200008_61110502", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b20000861110502;

    @LogField(tableName = "b200008", value = "b200008_611106", valueName = "长期股权投资")
    @ApiModelProperty(value = "长期股权投资")
    private BigDecimal b200008611106;

    @LogField(tableName = "b200008", value = "b200008_61110601", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b20000861110601;

    @LogField(tableName = "b200008", value = "b200008_61110602", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b20000861110602;

    @LogField(tableName = "b200008", value = "b200008_611107", valueName = "长期债券投资")
    @ApiModelProperty(value = "长期债券投资")
    private BigDecimal b200008611107;

    @LogField(tableName = "b200008", value = "b200008_61110701", valueName = "国债")
    @ApiModelProperty(value = "国债")
    private BigDecimal b20000861110701;

    @LogField(tableName = "b200008", value = "b200008_6111070101", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000086111070101;

    @LogField(tableName = "b200008", value = "b200008_6111070102", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000086111070102;

    @LogField(tableName = "b200008", value = "b200008_61110702", valueName = "地方政府债券")
    @ApiModelProperty(value = "地方政府债券")
    private BigDecimal b20000861110702;

    @LogField(tableName = "b200008", value = "b200008_6111070201", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000086111070201;

    @LogField(tableName = "b200008", value = "b200008_6111070202", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000086111070202;

    @LogField(tableName = "b200008", value = "b200008_61110703", valueName = "铁路债券")
    @ApiModelProperty(value = "铁路债券")
    private BigDecimal b20000861110703;

    @LogField(tableName = "b200008", value = "b200008_6111070301", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000086111070301;

    @LogField(tableName = "b200008", value = "b200008_6111070302", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000086111070302;

    @LogField(tableName = "b200008", value = "b200008_61110704", valueName = "永续债")
    @ApiModelProperty(value = "永续债")
    private BigDecimal b20000861110704;

    @LogField(tableName = "b200008", value = "b200008_6111070401", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000086111070401;

    @LogField(tableName = "b200008", value = "b200008_6111070402", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000086111070402;

    @LogField(tableName = "b200008", value = "b200008_61110705", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b20000861110705;

    @LogField(tableName = "b200008", value = "b200008_6111070501", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000086111070501;

    @LogField(tableName = "b200008", value = "b200008_6111070502", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000086111070502;

    @LogField(tableName = "b200008", value = "b200008_611108", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200008611108;

    @LogField(tableName = "b200008", value = "b200008_61110801", valueName = "国债")
    @ApiModelProperty(value = "国债")
    private BigDecimal b20000861110801;

    @LogField(tableName = "b200008", value = "b200008_6111080101", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000086111080101;

    @LogField(tableName = "b200008", value = "b200008_6111080102", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000086111080102;

    @LogField(tableName = "b200008", value = "b200008_61110802", valueName = "证券投资基金")
    @ApiModelProperty(value = "证券投资基金")
    private BigDecimal b20000861110802;

    @LogField(tableName = "b200008", value = "b200008_6111080201", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000086111080201;

    @LogField(tableName = "b200008", value = "b200008_6111080202", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000086111080202;

    @LogField(tableName = "b200008", value = "b200008_61110803", valueName = "地方政府债券")
    @ApiModelProperty(value = "地方政府债券")
    private BigDecimal b20000861110803;

    @LogField(tableName = "b200008", value = "b200008_6111080301", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000086111080301;

    @LogField(tableName = "b200008", value = "b200008_6111080302", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000086111080302;

    @LogField(tableName = "b200008", value = "b200008_61110804", valueName = "铁路债券")
    @ApiModelProperty(value = "铁路债券")
    private BigDecimal b20000861110804;

    @LogField(tableName = "b200008", value = "b200008_6111080401", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000086111080401;

    @LogField(tableName = "b200008", value = "b200008_6111080402", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000086111080402;

    @LogField(tableName = "b200008", value = "b200008_61110805", valueName = "连续持有公开发行并上市流通12个月及以上的股票")
    @ApiModelProperty(value = "连续持有公开发行并上市流通12个月及以上的股票")
    private BigDecimal b20000861110805;

    @LogField(tableName = "b200008", value = "b200008_6111080501", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000086111080501;

    @LogField(tableName = "b200008", value = "b200008_6111080502", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000086111080502;

    @LogField(tableName = "b200008", value = "b200008_61110806", valueName = "永续债")
    @ApiModelProperty(value = "永续债")
    private BigDecimal b20000861110806;

    @LogField(tableName = "b200008", value = "b200008_6111080601", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000086111080601;

    @LogField(tableName = "b200008", value = "b200008_6111080602", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000086111080602;

    @LogField(tableName = "b200008", value = "b200008_61110807", valueName = "创新企业CDR")
    @ApiModelProperty(value = "创新企业CDR")
    private BigDecimal b20000861110807;

    @LogField(tableName = "b200008", value = "b200008_6111080701", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000086111080701;

    @LogField(tableName = "b200008", value = "b200008_6111080702", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000086111080702;

    @LogField(tableName = "b200008", value = "b200008_61110808", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b20000861110808;

    @LogField(tableName = "b200008", value = "b200008_6111080801", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000086111080801;

    @LogField(tableName = "b200008", value = "b200008_6111080802", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000086111080802;

    @LogField(tableName = "b200008", value = "b200008_6115", valueName = "资产处置损益")
    @ApiModelProperty(value = "资产处置损益")
    private BigDecimal b2000086115;

    @LogField(tableName = "b200008", value = "b200008_611501", valueName = "固定资产损失")
    @ApiModelProperty(value = "固定资产损失")
    private BigDecimal b200008611501;

    @LogField(tableName = "b200008", value = "b200008_611502", valueName = "无形资产损失")
    @ApiModelProperty(value = "无形资产损失")
    private BigDecimal b200008611502;

    @LogField(tableName = "b200008", value = "b200008_611503", valueName = "在建工程损失")
    @ApiModelProperty(value = "在建工程损失")
    private BigDecimal b200008611503;

    @LogField(tableName = "b200008", value = "b200008_611504", valueName = "生产性生物资产损失")
    @ApiModelProperty(value = "生产性生物资产损失")
    private BigDecimal b200008611504;

    @LogField(tableName = "b200008", value = "b200008_611505", valueName = "打包出售资产损失")
    @ApiModelProperty(value = "打包出售资产损失")
    private BigDecimal b200008611505;

    @LogField(tableName = "b200008", value = "b200008_611506", valueName = "其他资产损失")
    @ApiModelProperty(value = "其他资产损失")
    private BigDecimal b200008611506;

    @LogField(tableName = "b200008", value = "b200008_611507", valueName = "资产处置收益")
    @ApiModelProperty(value = "资产处置收益")
    private BigDecimal b200008611507;

    @LogField(tableName = "b200008", value = "b200008_6117", valueName = "其他收益")
    @ApiModelProperty(value = "其他收益")
    private BigDecimal b2000086117;

    @LogField(tableName = "b200008", value = "b200008_6201", valueName = "摊回保险责任准备金")
    @ApiModelProperty(value = "摊回保险责任准备金")
    private BigDecimal b2000086201;

    @LogField(tableName = "b200008", value = "b200008_6202", valueName = "摊回赔付支出")
    @ApiModelProperty(value = "摊回赔付支出")
    private BigDecimal b2000086202;

    @LogField(tableName = "b200008", value = "b200008_6203", valueName = "摊回分保费用")
    @ApiModelProperty(value = "摊回分保费用")
    private BigDecimal b2000086203;

    @LogField(tableName = "b200008", value = "b200008_6301", valueName = "营业外收入")
    @ApiModelProperty(value = "营业外收入")
    private BigDecimal b2000086301;

    @LogField(tableName = "b200008", value = "b200008_630101", valueName = "非流动资产处置利得")
    @ApiModelProperty(value = "非流动资产处置利得")
    private BigDecimal b200008630101;

    @LogField(tableName = "b200008", value = "b200008_630102", valueName = "非货币性资产交换利得")
    @ApiModelProperty(value = "非货币性资产交换利得")
    private BigDecimal b200008630102;

    @LogField(tableName = "b200008", value = "b200008_630103", valueName = "债务重组利得")
    @ApiModelProperty(value = "债务重组利得")
    private BigDecimal b200008630103;

    @LogField(tableName = "b200008", value = "b200008_630104", valueName = "政府补助利得")
    @ApiModelProperty(value = "政府补助利得")
    private BigDecimal b200008630104;

    @LogField(tableName = "b200008", value = "b200008_630105", valueName = "盘盈利得")
    @ApiModelProperty(value = "盘盈利得")
    private BigDecimal b200008630105;

    @LogField(tableName = "b200008", value = "b200008_630106", valueName = "捐赠利得")
    @ApiModelProperty(value = "捐赠利得")
    private BigDecimal b200008630106;

    @LogField(tableName = "b200008", value = "b200008_630107", valueName = "罚没利得")
    @ApiModelProperty(value = "罚没利得")
    private BigDecimal b200008630107;

    @LogField(tableName = "b200008", value = "b200008_630108", valueName = "确实无法偿付的应付款项")
    @ApiModelProperty(value = "确实无法偿付的应付款项")
    private BigDecimal b200008630108;

    @LogField(tableName = "b200008", value = "b200008_630109", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200008630109;

    @LogField(tableName = "b200008", value = "b200008_6401", valueName = "主营业务成本")
    @ApiModelProperty(value = "主营业务成本")
    private BigDecimal b2000086401;

    @LogField(tableName = "b200008", value = "b200008_640101", valueName = "销售商品成本")
    @ApiModelProperty(value = "销售商品成本")
    private BigDecimal b200008640101;

    @LogField(tableName = "b200008", value = "b200008_640102", valueName = "提供劳务成本")
    @ApiModelProperty(value = "提供劳务成本")
    private BigDecimal b200008640102;

    @LogField(tableName = "b200008", value = "b200008_640103", valueName = "职工工资")
    @ApiModelProperty(value = "职工工资")
    private BigDecimal b200008640103;

    @LogField(tableName = "b200008", value = "b200008_640104", valueName = "年终奖")
    @ApiModelProperty(value = "年终奖")
    private BigDecimal b200008640104;

    @LogField(tableName = "b200008", value = "b200008_640105", valueName = "津贴与补贴")
    @ApiModelProperty(value = "津贴与补贴")
    private BigDecimal b200008640105;

    @LogField(tableName = "b200008", value = "b200008_640106", valueName = "股权激励")
    @ApiModelProperty(value = "股权激励")
    private BigDecimal b200008640106;

    @LogField(tableName = "b200008", value = "b200008_640107", valueName = "社会保险费")
    @ApiModelProperty(value = "社会保险费")
    private BigDecimal b200008640107;

    @LogField(tableName = "b200008", value = "b200008_640108", valueName = "住房公积金")
    @ApiModelProperty(value = "住房公积金")
    private BigDecimal b200008640108;

    @LogField(tableName = "b200008", value = "b200008_640109", valueName = "补充养老保险")
    @ApiModelProperty(value = "补充养老保险")
    private BigDecimal b200008640109;

    @LogField(tableName = "b200008", value = "b200008_640110", valueName = "补充医疗保险")
    @ApiModelProperty(value = "补充医疗保险")
    private BigDecimal b200008640110;

    @LogField(tableName = "b200008", value = "b200008_640111", valueName = "职工福利费")
    @ApiModelProperty(value = "职工福利费")
    private BigDecimal b200008640111;

    @LogField(tableName = "b200008", value = "b200008_640112", valueName = "职工教育经费")
    @ApiModelProperty(value = "职工教育经费")
    private BigDecimal b200008640112;

    @LogField(tableName = "b200008", value = "b200008_640113", valueName = "职工培训费用")
    @ApiModelProperty(value = "职工培训费用")
    private BigDecimal b200008640113;

    @LogField(tableName = "b200008", value = "b200008_640114", valueName = "工会经费")
    @ApiModelProperty(value = "工会经费")
    private BigDecimal b200008640114;

    @LogField(tableName = "b200008", value = "b200008_640115", valueName = "其他职工工资")
    @ApiModelProperty(value = "其他职工工资")
    private BigDecimal b200008640115;

    @LogField(tableName = "b200008", value = "b200008_640116", valueName = "商业保险费")
    @ApiModelProperty(value = "商业保险费")
    private BigDecimal b200008640116;

    @LogField(tableName = "b200008", value = "b200008_640117", valueName = "建造合同成本")
    @ApiModelProperty(value = "建造合同成本")
    private BigDecimal b200008640117;

    @LogField(tableName = "b200008", value = "b200008_640118", valueName = "让渡资产使用权成本")
    @ApiModelProperty(value = "让渡资产使用权成本")
    private BigDecimal b200008640118;

    @LogField(tableName = "b200008", value = "b200008_640119", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200008640119;

    @LogField(tableName = "b200008", value = "b200008_6402", valueName = "其他业务成本")
    @ApiModelProperty(value = "其他业务成本")
    private BigDecimal b2000086402;

    @LogField(tableName = "b200008", value = "b200008_640201", valueName = "销售材料成本")
    @ApiModelProperty(value = "销售材料成本")
    private BigDecimal b200008640201;

    @LogField(tableName = "b200008", value = "b200008_640202", valueName = "出租固定资产成本")
    @ApiModelProperty(value = "出租固定资产成本")
    private BigDecimal b200008640202;

    @LogField(tableName = "b200008", value = "b200008_640203", valueName = "出租无形资产成本")
    @ApiModelProperty(value = "出租无形资产成本")
    private BigDecimal b200008640203;

    @LogField(tableName = "b200008", value = "b200008_640204", valueName = "出租包装物和商品成本")
    @ApiModelProperty(value = "出租包装物和商品成本")
    private BigDecimal b200008640204;

    @LogField(tableName = "b200008", value = "b200008_640205", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200008640205;

    @LogField(tableName = "b200008", value = "b200008_6403", valueName = "税金及附加")
    @ApiModelProperty(value = "税金及附加")
    private BigDecimal b2000086403;

    @LogField(tableName = "b200008", value = "b200008_6411", valueName = "利息支出")
    @ApiModelProperty(value = "利息支出")
    private BigDecimal b2000086411;

    @LogField(tableName = "b200008", value = "b200008_6421", valueName = "手续费及佣金支出")
    @ApiModelProperty(value = "手续费及佣金支出")
    private BigDecimal b2000086421;

    @LogField(tableName = "b200008", value = "b200008_6501", valueName = "提取未到期责任准备金")
    @ApiModelProperty(value = "提取未到期责任准备金")
    private BigDecimal b2000086501;

    @LogField(tableName = "b200008", value = "b200008_6502", valueName = "提取保险责任准备金")
    @ApiModelProperty(value = "提取保险责任准备金")
    private BigDecimal b2000086502;

    @LogField(tableName = "b200008", value = "b200008_6511", valueName = "赔付支出")
    @ApiModelProperty(value = "赔付支出")
    private BigDecimal b2000086511;

    @LogField(tableName = "b200008", value = "b200008_6521", valueName = "保单红利支出")
    @ApiModelProperty(value = "保单红利支出")
    private BigDecimal b2000086521;

    @LogField(tableName = "b200008", value = "b200008_6531", valueName = "退保金")
    @ApiModelProperty(value = "退保金")
    private BigDecimal b2000086531;

    @LogField(tableName = "b200008", value = "b200008_6541", valueName = "分出保费")
    @ApiModelProperty(value = "分出保费")
    private BigDecimal b2000086541;

    @LogField(tableName = "b200008", value = "b200008_6542", valueName = "分保费用")
    @ApiModelProperty(value = "分保费用")
    private BigDecimal b2000086542;

    @LogField(tableName = "b200008", value = "b200008_6601", valueName = "销售费用")
    @ApiModelProperty(value = "销售费用")
    private BigDecimal b2000086601;

    @LogField(tableName = "b200008", value = "b200008_660101", valueName = "职工工资")
    @ApiModelProperty(value = "职工工资")
    private BigDecimal b200008660101;

    @LogField(tableName = "b200008", value = "b200008_660102", valueName = "年终奖")
    @ApiModelProperty(value = "年终奖")
    private BigDecimal b200008660102;

    @LogField(tableName = "b200008", value = "b200008_660103", valueName = "津贴与补贴")
    @ApiModelProperty(value = "津贴与补贴")
    private BigDecimal b200008660103;

    @LogField(tableName = "b200008", value = "b200008_660104", valueName = "股权激励")
    @ApiModelProperty(value = "股权激励")
    private BigDecimal b200008660104;

    @LogField(tableName = "b200008", value = "b200008_660105", valueName = "社会保险费")
    @ApiModelProperty(value = "社会保险费")
    private BigDecimal b200008660105;

    @LogField(tableName = "b200008", value = "b200008_660106", valueName = "住房公积金")
    @ApiModelProperty(value = "住房公积金")
    private BigDecimal b200008660106;

    @LogField(tableName = "b200008", value = "b200008_660107", valueName = "补充医疗保险")
    @ApiModelProperty(value = "补充医疗保险")
    private BigDecimal b200008660107;

    @LogField(tableName = "b200008", value = "b200008_660108", valueName = "补充养老保险")
    @ApiModelProperty(value = "补充养老保险")
    private BigDecimal b200008660108;

    @LogField(tableName = "b200008", value = "b200008_660109", valueName = "职工福利费")
    @ApiModelProperty(value = "职工福利费")
    private BigDecimal b200008660109;

    @LogField(tableName = "b200008", value = "b200008_660110", valueName = "职工教育经费")
    @ApiModelProperty(value = "职工教育经费")
    private BigDecimal b200008660110;

    @LogField(tableName = "b200008", value = "b200008_660111", valueName = "职工培训费用")
    @ApiModelProperty(value = "职工培训费用")
    private BigDecimal b200008660111;

    @LogField(tableName = "b200008", value = "b200008_660112", valueName = "工会经费")
    @ApiModelProperty(value = "工会经费")
    private BigDecimal b200008660112;

    @LogField(tableName = "b200008", value = "b200008_660113", valueName = "其他职工工资")
    @ApiModelProperty(value = "其他职工工资")
    private BigDecimal b200008660113;

    @LogField(tableName = "b200008", value = "b200008_660114", valueName = "商业保险费")
    @ApiModelProperty(value = "商业保险费")
    private BigDecimal b200008660114;

    @LogField(tableName = "b200008", value = "b200008_660115", valueName = "折旧费")
    @ApiModelProperty(value = "折旧费")
    private BigDecimal b200008660115;

    @LogField(tableName = "b200008", value = "b200008_660116", valueName = "无形资产摊销费")
    @ApiModelProperty(value = "无形资产摊销费")
    private BigDecimal b200008660116;

    @LogField(tableName = "b200008", value = "b200008_660117", valueName = "长期待摊费用摊销费")
    @ApiModelProperty(value = "长期待摊费用摊销费")
    private BigDecimal b200008660117;

    @LogField(tableName = "b200008", value = "b200008_660118", valueName = "修理费")
    @ApiModelProperty(value = "修理费")
    private BigDecimal b200008660118;

    @LogField(tableName = "b200008", value = "b200008_660119", valueName = "物料消耗")
    @ApiModelProperty(value = "物料消耗")
    private BigDecimal b200008660119;

    @LogField(tableName = "b200008", value = "b200008_660120", valueName = "低值易耗品摊销")
    @ApiModelProperty(value = "低值易耗品摊销")
    private BigDecimal b200008660120;

    @LogField(tableName = "b200008", value = "b200008_660121", valueName = "办公费")
    @ApiModelProperty(value = "办公费")
    private BigDecimal b200008660121;

    @LogField(tableName = "b200008", value = "b200008_660122", valueName = "差旅费")
    @ApiModelProperty(value = "差旅费")
    private BigDecimal b200008660122;

    @LogField(tableName = "b200008", value = "b200008_660123", valueName = "业务招待费")
    @ApiModelProperty(value = "业务招待费")
    private BigDecimal b200008660123;

    @LogField(tableName = "b200008", value = "b200008_660124", valueName = "通信费")
    @ApiModelProperty(value = "通信费")
    private BigDecimal b200008660124;

    @LogField(tableName = "b200008", value = "b200008_660125", valueName = "车辆费")
    @ApiModelProperty(value = "车辆费")
    private BigDecimal b200008660125;

    @LogField(tableName = "b200008", value = "b200008_660126", valueName = "能源费")
    @ApiModelProperty(value = "能源费")
    private BigDecimal b200008660126;

    @LogField(tableName = "b200008", value = "b200008_660127", valueName = "运杂费")
    @ApiModelProperty(value = "运杂费")
    private BigDecimal b200008660127;

    @LogField(tableName = "b200008", value = "b200008_660128", valueName = "交通费")
    @ApiModelProperty(value = "交通费")
    private BigDecimal b200008660128;

    @LogField(tableName = "b200008", value = "b200008_660129", valueName = "房租")
    @ApiModelProperty(value = "房租")
    private BigDecimal b200008660129;

    @LogField(tableName = "b200008", value = "b200008_660130", valueName = "物业管理费")
    @ApiModelProperty(value = "物业管理费")
    private BigDecimal b200008660130;

    @LogField(tableName = "b200008", value = "b200008_660131", valueName = "水电费")
    @ApiModelProperty(value = "水电费")
    private BigDecimal b200008660131;

    @LogField(tableName = "b200008", value = "b200008_660132", valueName = "中介服务费")
    @ApiModelProperty(value = "中介服务费")
    private BigDecimal b200008660132;

    @LogField(tableName = "b200008", value = "b200008_660133", valueName = "快递费")
    @ApiModelProperty(value = "快递费")
    private BigDecimal b200008660133;

    @LogField(tableName = "b200008", value = "b200008_660134", valueName = "保险费")
    @ApiModelProperty(value = "保险费")
    private BigDecimal b200008660134;

    @LogField(tableName = "b200008", value = "b200008_660135", valueName = "租赁费")
    @ApiModelProperty(value = "租赁费")
    private BigDecimal b200008660135;

    @LogField(tableName = "b200008", value = "b200008_660136", valueName = "装卸费")
    @ApiModelProperty(value = "装卸费")
    private BigDecimal b200008660136;

    @LogField(tableName = "b200008", value = "b200008_660137", valueName = "包装费")
    @ApiModelProperty(value = "包装费")
    private BigDecimal b200008660137;

    @LogField(tableName = "b200008", value = "b200008_660138", valueName = "通关费用")
    @ApiModelProperty(value = "通关费用")
    private BigDecimal b200008660138;

    @LogField(tableName = "b200008", value = "b200008_660139", valueName = "宣传展览费")
    @ApiModelProperty(value = "宣传展览费")
    private BigDecimal b200008660139;

    @LogField(tableName = "b200008", value = "b200008_660140", valueName = "仓储费")
    @ApiModelProperty(value = "仓储费")
    private BigDecimal b200008660140;

    @LogField(tableName = "b200008", value = "b200008_660141", valueName = "调试费")
    @ApiModelProperty(value = "调试费")
    private BigDecimal b200008660141;

    @LogField(tableName = "b200008", value = "b200008_660142", valueName = "广告费")
    @ApiModelProperty(value = "广告费")
    private BigDecimal b200008660142;

    @LogField(tableName = "b200008", value = "b200008_660143", valueName = "业务提成/佣金（销售服务费）")
    @ApiModelProperty(value = "业务提成/佣金（销售服务费）")
    private BigDecimal b200008660143;

    @LogField(tableName = "b200008", value = "b200008_660144", valueName = "投标费")
    @ApiModelProperty(value = "投标费")
    private BigDecimal b200008660144;

    @LogField(tableName = "b200008", value = "b200008_660145", valueName = "售后服务费")
    @ApiModelProperty(value = "售后服务费")
    private BigDecimal b200008660145;

    @LogField(tableName = "b200008", value = "b200008_660146", valueName = "其他经营费用")
    @ApiModelProperty(value = "其他经营费用")
    private BigDecimal b200008660146;

    @LogField(tableName = "b200008", value = "b200008_660147", valueName = "劳务费")
    @ApiModelProperty(value = "劳务费")
    private BigDecimal b200008660147;

    @LogField(tableName = "b200008", value = "b200008_660148", valueName = "董事会费")
    @ApiModelProperty(value = "董事会费")
    private BigDecimal b200008660148;

    @LogField(tableName = "b200008", value = "b200008_660149", valueName = "诉讼费")
    @ApiModelProperty(value = "诉讼费")
    private BigDecimal b200008660149;

    @LogField(tableName = "b200008", value = "b200008_660150", valueName = "技术转让费")
    @ApiModelProperty(value = "技术转让费")
    private BigDecimal b200008660150;

    @LogField(tableName = "b200008", value = "b200008_660151", valueName = "研究费用")
    @ApiModelProperty(value = "研究费用")
    private BigDecimal b200008660151;

    @LogField(tableName = "b200008", value = "b200008_660152", valueName = "税金")
    @ApiModelProperty(value = "税金")
    private BigDecimal b200008660152;

    @LogField(tableName = "b200008", value = "b200008_660153", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200008660153;

    @LogField(tableName = "b200008", value = "b200008_6602", valueName = "管理费用")
    @ApiModelProperty(value = "管理费用")
    private BigDecimal b2000086602;

    @LogField(tableName = "b200008", value = "b200008_660201", valueName = "职工工资")
    @ApiModelProperty(value = "职工工资")
    private BigDecimal b200008660201;

    @LogField(tableName = "b200008", value = "b200008_660202", valueName = "年终奖")
    @ApiModelProperty(value = "年终奖")
    private BigDecimal b200008660202;

    @LogField(tableName = "b200008", value = "b200008_660203", valueName = "津贴与补贴")
    @ApiModelProperty(value = "津贴与补贴")
    private BigDecimal b200008660203;

    @LogField(tableName = "b200008", value = "b200008_660204", valueName = "股权激励")
    @ApiModelProperty(value = "股权激励")
    private BigDecimal b200008660204;

    @LogField(tableName = "b200008", value = "b200008_660205", valueName = "社会保险费")
    @ApiModelProperty(value = "社会保险费")
    private BigDecimal b200008660205;

    @LogField(tableName = "b200008", value = "b200008_660206", valueName = "住房公积金")
    @ApiModelProperty(value = "住房公积金")
    private BigDecimal b200008660206;

    @LogField(tableName = "b200008", value = "b200008_660207", valueName = "补充医疗保险")
    @ApiModelProperty(value = "补充医疗保险")
    private BigDecimal b200008660207;

    @LogField(tableName = "b200008", value = "b200008_660208", valueName = "补充养老保险")
    @ApiModelProperty(value = "补充养老保险")
    private BigDecimal b200008660208;

    @LogField(tableName = "b200008", value = "b200008_660209", valueName = "职工福利费")
    @ApiModelProperty(value = "职工福利费")
    private BigDecimal b200008660209;

    @LogField(tableName = "b200008", value = "b200008_660210", valueName = "职工教育经费")
    @ApiModelProperty(value = "职工教育经费")
    private BigDecimal b200008660210;

    @LogField(tableName = "b200008", value = "b200008_660211", valueName = "职工培训费用")
    @ApiModelProperty(value = "职工培训费用")
    private BigDecimal b200008660211;

    @LogField(tableName = "b200008", value = "b200008_660212", valueName = "工会经费")
    @ApiModelProperty(value = "工会经费")
    private BigDecimal b200008660212;

    @LogField(tableName = "b200008", value = "b200008_660213", valueName = "其他职工工资")
    @ApiModelProperty(value = "其他职工工资")
    private BigDecimal b200008660213;

    @LogField(tableName = "b200008", value = "b200008_660214", valueName = "商业保险费")
    @ApiModelProperty(value = "商业保险费")
    private BigDecimal b200008660214;

    @LogField(tableName = "b200008", value = "b200008_660215", valueName = "差旅费")
    @ApiModelProperty(value = "差旅费")
    private BigDecimal b200008660215;

    @LogField(tableName = "b200008", value = "b200008_660216", valueName = "办公费")
    @ApiModelProperty(value = "办公费")
    private BigDecimal b200008660216;

    @LogField(tableName = "b200008", value = "b200008_660217", valueName = "折旧费")
    @ApiModelProperty(value = "折旧费")
    private BigDecimal b200008660217;

    @LogField(tableName = "b200008", value = "b200008_660218", valueName = "修理费")
    @ApiModelProperty(value = "修理费")
    private BigDecimal b200008660218;

    @LogField(tableName = "b200008", value = "b200008_660219", valueName = "物料消耗")
    @ApiModelProperty(value = "物料消耗")
    private BigDecimal b200008660219;

    @LogField(tableName = "b200008", value = "b200008_660220", valueName = "低值易耗品摊销")
    @ApiModelProperty(value = "低值易耗品摊销")
    private BigDecimal b200008660220;

    @LogField(tableName = "b200008", value = "b200008_660221", valueName = "会议费")
    @ApiModelProperty(value = "会议费")
    private BigDecimal b200008660221;

    @LogField(tableName = "b200008", value = "b200008_660222", valueName = "咨询费")
    @ApiModelProperty(value = "咨询费")
    private BigDecimal b200008660222;

    @LogField(tableName = "b200008", value = "b200008_660223", valueName = "软件使用费")
    @ApiModelProperty(value = "软件使用费")
    private BigDecimal b200008660223;

    @LogField(tableName = "b200008", value = "b200008_660224", valueName = "招聘费")
    @ApiModelProperty(value = "招聘费")
    private BigDecimal b200008660224;

    @LogField(tableName = "b200008", value = "b200008_660225", valueName = "专业服务费")
    @ApiModelProperty(value = "专业服务费")
    private BigDecimal b200008660225;

    @LogField(tableName = "b200008", value = "b200008_660226", valueName = "税金")
    @ApiModelProperty(value = "税金")
    private BigDecimal b200008660226;

    @LogField(tableName = "b200008", value = "b200008_660227", valueName = "技术开发费")
    @ApiModelProperty(value = "技术开发费")
    private BigDecimal b200008660227;

    @LogField(tableName = "b200008", value = "b200008_660228", valueName = "技术转让费")
    @ApiModelProperty(value = "技术转让费")
    private BigDecimal b200008660228;

    @LogField(tableName = "b200008", value = "b200008_660229", valueName = "无形资产摊销费")
    @ApiModelProperty(value = "无形资产摊销费")
    private BigDecimal b200008660229;

    @LogField(tableName = "b200008", value = "b200008_660230", valueName = "业务招待费")
    @ApiModelProperty(value = "业务招待费")
    private BigDecimal b200008660230;

    @LogField(tableName = "b200008", value = "b200008_660231", valueName = "研发费用")
    @ApiModelProperty(value = "研发费用")
    private BigDecimal b200008660231;

    @LogField(tableName = "b200008", value = "b200008_660232", valueName = "仓储费用")
    @ApiModelProperty(value = "仓储费用")
    private BigDecimal b200008660232;

    @LogField(tableName = "b200008", value = "b200008_660233", valueName = "保险费")
    @ApiModelProperty(value = "保险费")
    private BigDecimal b200008660233;

    @LogField(tableName = "b200008", value = "b200008_660234", valueName = "房租")
    @ApiModelProperty(value = "房租")
    private BigDecimal b200008660234;

    @LogField(tableName = "b200008", value = "b200008_660235", valueName = "物业管理费")
    @ApiModelProperty(value = "物业管理费")
    private BigDecimal b200008660235;

    @LogField(tableName = "b200008", value = "b200008_660236", valueName = "快递费")
    @ApiModelProperty(value = "快递费")
    private BigDecimal b200008660236;

    @LogField(tableName = "b200008", value = "b200008_660237", valueName = "中介服务费")
    @ApiModelProperty(value = "中介服务费")
    private BigDecimal b200008660237;

    @LogField(tableName = "b200008", value = "b200008_660238", valueName = "开办费")
    @ApiModelProperty(value = "开办费")
    private BigDecimal b200008660238;

    @LogField(tableName = "b200008", value = "b200008_660239", valueName = "水电费")
    @ApiModelProperty(value = "水电费")
    private BigDecimal b200008660239;

    @LogField(tableName = "b200008", value = "b200008_660240", valueName = "运杂费")
    @ApiModelProperty(value = "运杂费")
    private BigDecimal b200008660240;

    @LogField(tableName = "b200008", value = "b200008_660241", valueName = "劳务费")
    @ApiModelProperty(value = "劳务费")
    private BigDecimal b200008660241;

    @LogField(tableName = "b200008", value = "b200008_660242", valueName = "广告费")
    @ApiModelProperty(value = "广告费")
    private BigDecimal b200008660242;

    @LogField(tableName = "b200008", value = "b200008_660243", valueName = "佣金和手续费")
    @ApiModelProperty(value = "佣金和手续费")
    private BigDecimal b200008660243;

    @LogField(tableName = "b200008", value = "b200008_660244", valueName = "董事会费")
    @ApiModelProperty(value = "董事会费")
    private BigDecimal b200008660244;

    @LogField(tableName = "b200008", value = "b200008_660245", valueName = "诉讼费")
    @ApiModelProperty(value = "诉讼费")
    private BigDecimal b200008660245;

    @LogField(tableName = "b200008", value = "b200008_660246", valueName = "包装费")
    @ApiModelProperty(value = "包装费")
    private BigDecimal b200008660246;

    @LogField(tableName = "b200008", value = "b200008_660247", valueName = "党组织工作经费")
    @ApiModelProperty(value = "党组织工作经费")
    private BigDecimal b200008660247;

    @LogField(tableName = "b200008", value = "b200008_660248", valueName = "长期待摊费用摊销费")
    @ApiModelProperty(value = "长期待摊费用摊销费")
    private BigDecimal b200008660248;

    @LogField(tableName = "b200008", value = "b200008_660249", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200008660249;

    @LogField(tableName = "b200008", value = "b200008_660250", valueName = "现金及银行存款损失")
    @ApiModelProperty(value = "现金及银行存款损失")
    private BigDecimal b200008660250;

    @LogField(tableName = "b200008", value = "b200008_660251", valueName = "应收及预付款项坏账损失")
    @ApiModelProperty(value = "应收及预付款项坏账损失")
    private BigDecimal b200008660251;

    @LogField(tableName = "b200008", value = "b200008_660252", valueName = "存货损失")
    @ApiModelProperty(value = "存货损失")
    private BigDecimal b200008660252;

    @LogField(tableName = "b200008", value = "b200008_660253", valueName = "固定资产损失")
    @ApiModelProperty(value = "固定资产损失")
    private BigDecimal b200008660253;

    @LogField(tableName = "b200008", value = "b200008_660254", valueName = "无形资产损失")
    @ApiModelProperty(value = "无形资产损失")
    private BigDecimal b200008660254;

    @LogField(tableName = "b200008", value = "b200008_660255", valueName = "在建工程损失")
    @ApiModelProperty(value = "在建工程损失")
    private BigDecimal b200008660255;

    @LogField(tableName = "b200008", value = "b200008_660256", valueName = "生产性生物资产损失")
    @ApiModelProperty(value = "生产性生物资产损失")
    private BigDecimal b200008660256;

    @LogField(tableName = "b200008", value = "b200008_660257", valueName = "非金融企业债权性投资损失")
    @ApiModelProperty(value = "非金融企业债权性投资损失")
    private BigDecimal b200008660257;

    @LogField(tableName = "b200008", value = "b200008_660258", valueName = "股权（权益）性投资损失")
    @ApiModelProperty(value = "股权（权益）性投资损失")
    private BigDecimal b200008660258;

    @LogField(tableName = "b200008", value = "b200008_660259", valueName = "通过各种交易场所、市场买卖债券、股票、期货、基金以及金融衍生产品等发生的损失")
    @ApiModelProperty(value = "通过各种交易场所、市场买卖债券、股票、期货、基金以及金融衍生产品等发生的损失")
    private BigDecimal b200008660259;

    @LogField(tableName = "b200008", value = "b200008_660260", valueName = "打包出售资产损失")
    @ApiModelProperty(value = "打包出售资产损失")
    private BigDecimal b200008660260;

    @LogField(tableName = "b200008", value = "b200008_660261", valueName = "其他资产损失")
    @ApiModelProperty(value = "其他资产损失")
    private BigDecimal b200008660261;

    @LogField(tableName = "b200008", value = "b200008_6603", valueName = "财务费用")
    @ApiModelProperty(value = "财务费用")
    private BigDecimal b2000086603;

    @LogField(tableName = "b200008", value = "b200008_660301", valueName = "汇兑损益")
    @ApiModelProperty(value = "汇兑损益")
    private BigDecimal b200008660301;

    @LogField(tableName = "b200008", value = "b200008_660302", valueName = "利息费用")
    @ApiModelProperty(value = "利息费用")
    private BigDecimal b200008660302;

    @LogField(tableName = "b200008", value = "b200008_660303", valueName = "佣金和手续费")
    @ApiModelProperty(value = "佣金和手续费")
    private BigDecimal b200008660303;

    @LogField(tableName = "b200008", value = "b200008_660304", valueName = "账户管理费")
    @ApiModelProperty(value = "账户管理费")
    private BigDecimal b200008660304;

    @LogField(tableName = "b200008", value = "b200008_660305", valueName = "利息收入")
    @ApiModelProperty(value = "利息收入")
    private BigDecimal b200008660305;

    @LogField(tableName = "b200008", value = "b200008_66030501", valueName = "因未实现融资收益确认的利息收入")
    @ApiModelProperty(value = "因未实现融资收益确认的利息收入")
    private BigDecimal b20000866030501;

    @LogField(tableName = "b200008", value = "b200008_66030502", valueName = "其他利息收入")
    @ApiModelProperty(value = "其他利息收入")
    private BigDecimal b20000866030502;

    @LogField(tableName = "b200008", value = "b200008_660306", valueName = "现金折扣")
    @ApiModelProperty(value = "现金折扣")
    private BigDecimal b200008660306;

    @LogField(tableName = "b200008", value = "b200008_660307", valueName = "银行手续费")
    @ApiModelProperty(value = "银行手续费")
    private BigDecimal b200008660307;

    @LogField(tableName = "b200008", value = "b200008_660308", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200008660308;

    @LogField(tableName = "b200008", value = "b200008_6604", valueName = "勘探费用")
    @ApiModelProperty(value = "勘探费用")
    private BigDecimal b2000086604;

    @LogField(tableName = "b200008", value = "b200008_6701", valueName = "资产减值损失")
    @ApiModelProperty(value = "资产减值损失")
    private BigDecimal b2000086701;

    @LogField(tableName = "b200008", value = "b200008_6702", valueName = "信用减值损失")
    @ApiModelProperty(value = "信用减值损失")
    private BigDecimal b2000086702;

    @LogField(tableName = "b200008", value = "b200008_6711", valueName = "营业外支出")
    @ApiModelProperty(value = "营业外支出")
    private BigDecimal b2000086711;

    @LogField(tableName = "b200008", value = "b200008_671101", valueName = "非流动资产处置净损失")
    @ApiModelProperty(value = "非流动资产处置净损失")
    private BigDecimal b200008671101;

    @LogField(tableName = "b200008", value = "b200008_67110101", valueName = "存货损失")
    @ApiModelProperty(value = "存货损失")
    private BigDecimal b20000867110101;

    @LogField(tableName = "b200008", value = "b200008_67110102", valueName = "固定资产损失")
    @ApiModelProperty(value = "固定资产损失")
    private BigDecimal b20000867110102;

    @LogField(tableName = "b200008", value = "b200008_67110103", valueName = "无形资产损失")
    @ApiModelProperty(value = "无形资产损失")
    private BigDecimal b20000867110103;

    @LogField(tableName = "b200008", value = "b200008_67110104", valueName = "在建工程损失")
    @ApiModelProperty(value = "在建工程损失")
    private BigDecimal b20000867110104;

    @LogField(tableName = "b200008", value = "b200008_67110105", valueName = "生产性生物资产损失")
    @ApiModelProperty(value = "生产性生物资产损失")
    private BigDecimal b20000867110105;

    @LogField(tableName = "b200008", value = "b200008_67110106", valueName = "打包出售资产损失")
    @ApiModelProperty(value = "打包出售资产损失")
    private BigDecimal b20000867110106;

    @LogField(tableName = "b200008", value = "b200008_67110107", valueName = "其他资产损失")
    @ApiModelProperty(value = "其他资产损失")
    private BigDecimal b20000867110107;

    @LogField(tableName = "b200008", value = "b200008_671102", valueName = "非货币性资产交换损失")
    @ApiModelProperty(value = "非货币性资产交换损失")
    private BigDecimal b200008671102;

    @LogField(tableName = "b200008", value = "b200008_67110201", valueName = "存货损失")
    @ApiModelProperty(value = "存货损失")
    private BigDecimal b20000867110201;

    @LogField(tableName = "b200008", value = "b200008_67110202", valueName = "固定资产损失")
    @ApiModelProperty(value = "固定资产损失")
    private BigDecimal b20000867110202;

    @LogField(tableName = "b200008", value = "b200008_67110203", valueName = "无形资产损失")
    @ApiModelProperty(value = "无形资产损失")
    private BigDecimal b20000867110203;

    @LogField(tableName = "b200008", value = "b200008_67110204", valueName = "在建工程损失")
    @ApiModelProperty(value = "在建工程损失")
    private BigDecimal b20000867110204;

    @LogField(tableName = "b200008", value = "b200008_67110205", valueName = "生产性生物资产损失")
    @ApiModelProperty(value = "生产性生物资产损失")
    private BigDecimal b20000867110205;

    @LogField(tableName = "b200008", value = "b200008_67110206", valueName = "打包出售资产损失")
    @ApiModelProperty(value = "打包出售资产损失")
    private BigDecimal b20000867110206;

    @LogField(tableName = "b200008", value = "b200008_67110207", valueName = "其他资产损失")
    @ApiModelProperty(value = "其他资产损失")
    private BigDecimal b20000867110207;

    @LogField(tableName = "b200008", value = "b200008_671103", valueName = "债务重组损失")
    @ApiModelProperty(value = "债务重组损失")
    private BigDecimal b200008671103;

    @LogField(tableName = "b200008", value = "b200008_671104", valueName = "非常损失")
    @ApiModelProperty(value = "非常损失")
    private BigDecimal b200008671104;

    @LogField(tableName = "b200008", value = "b200008_67110401", valueName = "存货损失")
    @ApiModelProperty(value = "存货损失")
    private BigDecimal b20000867110401;

    @LogField(tableName = "b200008", value = "b200008_67110402", valueName = "固定资产损失")
    @ApiModelProperty(value = "固定资产损失")
    private BigDecimal b20000867110402;

    @LogField(tableName = "b200008", value = "b200008_67110403", valueName = "无形资产损失")
    @ApiModelProperty(value = "无形资产损失")
    private BigDecimal b20000867110403;

    @LogField(tableName = "b200008", value = "b200008_67110404", valueName = "在建工程损失")
    @ApiModelProperty(value = "在建工程损失")
    private BigDecimal b20000867110404;

    @LogField(tableName = "b200008", value = "b200008_67110405", valueName = "生产性生物资产损失")
    @ApiModelProperty(value = "生产性生物资产损失")
    private BigDecimal b20000867110405;

    @LogField(tableName = "b200008", value = "b200008_67110406", valueName = "打包出售资产损失")
    @ApiModelProperty(value = "打包出售资产损失")
    private BigDecimal b20000867110406;

    @LogField(tableName = "b200008", value = "b200008_67110407", valueName = "其他资产损失")
    @ApiModelProperty(value = "其他资产损失")
    private BigDecimal b20000867110407;

    @LogField(tableName = "b200008", value = "b200008_671105", valueName = "捐赠支出")
    @ApiModelProperty(value = "捐赠支出")
    private BigDecimal b200008671105;

    @LogField(tableName = "b200008", value = "b200008_671106", valueName = "赞助支出")
    @ApiModelProperty(value = "赞助支出")
    private BigDecimal b200008671106;

    @LogField(tableName = "b200008", value = "b200008_671107", valueName = "罚没支出")
    @ApiModelProperty(value = "罚没支出")
    private BigDecimal b200008671107;

    @LogField(tableName = "b200008", value = "b200008_67110701", valueName = "经营性处罚")
    @ApiModelProperty(value = "经营性处罚")
    private BigDecimal b20000867110701;

    @LogField(tableName = "b200008", value = "b200008_67110702", valueName = "税收滞纳金")
    @ApiModelProperty(value = "税收滞纳金")
    private BigDecimal b20000867110702;

    @LogField(tableName = "b200008", value = "b200008_67110703", valueName = "行政性处罚")
    @ApiModelProperty(value = "行政性处罚")
    private BigDecimal b20000867110703;

    @LogField(tableName = "b200008", value = "b200008_671108", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200008671108;

    @LogField(tableName = "b200008", value = "b200008_67110801", valueName = "现金及银行存款损失")
    @ApiModelProperty(value = "现金及银行存款损失")
    private BigDecimal b20000867110801;

    @LogField(tableName = "b200008", value = "b200008_67110802", valueName = "应收及预付款项坏账损失")
    @ApiModelProperty(value = "应收及预付款项坏账损失")
    private BigDecimal b20000867110802;

    @LogField(tableName = "b200008", value = "b200008_67110803", valueName = "存货损失")
    @ApiModelProperty(value = "存货损失")
    private BigDecimal b20000867110803;

    @LogField(tableName = "b200008", value = "b200008_67110804", valueName = "固定资产损失")
    @ApiModelProperty(value = "固定资产损失")
    private BigDecimal b20000867110804;

    @LogField(tableName = "b200008", value = "b200008_67110805", valueName = "无形资产损失")
    @ApiModelProperty(value = "无形资产损失")
    private BigDecimal b20000867110805;

    @LogField(tableName = "b200008", value = "b200008_67110806", valueName = "在建工程损失")
    @ApiModelProperty(value = "在建工程损失")
    private BigDecimal b20000867110806;

    @LogField(tableName = "b200008", value = "b200008_67110807", valueName = "生产性生物资产损失")
    @ApiModelProperty(value = "生产性生物资产损失")
    private BigDecimal b20000867110807;

    @LogField(tableName = "b200008", value = "b200008_67110808", valueName = "非金融企业债权性投资损失")
    @ApiModelProperty(value = "非金融企业债权性投资损失")
    private BigDecimal b20000867110808;

    @LogField(tableName = "b200008", value = "b200008_67110809", valueName = "股权（权益）性投资损失")
    @ApiModelProperty(value = "股权（权益）性投资损失")
    private BigDecimal b20000867110809;

    @LogField(tableName = "b200008", value = "b200008_67110810", valueName = "通过各种交易场所、市场买卖债券、股票、期货、基金以及金融衍生产品等发生的损失")
    @ApiModelProperty(value = "通过各种交易场所、市场买卖债券、股票、期货、基金以及金融衍生产品等发生的损失")
    private BigDecimal b20000867110810;

    @LogField(tableName = "b200008", value = "b200008_67110811", valueName = "打包出售资产损失")
    @ApiModelProperty(value = "打包出售资产损失")
    private BigDecimal b20000867110811;

    @LogField(tableName = "b200008", value = "b200008_67110812", valueName = "其他资产损失")
    @ApiModelProperty(value = "其他资产损失")
    private BigDecimal b20000867110812;

    @LogField(tableName = "b200008", value = "b200008_67110813", valueName = "其他支出")
    @ApiModelProperty(value = "其他支出")
    private BigDecimal b20000867110813;

    @LogField(tableName = "b200008", value = "b200008_6801", valueName = "所得税费用")
    @ApiModelProperty(value = "所得税费用")
    private BigDecimal b2000086801;

    @LogField(tableName = "b200008", value = "b200008_6901", valueName = "以前年度损益调整")
    @ApiModelProperty(value = "以前年度损益调整")
    private BigDecimal b2000086901;

    @LogField(tableName = "b200008", value = "b200008_222124", valueName = "应交税费-纳税检查调整")
    @ApiModelProperty(value = "应交税费-纳税检查调整")
    private BigDecimal b200008222124;

    @LogField(tableName = "b200008", value = "b200008_222125", valueName = "应交税费-加计抵减进项税额")
    @ApiModelProperty(value = "应交税费-加计抵减进项税额")
    private BigDecimal b200008222125;

    @LogField(tableName = "b200008", value = "b200008_67110501", valueName = "营业外支出—捐赠支出—公益性捐赠")
    @ApiModelProperty(value = "营业外支出—捐赠支出—公益性捐赠")
    private BigDecimal b20000867110501;

    @LogField(tableName = "b200008", value = "b200008_67110502", valueName = "营业外支出—捐赠支出—非公益性捐赠")
    @ApiModelProperty(value = "营业外支出—捐赠支出—非公益性捐赠")
    private BigDecimal b20000867110502;

    @ApiModelProperty(value = "创建时间")
    private String createTime;

    @ApiModelProperty(value = "更新时间")
    private String updateTime;

    public String getEntId() {
        return entId;
    }

    public void setEntId(String entId) {
        this.entId = entId;
    }

    public String getTaxYear() {
        return taxYear;
    }

    public void setTaxYear(String taxYear) {
        this.taxYear = taxYear;
    }

    public BigDecimal getB2000081001() {
        return b2000081001;
    }

    public void setB2000081001(BigDecimal b2000081001) {
        this.b2000081001 = b2000081001;
    }

    public BigDecimal getB2000081002() {
        return b2000081002;
    }

    public void setB2000081002(BigDecimal b2000081002) {
        this.b2000081002 = b2000081002;
    }

    public BigDecimal getB2000081003() {
        return b2000081003;
    }

    public void setB2000081003(BigDecimal b2000081003) {
        this.b2000081003 = b2000081003;
    }

    public BigDecimal getB2000081011() {
        return b2000081011;
    }

    public void setB2000081011(BigDecimal b2000081011) {
        this.b2000081011 = b2000081011;
    }

    public BigDecimal getB2000081012() {
        return b2000081012;
    }

    public void setB2000081012(BigDecimal b2000081012) {
        this.b2000081012 = b2000081012;
    }

    public BigDecimal getB2000081021() {
        return b2000081021;
    }

    public void setB2000081021(BigDecimal b2000081021) {
        this.b2000081021 = b2000081021;
    }

    public BigDecimal getB2000081031() {
        return b2000081031;
    }

    public void setB2000081031(BigDecimal b2000081031) {
        this.b2000081031 = b2000081031;
    }

    public BigDecimal getB2000081101() {
        return b2000081101;
    }

    public void setB2000081101(BigDecimal b2000081101) {
        this.b2000081101 = b2000081101;
    }

    public BigDecimal getB2000081111() {
        return b2000081111;
    }

    public void setB2000081111(BigDecimal b2000081111) {
        this.b2000081111 = b2000081111;
    }

    public BigDecimal getB2000081121() {
        return b2000081121;
    }

    public void setB2000081121(BigDecimal b2000081121) {
        this.b2000081121 = b2000081121;
    }

    public BigDecimal getB2000081122() {
        return b2000081122;
    }

    public void setB2000081122(BigDecimal b2000081122) {
        this.b2000081122 = b2000081122;
    }

    public BigDecimal getB2000081123() {
        return b2000081123;
    }

    public void setB2000081123(BigDecimal b2000081123) {
        this.b2000081123 = b2000081123;
    }

    public BigDecimal getB2000081124() {
        return b2000081124;
    }

    public void setB2000081124(BigDecimal b2000081124) {
        this.b2000081124 = b2000081124;
    }

    public BigDecimal getB2000081125() {
        return b2000081125;
    }

    public void setB2000081125(BigDecimal b2000081125) {
        this.b2000081125 = b2000081125;
    }

    public BigDecimal getB2000081131() {
        return b2000081131;
    }

    public void setB2000081131(BigDecimal b2000081131) {
        this.b2000081131 = b2000081131;
    }

    public BigDecimal getB2000081132() {
        return b2000081132;
    }

    public void setB2000081132(BigDecimal b2000081132) {
        this.b2000081132 = b2000081132;
    }

    public BigDecimal getB2000081201() {
        return b2000081201;
    }

    public void setB2000081201(BigDecimal b2000081201) {
        this.b2000081201 = b2000081201;
    }

    public BigDecimal getB2000081211() {
        return b2000081211;
    }

    public void setB2000081211(BigDecimal b2000081211) {
        this.b2000081211 = b2000081211;
    }

    public BigDecimal getB2000081212() {
        return b2000081212;
    }

    public void setB2000081212(BigDecimal b2000081212) {
        this.b2000081212 = b2000081212;
    }

    public BigDecimal getB2000081221() {
        return b2000081221;
    }

    public void setB2000081221(BigDecimal b2000081221) {
        this.b2000081221 = b2000081221;
    }

    public BigDecimal getB2000081231() {
        return b2000081231;
    }

    public void setB2000081231(BigDecimal b2000081231) {
        this.b2000081231 = b2000081231;
    }

    public BigDecimal getB2000081301() {
        return b2000081301;
    }

    public void setB2000081301(BigDecimal b2000081301) {
        this.b2000081301 = b2000081301;
    }

    public BigDecimal getB2000081302() {
        return b2000081302;
    }

    public void setB2000081302(BigDecimal b2000081302) {
        this.b2000081302 = b2000081302;
    }

    public BigDecimal getB2000081303() {
        return b2000081303;
    }

    public void setB2000081303(BigDecimal b2000081303) {
        this.b2000081303 = b2000081303;
    }

    public BigDecimal getB2000081304() {
        return b2000081304;
    }

    public void setB2000081304(BigDecimal b2000081304) {
        this.b2000081304 = b2000081304;
    }

    public BigDecimal getB2000081311() {
        return b2000081311;
    }

    public void setB2000081311(BigDecimal b2000081311) {
        this.b2000081311 = b2000081311;
    }

    public BigDecimal getB2000081321() {
        return b2000081321;
    }

    public void setB2000081321(BigDecimal b2000081321) {
        this.b2000081321 = b2000081321;
    }

    public BigDecimal getB2000081401() {
        return b2000081401;
    }

    public void setB2000081401(BigDecimal b2000081401) {
        this.b2000081401 = b2000081401;
    }

    public BigDecimal getB2000081402() {
        return b2000081402;
    }

    public void setB2000081402(BigDecimal b2000081402) {
        this.b2000081402 = b2000081402;
    }

    public BigDecimal getB2000081403() {
        return b2000081403;
    }

    public void setB2000081403(BigDecimal b2000081403) {
        this.b2000081403 = b2000081403;
    }

    public BigDecimal getB2000081404() {
        return b2000081404;
    }

    public void setB2000081404(BigDecimal b2000081404) {
        this.b2000081404 = b2000081404;
    }

    public BigDecimal getB2000081405() {
        return b2000081405;
    }

    public void setB2000081405(BigDecimal b2000081405) {
        this.b2000081405 = b2000081405;
    }

    public BigDecimal getB2000081406() {
        return b2000081406;
    }

    public void setB2000081406(BigDecimal b2000081406) {
        this.b2000081406 = b2000081406;
    }

    public BigDecimal getB2000081407() {
        return b2000081407;
    }

    public void setB2000081407(BigDecimal b2000081407) {
        this.b2000081407 = b2000081407;
    }

    public BigDecimal getB2000081408() {
        return b2000081408;
    }

    public void setB2000081408(BigDecimal b2000081408) {
        this.b2000081408 = b2000081408;
    }

    public BigDecimal getB2000081411() {
        return b2000081411;
    }

    public void setB2000081411(BigDecimal b2000081411) {
        this.b2000081411 = b2000081411;
    }

    public BigDecimal getB2000081421() {
        return b2000081421;
    }

    public void setB2000081421(BigDecimal b2000081421) {
        this.b2000081421 = b2000081421;
    }

    public BigDecimal getB2000081431() {
        return b2000081431;
    }

    public void setB2000081431(BigDecimal b2000081431) {
        this.b2000081431 = b2000081431;
    }

    public BigDecimal getB2000081441() {
        return b2000081441;
    }

    public void setB2000081441(BigDecimal b2000081441) {
        this.b2000081441 = b2000081441;
    }

    public BigDecimal getB2000081451() {
        return b2000081451;
    }

    public void setB2000081451(BigDecimal b2000081451) {
        this.b2000081451 = b2000081451;
    }

    public BigDecimal getB2000081461() {
        return b2000081461;
    }

    public void setB2000081461(BigDecimal b2000081461) {
        this.b2000081461 = b2000081461;
    }

    public BigDecimal getB2000081471() {
        return b2000081471;
    }

    public void setB2000081471(BigDecimal b2000081471) {
        this.b2000081471 = b2000081471;
    }

    public BigDecimal getB2000081481() {
        return b2000081481;
    }

    public void setB2000081481(BigDecimal b2000081481) {
        this.b2000081481 = b2000081481;
    }

    public BigDecimal getB2000081482() {
        return b2000081482;
    }

    public void setB2000081482(BigDecimal b2000081482) {
        this.b2000081482 = b2000081482;
    }

    public BigDecimal getB2000081501() {
        return b2000081501;
    }

    public void setB2000081501(BigDecimal b2000081501) {
        this.b2000081501 = b2000081501;
    }

    public BigDecimal getB2000081502() {
        return b2000081502;
    }

    public void setB2000081502(BigDecimal b2000081502) {
        this.b2000081502 = b2000081502;
    }

    public BigDecimal getB2000081503() {
        return b2000081503;
    }

    public void setB2000081503(BigDecimal b2000081503) {
        this.b2000081503 = b2000081503;
    }

    public BigDecimal getB2000081511() {
        return b2000081511;
    }

    public void setB2000081511(BigDecimal b2000081511) {
        this.b2000081511 = b2000081511;
    }

    public BigDecimal getB2000081512() {
        return b2000081512;
    }

    public void setB2000081512(BigDecimal b2000081512) {
        this.b2000081512 = b2000081512;
    }

    public BigDecimal getB2000081521() {
        return b2000081521;
    }

    public void setB2000081521(BigDecimal b2000081521) {
        this.b2000081521 = b2000081521;
    }

    public BigDecimal getB2000081531() {
        return b2000081531;
    }

    public void setB2000081531(BigDecimal b2000081531) {
        this.b2000081531 = b2000081531;
    }

    public BigDecimal getB2000081532() {
        return b2000081532;
    }

    public void setB2000081532(BigDecimal b2000081532) {
        this.b2000081532 = b2000081532;
    }

    public BigDecimal getB2000081541() {
        return b2000081541;
    }

    public void setB2000081541(BigDecimal b2000081541) {
        this.b2000081541 = b2000081541;
    }

    public BigDecimal getB2000081601() {
        return b2000081601;
    }

    public void setB2000081601(BigDecimal b2000081601) {
        this.b2000081601 = b2000081601;
    }

    public BigDecimal getB2000081602() {
        return b2000081602;
    }

    public void setB2000081602(BigDecimal b2000081602) {
        this.b2000081602 = b2000081602;
    }

    public BigDecimal getB2000081603() {
        return b2000081603;
    }

    public void setB2000081603(BigDecimal b2000081603) {
        this.b2000081603 = b2000081603;
    }

    public BigDecimal getB2000081604() {
        return b2000081604;
    }

    public void setB2000081604(BigDecimal b2000081604) {
        this.b2000081604 = b2000081604;
    }

    public BigDecimal getB2000081605() {
        return b2000081605;
    }

    public void setB2000081605(BigDecimal b2000081605) {
        this.b2000081605 = b2000081605;
    }

    public BigDecimal getB2000081606() {
        return b2000081606;
    }

    public void setB2000081606(BigDecimal b2000081606) {
        this.b2000081606 = b2000081606;
    }

    public BigDecimal getB2000081611() {
        return b2000081611;
    }

    public void setB2000081611(BigDecimal b2000081611) {
        this.b2000081611 = b2000081611;
    }

    public BigDecimal getB2000081621() {
        return b2000081621;
    }

    public void setB2000081621(BigDecimal b2000081621) {
        this.b2000081621 = b2000081621;
    }

    public BigDecimal getB2000081622() {
        return b2000081622;
    }

    public void setB2000081622(BigDecimal b2000081622) {
        this.b2000081622 = b2000081622;
    }

    public BigDecimal getB2000081623() {
        return b2000081623;
    }

    public void setB2000081623(BigDecimal b2000081623) {
        this.b2000081623 = b2000081623;
    }

    public BigDecimal getB2000081631() {
        return b2000081631;
    }

    public void setB2000081631(BigDecimal b2000081631) {
        this.b2000081631 = b2000081631;
    }

    public BigDecimal getB2000081632() {
        return b2000081632;
    }

    public void setB2000081632(BigDecimal b2000081632) {
        this.b2000081632 = b2000081632;
    }

    public BigDecimal getB2000081701() {
        return b2000081701;
    }

    public void setB2000081701(BigDecimal b2000081701) {
        this.b2000081701 = b2000081701;
    }

    public BigDecimal getB2000081702() {
        return b2000081702;
    }

    public void setB2000081702(BigDecimal b2000081702) {
        this.b2000081702 = b2000081702;
    }

    public BigDecimal getB2000081703() {
        return b2000081703;
    }

    public void setB2000081703(BigDecimal b2000081703) {
        this.b2000081703 = b2000081703;
    }

    public BigDecimal getB2000081711() {
        return b2000081711;
    }

    public void setB2000081711(BigDecimal b2000081711) {
        this.b2000081711 = b2000081711;
    }

    public BigDecimal getB2000081801() {
        return b2000081801;
    }

    public void setB2000081801(BigDecimal b2000081801) {
        this.b2000081801 = b2000081801;
    }

    public BigDecimal getB2000081811() {
        return b2000081811;
    }

    public void setB2000081811(BigDecimal b2000081811) {
        this.b2000081811 = b2000081811;
    }

    public BigDecimal getB2000081821() {
        return b2000081821;
    }

    public void setB2000081821(BigDecimal b2000081821) {
        this.b2000081821 = b2000081821;
    }

    public BigDecimal getB2000081901() {
        return b2000081901;
    }

    public void setB2000081901(BigDecimal b2000081901) {
        this.b2000081901 = b2000081901;
    }

    public BigDecimal getB2000082001() {
        return b2000082001;
    }

    public void setB2000082001(BigDecimal b2000082001) {
        this.b2000082001 = b2000082001;
    }

    public BigDecimal getB2000082002() {
        return b2000082002;
    }

    public void setB2000082002(BigDecimal b2000082002) {
        this.b2000082002 = b2000082002;
    }

    public BigDecimal getB2000082003() {
        return b2000082003;
    }

    public void setB2000082003(BigDecimal b2000082003) {
        this.b2000082003 = b2000082003;
    }

    public BigDecimal getB2000082004() {
        return b2000082004;
    }

    public void setB2000082004(BigDecimal b2000082004) {
        this.b2000082004 = b2000082004;
    }

    public BigDecimal getB2000082011() {
        return b2000082011;
    }

    public void setB2000082011(BigDecimal b2000082011) {
        this.b2000082011 = b2000082011;
    }

    public BigDecimal getB2000082012() {
        return b2000082012;
    }

    public void setB2000082012(BigDecimal b2000082012) {
        this.b2000082012 = b2000082012;
    }

    public BigDecimal getB2000082021() {
        return b2000082021;
    }

    public void setB2000082021(BigDecimal b2000082021) {
        this.b2000082021 = b2000082021;
    }

    public BigDecimal getB2000082101() {
        return b2000082101;
    }

    public void setB2000082101(BigDecimal b2000082101) {
        this.b2000082101 = b2000082101;
    }

    public BigDecimal getB2000082111() {
        return b2000082111;
    }

    public void setB2000082111(BigDecimal b2000082111) {
        this.b2000082111 = b2000082111;
    }

    public BigDecimal getB2000082201() {
        return b2000082201;
    }

    public void setB2000082201(BigDecimal b2000082201) {
        this.b2000082201 = b2000082201;
    }

    public BigDecimal getB2000082202() {
        return b2000082202;
    }

    public void setB2000082202(BigDecimal b2000082202) {
        this.b2000082202 = b2000082202;
    }

    public BigDecimal getB2000082203() {
        return b2000082203;
    }

    public void setB2000082203(BigDecimal b2000082203) {
        this.b2000082203 = b2000082203;
    }

    public BigDecimal getB2000082204() {
        return b2000082204;
    }

    public void setB2000082204(BigDecimal b2000082204) {
        this.b2000082204 = b2000082204;
    }

    public BigDecimal getB2000082211() {
        return b2000082211;
    }

    public void setB2000082211(BigDecimal b2000082211) {
        this.b2000082211 = b2000082211;
    }

    public BigDecimal getB200008221101() {
        return b200008221101;
    }

    public void setB200008221101(BigDecimal b200008221101) {
        this.b200008221101 = b200008221101;
    }

    public BigDecimal getB200008221102() {
        return b200008221102;
    }

    public void setB200008221102(BigDecimal b200008221102) {
        this.b200008221102 = b200008221102;
    }

    public BigDecimal getB200008221103() {
        return b200008221103;
    }

    public void setB200008221103(BigDecimal b200008221103) {
        this.b200008221103 = b200008221103;
    }

    public BigDecimal getB200008221104() {
        return b200008221104;
    }

    public void setB200008221104(BigDecimal b200008221104) {
        this.b200008221104 = b200008221104;
    }

    public BigDecimal getB200008221105() {
        return b200008221105;
    }

    public void setB200008221105(BigDecimal b200008221105) {
        this.b200008221105 = b200008221105;
    }

    public BigDecimal getB200008221106() {
        return b200008221106;
    }

    public void setB200008221106(BigDecimal b200008221106) {
        this.b200008221106 = b200008221106;
    }

    public BigDecimal getB200008221107() {
        return b200008221107;
    }

    public void setB200008221107(BigDecimal b200008221107) {
        this.b200008221107 = b200008221107;
    }

    public BigDecimal getB200008221108() {
        return b200008221108;
    }

    public void setB200008221108(BigDecimal b200008221108) {
        this.b200008221108 = b200008221108;
    }

    public BigDecimal getB200008221109() {
        return b200008221109;
    }

    public void setB200008221109(BigDecimal b200008221109) {
        this.b200008221109 = b200008221109;
    }

    public BigDecimal getB200008221110() {
        return b200008221110;
    }

    public void setB200008221110(BigDecimal b200008221110) {
        this.b200008221110 = b200008221110;
    }

    public BigDecimal getB200008221111() {
        return b200008221111;
    }

    public void setB200008221111(BigDecimal b200008221111) {
        this.b200008221111 = b200008221111;
    }

    public BigDecimal getB200008221112() {
        return b200008221112;
    }

    public void setB200008221112(BigDecimal b200008221112) {
        this.b200008221112 = b200008221112;
    }

    public BigDecimal getB200008221113() {
        return b200008221113;
    }

    public void setB200008221113(BigDecimal b200008221113) {
        this.b200008221113 = b200008221113;
    }

    public BigDecimal getB2000082221() {
        return b2000082221;
    }

    public void setB2000082221(BigDecimal b2000082221) {
        this.b2000082221 = b2000082221;
    }

    public BigDecimal getB200008222101() {
        return b200008222101;
    }

    public void setB200008222101(BigDecimal b200008222101) {
        this.b200008222101 = b200008222101;
    }

    public BigDecimal getB20000822210101() {
        return b20000822210101;
    }

    public void setB20000822210101(BigDecimal b20000822210101) {
        this.b20000822210101 = b20000822210101;
    }

    public BigDecimal getB20000822210102() {
        return b20000822210102;
    }

    public void setB20000822210102(BigDecimal b20000822210102) {
        this.b20000822210102 = b20000822210102;
    }

    public BigDecimal getB20000822210103() {
        return b20000822210103;
    }

    public void setB20000822210103(BigDecimal b20000822210103) {
        this.b20000822210103 = b20000822210103;
    }

    public BigDecimal getB20000822210104() {
        return b20000822210104;
    }

    public void setB20000822210104(BigDecimal b20000822210104) {
        this.b20000822210104 = b20000822210104;
    }

    public BigDecimal getB20000822210105() {
        return b20000822210105;
    }

    public void setB20000822210105(BigDecimal b20000822210105) {
        this.b20000822210105 = b20000822210105;
    }

    public BigDecimal getB20000822210106() {
        return b20000822210106;
    }

    public void setB20000822210106(BigDecimal b20000822210106) {
        this.b20000822210106 = b20000822210106;
    }

    public BigDecimal getB20000822210107() {
        return b20000822210107;
    }

    public void setB20000822210107(BigDecimal b20000822210107) {
        this.b20000822210107 = b20000822210107;
    }

    public BigDecimal getB20000822210108() {
        return b20000822210108;
    }

    public void setB20000822210108(BigDecimal b20000822210108) {
        this.b20000822210108 = b20000822210108;
    }

    public BigDecimal getB20000822210109() {
        return b20000822210109;
    }

    public void setB20000822210109(BigDecimal b20000822210109) {
        this.b20000822210109 = b20000822210109;
    }

    public BigDecimal getB20000822210110() {
        return b20000822210110;
    }

    public void setB20000822210110(BigDecimal b20000822210110) {
        this.b20000822210110 = b20000822210110;
    }

    public BigDecimal getB200008222102() {
        return b200008222102;
    }

    public void setB200008222102(BigDecimal b200008222102) {
        this.b200008222102 = b200008222102;
    }

    public BigDecimal getB200008222103() {
        return b200008222103;
    }

    public void setB200008222103(BigDecimal b200008222103) {
        this.b200008222103 = b200008222103;
    }

    public BigDecimal getB200008222104() {
        return b200008222104;
    }

    public void setB200008222104(BigDecimal b200008222104) {
        this.b200008222104 = b200008222104;
    }

    public BigDecimal getB200008222105() {
        return b200008222105;
    }

    public void setB200008222105(BigDecimal b200008222105) {
        this.b200008222105 = b200008222105;
    }

    public BigDecimal getB200008222106() {
        return b200008222106;
    }

    public void setB200008222106(BigDecimal b200008222106) {
        this.b200008222106 = b200008222106;
    }

    public BigDecimal getB200008222107() {
        return b200008222107;
    }

    public void setB200008222107(BigDecimal b200008222107) {
        this.b200008222107 = b200008222107;
    }

    public BigDecimal getB200008222108() {
        return b200008222108;
    }

    public void setB200008222108(BigDecimal b200008222108) {
        this.b200008222108 = b200008222108;
    }

    public BigDecimal getB200008222109() {
        return b200008222109;
    }

    public void setB200008222109(BigDecimal b200008222109) {
        this.b200008222109 = b200008222109;
    }

    public BigDecimal getB200008222110() {
        return b200008222110;
    }

    public void setB200008222110(BigDecimal b200008222110) {
        this.b200008222110 = b200008222110;
    }

    public BigDecimal getB200008222111() {
        return b200008222111;
    }

    public void setB200008222111(BigDecimal b200008222111) {
        this.b200008222111 = b200008222111;
    }

    public BigDecimal getB200008222112() {
        return b200008222112;
    }

    public void setB200008222112(BigDecimal b200008222112) {
        this.b200008222112 = b200008222112;
    }

    public BigDecimal getB200008222113() {
        return b200008222113;
    }

    public void setB200008222113(BigDecimal b200008222113) {
        this.b200008222113 = b200008222113;
    }

    public BigDecimal getB200008222114() {
        return b200008222114;
    }

    public void setB200008222114(BigDecimal b200008222114) {
        this.b200008222114 = b200008222114;
    }

    public BigDecimal getB200008222115() {
        return b200008222115;
    }

    public void setB200008222115(BigDecimal b200008222115) {
        this.b200008222115 = b200008222115;
    }

    public BigDecimal getB200008222116() {
        return b200008222116;
    }

    public void setB200008222116(BigDecimal b200008222116) {
        this.b200008222116 = b200008222116;
    }

    public BigDecimal getB200008222117() {
        return b200008222117;
    }

    public void setB200008222117(BigDecimal b200008222117) {
        this.b200008222117 = b200008222117;
    }

    public BigDecimal getB200008222118() {
        return b200008222118;
    }

    public void setB200008222118(BigDecimal b200008222118) {
        this.b200008222118 = b200008222118;
    }

    public BigDecimal getB200008222119() {
        return b200008222119;
    }

    public void setB200008222119(BigDecimal b200008222119) {
        this.b200008222119 = b200008222119;
    }

    public BigDecimal getB200008222120() {
        return b200008222120;
    }

    public void setB200008222120(BigDecimal b200008222120) {
        this.b200008222120 = b200008222120;
    }

    public BigDecimal getB200008222121() {
        return b200008222121;
    }

    public void setB200008222121(BigDecimal b200008222121) {
        this.b200008222121 = b200008222121;
    }

    public BigDecimal getB200008222122() {
        return b200008222122;
    }

    public void setB200008222122(BigDecimal b200008222122) {
        this.b200008222122 = b200008222122;
    }

    public BigDecimal getB200008222123() {
        return b200008222123;
    }

    public void setB200008222123(BigDecimal b200008222123) {
        this.b200008222123 = b200008222123;
    }

    public BigDecimal getB2000082231() {
        return b2000082231;
    }

    public void setB2000082231(BigDecimal b2000082231) {
        this.b2000082231 = b2000082231;
    }

    public BigDecimal getB2000082232() {
        return b2000082232;
    }

    public void setB2000082232(BigDecimal b2000082232) {
        this.b2000082232 = b2000082232;
    }

    public BigDecimal getB2000082241() {
        return b2000082241;
    }

    public void setB2000082241(BigDecimal b2000082241) {
        this.b2000082241 = b2000082241;
    }

    public BigDecimal getB2000082251() {
        return b2000082251;
    }

    public void setB2000082251(BigDecimal b2000082251) {
        this.b2000082251 = b2000082251;
    }

    public BigDecimal getB2000082261() {
        return b2000082261;
    }

    public void setB2000082261(BigDecimal b2000082261) {
        this.b2000082261 = b2000082261;
    }

    public BigDecimal getB2000082311() {
        return b2000082311;
    }

    public void setB2000082311(BigDecimal b2000082311) {
        this.b2000082311 = b2000082311;
    }

    public BigDecimal getB2000082312() {
        return b2000082312;
    }

    public void setB2000082312(BigDecimal b2000082312) {
        this.b2000082312 = b2000082312;
    }

    public BigDecimal getB2000082313() {
        return b2000082313;
    }

    public void setB2000082313(BigDecimal b2000082313) {
        this.b2000082313 = b2000082313;
    }

    public BigDecimal getB2000082314() {
        return b2000082314;
    }

    public void setB2000082314(BigDecimal b2000082314) {
        this.b2000082314 = b2000082314;
    }

    public BigDecimal getB2000082401() {
        return b2000082401;
    }

    public void setB2000082401(BigDecimal b2000082401) {
        this.b2000082401 = b2000082401;
    }

    public BigDecimal getB2000082245() {
        return b2000082245;
    }

    public void setB2000082245(BigDecimal b2000082245) {
        this.b2000082245 = b2000082245;
    }

    public BigDecimal getB2000082501() {
        return b2000082501;
    }

    public void setB2000082501(BigDecimal b2000082501) {
        this.b2000082501 = b2000082501;
    }

    public BigDecimal getB2000082502() {
        return b2000082502;
    }

    public void setB2000082502(BigDecimal b2000082502) {
        this.b2000082502 = b2000082502;
    }

    public BigDecimal getB2000082601() {
        return b2000082601;
    }

    public void setB2000082601(BigDecimal b2000082601) {
        this.b2000082601 = b2000082601;
    }

    public BigDecimal getB2000082602() {
        return b2000082602;
    }

    public void setB2000082602(BigDecimal b2000082602) {
        this.b2000082602 = b2000082602;
    }

    public BigDecimal getB2000082611() {
        return b2000082611;
    }

    public void setB2000082611(BigDecimal b2000082611) {
        this.b2000082611 = b2000082611;
    }

    public BigDecimal getB2000082621() {
        return b2000082621;
    }

    public void setB2000082621(BigDecimal b2000082621) {
        this.b2000082621 = b2000082621;
    }

    public BigDecimal getB2000082701() {
        return b2000082701;
    }

    public void setB2000082701(BigDecimal b2000082701) {
        this.b2000082701 = b2000082701;
    }

    public BigDecimal getB2000082702() {
        return b2000082702;
    }

    public void setB2000082702(BigDecimal b2000082702) {
        this.b2000082702 = b2000082702;
    }

    public BigDecimal getB2000082711() {
        return b2000082711;
    }

    public void setB2000082711(BigDecimal b2000082711) {
        this.b2000082711 = b2000082711;
    }

    public BigDecimal getB2000082801() {
        return b2000082801;
    }

    public void setB2000082801(BigDecimal b2000082801) {
        this.b2000082801 = b2000082801;
    }

    public BigDecimal getB2000082901() {
        return b2000082901;
    }

    public void setB2000082901(BigDecimal b2000082901) {
        this.b2000082901 = b2000082901;
    }

    public BigDecimal getB2000083001() {
        return b2000083001;
    }

    public void setB2000083001(BigDecimal b2000083001) {
        this.b2000083001 = b2000083001;
    }

    public BigDecimal getB2000083002() {
        return b2000083002;
    }

    public void setB2000083002(BigDecimal b2000083002) {
        this.b2000083002 = b2000083002;
    }

    public BigDecimal getB2000083101() {
        return b2000083101;
    }

    public void setB2000083101(BigDecimal b2000083101) {
        this.b2000083101 = b2000083101;
    }

    public BigDecimal getB2000083201() {
        return b2000083201;
    }

    public void setB2000083201(BigDecimal b2000083201) {
        this.b2000083201 = b2000083201;
    }

    public BigDecimal getB2000083202() {
        return b2000083202;
    }

    public void setB2000083202(BigDecimal b2000083202) {
        this.b2000083202 = b2000083202;
    }

    public BigDecimal getB2000084001() {
        return b2000084001;
    }

    public void setB2000084001(BigDecimal b2000084001) {
        this.b2000084001 = b2000084001;
    }

    public BigDecimal getB2000084002() {
        return b2000084002;
    }

    public void setB2000084002(BigDecimal b2000084002) {
        this.b2000084002 = b2000084002;
    }

    public BigDecimal getB2000084003() {
        return b2000084003;
    }

    public void setB2000084003(BigDecimal b2000084003) {
        this.b2000084003 = b2000084003;
    }

    public BigDecimal getB2000084101() {
        return b2000084101;
    }

    public void setB2000084101(BigDecimal b2000084101) {
        this.b2000084101 = b2000084101;
    }

    public BigDecimal getB2000084102() {
        return b2000084102;
    }

    public void setB2000084102(BigDecimal b2000084102) {
        this.b2000084102 = b2000084102;
    }

    public BigDecimal getB2000084103() {
        return b2000084103;
    }

    public void setB2000084103(BigDecimal b2000084103) {
        this.b2000084103 = b2000084103;
    }

    public BigDecimal getB2000084104() {
        return b2000084104;
    }

    public void setB2000084104(BigDecimal b2000084104) {
        this.b2000084104 = b2000084104;
    }

    public BigDecimal getB2000084201() {
        return b2000084201;
    }

    public void setB2000084201(BigDecimal b2000084201) {
        this.b2000084201 = b2000084201;
    }

    public BigDecimal getB2000084301() {
        return b2000084301;
    }

    public void setB2000084301(BigDecimal b2000084301) {
        this.b2000084301 = b2000084301;
    }

    public BigDecimal getB2000085001() {
        return b2000085001;
    }

    public void setB2000085001(BigDecimal b2000085001) {
        this.b2000085001 = b2000085001;
    }

    public BigDecimal getB200008500101() {
        return b200008500101;
    }

    public void setB200008500101(BigDecimal b200008500101) {
        this.b200008500101 = b200008500101;
    }

    public BigDecimal getB200008500102() {
        return b200008500102;
    }

    public void setB200008500102(BigDecimal b200008500102) {
        this.b200008500102 = b200008500102;
    }

    public BigDecimal getB200008500103() {
        return b200008500103;
    }

    public void setB200008500103(BigDecimal b200008500103) {
        this.b200008500103 = b200008500103;
    }

    public BigDecimal getB200008500104() {
        return b200008500104;
    }

    public void setB200008500104(BigDecimal b200008500104) {
        this.b200008500104 = b200008500104;
    }

    public BigDecimal getB200008500105() {
        return b200008500105;
    }

    public void setB200008500105(BigDecimal b200008500105) {
        this.b200008500105 = b200008500105;
    }

    public BigDecimal getB200008500106() {
        return b200008500106;
    }

    public void setB200008500106(BigDecimal b200008500106) {
        this.b200008500106 = b200008500106;
    }

    public BigDecimal getB200008500107() {
        return b200008500107;
    }

    public void setB200008500107(BigDecimal b200008500107) {
        this.b200008500107 = b200008500107;
    }

    public BigDecimal getB200008500108() {
        return b200008500108;
    }

    public void setB200008500108(BigDecimal b200008500108) {
        this.b200008500108 = b200008500108;
    }

    public BigDecimal getB200008500109() {
        return b200008500109;
    }

    public void setB200008500109(BigDecimal b200008500109) {
        this.b200008500109 = b200008500109;
    }

    public BigDecimal getB200008500110() {
        return b200008500110;
    }

    public void setB200008500110(BigDecimal b200008500110) {
        this.b200008500110 = b200008500110;
    }

    public BigDecimal getB200008500111() {
        return b200008500111;
    }

    public void setB200008500111(BigDecimal b200008500111) {
        this.b200008500111 = b200008500111;
    }

    public BigDecimal getB200008500112() {
        return b200008500112;
    }

    public void setB200008500112(BigDecimal b200008500112) {
        this.b200008500112 = b200008500112;
    }

    public BigDecimal getB200008500113() {
        return b200008500113;
    }

    public void setB200008500113(BigDecimal b200008500113) {
        this.b200008500113 = b200008500113;
    }

    public BigDecimal getB200008500114() {
        return b200008500114;
    }

    public void setB200008500114(BigDecimal b200008500114) {
        this.b200008500114 = b200008500114;
    }

    public BigDecimal getB200008500115() {
        return b200008500115;
    }

    public void setB200008500115(BigDecimal b200008500115) {
        this.b200008500115 = b200008500115;
    }

    public BigDecimal getB200008500116() {
        return b200008500116;
    }

    public void setB200008500116(BigDecimal b200008500116) {
        this.b200008500116 = b200008500116;
    }

    public BigDecimal getB200008500117() {
        return b200008500117;
    }

    public void setB200008500117(BigDecimal b200008500117) {
        this.b200008500117 = b200008500117;
    }

    public BigDecimal getB200008500118() {
        return b200008500118;
    }

    public void setB200008500118(BigDecimal b200008500118) {
        this.b200008500118 = b200008500118;
    }

    public BigDecimal getB2000085101() {
        return b2000085101;
    }

    public void setB2000085101(BigDecimal b2000085101) {
        this.b2000085101 = b2000085101;
    }

    public BigDecimal getB200008510101() {
        return b200008510101;
    }

    public void setB200008510101(BigDecimal b200008510101) {
        this.b200008510101 = b200008510101;
    }

    public BigDecimal getB200008510102() {
        return b200008510102;
    }

    public void setB200008510102(BigDecimal b200008510102) {
        this.b200008510102 = b200008510102;
    }

    public BigDecimal getB200008510103() {
        return b200008510103;
    }

    public void setB200008510103(BigDecimal b200008510103) {
        this.b200008510103 = b200008510103;
    }

    public BigDecimal getB200008510104() {
        return b200008510104;
    }

    public void setB200008510104(BigDecimal b200008510104) {
        this.b200008510104 = b200008510104;
    }

    public BigDecimal getB200008510105() {
        return b200008510105;
    }

    public void setB200008510105(BigDecimal b200008510105) {
        this.b200008510105 = b200008510105;
    }

    public BigDecimal getB200008510106() {
        return b200008510106;
    }

    public void setB200008510106(BigDecimal b200008510106) {
        this.b200008510106 = b200008510106;
    }

    public BigDecimal getB200008510107() {
        return b200008510107;
    }

    public void setB200008510107(BigDecimal b200008510107) {
        this.b200008510107 = b200008510107;
    }

    public BigDecimal getB200008510108() {
        return b200008510108;
    }

    public void setB200008510108(BigDecimal b200008510108) {
        this.b200008510108 = b200008510108;
    }

    public BigDecimal getB200008510109() {
        return b200008510109;
    }

    public void setB200008510109(BigDecimal b200008510109) {
        this.b200008510109 = b200008510109;
    }

    public BigDecimal getB200008510110() {
        return b200008510110;
    }

    public void setB200008510110(BigDecimal b200008510110) {
        this.b200008510110 = b200008510110;
    }

    public BigDecimal getB200008510111() {
        return b200008510111;
    }

    public void setB200008510111(BigDecimal b200008510111) {
        this.b200008510111 = b200008510111;
    }

    public BigDecimal getB200008510112() {
        return b200008510112;
    }

    public void setB200008510112(BigDecimal b200008510112) {
        this.b200008510112 = b200008510112;
    }

    public BigDecimal getB200008510113() {
        return b200008510113;
    }

    public void setB200008510113(BigDecimal b200008510113) {
        this.b200008510113 = b200008510113;
    }

    public BigDecimal getB200008510114() {
        return b200008510114;
    }

    public void setB200008510114(BigDecimal b200008510114) {
        this.b200008510114 = b200008510114;
    }

    public BigDecimal getB200008510115() {
        return b200008510115;
    }

    public void setB200008510115(BigDecimal b200008510115) {
        this.b200008510115 = b200008510115;
    }

    public BigDecimal getB200008510116() {
        return b200008510116;
    }

    public void setB200008510116(BigDecimal b200008510116) {
        this.b200008510116 = b200008510116;
    }

    public BigDecimal getB200008510117() {
        return b200008510117;
    }

    public void setB200008510117(BigDecimal b200008510117) {
        this.b200008510117 = b200008510117;
    }

    public BigDecimal getB200008510118() {
        return b200008510118;
    }

    public void setB200008510118(BigDecimal b200008510118) {
        this.b200008510118 = b200008510118;
    }

    public BigDecimal getB200008510119() {
        return b200008510119;
    }

    public void setB200008510119(BigDecimal b200008510119) {
        this.b200008510119 = b200008510119;
    }

    public BigDecimal getB200008510120() {
        return b200008510120;
    }

    public void setB200008510120(BigDecimal b200008510120) {
        this.b200008510120 = b200008510120;
    }

    public BigDecimal getB200008510121() {
        return b200008510121;
    }

    public void setB200008510121(BigDecimal b200008510121) {
        this.b200008510121 = b200008510121;
    }

    public BigDecimal getB200008510122() {
        return b200008510122;
    }

    public void setB200008510122(BigDecimal b200008510122) {
        this.b200008510122 = b200008510122;
    }

    public BigDecimal getB200008510123() {
        return b200008510123;
    }

    public void setB200008510123(BigDecimal b200008510123) {
        this.b200008510123 = b200008510123;
    }

    public BigDecimal getB200008510124() {
        return b200008510124;
    }

    public void setB200008510124(BigDecimal b200008510124) {
        this.b200008510124 = b200008510124;
    }

    public BigDecimal getB200008510125() {
        return b200008510125;
    }

    public void setB200008510125(BigDecimal b200008510125) {
        this.b200008510125 = b200008510125;
    }

    public BigDecimal getB200008510126() {
        return b200008510126;
    }

    public void setB200008510126(BigDecimal b200008510126) {
        this.b200008510126 = b200008510126;
    }

    public BigDecimal getB200008510127() {
        return b200008510127;
    }

    public void setB200008510127(BigDecimal b200008510127) {
        this.b200008510127 = b200008510127;
    }

    public BigDecimal getB200008510128() {
        return b200008510128;
    }

    public void setB200008510128(BigDecimal b200008510128) {
        this.b200008510128 = b200008510128;
    }

    public BigDecimal getB200008510129() {
        return b200008510129;
    }

    public void setB200008510129(BigDecimal b200008510129) {
        this.b200008510129 = b200008510129;
    }

    public BigDecimal getB200008510130() {
        return b200008510130;
    }

    public void setB200008510130(BigDecimal b200008510130) {
        this.b200008510130 = b200008510130;
    }

    public BigDecimal getB200008510131() {
        return b200008510131;
    }

    public void setB200008510131(BigDecimal b200008510131) {
        this.b200008510131 = b200008510131;
    }

    public BigDecimal getB2000085201() {
        return b2000085201;
    }

    public void setB2000085201(BigDecimal b2000085201) {
        this.b2000085201 = b2000085201;
    }

    public BigDecimal getB200008520101() {
        return b200008520101;
    }

    public void setB200008520101(BigDecimal b200008520101) {
        this.b200008520101 = b200008520101;
    }

    public BigDecimal getB200008520102() {
        return b200008520102;
    }

    public void setB200008520102(BigDecimal b200008520102) {
        this.b200008520102 = b200008520102;
    }

    public BigDecimal getB200008520103() {
        return b200008520103;
    }

    public void setB200008520103(BigDecimal b200008520103) {
        this.b200008520103 = b200008520103;
    }

    public BigDecimal getB200008520104() {
        return b200008520104;
    }

    public void setB200008520104(BigDecimal b200008520104) {
        this.b200008520104 = b200008520104;
    }

    public BigDecimal getB200008520105() {
        return b200008520105;
    }

    public void setB200008520105(BigDecimal b200008520105) {
        this.b200008520105 = b200008520105;
    }

    public BigDecimal getB200008520106() {
        return b200008520106;
    }

    public void setB200008520106(BigDecimal b200008520106) {
        this.b200008520106 = b200008520106;
    }

    public BigDecimal getB200008520107() {
        return b200008520107;
    }

    public void setB200008520107(BigDecimal b200008520107) {
        this.b200008520107 = b200008520107;
    }

    public BigDecimal getB200008520108() {
        return b200008520108;
    }

    public void setB200008520108(BigDecimal b200008520108) {
        this.b200008520108 = b200008520108;
    }

    public BigDecimal getB200008520109() {
        return b200008520109;
    }

    public void setB200008520109(BigDecimal b200008520109) {
        this.b200008520109 = b200008520109;
    }

    public BigDecimal getB200008520110() {
        return b200008520110;
    }

    public void setB200008520110(BigDecimal b200008520110) {
        this.b200008520110 = b200008520110;
    }

    public BigDecimal getB200008520111() {
        return b200008520111;
    }

    public void setB200008520111(BigDecimal b200008520111) {
        this.b200008520111 = b200008520111;
    }

    public BigDecimal getB200008520112() {
        return b200008520112;
    }

    public void setB200008520112(BigDecimal b200008520112) {
        this.b200008520112 = b200008520112;
    }

    public BigDecimal getB200008520113() {
        return b200008520113;
    }

    public void setB200008520113(BigDecimal b200008520113) {
        this.b200008520113 = b200008520113;
    }

    public BigDecimal getB200008520114() {
        return b200008520114;
    }

    public void setB200008520114(BigDecimal b200008520114) {
        this.b200008520114 = b200008520114;
    }

    public BigDecimal getB200008520115() {
        return b200008520115;
    }

    public void setB200008520115(BigDecimal b200008520115) {
        this.b200008520115 = b200008520115;
    }

    public BigDecimal getB2000085301() {
        return b2000085301;
    }

    public void setB2000085301(BigDecimal b2000085301) {
        this.b2000085301 = b2000085301;
    }

    public BigDecimal getB200008530101() {
        return b200008530101;
    }

    public void setB200008530101(BigDecimal b200008530101) {
        this.b200008530101 = b200008530101;
    }

    public BigDecimal getB20000853010101() {
        return b20000853010101;
    }

    public void setB20000853010101(BigDecimal b20000853010101) {
        this.b20000853010101 = b20000853010101;
    }

    public BigDecimal getB2000085301010101() {
        return b2000085301010101;
    }

    public void setB2000085301010101(BigDecimal b2000085301010101) {
        this.b2000085301010101 = b2000085301010101;
    }

    public BigDecimal getB2000085301010102() {
        return b2000085301010102;
    }

    public void setB2000085301010102(BigDecimal b2000085301010102) {
        this.b2000085301010102 = b2000085301010102;
    }

    public BigDecimal getB2000085301010103() {
        return b2000085301010103;
    }

    public void setB2000085301010103(BigDecimal b2000085301010103) {
        this.b2000085301010103 = b2000085301010103;
    }

    public BigDecimal getB2000085301010104() {
        return b2000085301010104;
    }

    public void setB2000085301010104(BigDecimal b2000085301010104) {
        this.b2000085301010104 = b2000085301010104;
    }

    public BigDecimal getB2000085301010105() {
        return b2000085301010105;
    }

    public void setB2000085301010105(BigDecimal b2000085301010105) {
        this.b2000085301010105 = b2000085301010105;
    }

    public BigDecimal getB2000085301010106() {
        return b2000085301010106;
    }

    public void setB2000085301010106(BigDecimal b2000085301010106) {
        this.b2000085301010106 = b2000085301010106;
    }

    public BigDecimal getB2000085301010107() {
        return b2000085301010107;
    }

    public void setB2000085301010107(BigDecimal b2000085301010107) {
        this.b2000085301010107 = b2000085301010107;
    }

    public BigDecimal getB2000085301010108() {
        return b2000085301010108;
    }

    public void setB2000085301010108(BigDecimal b2000085301010108) {
        this.b2000085301010108 = b2000085301010108;
    }

    public BigDecimal getB2000085301010109() {
        return b2000085301010109;
    }

    public void setB2000085301010109(BigDecimal b2000085301010109) {
        this.b2000085301010109 = b2000085301010109;
    }

    public BigDecimal getB2000085301010110() {
        return b2000085301010110;
    }

    public void setB2000085301010110(BigDecimal b2000085301010110) {
        this.b2000085301010110 = b2000085301010110;
    }

    public BigDecimal getB2000085301010111() {
        return b2000085301010111;
    }

    public void setB2000085301010111(BigDecimal b2000085301010111) {
        this.b2000085301010111 = b2000085301010111;
    }

    public BigDecimal getB2000085301010112() {
        return b2000085301010112;
    }

    public void setB2000085301010112(BigDecimal b2000085301010112) {
        this.b2000085301010112 = b2000085301010112;
    }

    public BigDecimal getB2000085301010113() {
        return b2000085301010113;
    }

    public void setB2000085301010113(BigDecimal b2000085301010113) {
        this.b2000085301010113 = b2000085301010113;
    }

    public BigDecimal getB20000853010102() {
        return b20000853010102;
    }

    public void setB20000853010102(BigDecimal b20000853010102) {
        this.b20000853010102 = b20000853010102;
    }

    public BigDecimal getB20000853010103() {
        return b20000853010103;
    }

    public void setB20000853010103(BigDecimal b20000853010103) {
        this.b20000853010103 = b20000853010103;
    }

    public BigDecimal getB2000085301010301() {
        return b2000085301010301;
    }

    public void setB2000085301010301(BigDecimal b2000085301010301) {
        this.b2000085301010301 = b2000085301010301;
    }

    public BigDecimal getB2000085301010302() {
        return b2000085301010302;
    }

    public void setB2000085301010302(BigDecimal b2000085301010302) {
        this.b2000085301010302 = b2000085301010302;
    }

    public BigDecimal getB2000085301010303() {
        return b2000085301010303;
    }

    public void setB2000085301010303(BigDecimal b2000085301010303) {
        this.b2000085301010303 = b2000085301010303;
    }

    public BigDecimal getB2000085301010304() {
        return b2000085301010304;
    }

    public void setB2000085301010304(BigDecimal b2000085301010304) {
        this.b2000085301010304 = b2000085301010304;
    }

    public BigDecimal getB2000085301010305() {
        return b2000085301010305;
    }

    public void setB2000085301010305(BigDecimal b2000085301010305) {
        this.b2000085301010305 = b2000085301010305;
    }

    public BigDecimal getB2000085301010306() {
        return b2000085301010306;
    }

    public void setB2000085301010306(BigDecimal b2000085301010306) {
        this.b2000085301010306 = b2000085301010306;
    }

    public BigDecimal getB2000085301010307() {
        return b2000085301010307;
    }

    public void setB2000085301010307(BigDecimal b2000085301010307) {
        this.b2000085301010307 = b2000085301010307;
    }

    public BigDecimal getB2000085301010308() {
        return b2000085301010308;
    }

    public void setB2000085301010308(BigDecimal b2000085301010308) {
        this.b2000085301010308 = b2000085301010308;
    }

    public BigDecimal getB2000085301010309() {
        return b2000085301010309;
    }

    public void setB2000085301010309(BigDecimal b2000085301010309) {
        this.b2000085301010309 = b2000085301010309;
    }

    public BigDecimal getB2000085301010310() {
        return b2000085301010310;
    }

    public void setB2000085301010310(BigDecimal b2000085301010310) {
        this.b2000085301010310 = b2000085301010310;
    }

    public BigDecimal getB2000085301010311() {
        return b2000085301010311;
    }

    public void setB2000085301010311(BigDecimal b2000085301010311) {
        this.b2000085301010311 = b2000085301010311;
    }

    public BigDecimal getB2000085301010312() {
        return b2000085301010312;
    }

    public void setB2000085301010312(BigDecimal b2000085301010312) {
        this.b2000085301010312 = b2000085301010312;
    }

    public BigDecimal getB2000085301010313() {
        return b2000085301010313;
    }

    public void setB2000085301010313(BigDecimal b2000085301010313) {
        this.b2000085301010313 = b2000085301010313;
    }

    public BigDecimal getB20000853010104() {
        return b20000853010104;
    }

    public void setB20000853010104(BigDecimal b20000853010104) {
        this.b20000853010104 = b20000853010104;
    }

    public BigDecimal getB2000085301010401() {
        return b2000085301010401;
    }

    public void setB2000085301010401(BigDecimal b2000085301010401) {
        this.b2000085301010401 = b2000085301010401;
    }

    public BigDecimal getB2000085301010402() {
        return b2000085301010402;
    }

    public void setB2000085301010402(BigDecimal b2000085301010402) {
        this.b2000085301010402 = b2000085301010402;
    }

    public BigDecimal getB20000853010105() {
        return b20000853010105;
    }

    public void setB20000853010105(BigDecimal b20000853010105) {
        this.b20000853010105 = b20000853010105;
    }

    public BigDecimal getB2000085301010501() {
        return b2000085301010501;
    }

    public void setB2000085301010501(BigDecimal b2000085301010501) {
        this.b2000085301010501 = b2000085301010501;
    }

    public BigDecimal getB2000085301010502() {
        return b2000085301010502;
    }

    public void setB2000085301010502(BigDecimal b2000085301010502) {
        this.b2000085301010502 = b2000085301010502;
    }

    public BigDecimal getB20000853010106() {
        return b20000853010106;
    }

    public void setB20000853010106(BigDecimal b20000853010106) {
        this.b20000853010106 = b20000853010106;
    }

    public BigDecimal getB2000085301010601() {
        return b2000085301010601;
    }

    public void setB2000085301010601(BigDecimal b2000085301010601) {
        this.b2000085301010601 = b2000085301010601;
    }

    public BigDecimal getB2000085301010602() {
        return b2000085301010602;
    }

    public void setB2000085301010602(BigDecimal b2000085301010602) {
        this.b2000085301010602 = b2000085301010602;
    }

    public BigDecimal getB2000085301010603() {
        return b2000085301010603;
    }

    public void setB2000085301010603(BigDecimal b2000085301010603) {
        this.b2000085301010603 = b2000085301010603;
    }

    public BigDecimal getB2000085301010604() {
        return b2000085301010604;
    }

    public void setB2000085301010604(BigDecimal b2000085301010604) {
        this.b2000085301010604 = b2000085301010604;
    }

    public BigDecimal getB20000853010107() {
        return b20000853010107;
    }

    public void setB20000853010107(BigDecimal b20000853010107) {
        this.b20000853010107 = b20000853010107;
    }

    public BigDecimal getB2000085301010701() {
        return b2000085301010701;
    }

    public void setB2000085301010701(BigDecimal b2000085301010701) {
        this.b2000085301010701 = b2000085301010701;
    }

    public BigDecimal getB2000085301010702() {
        return b2000085301010702;
    }

    public void setB2000085301010702(BigDecimal b2000085301010702) {
        this.b2000085301010702 = b2000085301010702;
    }

    public BigDecimal getB2000085301010703() {
        return b2000085301010703;
    }

    public void setB2000085301010703(BigDecimal b2000085301010703) {
        this.b2000085301010703 = b2000085301010703;
    }

    public BigDecimal getB20000853010108() {
        return b20000853010108;
    }

    public void setB20000853010108(BigDecimal b20000853010108) {
        this.b20000853010108 = b20000853010108;
    }

    public BigDecimal getB2000085301010801() {
        return b2000085301010801;
    }

    public void setB2000085301010801(BigDecimal b2000085301010801) {
        this.b2000085301010801 = b2000085301010801;
    }

    public BigDecimal getB2000085301010802() {
        return b2000085301010802;
    }

    public void setB2000085301010802(BigDecimal b2000085301010802) {
        this.b2000085301010802 = b2000085301010802;
    }

    public BigDecimal getB2000085301010803() {
        return b2000085301010803;
    }

    public void setB2000085301010803(BigDecimal b2000085301010803) {
        this.b2000085301010803 = b2000085301010803;
    }

    public BigDecimal getB2000085301010804() {
        return b2000085301010804;
    }

    public void setB2000085301010804(BigDecimal b2000085301010804) {
        this.b2000085301010804 = b2000085301010804;
    }

    public BigDecimal getB2000085301010805() {
        return b2000085301010805;
    }

    public void setB2000085301010805(BigDecimal b2000085301010805) {
        this.b2000085301010805 = b2000085301010805;
    }

    public BigDecimal getB2000085301010806() {
        return b2000085301010806;
    }

    public void setB2000085301010806(BigDecimal b2000085301010806) {
        this.b2000085301010806 = b2000085301010806;
    }

    public BigDecimal getB20000853010109() {
        return b20000853010109;
    }

    public void setB20000853010109(BigDecimal b20000853010109) {
        this.b20000853010109 = b20000853010109;
    }

    public BigDecimal getB2000085301010901() {
        return b2000085301010901;
    }

    public void setB2000085301010901(BigDecimal b2000085301010901) {
        this.b2000085301010901 = b2000085301010901;
    }

    public BigDecimal getB2000085301010902() {
        return b2000085301010902;
    }

    public void setB2000085301010902(BigDecimal b2000085301010902) {
        this.b2000085301010902 = b2000085301010902;
    }

    public BigDecimal getB2000085301010903() {
        return b2000085301010903;
    }

    public void setB2000085301010903(BigDecimal b2000085301010903) {
        this.b2000085301010903 = b2000085301010903;
    }

    public BigDecimal getB2000085301010904() {
        return b2000085301010904;
    }

    public void setB2000085301010904(BigDecimal b2000085301010904) {
        this.b2000085301010904 = b2000085301010904;
    }

    public BigDecimal getB2000085301010905() {
        return b2000085301010905;
    }

    public void setB2000085301010905(BigDecimal b2000085301010905) {
        this.b2000085301010905 = b2000085301010905;
    }

    public BigDecimal getB2000085301010906() {
        return b2000085301010906;
    }

    public void setB2000085301010906(BigDecimal b2000085301010906) {
        this.b2000085301010906 = b2000085301010906;
    }

    public BigDecimal getB2000085301010907() {
        return b2000085301010907;
    }

    public void setB2000085301010907(BigDecimal b2000085301010907) {
        this.b2000085301010907 = b2000085301010907;
    }

    public BigDecimal getB20000853010110() {
        return b20000853010110;
    }

    public void setB20000853010110(BigDecimal b20000853010110) {
        this.b20000853010110 = b20000853010110;
    }

    public BigDecimal getB20000853010111() {
        return b20000853010111;
    }

    public void setB20000853010111(BigDecimal b20000853010111) {
        this.b20000853010111 = b20000853010111;
    }

    public BigDecimal getB2000085301011101() {
        return b2000085301011101;
    }

    public void setB2000085301011101(BigDecimal b2000085301011101) {
        this.b2000085301011101 = b2000085301011101;
    }

    public BigDecimal getB2000085301011102() {
        return b2000085301011102;
    }

    public void setB2000085301011102(BigDecimal b2000085301011102) {
        this.b2000085301011102 = b2000085301011102;
    }

    public BigDecimal getB2000085301011103() {
        return b2000085301011103;
    }

    public void setB2000085301011103(BigDecimal b2000085301011103) {
        this.b2000085301011103 = b2000085301011103;
    }

    public BigDecimal getB2000085301011104() {
        return b2000085301011104;
    }

    public void setB2000085301011104(BigDecimal b2000085301011104) {
        this.b2000085301011104 = b2000085301011104;
    }

    public BigDecimal getB20000853010112() {
        return b20000853010112;
    }

    public void setB20000853010112(BigDecimal b20000853010112) {
        this.b20000853010112 = b20000853010112;
    }

    public BigDecimal getB20000853010113() {
        return b20000853010113;
    }

    public void setB20000853010113(BigDecimal b20000853010113) {
        this.b20000853010113 = b20000853010113;
    }

    public BigDecimal getB20000853010114() {
        return b20000853010114;
    }

    public void setB20000853010114(BigDecimal b20000853010114) {
        this.b20000853010114 = b20000853010114;
    }

    public BigDecimal getB20000853010115() {
        return b20000853010115;
    }

    public void setB20000853010115(BigDecimal b20000853010115) {
        this.b20000853010115 = b20000853010115;
    }

    public BigDecimal getB20000853010116() {
        return b20000853010116;
    }

    public void setB20000853010116(BigDecimal b20000853010116) {
        this.b20000853010116 = b20000853010116;
    }

    public BigDecimal getB2000085301011601() {
        return b2000085301011601;
    }

    public void setB2000085301011601(BigDecimal b2000085301011601) {
        this.b2000085301011601 = b2000085301011601;
    }

    public BigDecimal getB2000085301011602() {
        return b2000085301011602;
    }

    public void setB2000085301011602(BigDecimal b2000085301011602) {
        this.b2000085301011602 = b2000085301011602;
    }

    public BigDecimal getB2000085301011603() {
        return b2000085301011603;
    }

    public void setB2000085301011603(BigDecimal b2000085301011603) {
        this.b2000085301011603 = b2000085301011603;
    }

    public BigDecimal getB2000085301011604() {
        return b2000085301011604;
    }

    public void setB2000085301011604(BigDecimal b2000085301011604) {
        this.b2000085301011604 = b2000085301011604;
    }

    public BigDecimal getB2000085301011605() {
        return b2000085301011605;
    }

    public void setB2000085301011605(BigDecimal b2000085301011605) {
        this.b2000085301011605 = b2000085301011605;
    }

    public BigDecimal getB2000085301011606() {
        return b2000085301011606;
    }

    public void setB2000085301011606(BigDecimal b2000085301011606) {
        this.b2000085301011606 = b2000085301011606;
    }

    public BigDecimal getB2000085301011607() {
        return b2000085301011607;
    }

    public void setB2000085301011607(BigDecimal b2000085301011607) {
        this.b2000085301011607 = b2000085301011607;
    }

    public BigDecimal getB2000085301011608() {
        return b2000085301011608;
    }

    public void setB2000085301011608(BigDecimal b2000085301011608) {
        this.b2000085301011608 = b2000085301011608;
    }

    public BigDecimal getB2000085301011609() {
        return b2000085301011609;
    }

    public void setB2000085301011609(BigDecimal b2000085301011609) {
        this.b2000085301011609 = b2000085301011609;
    }

    public BigDecimal getB20000853010117() {
        return b20000853010117;
    }

    public void setB20000853010117(BigDecimal b20000853010117) {
        this.b20000853010117 = b20000853010117;
    }

    public BigDecimal getB2000085301011701() {
        return b2000085301011701;
    }

    public void setB2000085301011701(BigDecimal b2000085301011701) {
        this.b2000085301011701 = b2000085301011701;
    }

    public BigDecimal getB2000085301011702() {
        return b2000085301011702;
    }

    public void setB2000085301011702(BigDecimal b2000085301011702) {
        this.b2000085301011702 = b2000085301011702;
    }

    public BigDecimal getB2000085301011703() {
        return b2000085301011703;
    }

    public void setB2000085301011703(BigDecimal b2000085301011703) {
        this.b2000085301011703 = b2000085301011703;
    }

    public BigDecimal getB2000085301011704() {
        return b2000085301011704;
    }

    public void setB2000085301011704(BigDecimal b2000085301011704) {
        this.b2000085301011704 = b2000085301011704;
    }

    public BigDecimal getB20000853010118() {
        return b20000853010118;
    }

    public void setB20000853010118(BigDecimal b20000853010118) {
        this.b20000853010118 = b20000853010118;
    }

    public BigDecimal getB20000853010119() {
        return b20000853010119;
    }

    public void setB20000853010119(BigDecimal b20000853010119) {
        this.b20000853010119 = b20000853010119;
    }

    public BigDecimal getB20000853010120() {
        return b20000853010120;
    }

    public void setB20000853010120(BigDecimal b20000853010120) {
        this.b20000853010120 = b20000853010120;
    }

    public BigDecimal getB20000853010121() {
        return b20000853010121;
    }

    public void setB20000853010121(BigDecimal b20000853010121) {
        this.b20000853010121 = b20000853010121;
    }

    public BigDecimal getB20000853010122() {
        return b20000853010122;
    }

    public void setB20000853010122(BigDecimal b20000853010122) {
        this.b20000853010122 = b20000853010122;
    }

    public BigDecimal getB20000853010123() {
        return b20000853010123;
    }

    public void setB20000853010123(BigDecimal b20000853010123) {
        this.b20000853010123 = b20000853010123;
    }

    public BigDecimal getB20000853010124() {
        return b20000853010124;
    }

    public void setB20000853010124(BigDecimal b20000853010124) {
        this.b20000853010124 = b20000853010124;
    }

    public BigDecimal getB20000853010125() {
        return b20000853010125;
    }

    public void setB20000853010125(BigDecimal b20000853010125) {
        this.b20000853010125 = b20000853010125;
    }

    public BigDecimal getB20000853010126() {
        return b20000853010126;
    }

    public void setB20000853010126(BigDecimal b20000853010126) {
        this.b20000853010126 = b20000853010126;
    }

    public BigDecimal getB2000085301012601() {
        return b2000085301012601;
    }

    public void setB2000085301012601(BigDecimal b2000085301012601) {
        this.b2000085301012601 = b2000085301012601;
    }

    public BigDecimal getB2000085301012602() {
        return b2000085301012602;
    }

    public void setB2000085301012602(BigDecimal b2000085301012602) {
        this.b2000085301012602 = b2000085301012602;
    }

    public BigDecimal getB2000085301012603() {
        return b2000085301012603;
    }

    public void setB2000085301012603(BigDecimal b2000085301012603) {
        this.b2000085301012603 = b2000085301012603;
    }

    public BigDecimal getB2000085301012604() {
        return b2000085301012604;
    }

    public void setB2000085301012604(BigDecimal b2000085301012604) {
        this.b2000085301012604 = b2000085301012604;
    }

    public BigDecimal getB200008530102() {
        return b200008530102;
    }

    public void setB200008530102(BigDecimal b200008530102) {
        this.b200008530102 = b200008530102;
    }

    public BigDecimal getB20000853010201() {
        return b20000853010201;
    }

    public void setB20000853010201(BigDecimal b20000853010201) {
        this.b20000853010201 = b20000853010201;
    }

    public BigDecimal getB2000085301020101() {
        return b2000085301020101;
    }

    public void setB2000085301020101(BigDecimal b2000085301020101) {
        this.b2000085301020101 = b2000085301020101;
    }

    public BigDecimal getB2000085301020102() {
        return b2000085301020102;
    }

    public void setB2000085301020102(BigDecimal b2000085301020102) {
        this.b2000085301020102 = b2000085301020102;
    }

    public BigDecimal getB2000085301020103() {
        return b2000085301020103;
    }

    public void setB2000085301020103(BigDecimal b2000085301020103) {
        this.b2000085301020103 = b2000085301020103;
    }

    public BigDecimal getB2000085301020104() {
        return b2000085301020104;
    }

    public void setB2000085301020104(BigDecimal b2000085301020104) {
        this.b2000085301020104 = b2000085301020104;
    }

    public BigDecimal getB2000085301020105() {
        return b2000085301020105;
    }

    public void setB2000085301020105(BigDecimal b2000085301020105) {
        this.b2000085301020105 = b2000085301020105;
    }

    public BigDecimal getB2000085301020106() {
        return b2000085301020106;
    }

    public void setB2000085301020106(BigDecimal b2000085301020106) {
        this.b2000085301020106 = b2000085301020106;
    }

    public BigDecimal getB2000085301020107() {
        return b2000085301020107;
    }

    public void setB2000085301020107(BigDecimal b2000085301020107) {
        this.b2000085301020107 = b2000085301020107;
    }

    public BigDecimal getB2000085301020108() {
        return b2000085301020108;
    }

    public void setB2000085301020108(BigDecimal b2000085301020108) {
        this.b2000085301020108 = b2000085301020108;
    }

    public BigDecimal getB2000085301020109() {
        return b2000085301020109;
    }

    public void setB2000085301020109(BigDecimal b2000085301020109) {
        this.b2000085301020109 = b2000085301020109;
    }

    public BigDecimal getB2000085301020110() {
        return b2000085301020110;
    }

    public void setB2000085301020110(BigDecimal b2000085301020110) {
        this.b2000085301020110 = b2000085301020110;
    }

    public BigDecimal getB2000085301020111() {
        return b2000085301020111;
    }

    public void setB2000085301020111(BigDecimal b2000085301020111) {
        this.b2000085301020111 = b2000085301020111;
    }

    public BigDecimal getB2000085301020112() {
        return b2000085301020112;
    }

    public void setB2000085301020112(BigDecimal b2000085301020112) {
        this.b2000085301020112 = b2000085301020112;
    }

    public BigDecimal getB2000085301020113() {
        return b2000085301020113;
    }

    public void setB2000085301020113(BigDecimal b2000085301020113) {
        this.b2000085301020113 = b2000085301020113;
    }

    public BigDecimal getB20000853010202() {
        return b20000853010202;
    }

    public void setB20000853010202(BigDecimal b20000853010202) {
        this.b20000853010202 = b20000853010202;
    }

    public BigDecimal getB20000853010203() {
        return b20000853010203;
    }

    public void setB20000853010203(BigDecimal b20000853010203) {
        this.b20000853010203 = b20000853010203;
    }

    public BigDecimal getB2000085301020301() {
        return b2000085301020301;
    }

    public void setB2000085301020301(BigDecimal b2000085301020301) {
        this.b2000085301020301 = b2000085301020301;
    }

    public BigDecimal getB2000085301020302() {
        return b2000085301020302;
    }

    public void setB2000085301020302(BigDecimal b2000085301020302) {
        this.b2000085301020302 = b2000085301020302;
    }

    public BigDecimal getB2000085301020303() {
        return b2000085301020303;
    }

    public void setB2000085301020303(BigDecimal b2000085301020303) {
        this.b2000085301020303 = b2000085301020303;
    }

    public BigDecimal getB2000085301020304() {
        return b2000085301020304;
    }

    public void setB2000085301020304(BigDecimal b2000085301020304) {
        this.b2000085301020304 = b2000085301020304;
    }

    public BigDecimal getB2000085301020305() {
        return b2000085301020305;
    }

    public void setB2000085301020305(BigDecimal b2000085301020305) {
        this.b2000085301020305 = b2000085301020305;
    }

    public BigDecimal getB2000085301020306() {
        return b2000085301020306;
    }

    public void setB2000085301020306(BigDecimal b2000085301020306) {
        this.b2000085301020306 = b2000085301020306;
    }

    public BigDecimal getB2000085301020307() {
        return b2000085301020307;
    }

    public void setB2000085301020307(BigDecimal b2000085301020307) {
        this.b2000085301020307 = b2000085301020307;
    }

    public BigDecimal getB2000085301020308() {
        return b2000085301020308;
    }

    public void setB2000085301020308(BigDecimal b2000085301020308) {
        this.b2000085301020308 = b2000085301020308;
    }

    public BigDecimal getB2000085301020309() {
        return b2000085301020309;
    }

    public void setB2000085301020309(BigDecimal b2000085301020309) {
        this.b2000085301020309 = b2000085301020309;
    }

    public BigDecimal getB2000085301020310() {
        return b2000085301020310;
    }

    public void setB2000085301020310(BigDecimal b2000085301020310) {
        this.b2000085301020310 = b2000085301020310;
    }

    public BigDecimal getB2000085301020311() {
        return b2000085301020311;
    }

    public void setB2000085301020311(BigDecimal b2000085301020311) {
        this.b2000085301020311 = b2000085301020311;
    }

    public BigDecimal getB2000085301020312() {
        return b2000085301020312;
    }

    public void setB2000085301020312(BigDecimal b2000085301020312) {
        this.b2000085301020312 = b2000085301020312;
    }

    public BigDecimal getB2000085301020313() {
        return b2000085301020313;
    }

    public void setB2000085301020313(BigDecimal b2000085301020313) {
        this.b2000085301020313 = b2000085301020313;
    }

    public BigDecimal getB20000853010204() {
        return b20000853010204;
    }

    public void setB20000853010204(BigDecimal b20000853010204) {
        this.b20000853010204 = b20000853010204;
    }

    public BigDecimal getB2000085301020401() {
        return b2000085301020401;
    }

    public void setB2000085301020401(BigDecimal b2000085301020401) {
        this.b2000085301020401 = b2000085301020401;
    }

    public BigDecimal getB2000085301020402() {
        return b2000085301020402;
    }

    public void setB2000085301020402(BigDecimal b2000085301020402) {
        this.b2000085301020402 = b2000085301020402;
    }

    public BigDecimal getB20000853010205() {
        return b20000853010205;
    }

    public void setB20000853010205(BigDecimal b20000853010205) {
        this.b20000853010205 = b20000853010205;
    }

    public BigDecimal getB2000085301020501() {
        return b2000085301020501;
    }

    public void setB2000085301020501(BigDecimal b2000085301020501) {
        this.b2000085301020501 = b2000085301020501;
    }

    public BigDecimal getB2000085301020502() {
        return b2000085301020502;
    }

    public void setB2000085301020502(BigDecimal b2000085301020502) {
        this.b2000085301020502 = b2000085301020502;
    }

    public BigDecimal getB20000853010206() {
        return b20000853010206;
    }

    public void setB20000853010206(BigDecimal b20000853010206) {
        this.b20000853010206 = b20000853010206;
    }

    public BigDecimal getB2000085301020601() {
        return b2000085301020601;
    }

    public void setB2000085301020601(BigDecimal b2000085301020601) {
        this.b2000085301020601 = b2000085301020601;
    }

    public BigDecimal getB2000085301020602() {
        return b2000085301020602;
    }

    public void setB2000085301020602(BigDecimal b2000085301020602) {
        this.b2000085301020602 = b2000085301020602;
    }

    public BigDecimal getB2000085301020603() {
        return b2000085301020603;
    }

    public void setB2000085301020603(BigDecimal b2000085301020603) {
        this.b2000085301020603 = b2000085301020603;
    }

    public BigDecimal getB2000085301020604() {
        return b2000085301020604;
    }

    public void setB2000085301020604(BigDecimal b2000085301020604) {
        this.b2000085301020604 = b2000085301020604;
    }

    public BigDecimal getB20000853010207() {
        return b20000853010207;
    }

    public void setB20000853010207(BigDecimal b20000853010207) {
        this.b20000853010207 = b20000853010207;
    }

    public BigDecimal getB2000085301020701() {
        return b2000085301020701;
    }

    public void setB2000085301020701(BigDecimal b2000085301020701) {
        this.b2000085301020701 = b2000085301020701;
    }

    public BigDecimal getB2000085301020702() {
        return b2000085301020702;
    }

    public void setB2000085301020702(BigDecimal b2000085301020702) {
        this.b2000085301020702 = b2000085301020702;
    }

    public BigDecimal getB2000085301020703() {
        return b2000085301020703;
    }

    public void setB2000085301020703(BigDecimal b2000085301020703) {
        this.b2000085301020703 = b2000085301020703;
    }

    public BigDecimal getB20000853010208() {
        return b20000853010208;
    }

    public void setB20000853010208(BigDecimal b20000853010208) {
        this.b20000853010208 = b20000853010208;
    }

    public BigDecimal getB2000085301020801() {
        return b2000085301020801;
    }

    public void setB2000085301020801(BigDecimal b2000085301020801) {
        this.b2000085301020801 = b2000085301020801;
    }

    public BigDecimal getB2000085301020802() {
        return b2000085301020802;
    }

    public void setB2000085301020802(BigDecimal b2000085301020802) {
        this.b2000085301020802 = b2000085301020802;
    }

    public BigDecimal getB2000085301020803() {
        return b2000085301020803;
    }

    public void setB2000085301020803(BigDecimal b2000085301020803) {
        this.b2000085301020803 = b2000085301020803;
    }

    public BigDecimal getB2000085301020804() {
        return b2000085301020804;
    }

    public void setB2000085301020804(BigDecimal b2000085301020804) {
        this.b2000085301020804 = b2000085301020804;
    }

    public BigDecimal getB2000085301020805() {
        return b2000085301020805;
    }

    public void setB2000085301020805(BigDecimal b2000085301020805) {
        this.b2000085301020805 = b2000085301020805;
    }

    public BigDecimal getB2000085301020806() {
        return b2000085301020806;
    }

    public void setB2000085301020806(BigDecimal b2000085301020806) {
        this.b2000085301020806 = b2000085301020806;
    }

    public BigDecimal getB20000853010209() {
        return b20000853010209;
    }

    public void setB20000853010209(BigDecimal b20000853010209) {
        this.b20000853010209 = b20000853010209;
    }

    public BigDecimal getB2000085301020901() {
        return b2000085301020901;
    }

    public void setB2000085301020901(BigDecimal b2000085301020901) {
        this.b2000085301020901 = b2000085301020901;
    }

    public BigDecimal getB2000085301020902() {
        return b2000085301020902;
    }

    public void setB2000085301020902(BigDecimal b2000085301020902) {
        this.b2000085301020902 = b2000085301020902;
    }

    public BigDecimal getB2000085301020903() {
        return b2000085301020903;
    }

    public void setB2000085301020903(BigDecimal b2000085301020903) {
        this.b2000085301020903 = b2000085301020903;
    }

    public BigDecimal getB2000085301020904() {
        return b2000085301020904;
    }

    public void setB2000085301020904(BigDecimal b2000085301020904) {
        this.b2000085301020904 = b2000085301020904;
    }

    public BigDecimal getB2000085301020905() {
        return b2000085301020905;
    }

    public void setB2000085301020905(BigDecimal b2000085301020905) {
        this.b2000085301020905 = b2000085301020905;
    }

    public BigDecimal getB2000085301020906() {
        return b2000085301020906;
    }

    public void setB2000085301020906(BigDecimal b2000085301020906) {
        this.b2000085301020906 = b2000085301020906;
    }

    public BigDecimal getB2000085301020907() {
        return b2000085301020907;
    }

    public void setB2000085301020907(BigDecimal b2000085301020907) {
        this.b2000085301020907 = b2000085301020907;
    }

    public BigDecimal getB20000853010210() {
        return b20000853010210;
    }

    public void setB20000853010210(BigDecimal b20000853010210) {
        this.b20000853010210 = b20000853010210;
    }

    public BigDecimal getB20000853010211() {
        return b20000853010211;
    }

    public void setB20000853010211(BigDecimal b20000853010211) {
        this.b20000853010211 = b20000853010211;
    }

    public BigDecimal getB2000085301021101() {
        return b2000085301021101;
    }

    public void setB2000085301021101(BigDecimal b2000085301021101) {
        this.b2000085301021101 = b2000085301021101;
    }

    public BigDecimal getB2000085301021102() {
        return b2000085301021102;
    }

    public void setB2000085301021102(BigDecimal b2000085301021102) {
        this.b2000085301021102 = b2000085301021102;
    }

    public BigDecimal getB2000085301021103() {
        return b2000085301021103;
    }

    public void setB2000085301021103(BigDecimal b2000085301021103) {
        this.b2000085301021103 = b2000085301021103;
    }

    public BigDecimal getB2000085301021104() {
        return b2000085301021104;
    }

    public void setB2000085301021104(BigDecimal b2000085301021104) {
        this.b2000085301021104 = b2000085301021104;
    }

    public BigDecimal getB20000853010212() {
        return b20000853010212;
    }

    public void setB20000853010212(BigDecimal b20000853010212) {
        this.b20000853010212 = b20000853010212;
    }

    public BigDecimal getB20000853010213() {
        return b20000853010213;
    }

    public void setB20000853010213(BigDecimal b20000853010213) {
        this.b20000853010213 = b20000853010213;
    }

    public BigDecimal getB20000853010214() {
        return b20000853010214;
    }

    public void setB20000853010214(BigDecimal b20000853010214) {
        this.b20000853010214 = b20000853010214;
    }

    public BigDecimal getB20000853010215() {
        return b20000853010215;
    }

    public void setB20000853010215(BigDecimal b20000853010215) {
        this.b20000853010215 = b20000853010215;
    }

    public BigDecimal getB20000853010216() {
        return b20000853010216;
    }

    public void setB20000853010216(BigDecimal b20000853010216) {
        this.b20000853010216 = b20000853010216;
    }

    public BigDecimal getB2000085301021601() {
        return b2000085301021601;
    }

    public void setB2000085301021601(BigDecimal b2000085301021601) {
        this.b2000085301021601 = b2000085301021601;
    }

    public BigDecimal getB2000085301021602() {
        return b2000085301021602;
    }

    public void setB2000085301021602(BigDecimal b2000085301021602) {
        this.b2000085301021602 = b2000085301021602;
    }

    public BigDecimal getB2000085301021603() {
        return b2000085301021603;
    }

    public void setB2000085301021603(BigDecimal b2000085301021603) {
        this.b2000085301021603 = b2000085301021603;
    }

    public BigDecimal getB2000085301021604() {
        return b2000085301021604;
    }

    public void setB2000085301021604(BigDecimal b2000085301021604) {
        this.b2000085301021604 = b2000085301021604;
    }

    public BigDecimal getB2000085301021605() {
        return b2000085301021605;
    }

    public void setB2000085301021605(BigDecimal b2000085301021605) {
        this.b2000085301021605 = b2000085301021605;
    }

    public BigDecimal getB2000085301021606() {
        return b2000085301021606;
    }

    public void setB2000085301021606(BigDecimal b2000085301021606) {
        this.b2000085301021606 = b2000085301021606;
    }

    public BigDecimal getB2000085301021607() {
        return b2000085301021607;
    }

    public void setB2000085301021607(BigDecimal b2000085301021607) {
        this.b2000085301021607 = b2000085301021607;
    }

    public BigDecimal getB2000085301021608() {
        return b2000085301021608;
    }

    public void setB2000085301021608(BigDecimal b2000085301021608) {
        this.b2000085301021608 = b2000085301021608;
    }

    public BigDecimal getB2000085301021609() {
        return b2000085301021609;
    }

    public void setB2000085301021609(BigDecimal b2000085301021609) {
        this.b2000085301021609 = b2000085301021609;
    }

    public BigDecimal getB20000853010217() {
        return b20000853010217;
    }

    public void setB20000853010217(BigDecimal b20000853010217) {
        this.b20000853010217 = b20000853010217;
    }

    public BigDecimal getB2000085301021701() {
        return b2000085301021701;
    }

    public void setB2000085301021701(BigDecimal b2000085301021701) {
        this.b2000085301021701 = b2000085301021701;
    }

    public BigDecimal getB2000085301021702() {
        return b2000085301021702;
    }

    public void setB2000085301021702(BigDecimal b2000085301021702) {
        this.b2000085301021702 = b2000085301021702;
    }

    public BigDecimal getB2000085301021703() {
        return b2000085301021703;
    }

    public void setB2000085301021703(BigDecimal b2000085301021703) {
        this.b2000085301021703 = b2000085301021703;
    }

    public BigDecimal getB2000085301021704() {
        return b2000085301021704;
    }

    public void setB2000085301021704(BigDecimal b2000085301021704) {
        this.b2000085301021704 = b2000085301021704;
    }

    public BigDecimal getB20000853010218() {
        return b20000853010218;
    }

    public void setB20000853010218(BigDecimal b20000853010218) {
        this.b20000853010218 = b20000853010218;
    }

    public BigDecimal getB20000853010219() {
        return b20000853010219;
    }

    public void setB20000853010219(BigDecimal b20000853010219) {
        this.b20000853010219 = b20000853010219;
    }

    public BigDecimal getB20000853010220() {
        return b20000853010220;
    }

    public void setB20000853010220(BigDecimal b20000853010220) {
        this.b20000853010220 = b20000853010220;
    }

    public BigDecimal getB20000853010221() {
        return b20000853010221;
    }

    public void setB20000853010221(BigDecimal b20000853010221) {
        this.b20000853010221 = b20000853010221;
    }

    public BigDecimal getB20000853010222() {
        return b20000853010222;
    }

    public void setB20000853010222(BigDecimal b20000853010222) {
        this.b20000853010222 = b20000853010222;
    }

    public BigDecimal getB20000853010223() {
        return b20000853010223;
    }

    public void setB20000853010223(BigDecimal b20000853010223) {
        this.b20000853010223 = b20000853010223;
    }

    public BigDecimal getB20000853010224() {
        return b20000853010224;
    }

    public void setB20000853010224(BigDecimal b20000853010224) {
        this.b20000853010224 = b20000853010224;
    }

    public BigDecimal getB20000853010225() {
        return b20000853010225;
    }

    public void setB20000853010225(BigDecimal b20000853010225) {
        this.b20000853010225 = b20000853010225;
    }

    public BigDecimal getB20000853010226() {
        return b20000853010226;
    }

    public void setB20000853010226(BigDecimal b20000853010226) {
        this.b20000853010226 = b20000853010226;
    }

    public BigDecimal getB2000085301022601() {
        return b2000085301022601;
    }

    public void setB2000085301022601(BigDecimal b2000085301022601) {
        this.b2000085301022601 = b2000085301022601;
    }

    public BigDecimal getB2000085301022602() {
        return b2000085301022602;
    }

    public void setB2000085301022602(BigDecimal b2000085301022602) {
        this.b2000085301022602 = b2000085301022602;
    }

    public BigDecimal getB2000085301022603() {
        return b2000085301022603;
    }

    public void setB2000085301022603(BigDecimal b2000085301022603) {
        this.b2000085301022603 = b2000085301022603;
    }

    public BigDecimal getB2000085301022604() {
        return b2000085301022604;
    }

    public void setB2000085301022604(BigDecimal b2000085301022604) {
        this.b2000085301022604 = b2000085301022604;
    }

    public BigDecimal getB2000085401() {
        return b2000085401;
    }

    public void setB2000085401(BigDecimal b2000085401) {
        this.b2000085401 = b2000085401;
    }

    public BigDecimal getB2000085402() {
        return b2000085402;
    }

    public void setB2000085402(BigDecimal b2000085402) {
        this.b2000085402 = b2000085402;
    }

    public BigDecimal getB2000085403() {
        return b2000085403;
    }

    public void setB2000085403(BigDecimal b2000085403) {
        this.b2000085403 = b2000085403;
    }

    public BigDecimal getB2000085501() {
        return b2000085501;
    }

    public void setB2000085501(BigDecimal b2000085501) {
        this.b2000085501 = b2000085501;
    }

    public BigDecimal getB200008550101() {
        return b200008550101;
    }

    public void setB200008550101(BigDecimal b200008550101) {
        this.b200008550101 = b200008550101;
    }

    public BigDecimal getB200008550102() {
        return b200008550102;
    }

    public void setB200008550102(BigDecimal b200008550102) {
        this.b200008550102 = b200008550102;
    }

    public BigDecimal getB200008550103() {
        return b200008550103;
    }

    public void setB200008550103(BigDecimal b200008550103) {
        this.b200008550103 = b200008550103;
    }

    public BigDecimal getB200008550104() {
        return b200008550104;
    }

    public void setB200008550104(BigDecimal b200008550104) {
        this.b200008550104 = b200008550104;
    }

    public BigDecimal getB200008550105() {
        return b200008550105;
    }

    public void setB200008550105(BigDecimal b200008550105) {
        this.b200008550105 = b200008550105;
    }

    public BigDecimal getB200008550106() {
        return b200008550106;
    }

    public void setB200008550106(BigDecimal b200008550106) {
        this.b200008550106 = b200008550106;
    }

    public BigDecimal getB200008550107() {
        return b200008550107;
    }

    public void setB200008550107(BigDecimal b200008550107) {
        this.b200008550107 = b200008550107;
    }

    public BigDecimal getB200008550108() {
        return b200008550108;
    }

    public void setB200008550108(BigDecimal b200008550108) {
        this.b200008550108 = b200008550108;
    }

    public BigDecimal getB200008550109() {
        return b200008550109;
    }

    public void setB200008550109(BigDecimal b200008550109) {
        this.b200008550109 = b200008550109;
    }

    public BigDecimal getB200008550110() {
        return b200008550110;
    }

    public void setB200008550110(BigDecimal b200008550110) {
        this.b200008550110 = b200008550110;
    }

    public BigDecimal getB200008550111() {
        return b200008550111;
    }

    public void setB200008550111(BigDecimal b200008550111) {
        this.b200008550111 = b200008550111;
    }

    public BigDecimal getB200008550112() {
        return b200008550112;
    }

    public void setB200008550112(BigDecimal b200008550112) {
        this.b200008550112 = b200008550112;
    }

    public BigDecimal getB200008550113() {
        return b200008550113;
    }

    public void setB200008550113(BigDecimal b200008550113) {
        this.b200008550113 = b200008550113;
    }

    public BigDecimal getB200008550114() {
        return b200008550114;
    }

    public void setB200008550114(BigDecimal b200008550114) {
        this.b200008550114 = b200008550114;
    }

    public BigDecimal getB200008550115() {
        return b200008550115;
    }

    public void setB200008550115(BigDecimal b200008550115) {
        this.b200008550115 = b200008550115;
    }

    public BigDecimal getB2000085502() {
        return b2000085502;
    }

    public void setB2000085502(BigDecimal b2000085502) {
        this.b2000085502 = b2000085502;
    }

    public BigDecimal getB2000085503() {
        return b2000085503;
    }

    public void setB2000085503(BigDecimal b2000085503) {
        this.b2000085503 = b2000085503;
    }

    public BigDecimal getB2000085504() {
        return b2000085504;
    }

    public void setB2000085504(BigDecimal b2000085504) {
        this.b2000085504 = b2000085504;
    }

    public BigDecimal getB2000086001() {
        return b2000086001;
    }

    public void setB2000086001(BigDecimal b2000086001) {
        this.b2000086001 = b2000086001;
    }

    public BigDecimal getB200008600101() {
        return b200008600101;
    }

    public void setB200008600101(BigDecimal b200008600101) {
        this.b200008600101 = b200008600101;
    }

    public BigDecimal getB200008600102() {
        return b200008600102;
    }

    public void setB200008600102(BigDecimal b200008600102) {
        this.b200008600102 = b200008600102;
    }

    public BigDecimal getB200008600103() {
        return b200008600103;
    }

    public void setB200008600103(BigDecimal b200008600103) {
        this.b200008600103 = b200008600103;
    }

    public BigDecimal getB200008600104() {
        return b200008600104;
    }

    public void setB200008600104(BigDecimal b200008600104) {
        this.b200008600104 = b200008600104;
    }

    public BigDecimal getB200008600105() {
        return b200008600105;
    }

    public void setB200008600105(BigDecimal b200008600105) {
        this.b200008600105 = b200008600105;
    }

    public BigDecimal getB2000086011() {
        return b2000086011;
    }

    public void setB2000086011(BigDecimal b2000086011) {
        this.b2000086011 = b2000086011;
    }

    public BigDecimal getB2000086021() {
        return b2000086021;
    }

    public void setB2000086021(BigDecimal b2000086021) {
        this.b2000086021 = b2000086021;
    }

    public BigDecimal getB2000086031() {
        return b2000086031;
    }

    public void setB2000086031(BigDecimal b2000086031) {
        this.b2000086031 = b2000086031;
    }

    public BigDecimal getB2000086041() {
        return b2000086041;
    }

    public void setB2000086041(BigDecimal b2000086041) {
        this.b2000086041 = b2000086041;
    }

    public BigDecimal getB2000086051() {
        return b2000086051;
    }

    public void setB2000086051(BigDecimal b2000086051) {
        this.b2000086051 = b2000086051;
    }

    public BigDecimal getB200008605101() {
        return b200008605101;
    }

    public void setB200008605101(BigDecimal b200008605101) {
        this.b200008605101 = b200008605101;
    }

    public BigDecimal getB200008605102() {
        return b200008605102;
    }

    public void setB200008605102(BigDecimal b200008605102) {
        this.b200008605102 = b200008605102;
    }

    public BigDecimal getB200008605103() {
        return b200008605103;
    }

    public void setB200008605103(BigDecimal b200008605103) {
        this.b200008605103 = b200008605103;
    }

    public BigDecimal getB200008605104() {
        return b200008605104;
    }

    public void setB200008605104(BigDecimal b200008605104) {
        this.b200008605104 = b200008605104;
    }

    public BigDecimal getB200008605105() {
        return b200008605105;
    }

    public void setB200008605105(BigDecimal b200008605105) {
        this.b200008605105 = b200008605105;
    }

    public BigDecimal getB2000086061() {
        return b2000086061;
    }

    public void setB2000086061(BigDecimal b2000086061) {
        this.b2000086061 = b2000086061;
    }

    public BigDecimal getB2000086101() {
        return b2000086101;
    }

    public void setB2000086101(BigDecimal b2000086101) {
        this.b2000086101 = b2000086101;
    }

    public BigDecimal getB2000086111() {
        return b2000086111;
    }

    public void setB2000086111(BigDecimal b2000086111) {
        this.b2000086111 = b2000086111;
    }

    public BigDecimal getB200008611101() {
        return b200008611101;
    }

    public void setB200008611101(BigDecimal b200008611101) {
        this.b200008611101 = b200008611101;
    }

    public BigDecimal getB20000861110101() {
        return b20000861110101;
    }

    public void setB20000861110101(BigDecimal b20000861110101) {
        this.b20000861110101 = b20000861110101;
    }

    public BigDecimal getB2000086111010101() {
        return b2000086111010101;
    }

    public void setB2000086111010101(BigDecimal b2000086111010101) {
        this.b2000086111010101 = b2000086111010101;
    }

    public BigDecimal getB2000086111010102() {
        return b2000086111010102;
    }

    public void setB2000086111010102(BigDecimal b2000086111010102) {
        this.b2000086111010102 = b2000086111010102;
    }

    public BigDecimal getB20000861110102() {
        return b20000861110102;
    }

    public void setB20000861110102(BigDecimal b20000861110102) {
        this.b20000861110102 = b20000861110102;
    }

    public BigDecimal getB2000086111010201() {
        return b2000086111010201;
    }

    public void setB2000086111010201(BigDecimal b2000086111010201) {
        this.b2000086111010201 = b2000086111010201;
    }

    public BigDecimal getB2000086111010202() {
        return b2000086111010202;
    }

    public void setB2000086111010202(BigDecimal b2000086111010202) {
        this.b2000086111010202 = b2000086111010202;
    }

    public BigDecimal getB20000861110103() {
        return b20000861110103;
    }

    public void setB20000861110103(BigDecimal b20000861110103) {
        this.b20000861110103 = b20000861110103;
    }

    public BigDecimal getB2000086111010301() {
        return b2000086111010301;
    }

    public void setB2000086111010301(BigDecimal b2000086111010301) {
        this.b2000086111010301 = b2000086111010301;
    }

    public BigDecimal getB2000086111010302() {
        return b2000086111010302;
    }

    public void setB2000086111010302(BigDecimal b2000086111010302) {
        this.b2000086111010302 = b2000086111010302;
    }

    public BigDecimal getB20000861110104() {
        return b20000861110104;
    }

    public void setB20000861110104(BigDecimal b20000861110104) {
        this.b20000861110104 = b20000861110104;
    }

    public BigDecimal getB2000086111010401() {
        return b2000086111010401;
    }

    public void setB2000086111010401(BigDecimal b2000086111010401) {
        this.b2000086111010401 = b2000086111010401;
    }

    public BigDecimal getB2000086111010402() {
        return b2000086111010402;
    }

    public void setB2000086111010402(BigDecimal b2000086111010402) {
        this.b2000086111010402 = b2000086111010402;
    }

    public BigDecimal getB20000861110105() {
        return b20000861110105;
    }

    public void setB20000861110105(BigDecimal b20000861110105) {
        this.b20000861110105 = b20000861110105;
    }

    public BigDecimal getB2000086111010501() {
        return b2000086111010501;
    }

    public void setB2000086111010501(BigDecimal b2000086111010501) {
        this.b2000086111010501 = b2000086111010501;
    }

    public BigDecimal getB2000086111010502() {
        return b2000086111010502;
    }

    public void setB2000086111010502(BigDecimal b2000086111010502) {
        this.b2000086111010502 = b2000086111010502;
    }

    public BigDecimal getB20000861110106() {
        return b20000861110106;
    }

    public void setB20000861110106(BigDecimal b20000861110106) {
        this.b20000861110106 = b20000861110106;
    }

    public BigDecimal getB2000086111010601() {
        return b2000086111010601;
    }

    public void setB2000086111010601(BigDecimal b2000086111010601) {
        this.b2000086111010601 = b2000086111010601;
    }

    public BigDecimal getB2000086111010602() {
        return b2000086111010602;
    }

    public void setB2000086111010602(BigDecimal b2000086111010602) {
        this.b2000086111010602 = b2000086111010602;
    }

    public BigDecimal getB20000861110107() {
        return b20000861110107;
    }

    public void setB20000861110107(BigDecimal b20000861110107) {
        this.b20000861110107 = b20000861110107;
    }

    public BigDecimal getB2000086111010701() {
        return b2000086111010701;
    }

    public void setB2000086111010701(BigDecimal b2000086111010701) {
        this.b2000086111010701 = b2000086111010701;
    }

    public BigDecimal getB2000086111010702() {
        return b2000086111010702;
    }

    public void setB2000086111010702(BigDecimal b2000086111010702) {
        this.b2000086111010702 = b2000086111010702;
    }

    public BigDecimal getB20000861110108() {
        return b20000861110108;
    }

    public void setB20000861110108(BigDecimal b20000861110108) {
        this.b20000861110108 = b20000861110108;
    }

    public BigDecimal getB2000086111010801() {
        return b2000086111010801;
    }

    public void setB2000086111010801(BigDecimal b2000086111010801) {
        this.b2000086111010801 = b2000086111010801;
    }

    public BigDecimal getB2000086111010802() {
        return b2000086111010802;
    }

    public void setB2000086111010802(BigDecimal b2000086111010802) {
        this.b2000086111010802 = b2000086111010802;
    }

    public BigDecimal getB200008611102() {
        return b200008611102;
    }

    public void setB200008611102(BigDecimal b200008611102) {
        this.b200008611102 = b200008611102;
    }

    public BigDecimal getB20000861110201() {
        return b20000861110201;
    }

    public void setB20000861110201(BigDecimal b20000861110201) {
        this.b20000861110201 = b20000861110201;
    }

    public BigDecimal getB2000086111020101() {
        return b2000086111020101;
    }

    public void setB2000086111020101(BigDecimal b2000086111020101) {
        this.b2000086111020101 = b2000086111020101;
    }

    public BigDecimal getB2000086111020102() {
        return b2000086111020102;
    }

    public void setB2000086111020102(BigDecimal b2000086111020102) {
        this.b2000086111020102 = b2000086111020102;
    }

    public BigDecimal getB20000861110202() {
        return b20000861110202;
    }

    public void setB20000861110202(BigDecimal b20000861110202) {
        this.b20000861110202 = b20000861110202;
    }

    public BigDecimal getB2000086111020201() {
        return b2000086111020201;
    }

    public void setB2000086111020201(BigDecimal b2000086111020201) {
        this.b2000086111020201 = b2000086111020201;
    }

    public BigDecimal getB2000086111020202() {
        return b2000086111020202;
    }

    public void setB2000086111020202(BigDecimal b2000086111020202) {
        this.b2000086111020202 = b2000086111020202;
    }

    public BigDecimal getB20000861110203() {
        return b20000861110203;
    }

    public void setB20000861110203(BigDecimal b20000861110203) {
        this.b20000861110203 = b20000861110203;
    }

    public BigDecimal getB2000086111020301() {
        return b2000086111020301;
    }

    public void setB2000086111020301(BigDecimal b2000086111020301) {
        this.b2000086111020301 = b2000086111020301;
    }

    public BigDecimal getB2000086111020302() {
        return b2000086111020302;
    }

    public void setB2000086111020302(BigDecimal b2000086111020302) {
        this.b2000086111020302 = b2000086111020302;
    }

    public BigDecimal getB20000861110204() {
        return b20000861110204;
    }

    public void setB20000861110204(BigDecimal b20000861110204) {
        this.b20000861110204 = b20000861110204;
    }

    public BigDecimal getB2000086111020401() {
        return b2000086111020401;
    }

    public void setB2000086111020401(BigDecimal b2000086111020401) {
        this.b2000086111020401 = b2000086111020401;
    }

    public BigDecimal getB2000086111020402() {
        return b2000086111020402;
    }

    public void setB2000086111020402(BigDecimal b2000086111020402) {
        this.b2000086111020402 = b2000086111020402;
    }

    public BigDecimal getB20000861110205() {
        return b20000861110205;
    }

    public void setB20000861110205(BigDecimal b20000861110205) {
        this.b20000861110205 = b20000861110205;
    }

    public BigDecimal getB2000086111020501() {
        return b2000086111020501;
    }

    public void setB2000086111020501(BigDecimal b2000086111020501) {
        this.b2000086111020501 = b2000086111020501;
    }

    public BigDecimal getB2000086111020502() {
        return b2000086111020502;
    }

    public void setB2000086111020502(BigDecimal b2000086111020502) {
        this.b2000086111020502 = b2000086111020502;
    }

    public BigDecimal getB20000861110206() {
        return b20000861110206;
    }

    public void setB20000861110206(BigDecimal b20000861110206) {
        this.b20000861110206 = b20000861110206;
    }

    public BigDecimal getB2000086111020601() {
        return b2000086111020601;
    }

    public void setB2000086111020601(BigDecimal b2000086111020601) {
        this.b2000086111020601 = b2000086111020601;
    }

    public BigDecimal getB2000086111020602() {
        return b2000086111020602;
    }

    public void setB2000086111020602(BigDecimal b2000086111020602) {
        this.b2000086111020602 = b2000086111020602;
    }

    public BigDecimal getB20000861110207() {
        return b20000861110207;
    }

    public void setB20000861110207(BigDecimal b20000861110207) {
        this.b20000861110207 = b20000861110207;
    }

    public BigDecimal getB2000086111020701() {
        return b2000086111020701;
    }

    public void setB2000086111020701(BigDecimal b2000086111020701) {
        this.b2000086111020701 = b2000086111020701;
    }

    public BigDecimal getB2000086111020702() {
        return b2000086111020702;
    }

    public void setB2000086111020702(BigDecimal b2000086111020702) {
        this.b2000086111020702 = b2000086111020702;
    }

    public BigDecimal getB20000861110208() {
        return b20000861110208;
    }

    public void setB20000861110208(BigDecimal b20000861110208) {
        this.b20000861110208 = b20000861110208;
    }

    public BigDecimal getB2000086111020801() {
        return b2000086111020801;
    }

    public void setB2000086111020801(BigDecimal b2000086111020801) {
        this.b2000086111020801 = b2000086111020801;
    }

    public BigDecimal getB2000086111020802() {
        return b2000086111020802;
    }

    public void setB2000086111020802(BigDecimal b2000086111020802) {
        this.b2000086111020802 = b2000086111020802;
    }

    public BigDecimal getB200008611103() {
        return b200008611103;
    }

    public void setB200008611103(BigDecimal b200008611103) {
        this.b200008611103 = b200008611103;
    }

    public BigDecimal getB20000861110301() {
        return b20000861110301;
    }

    public void setB20000861110301(BigDecimal b20000861110301) {
        this.b20000861110301 = b20000861110301;
    }

    public BigDecimal getB2000086111030101() {
        return b2000086111030101;
    }

    public void setB2000086111030101(BigDecimal b2000086111030101) {
        this.b2000086111030101 = b2000086111030101;
    }

    public BigDecimal getB2000086111030102() {
        return b2000086111030102;
    }

    public void setB2000086111030102(BigDecimal b2000086111030102) {
        this.b2000086111030102 = b2000086111030102;
    }

    public BigDecimal getB20000861110302() {
        return b20000861110302;
    }

    public void setB20000861110302(BigDecimal b20000861110302) {
        this.b20000861110302 = b20000861110302;
    }

    public BigDecimal getB2000086111030201() {
        return b2000086111030201;
    }

    public void setB2000086111030201(BigDecimal b2000086111030201) {
        this.b2000086111030201 = b2000086111030201;
    }

    public BigDecimal getB2000086111030202() {
        return b2000086111030202;
    }

    public void setB2000086111030202(BigDecimal b2000086111030202) {
        this.b2000086111030202 = b2000086111030202;
    }

    public BigDecimal getB20000861110303() {
        return b20000861110303;
    }

    public void setB20000861110303(BigDecimal b20000861110303) {
        this.b20000861110303 = b20000861110303;
    }

    public BigDecimal getB2000086111030301() {
        return b2000086111030301;
    }

    public void setB2000086111030301(BigDecimal b2000086111030301) {
        this.b2000086111030301 = b2000086111030301;
    }

    public BigDecimal getB2000086111030302() {
        return b2000086111030302;
    }

    public void setB2000086111030302(BigDecimal b2000086111030302) {
        this.b2000086111030302 = b2000086111030302;
    }

    public BigDecimal getB20000861110304() {
        return b20000861110304;
    }

    public void setB20000861110304(BigDecimal b20000861110304) {
        this.b20000861110304 = b20000861110304;
    }

    public BigDecimal getB2000086111030401() {
        return b2000086111030401;
    }

    public void setB2000086111030401(BigDecimal b2000086111030401) {
        this.b2000086111030401 = b2000086111030401;
    }

    public BigDecimal getB2000086111030402() {
        return b2000086111030402;
    }

    public void setB2000086111030402(BigDecimal b2000086111030402) {
        this.b2000086111030402 = b2000086111030402;
    }

    public BigDecimal getB20000861110305() {
        return b20000861110305;
    }

    public void setB20000861110305(BigDecimal b20000861110305) {
        this.b20000861110305 = b20000861110305;
    }

    public BigDecimal getB2000086111030501() {
        return b2000086111030501;
    }

    public void setB2000086111030501(BigDecimal b2000086111030501) {
        this.b2000086111030501 = b2000086111030501;
    }

    public BigDecimal getB2000086111030502() {
        return b2000086111030502;
    }

    public void setB2000086111030502(BigDecimal b2000086111030502) {
        this.b2000086111030502 = b2000086111030502;
    }

    public BigDecimal getB200008611104() {
        return b200008611104;
    }

    public void setB200008611104(BigDecimal b200008611104) {
        this.b200008611104 = b200008611104;
    }

    public BigDecimal getB20000861110401() {
        return b20000861110401;
    }

    public void setB20000861110401(BigDecimal b20000861110401) {
        this.b20000861110401 = b20000861110401;
    }

    public BigDecimal getB20000861110402() {
        return b20000861110402;
    }

    public void setB20000861110402(BigDecimal b20000861110402) {
        this.b20000861110402 = b20000861110402;
    }

    public BigDecimal getB200008611105() {
        return b200008611105;
    }

    public void setB200008611105(BigDecimal b200008611105) {
        this.b200008611105 = b200008611105;
    }

    public BigDecimal getB20000861110501() {
        return b20000861110501;
    }

    public void setB20000861110501(BigDecimal b20000861110501) {
        this.b20000861110501 = b20000861110501;
    }

    public BigDecimal getB20000861110502() {
        return b20000861110502;
    }

    public void setB20000861110502(BigDecimal b20000861110502) {
        this.b20000861110502 = b20000861110502;
    }

    public BigDecimal getB200008611106() {
        return b200008611106;
    }

    public void setB200008611106(BigDecimal b200008611106) {
        this.b200008611106 = b200008611106;
    }

    public BigDecimal getB20000861110601() {
        return b20000861110601;
    }

    public void setB20000861110601(BigDecimal b20000861110601) {
        this.b20000861110601 = b20000861110601;
    }

    public BigDecimal getB20000861110602() {
        return b20000861110602;
    }

    public void setB20000861110602(BigDecimal b20000861110602) {
        this.b20000861110602 = b20000861110602;
    }

    public BigDecimal getB200008611107() {
        return b200008611107;
    }

    public void setB200008611107(BigDecimal b200008611107) {
        this.b200008611107 = b200008611107;
    }

    public BigDecimal getB20000861110701() {
        return b20000861110701;
    }

    public void setB20000861110701(BigDecimal b20000861110701) {
        this.b20000861110701 = b20000861110701;
    }

    public BigDecimal getB2000086111070101() {
        return b2000086111070101;
    }

    public void setB2000086111070101(BigDecimal b2000086111070101) {
        this.b2000086111070101 = b2000086111070101;
    }

    public BigDecimal getB2000086111070102() {
        return b2000086111070102;
    }

    public void setB2000086111070102(BigDecimal b2000086111070102) {
        this.b2000086111070102 = b2000086111070102;
    }

    public BigDecimal getB20000861110702() {
        return b20000861110702;
    }

    public void setB20000861110702(BigDecimal b20000861110702) {
        this.b20000861110702 = b20000861110702;
    }

    public BigDecimal getB2000086111070201() {
        return b2000086111070201;
    }

    public void setB2000086111070201(BigDecimal b2000086111070201) {
        this.b2000086111070201 = b2000086111070201;
    }

    public BigDecimal getB2000086111070202() {
        return b2000086111070202;
    }

    public void setB2000086111070202(BigDecimal b2000086111070202) {
        this.b2000086111070202 = b2000086111070202;
    }

    public BigDecimal getB20000861110703() {
        return b20000861110703;
    }

    public void setB20000861110703(BigDecimal b20000861110703) {
        this.b20000861110703 = b20000861110703;
    }

    public BigDecimal getB2000086111070301() {
        return b2000086111070301;
    }

    public void setB2000086111070301(BigDecimal b2000086111070301) {
        this.b2000086111070301 = b2000086111070301;
    }

    public BigDecimal getB2000086111070302() {
        return b2000086111070302;
    }

    public void setB2000086111070302(BigDecimal b2000086111070302) {
        this.b2000086111070302 = b2000086111070302;
    }

    public BigDecimal getB20000861110704() {
        return b20000861110704;
    }

    public void setB20000861110704(BigDecimal b20000861110704) {
        this.b20000861110704 = b20000861110704;
    }

    public BigDecimal getB2000086111070401() {
        return b2000086111070401;
    }

    public void setB2000086111070401(BigDecimal b2000086111070401) {
        this.b2000086111070401 = b2000086111070401;
    }

    public BigDecimal getB2000086111070402() {
        return b2000086111070402;
    }

    public void setB2000086111070402(BigDecimal b2000086111070402) {
        this.b2000086111070402 = b2000086111070402;
    }

    public BigDecimal getB20000861110705() {
        return b20000861110705;
    }

    public void setB20000861110705(BigDecimal b20000861110705) {
        this.b20000861110705 = b20000861110705;
    }

    public BigDecimal getB2000086111070501() {
        return b2000086111070501;
    }

    public void setB2000086111070501(BigDecimal b2000086111070501) {
        this.b2000086111070501 = b2000086111070501;
    }

    public BigDecimal getB2000086111070502() {
        return b2000086111070502;
    }

    public void setB2000086111070502(BigDecimal b2000086111070502) {
        this.b2000086111070502 = b2000086111070502;
    }

    public BigDecimal getB200008611108() {
        return b200008611108;
    }

    public void setB200008611108(BigDecimal b200008611108) {
        this.b200008611108 = b200008611108;
    }

    public BigDecimal getB20000861110801() {
        return b20000861110801;
    }

    public void setB20000861110801(BigDecimal b20000861110801) {
        this.b20000861110801 = b20000861110801;
    }

    public BigDecimal getB2000086111080101() {
        return b2000086111080101;
    }

    public void setB2000086111080101(BigDecimal b2000086111080101) {
        this.b2000086111080101 = b2000086111080101;
    }

    public BigDecimal getB2000086111080102() {
        return b2000086111080102;
    }

    public void setB2000086111080102(BigDecimal b2000086111080102) {
        this.b2000086111080102 = b2000086111080102;
    }

    public BigDecimal getB20000861110802() {
        return b20000861110802;
    }

    public void setB20000861110802(BigDecimal b20000861110802) {
        this.b20000861110802 = b20000861110802;
    }

    public BigDecimal getB2000086111080201() {
        return b2000086111080201;
    }

    public void setB2000086111080201(BigDecimal b2000086111080201) {
        this.b2000086111080201 = b2000086111080201;
    }

    public BigDecimal getB2000086111080202() {
        return b2000086111080202;
    }

    public void setB2000086111080202(BigDecimal b2000086111080202) {
        this.b2000086111080202 = b2000086111080202;
    }

    public BigDecimal getB20000861110803() {
        return b20000861110803;
    }

    public void setB20000861110803(BigDecimal b20000861110803) {
        this.b20000861110803 = b20000861110803;
    }

    public BigDecimal getB2000086111080301() {
        return b2000086111080301;
    }

    public void setB2000086111080301(BigDecimal b2000086111080301) {
        this.b2000086111080301 = b2000086111080301;
    }

    public BigDecimal getB2000086111080302() {
        return b2000086111080302;
    }

    public void setB2000086111080302(BigDecimal b2000086111080302) {
        this.b2000086111080302 = b2000086111080302;
    }

    public BigDecimal getB20000861110804() {
        return b20000861110804;
    }

    public void setB20000861110804(BigDecimal b20000861110804) {
        this.b20000861110804 = b20000861110804;
    }

    public BigDecimal getB2000086111080401() {
        return b2000086111080401;
    }

    public void setB2000086111080401(BigDecimal b2000086111080401) {
        this.b2000086111080401 = b2000086111080401;
    }

    public BigDecimal getB2000086111080402() {
        return b2000086111080402;
    }

    public void setB2000086111080402(BigDecimal b2000086111080402) {
        this.b2000086111080402 = b2000086111080402;
    }

    public BigDecimal getB20000861110805() {
        return b20000861110805;
    }

    public void setB20000861110805(BigDecimal b20000861110805) {
        this.b20000861110805 = b20000861110805;
    }

    public BigDecimal getB2000086111080501() {
        return b2000086111080501;
    }

    public void setB2000086111080501(BigDecimal b2000086111080501) {
        this.b2000086111080501 = b2000086111080501;
    }

    public BigDecimal getB2000086111080502() {
        return b2000086111080502;
    }

    public void setB2000086111080502(BigDecimal b2000086111080502) {
        this.b2000086111080502 = b2000086111080502;
    }

    public BigDecimal getB20000861110806() {
        return b20000861110806;
    }

    public void setB20000861110806(BigDecimal b20000861110806) {
        this.b20000861110806 = b20000861110806;
    }

    public BigDecimal getB2000086111080601() {
        return b2000086111080601;
    }

    public void setB2000086111080601(BigDecimal b2000086111080601) {
        this.b2000086111080601 = b2000086111080601;
    }

    public BigDecimal getB2000086111080602() {
        return b2000086111080602;
    }

    public void setB2000086111080602(BigDecimal b2000086111080602) {
        this.b2000086111080602 = b2000086111080602;
    }

    public BigDecimal getB20000861110807() {
        return b20000861110807;
    }

    public void setB20000861110807(BigDecimal b20000861110807) {
        this.b20000861110807 = b20000861110807;
    }

    public BigDecimal getB2000086111080701() {
        return b2000086111080701;
    }

    public void setB2000086111080701(BigDecimal b2000086111080701) {
        this.b2000086111080701 = b2000086111080701;
    }

    public BigDecimal getB2000086111080702() {
        return b2000086111080702;
    }

    public void setB2000086111080702(BigDecimal b2000086111080702) {
        this.b2000086111080702 = b2000086111080702;
    }

    public BigDecimal getB20000861110808() {
        return b20000861110808;
    }

    public void setB20000861110808(BigDecimal b20000861110808) {
        this.b20000861110808 = b20000861110808;
    }

    public BigDecimal getB2000086111080801() {
        return b2000086111080801;
    }

    public void setB2000086111080801(BigDecimal b2000086111080801) {
        this.b2000086111080801 = b2000086111080801;
    }

    public BigDecimal getB2000086111080802() {
        return b2000086111080802;
    }

    public void setB2000086111080802(BigDecimal b2000086111080802) {
        this.b2000086111080802 = b2000086111080802;
    }

    public BigDecimal getB2000086115() {
        return b2000086115;
    }

    public void setB2000086115(BigDecimal b2000086115) {
        this.b2000086115 = b2000086115;
    }

    public BigDecimal getB200008611501() {
        return b200008611501;
    }

    public void setB200008611501(BigDecimal b200008611501) {
        this.b200008611501 = b200008611501;
    }

    public BigDecimal getB200008611502() {
        return b200008611502;
    }

    public void setB200008611502(BigDecimal b200008611502) {
        this.b200008611502 = b200008611502;
    }

    public BigDecimal getB200008611503() {
        return b200008611503;
    }

    public void setB200008611503(BigDecimal b200008611503) {
        this.b200008611503 = b200008611503;
    }

    public BigDecimal getB200008611504() {
        return b200008611504;
    }

    public void setB200008611504(BigDecimal b200008611504) {
        this.b200008611504 = b200008611504;
    }

    public BigDecimal getB200008611505() {
        return b200008611505;
    }

    public void setB200008611505(BigDecimal b200008611505) {
        this.b200008611505 = b200008611505;
    }

    public BigDecimal getB200008611506() {
        return b200008611506;
    }

    public void setB200008611506(BigDecimal b200008611506) {
        this.b200008611506 = b200008611506;
    }

    public BigDecimal getB200008611507() {
        return b200008611507;
    }

    public void setB200008611507(BigDecimal b200008611507) {
        this.b200008611507 = b200008611507;
    }

    public BigDecimal getB2000086117() {
        return b2000086117;
    }

    public void setB2000086117(BigDecimal b2000086117) {
        this.b2000086117 = b2000086117;
    }

    public BigDecimal getB2000086201() {
        return b2000086201;
    }

    public void setB2000086201(BigDecimal b2000086201) {
        this.b2000086201 = b2000086201;
    }

    public BigDecimal getB2000086202() {
        return b2000086202;
    }

    public void setB2000086202(BigDecimal b2000086202) {
        this.b2000086202 = b2000086202;
    }

    public BigDecimal getB2000086203() {
        return b2000086203;
    }

    public void setB2000086203(BigDecimal b2000086203) {
        this.b2000086203 = b2000086203;
    }

    public BigDecimal getB2000086301() {
        return b2000086301;
    }

    public void setB2000086301(BigDecimal b2000086301) {
        this.b2000086301 = b2000086301;
    }

    public BigDecimal getB200008630101() {
        return b200008630101;
    }

    public void setB200008630101(BigDecimal b200008630101) {
        this.b200008630101 = b200008630101;
    }

    public BigDecimal getB200008630102() {
        return b200008630102;
    }

    public void setB200008630102(BigDecimal b200008630102) {
        this.b200008630102 = b200008630102;
    }

    public BigDecimal getB200008630103() {
        return b200008630103;
    }

    public void setB200008630103(BigDecimal b200008630103) {
        this.b200008630103 = b200008630103;
    }

    public BigDecimal getB200008630104() {
        return b200008630104;
    }

    public void setB200008630104(BigDecimal b200008630104) {
        this.b200008630104 = b200008630104;
    }

    public BigDecimal getB200008630105() {
        return b200008630105;
    }

    public void setB200008630105(BigDecimal b200008630105) {
        this.b200008630105 = b200008630105;
    }

    public BigDecimal getB200008630106() {
        return b200008630106;
    }

    public void setB200008630106(BigDecimal b200008630106) {
        this.b200008630106 = b200008630106;
    }

    public BigDecimal getB200008630107() {
        return b200008630107;
    }

    public void setB200008630107(BigDecimal b200008630107) {
        this.b200008630107 = b200008630107;
    }

    public BigDecimal getB200008630108() {
        return b200008630108;
    }

    public void setB200008630108(BigDecimal b200008630108) {
        this.b200008630108 = b200008630108;
    }

    public BigDecimal getB200008630109() {
        return b200008630109;
    }

    public void setB200008630109(BigDecimal b200008630109) {
        this.b200008630109 = b200008630109;
    }

    public BigDecimal getB2000086401() {
        return b2000086401;
    }

    public void setB2000086401(BigDecimal b2000086401) {
        this.b2000086401 = b2000086401;
    }

    public BigDecimal getB200008640101() {
        return b200008640101;
    }

    public void setB200008640101(BigDecimal b200008640101) {
        this.b200008640101 = b200008640101;
    }

    public BigDecimal getB200008640102() {
        return b200008640102;
    }

    public void setB200008640102(BigDecimal b200008640102) {
        this.b200008640102 = b200008640102;
    }

    public BigDecimal getB200008640103() {
        return b200008640103;
    }

    public void setB200008640103(BigDecimal b200008640103) {
        this.b200008640103 = b200008640103;
    }

    public BigDecimal getB200008640104() {
        return b200008640104;
    }

    public void setB200008640104(BigDecimal b200008640104) {
        this.b200008640104 = b200008640104;
    }

    public BigDecimal getB200008640105() {
        return b200008640105;
    }

    public void setB200008640105(BigDecimal b200008640105) {
        this.b200008640105 = b200008640105;
    }

    public BigDecimal getB200008640106() {
        return b200008640106;
    }

    public void setB200008640106(BigDecimal b200008640106) {
        this.b200008640106 = b200008640106;
    }

    public BigDecimal getB200008640107() {
        return b200008640107;
    }

    public void setB200008640107(BigDecimal b200008640107) {
        this.b200008640107 = b200008640107;
    }

    public BigDecimal getB200008640108() {
        return b200008640108;
    }

    public void setB200008640108(BigDecimal b200008640108) {
        this.b200008640108 = b200008640108;
    }

    public BigDecimal getB200008640109() {
        return b200008640109;
    }

    public void setB200008640109(BigDecimal b200008640109) {
        this.b200008640109 = b200008640109;
    }

    public BigDecimal getB200008640110() {
        return b200008640110;
    }

    public void setB200008640110(BigDecimal b200008640110) {
        this.b200008640110 = b200008640110;
    }

    public BigDecimal getB200008640111() {
        return b200008640111;
    }

    public void setB200008640111(BigDecimal b200008640111) {
        this.b200008640111 = b200008640111;
    }

    public BigDecimal getB200008640112() {
        return b200008640112;
    }

    public void setB200008640112(BigDecimal b200008640112) {
        this.b200008640112 = b200008640112;
    }

    public BigDecimal getB200008640113() {
        return b200008640113;
    }

    public void setB200008640113(BigDecimal b200008640113) {
        this.b200008640113 = b200008640113;
    }

    public BigDecimal getB200008640114() {
        return b200008640114;
    }

    public void setB200008640114(BigDecimal b200008640114) {
        this.b200008640114 = b200008640114;
    }

    public BigDecimal getB200008640115() {
        return b200008640115;
    }

    public void setB200008640115(BigDecimal b200008640115) {
        this.b200008640115 = b200008640115;
    }

    public BigDecimal getB200008640116() {
        return b200008640116;
    }

    public void setB200008640116(BigDecimal b200008640116) {
        this.b200008640116 = b200008640116;
    }

    public BigDecimal getB200008640117() {
        return b200008640117;
    }

    public void setB200008640117(BigDecimal b200008640117) {
        this.b200008640117 = b200008640117;
    }

    public BigDecimal getB200008640118() {
        return b200008640118;
    }

    public void setB200008640118(BigDecimal b200008640118) {
        this.b200008640118 = b200008640118;
    }

    public BigDecimal getB200008640119() {
        return b200008640119;
    }

    public void setB200008640119(BigDecimal b200008640119) {
        this.b200008640119 = b200008640119;
    }

    public BigDecimal getB2000086402() {
        return b2000086402;
    }

    public void setB2000086402(BigDecimal b2000086402) {
        this.b2000086402 = b2000086402;
    }

    public BigDecimal getB200008640201() {
        return b200008640201;
    }

    public void setB200008640201(BigDecimal b200008640201) {
        this.b200008640201 = b200008640201;
    }

    public BigDecimal getB200008640202() {
        return b200008640202;
    }

    public void setB200008640202(BigDecimal b200008640202) {
        this.b200008640202 = b200008640202;
    }

    public BigDecimal getB200008640203() {
        return b200008640203;
    }

    public void setB200008640203(BigDecimal b200008640203) {
        this.b200008640203 = b200008640203;
    }

    public BigDecimal getB200008640204() {
        return b200008640204;
    }

    public void setB200008640204(BigDecimal b200008640204) {
        this.b200008640204 = b200008640204;
    }

    public BigDecimal getB200008640205() {
        return b200008640205;
    }

    public void setB200008640205(BigDecimal b200008640205) {
        this.b200008640205 = b200008640205;
    }

    public BigDecimal getB2000086403() {
        return b2000086403;
    }

    public void setB2000086403(BigDecimal b2000086403) {
        this.b2000086403 = b2000086403;
    }

    public BigDecimal getB2000086411() {
        return b2000086411;
    }

    public void setB2000086411(BigDecimal b2000086411) {
        this.b2000086411 = b2000086411;
    }

    public BigDecimal getB2000086421() {
        return b2000086421;
    }

    public void setB2000086421(BigDecimal b2000086421) {
        this.b2000086421 = b2000086421;
    }

    public BigDecimal getB2000086501() {
        return b2000086501;
    }

    public void setB2000086501(BigDecimal b2000086501) {
        this.b2000086501 = b2000086501;
    }

    public BigDecimal getB2000086502() {
        return b2000086502;
    }

    public void setB2000086502(BigDecimal b2000086502) {
        this.b2000086502 = b2000086502;
    }

    public BigDecimal getB2000086511() {
        return b2000086511;
    }

    public void setB2000086511(BigDecimal b2000086511) {
        this.b2000086511 = b2000086511;
    }

    public BigDecimal getB2000086521() {
        return b2000086521;
    }

    public void setB2000086521(BigDecimal b2000086521) {
        this.b2000086521 = b2000086521;
    }

    public BigDecimal getB2000086531() {
        return b2000086531;
    }

    public void setB2000086531(BigDecimal b2000086531) {
        this.b2000086531 = b2000086531;
    }

    public BigDecimal getB2000086541() {
        return b2000086541;
    }

    public void setB2000086541(BigDecimal b2000086541) {
        this.b2000086541 = b2000086541;
    }

    public BigDecimal getB2000086542() {
        return b2000086542;
    }

    public void setB2000086542(BigDecimal b2000086542) {
        this.b2000086542 = b2000086542;
    }

    public BigDecimal getB2000086601() {
        return b2000086601;
    }

    public void setB2000086601(BigDecimal b2000086601) {
        this.b2000086601 = b2000086601;
    }

    public BigDecimal getB200008660101() {
        return b200008660101;
    }

    public void setB200008660101(BigDecimal b200008660101) {
        this.b200008660101 = b200008660101;
    }

    public BigDecimal getB200008660102() {
        return b200008660102;
    }

    public void setB200008660102(BigDecimal b200008660102) {
        this.b200008660102 = b200008660102;
    }

    public BigDecimal getB200008660103() {
        return b200008660103;
    }

    public void setB200008660103(BigDecimal b200008660103) {
        this.b200008660103 = b200008660103;
    }

    public BigDecimal getB200008660104() {
        return b200008660104;
    }

    public void setB200008660104(BigDecimal b200008660104) {
        this.b200008660104 = b200008660104;
    }

    public BigDecimal getB200008660105() {
        return b200008660105;
    }

    public void setB200008660105(BigDecimal b200008660105) {
        this.b200008660105 = b200008660105;
    }

    public BigDecimal getB200008660106() {
        return b200008660106;
    }

    public void setB200008660106(BigDecimal b200008660106) {
        this.b200008660106 = b200008660106;
    }

    public BigDecimal getB200008660107() {
        return b200008660107;
    }

    public void setB200008660107(BigDecimal b200008660107) {
        this.b200008660107 = b200008660107;
    }

    public BigDecimal getB200008660108() {
        return b200008660108;
    }

    public void setB200008660108(BigDecimal b200008660108) {
        this.b200008660108 = b200008660108;
    }

    public BigDecimal getB200008660109() {
        return b200008660109;
    }

    public void setB200008660109(BigDecimal b200008660109) {
        this.b200008660109 = b200008660109;
    }

    public BigDecimal getB200008660110() {
        return b200008660110;
    }

    public void setB200008660110(BigDecimal b200008660110) {
        this.b200008660110 = b200008660110;
    }

    public BigDecimal getB200008660111() {
        return b200008660111;
    }

    public void setB200008660111(BigDecimal b200008660111) {
        this.b200008660111 = b200008660111;
    }

    public BigDecimal getB200008660112() {
        return b200008660112;
    }

    public void setB200008660112(BigDecimal b200008660112) {
        this.b200008660112 = b200008660112;
    }

    public BigDecimal getB200008660113() {
        return b200008660113;
    }

    public void setB200008660113(BigDecimal b200008660113) {
        this.b200008660113 = b200008660113;
    }

    public BigDecimal getB200008660114() {
        return b200008660114;
    }

    public void setB200008660114(BigDecimal b200008660114) {
        this.b200008660114 = b200008660114;
    }

    public BigDecimal getB200008660115() {
        return b200008660115;
    }

    public void setB200008660115(BigDecimal b200008660115) {
        this.b200008660115 = b200008660115;
    }

    public BigDecimal getB200008660116() {
        return b200008660116;
    }

    public void setB200008660116(BigDecimal b200008660116) {
        this.b200008660116 = b200008660116;
    }

    public BigDecimal getB200008660117() {
        return b200008660117;
    }

    public void setB200008660117(BigDecimal b200008660117) {
        this.b200008660117 = b200008660117;
    }

    public BigDecimal getB200008660118() {
        return b200008660118;
    }

    public void setB200008660118(BigDecimal b200008660118) {
        this.b200008660118 = b200008660118;
    }

    public BigDecimal getB200008660119() {
        return b200008660119;
    }

    public void setB200008660119(BigDecimal b200008660119) {
        this.b200008660119 = b200008660119;
    }

    public BigDecimal getB200008660120() {
        return b200008660120;
    }

    public void setB200008660120(BigDecimal b200008660120) {
        this.b200008660120 = b200008660120;
    }

    public BigDecimal getB200008660121() {
        return b200008660121;
    }

    public void setB200008660121(BigDecimal b200008660121) {
        this.b200008660121 = b200008660121;
    }

    public BigDecimal getB200008660122() {
        return b200008660122;
    }

    public void setB200008660122(BigDecimal b200008660122) {
        this.b200008660122 = b200008660122;
    }

    public BigDecimal getB200008660123() {
        return b200008660123;
    }

    public void setB200008660123(BigDecimal b200008660123) {
        this.b200008660123 = b200008660123;
    }

    public BigDecimal getB200008660124() {
        return b200008660124;
    }

    public void setB200008660124(BigDecimal b200008660124) {
        this.b200008660124 = b200008660124;
    }

    public BigDecimal getB200008660125() {
        return b200008660125;
    }

    public void setB200008660125(BigDecimal b200008660125) {
        this.b200008660125 = b200008660125;
    }

    public BigDecimal getB200008660126() {
        return b200008660126;
    }

    public void setB200008660126(BigDecimal b200008660126) {
        this.b200008660126 = b200008660126;
    }

    public BigDecimal getB200008660127() {
        return b200008660127;
    }

    public void setB200008660127(BigDecimal b200008660127) {
        this.b200008660127 = b200008660127;
    }

    public BigDecimal getB200008660128() {
        return b200008660128;
    }

    public void setB200008660128(BigDecimal b200008660128) {
        this.b200008660128 = b200008660128;
    }

    public BigDecimal getB200008660129() {
        return b200008660129;
    }

    public void setB200008660129(BigDecimal b200008660129) {
        this.b200008660129 = b200008660129;
    }

    public BigDecimal getB200008660130() {
        return b200008660130;
    }

    public void setB200008660130(BigDecimal b200008660130) {
        this.b200008660130 = b200008660130;
    }

    public BigDecimal getB200008660131() {
        return b200008660131;
    }

    public void setB200008660131(BigDecimal b200008660131) {
        this.b200008660131 = b200008660131;
    }

    public BigDecimal getB200008660132() {
        return b200008660132;
    }

    public void setB200008660132(BigDecimal b200008660132) {
        this.b200008660132 = b200008660132;
    }

    public BigDecimal getB200008660133() {
        return b200008660133;
    }

    public void setB200008660133(BigDecimal b200008660133) {
        this.b200008660133 = b200008660133;
    }

    public BigDecimal getB200008660134() {
        return b200008660134;
    }

    public void setB200008660134(BigDecimal b200008660134) {
        this.b200008660134 = b200008660134;
    }

    public BigDecimal getB200008660135() {
        return b200008660135;
    }

    public void setB200008660135(BigDecimal b200008660135) {
        this.b200008660135 = b200008660135;
    }

    public BigDecimal getB200008660136() {
        return b200008660136;
    }

    public void setB200008660136(BigDecimal b200008660136) {
        this.b200008660136 = b200008660136;
    }

    public BigDecimal getB200008660137() {
        return b200008660137;
    }

    public void setB200008660137(BigDecimal b200008660137) {
        this.b200008660137 = b200008660137;
    }

    public BigDecimal getB200008660138() {
        return b200008660138;
    }

    public void setB200008660138(BigDecimal b200008660138) {
        this.b200008660138 = b200008660138;
    }

    public BigDecimal getB200008660139() {
        return b200008660139;
    }

    public void setB200008660139(BigDecimal b200008660139) {
        this.b200008660139 = b200008660139;
    }

    public BigDecimal getB200008660140() {
        return b200008660140;
    }

    public void setB200008660140(BigDecimal b200008660140) {
        this.b200008660140 = b200008660140;
    }

    public BigDecimal getB200008660141() {
        return b200008660141;
    }

    public void setB200008660141(BigDecimal b200008660141) {
        this.b200008660141 = b200008660141;
    }

    public BigDecimal getB200008660142() {
        return b200008660142;
    }

    public void setB200008660142(BigDecimal b200008660142) {
        this.b200008660142 = b200008660142;
    }

    public BigDecimal getB200008660143() {
        return b200008660143;
    }

    public void setB200008660143(BigDecimal b200008660143) {
        this.b200008660143 = b200008660143;
    }

    public BigDecimal getB200008660144() {
        return b200008660144;
    }

    public void setB200008660144(BigDecimal b200008660144) {
        this.b200008660144 = b200008660144;
    }

    public BigDecimal getB200008660145() {
        return b200008660145;
    }

    public void setB200008660145(BigDecimal b200008660145) {
        this.b200008660145 = b200008660145;
    }

    public BigDecimal getB200008660146() {
        return b200008660146;
    }

    public void setB200008660146(BigDecimal b200008660146) {
        this.b200008660146 = b200008660146;
    }

    public BigDecimal getB200008660147() {
        return b200008660147;
    }

    public void setB200008660147(BigDecimal b200008660147) {
        this.b200008660147 = b200008660147;
    }

    public BigDecimal getB200008660148() {
        return b200008660148;
    }

    public void setB200008660148(BigDecimal b200008660148) {
        this.b200008660148 = b200008660148;
    }

    public BigDecimal getB200008660149() {
        return b200008660149;
    }

    public void setB200008660149(BigDecimal b200008660149) {
        this.b200008660149 = b200008660149;
    }

    public BigDecimal getB200008660150() {
        return b200008660150;
    }

    public void setB200008660150(BigDecimal b200008660150) {
        this.b200008660150 = b200008660150;
    }

    public BigDecimal getB200008660151() {
        return b200008660151;
    }

    public void setB200008660151(BigDecimal b200008660151) {
        this.b200008660151 = b200008660151;
    }

    public BigDecimal getB200008660152() {
        return b200008660152;
    }

    public void setB200008660152(BigDecimal b200008660152) {
        this.b200008660152 = b200008660152;
    }

    public BigDecimal getB200008660153() {
        return b200008660153;
    }

    public void setB200008660153(BigDecimal b200008660153) {
        this.b200008660153 = b200008660153;
    }

    public BigDecimal getB2000086602() {
        return b2000086602;
    }

    public void setB2000086602(BigDecimal b2000086602) {
        this.b2000086602 = b2000086602;
    }

    public BigDecimal getB200008660201() {
        return b200008660201;
    }

    public void setB200008660201(BigDecimal b200008660201) {
        this.b200008660201 = b200008660201;
    }

    public BigDecimal getB200008660202() {
        return b200008660202;
    }

    public void setB200008660202(BigDecimal b200008660202) {
        this.b200008660202 = b200008660202;
    }

    public BigDecimal getB200008660203() {
        return b200008660203;
    }

    public void setB200008660203(BigDecimal b200008660203) {
        this.b200008660203 = b200008660203;
    }

    public BigDecimal getB200008660204() {
        return b200008660204;
    }

    public void setB200008660204(BigDecimal b200008660204) {
        this.b200008660204 = b200008660204;
    }

    public BigDecimal getB200008660205() {
        return b200008660205;
    }

    public void setB200008660205(BigDecimal b200008660205) {
        this.b200008660205 = b200008660205;
    }

    public BigDecimal getB200008660206() {
        return b200008660206;
    }

    public void setB200008660206(BigDecimal b200008660206) {
        this.b200008660206 = b200008660206;
    }

    public BigDecimal getB200008660207() {
        return b200008660207;
    }

    public void setB200008660207(BigDecimal b200008660207) {
        this.b200008660207 = b200008660207;
    }

    public BigDecimal getB200008660208() {
        return b200008660208;
    }

    public void setB200008660208(BigDecimal b200008660208) {
        this.b200008660208 = b200008660208;
    }

    public BigDecimal getB200008660209() {
        return b200008660209;
    }

    public void setB200008660209(BigDecimal b200008660209) {
        this.b200008660209 = b200008660209;
    }

    public BigDecimal getB200008660210() {
        return b200008660210;
    }

    public void setB200008660210(BigDecimal b200008660210) {
        this.b200008660210 = b200008660210;
    }

    public BigDecimal getB200008660211() {
        return b200008660211;
    }

    public void setB200008660211(BigDecimal b200008660211) {
        this.b200008660211 = b200008660211;
    }

    public BigDecimal getB200008660212() {
        return b200008660212;
    }

    public void setB200008660212(BigDecimal b200008660212) {
        this.b200008660212 = b200008660212;
    }

    public BigDecimal getB200008660213() {
        return b200008660213;
    }

    public void setB200008660213(BigDecimal b200008660213) {
        this.b200008660213 = b200008660213;
    }

    public BigDecimal getB200008660214() {
        return b200008660214;
    }

    public void setB200008660214(BigDecimal b200008660214) {
        this.b200008660214 = b200008660214;
    }

    public BigDecimal getB200008660215() {
        return b200008660215;
    }

    public void setB200008660215(BigDecimal b200008660215) {
        this.b200008660215 = b200008660215;
    }

    public BigDecimal getB200008660216() {
        return b200008660216;
    }

    public void setB200008660216(BigDecimal b200008660216) {
        this.b200008660216 = b200008660216;
    }

    public BigDecimal getB200008660217() {
        return b200008660217;
    }

    public void setB200008660217(BigDecimal b200008660217) {
        this.b200008660217 = b200008660217;
    }

    public BigDecimal getB200008660218() {
        return b200008660218;
    }

    public void setB200008660218(BigDecimal b200008660218) {
        this.b200008660218 = b200008660218;
    }

    public BigDecimal getB200008660219() {
        return b200008660219;
    }

    public void setB200008660219(BigDecimal b200008660219) {
        this.b200008660219 = b200008660219;
    }

    public BigDecimal getB200008660220() {
        return b200008660220;
    }

    public void setB200008660220(BigDecimal b200008660220) {
        this.b200008660220 = b200008660220;
    }

    public BigDecimal getB200008660221() {
        return b200008660221;
    }

    public void setB200008660221(BigDecimal b200008660221) {
        this.b200008660221 = b200008660221;
    }

    public BigDecimal getB200008660222() {
        return b200008660222;
    }

    public void setB200008660222(BigDecimal b200008660222) {
        this.b200008660222 = b200008660222;
    }

    public BigDecimal getB200008660223() {
        return b200008660223;
    }

    public void setB200008660223(BigDecimal b200008660223) {
        this.b200008660223 = b200008660223;
    }

    public BigDecimal getB200008660224() {
        return b200008660224;
    }

    public void setB200008660224(BigDecimal b200008660224) {
        this.b200008660224 = b200008660224;
    }

    public BigDecimal getB200008660225() {
        return b200008660225;
    }

    public void setB200008660225(BigDecimal b200008660225) {
        this.b200008660225 = b200008660225;
    }

    public BigDecimal getB200008660226() {
        return b200008660226;
    }

    public void setB200008660226(BigDecimal b200008660226) {
        this.b200008660226 = b200008660226;
    }

    public BigDecimal getB200008660227() {
        return b200008660227;
    }

    public void setB200008660227(BigDecimal b200008660227) {
        this.b200008660227 = b200008660227;
    }

    public BigDecimal getB200008660228() {
        return b200008660228;
    }

    public void setB200008660228(BigDecimal b200008660228) {
        this.b200008660228 = b200008660228;
    }

    public BigDecimal getB200008660229() {
        return b200008660229;
    }

    public void setB200008660229(BigDecimal b200008660229) {
        this.b200008660229 = b200008660229;
    }

    public BigDecimal getB200008660230() {
        return b200008660230;
    }

    public void setB200008660230(BigDecimal b200008660230) {
        this.b200008660230 = b200008660230;
    }

    public BigDecimal getB200008660231() {
        return b200008660231;
    }

    public void setB200008660231(BigDecimal b200008660231) {
        this.b200008660231 = b200008660231;
    }

    public BigDecimal getB200008660232() {
        return b200008660232;
    }

    public void setB200008660232(BigDecimal b200008660232) {
        this.b200008660232 = b200008660232;
    }

    public BigDecimal getB200008660233() {
        return b200008660233;
    }

    public void setB200008660233(BigDecimal b200008660233) {
        this.b200008660233 = b200008660233;
    }

    public BigDecimal getB200008660234() {
        return b200008660234;
    }

    public void setB200008660234(BigDecimal b200008660234) {
        this.b200008660234 = b200008660234;
    }

    public BigDecimal getB200008660235() {
        return b200008660235;
    }

    public void setB200008660235(BigDecimal b200008660235) {
        this.b200008660235 = b200008660235;
    }

    public BigDecimal getB200008660236() {
        return b200008660236;
    }

    public void setB200008660236(BigDecimal b200008660236) {
        this.b200008660236 = b200008660236;
    }

    public BigDecimal getB200008660237() {
        return b200008660237;
    }

    public void setB200008660237(BigDecimal b200008660237) {
        this.b200008660237 = b200008660237;
    }

    public BigDecimal getB200008660238() {
        return b200008660238;
    }

    public void setB200008660238(BigDecimal b200008660238) {
        this.b200008660238 = b200008660238;
    }

    public BigDecimal getB200008660239() {
        return b200008660239;
    }

    public void setB200008660239(BigDecimal b200008660239) {
        this.b200008660239 = b200008660239;
    }

    public BigDecimal getB200008660240() {
        return b200008660240;
    }

    public void setB200008660240(BigDecimal b200008660240) {
        this.b200008660240 = b200008660240;
    }

    public BigDecimal getB200008660241() {
        return b200008660241;
    }

    public void setB200008660241(BigDecimal b200008660241) {
        this.b200008660241 = b200008660241;
    }

    public BigDecimal getB200008660242() {
        return b200008660242;
    }

    public void setB200008660242(BigDecimal b200008660242) {
        this.b200008660242 = b200008660242;
    }

    public BigDecimal getB200008660243() {
        return b200008660243;
    }

    public void setB200008660243(BigDecimal b200008660243) {
        this.b200008660243 = b200008660243;
    }

    public BigDecimal getB200008660244() {
        return b200008660244;
    }

    public void setB200008660244(BigDecimal b200008660244) {
        this.b200008660244 = b200008660244;
    }

    public BigDecimal getB200008660245() {
        return b200008660245;
    }

    public void setB200008660245(BigDecimal b200008660245) {
        this.b200008660245 = b200008660245;
    }

    public BigDecimal getB200008660246() {
        return b200008660246;
    }

    public void setB200008660246(BigDecimal b200008660246) {
        this.b200008660246 = b200008660246;
    }

    public BigDecimal getB200008660247() {
        return b200008660247;
    }

    public void setB200008660247(BigDecimal b200008660247) {
        this.b200008660247 = b200008660247;
    }

    public BigDecimal getB200008660248() {
        return b200008660248;
    }

    public void setB200008660248(BigDecimal b200008660248) {
        this.b200008660248 = b200008660248;
    }

    public BigDecimal getB200008660249() {
        return b200008660249;
    }

    public void setB200008660249(BigDecimal b200008660249) {
        this.b200008660249 = b200008660249;
    }

    public BigDecimal getB200008660250() {
        return b200008660250;
    }

    public void setB200008660250(BigDecimal b200008660250) {
        this.b200008660250 = b200008660250;
    }

    public BigDecimal getB200008660251() {
        return b200008660251;
    }

    public void setB200008660251(BigDecimal b200008660251) {
        this.b200008660251 = b200008660251;
    }

    public BigDecimal getB200008660252() {
        return b200008660252;
    }

    public void setB200008660252(BigDecimal b200008660252) {
        this.b200008660252 = b200008660252;
    }

    public BigDecimal getB200008660253() {
        return b200008660253;
    }

    public void setB200008660253(BigDecimal b200008660253) {
        this.b200008660253 = b200008660253;
    }

    public BigDecimal getB200008660254() {
        return b200008660254;
    }

    public void setB200008660254(BigDecimal b200008660254) {
        this.b200008660254 = b200008660254;
    }

    public BigDecimal getB200008660255() {
        return b200008660255;
    }

    public void setB200008660255(BigDecimal b200008660255) {
        this.b200008660255 = b200008660255;
    }

    public BigDecimal getB200008660256() {
        return b200008660256;
    }

    public void setB200008660256(BigDecimal b200008660256) {
        this.b200008660256 = b200008660256;
    }

    public BigDecimal getB200008660257() {
        return b200008660257;
    }

    public void setB200008660257(BigDecimal b200008660257) {
        this.b200008660257 = b200008660257;
    }

    public BigDecimal getB200008660258() {
        return b200008660258;
    }

    public void setB200008660258(BigDecimal b200008660258) {
        this.b200008660258 = b200008660258;
    }

    public BigDecimal getB200008660259() {
        return b200008660259;
    }

    public void setB200008660259(BigDecimal b200008660259) {
        this.b200008660259 = b200008660259;
    }

    public BigDecimal getB200008660260() {
        return b200008660260;
    }

    public void setB200008660260(BigDecimal b200008660260) {
        this.b200008660260 = b200008660260;
    }

    public BigDecimal getB200008660261() {
        return b200008660261;
    }

    public void setB200008660261(BigDecimal b200008660261) {
        this.b200008660261 = b200008660261;
    }

    public BigDecimal getB2000086603() {
        return b2000086603;
    }

    public void setB2000086603(BigDecimal b2000086603) {
        this.b2000086603 = b2000086603;
    }

    public BigDecimal getB200008660301() {
        return b200008660301;
    }

    public void setB200008660301(BigDecimal b200008660301) {
        this.b200008660301 = b200008660301;
    }

    public BigDecimal getB200008660302() {
        return b200008660302;
    }

    public void setB200008660302(BigDecimal b200008660302) {
        this.b200008660302 = b200008660302;
    }

    public BigDecimal getB200008660303() {
        return b200008660303;
    }

    public void setB200008660303(BigDecimal b200008660303) {
        this.b200008660303 = b200008660303;
    }

    public BigDecimal getB200008660304() {
        return b200008660304;
    }

    public void setB200008660304(BigDecimal b200008660304) {
        this.b200008660304 = b200008660304;
    }

    public BigDecimal getB200008660305() {
        return b200008660305;
    }

    public void setB200008660305(BigDecimal b200008660305) {
        this.b200008660305 = b200008660305;
    }

    public BigDecimal getB20000866030501() {
        return b20000866030501;
    }

    public void setB20000866030501(BigDecimal b20000866030501) {
        this.b20000866030501 = b20000866030501;
    }

    public BigDecimal getB20000866030502() {
        return b20000866030502;
    }

    public void setB20000866030502(BigDecimal b20000866030502) {
        this.b20000866030502 = b20000866030502;
    }

    public BigDecimal getB200008660306() {
        return b200008660306;
    }

    public void setB200008660306(BigDecimal b200008660306) {
        this.b200008660306 = b200008660306;
    }

    public BigDecimal getB200008660307() {
        return b200008660307;
    }

    public void setB200008660307(BigDecimal b200008660307) {
        this.b200008660307 = b200008660307;
    }

    public BigDecimal getB200008660308() {
        return b200008660308;
    }

    public void setB200008660308(BigDecimal b200008660308) {
        this.b200008660308 = b200008660308;
    }

    public BigDecimal getB2000086604() {
        return b2000086604;
    }

    public void setB2000086604(BigDecimal b2000086604) {
        this.b2000086604 = b2000086604;
    }

    public BigDecimal getB2000086701() {
        return b2000086701;
    }

    public void setB2000086701(BigDecimal b2000086701) {
        this.b2000086701 = b2000086701;
    }

    public BigDecimal getB2000086702() {
        return b2000086702;
    }

    public void setB2000086702(BigDecimal b2000086702) {
        this.b2000086702 = b2000086702;
    }

    public BigDecimal getB2000086711() {
        return b2000086711;
    }

    public void setB2000086711(BigDecimal b2000086711) {
        this.b2000086711 = b2000086711;
    }

    public BigDecimal getB200008671101() {
        return b200008671101;
    }

    public void setB200008671101(BigDecimal b200008671101) {
        this.b200008671101 = b200008671101;
    }

    public BigDecimal getB20000867110101() {
        return b20000867110101;
    }

    public void setB20000867110101(BigDecimal b20000867110101) {
        this.b20000867110101 = b20000867110101;
    }

    public BigDecimal getB20000867110102() {
        return b20000867110102;
    }

    public void setB20000867110102(BigDecimal b20000867110102) {
        this.b20000867110102 = b20000867110102;
    }

    public BigDecimal getB20000867110103() {
        return b20000867110103;
    }

    public void setB20000867110103(BigDecimal b20000867110103) {
        this.b20000867110103 = b20000867110103;
    }

    public BigDecimal getB20000867110104() {
        return b20000867110104;
    }

    public void setB20000867110104(BigDecimal b20000867110104) {
        this.b20000867110104 = b20000867110104;
    }

    public BigDecimal getB20000867110105() {
        return b20000867110105;
    }

    public void setB20000867110105(BigDecimal b20000867110105) {
        this.b20000867110105 = b20000867110105;
    }

    public BigDecimal getB20000867110106() {
        return b20000867110106;
    }

    public void setB20000867110106(BigDecimal b20000867110106) {
        this.b20000867110106 = b20000867110106;
    }

    public BigDecimal getB20000867110107() {
        return b20000867110107;
    }

    public void setB20000867110107(BigDecimal b20000867110107) {
        this.b20000867110107 = b20000867110107;
    }

    public BigDecimal getB200008671102() {
        return b200008671102;
    }

    public void setB200008671102(BigDecimal b200008671102) {
        this.b200008671102 = b200008671102;
    }

    public BigDecimal getB20000867110201() {
        return b20000867110201;
    }

    public void setB20000867110201(BigDecimal b20000867110201) {
        this.b20000867110201 = b20000867110201;
    }

    public BigDecimal getB20000867110202() {
        return b20000867110202;
    }

    public void setB20000867110202(BigDecimal b20000867110202) {
        this.b20000867110202 = b20000867110202;
    }

    public BigDecimal getB20000867110203() {
        return b20000867110203;
    }

    public void setB20000867110203(BigDecimal b20000867110203) {
        this.b20000867110203 = b20000867110203;
    }

    public BigDecimal getB20000867110204() {
        return b20000867110204;
    }

    public void setB20000867110204(BigDecimal b20000867110204) {
        this.b20000867110204 = b20000867110204;
    }

    public BigDecimal getB20000867110205() {
        return b20000867110205;
    }

    public void setB20000867110205(BigDecimal b20000867110205) {
        this.b20000867110205 = b20000867110205;
    }

    public BigDecimal getB20000867110206() {
        return b20000867110206;
    }

    public void setB20000867110206(BigDecimal b20000867110206) {
        this.b20000867110206 = b20000867110206;
    }

    public BigDecimal getB20000867110207() {
        return b20000867110207;
    }

    public void setB20000867110207(BigDecimal b20000867110207) {
        this.b20000867110207 = b20000867110207;
    }

    public BigDecimal getB200008671103() {
        return b200008671103;
    }

    public void setB200008671103(BigDecimal b200008671103) {
        this.b200008671103 = b200008671103;
    }

    public BigDecimal getB200008671104() {
        return b200008671104;
    }

    public void setB200008671104(BigDecimal b200008671104) {
        this.b200008671104 = b200008671104;
    }

    public BigDecimal getB20000867110401() {
        return b20000867110401;
    }

    public void setB20000867110401(BigDecimal b20000867110401) {
        this.b20000867110401 = b20000867110401;
    }

    public BigDecimal getB20000867110402() {
        return b20000867110402;
    }

    public void setB20000867110402(BigDecimal b20000867110402) {
        this.b20000867110402 = b20000867110402;
    }

    public BigDecimal getB20000867110403() {
        return b20000867110403;
    }

    public void setB20000867110403(BigDecimal b20000867110403) {
        this.b20000867110403 = b20000867110403;
    }

    public BigDecimal getB20000867110404() {
        return b20000867110404;
    }

    public void setB20000867110404(BigDecimal b20000867110404) {
        this.b20000867110404 = b20000867110404;
    }

    public BigDecimal getB20000867110405() {
        return b20000867110405;
    }

    public void setB20000867110405(BigDecimal b20000867110405) {
        this.b20000867110405 = b20000867110405;
    }

    public BigDecimal getB20000867110406() {
        return b20000867110406;
    }

    public void setB20000867110406(BigDecimal b20000867110406) {
        this.b20000867110406 = b20000867110406;
    }

    public BigDecimal getB20000867110407() {
        return b20000867110407;
    }

    public void setB20000867110407(BigDecimal b20000867110407) {
        this.b20000867110407 = b20000867110407;
    }

    public BigDecimal getB200008671105() {
        return b200008671105;
    }

    public void setB200008671105(BigDecimal b200008671105) {
        this.b200008671105 = b200008671105;
    }

    public BigDecimal getB200008671106() {
        return b200008671106;
    }

    public void setB200008671106(BigDecimal b200008671106) {
        this.b200008671106 = b200008671106;
    }

    public BigDecimal getB200008671107() {
        return b200008671107;
    }

    public void setB200008671107(BigDecimal b200008671107) {
        this.b200008671107 = b200008671107;
    }

    public BigDecimal getB20000867110701() {
        return b20000867110701;
    }

    public void setB20000867110701(BigDecimal b20000867110701) {
        this.b20000867110701 = b20000867110701;
    }

    public BigDecimal getB20000867110702() {
        return b20000867110702;
    }

    public void setB20000867110702(BigDecimal b20000867110702) {
        this.b20000867110702 = b20000867110702;
    }

    public BigDecimal getB20000867110703() {
        return b20000867110703;
    }

    public void setB20000867110703(BigDecimal b20000867110703) {
        this.b20000867110703 = b20000867110703;
    }

    public BigDecimal getB200008671108() {
        return b200008671108;
    }

    public void setB200008671108(BigDecimal b200008671108) {
        this.b200008671108 = b200008671108;
    }

    public BigDecimal getB20000867110801() {
        return b20000867110801;
    }

    public void setB20000867110801(BigDecimal b20000867110801) {
        this.b20000867110801 = b20000867110801;
    }

    public BigDecimal getB20000867110802() {
        return b20000867110802;
    }

    public void setB20000867110802(BigDecimal b20000867110802) {
        this.b20000867110802 = b20000867110802;
    }

    public BigDecimal getB20000867110803() {
        return b20000867110803;
    }

    public void setB20000867110803(BigDecimal b20000867110803) {
        this.b20000867110803 = b20000867110803;
    }

    public BigDecimal getB20000867110804() {
        return b20000867110804;
    }

    public void setB20000867110804(BigDecimal b20000867110804) {
        this.b20000867110804 = b20000867110804;
    }

    public BigDecimal getB20000867110805() {
        return b20000867110805;
    }

    public void setB20000867110805(BigDecimal b20000867110805) {
        this.b20000867110805 = b20000867110805;
    }

    public BigDecimal getB20000867110806() {
        return b20000867110806;
    }

    public void setB20000867110806(BigDecimal b20000867110806) {
        this.b20000867110806 = b20000867110806;
    }

    public BigDecimal getB20000867110807() {
        return b20000867110807;
    }

    public void setB20000867110807(BigDecimal b20000867110807) {
        this.b20000867110807 = b20000867110807;
    }

    public BigDecimal getB20000867110808() {
        return b20000867110808;
    }

    public void setB20000867110808(BigDecimal b20000867110808) {
        this.b20000867110808 = b20000867110808;
    }

    public BigDecimal getB20000867110809() {
        return b20000867110809;
    }

    public void setB20000867110809(BigDecimal b20000867110809) {
        this.b20000867110809 = b20000867110809;
    }

    public BigDecimal getB20000867110810() {
        return b20000867110810;
    }

    public void setB20000867110810(BigDecimal b20000867110810) {
        this.b20000867110810 = b20000867110810;
    }

    public BigDecimal getB20000867110811() {
        return b20000867110811;
    }

    public void setB20000867110811(BigDecimal b20000867110811) {
        this.b20000867110811 = b20000867110811;
    }

    public BigDecimal getB20000867110812() {
        return b20000867110812;
    }

    public void setB20000867110812(BigDecimal b20000867110812) {
        this.b20000867110812 = b20000867110812;
    }

    public BigDecimal getB20000867110813() {
        return b20000867110813;
    }

    public void setB20000867110813(BigDecimal b20000867110813) {
        this.b20000867110813 = b20000867110813;
    }

    public BigDecimal getB2000086801() {
        return b2000086801;
    }

    public void setB2000086801(BigDecimal b2000086801) {
        this.b2000086801 = b2000086801;
    }

    public BigDecimal getB2000086901() {
        return b2000086901;
    }

    public void setB2000086901(BigDecimal b2000086901) {
        this.b2000086901 = b2000086901;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getSubjectMonth() {
        return subjectMonth;
    }

    public void setSubjectMonth(Integer subjectMonth) {
        this.subjectMonth = subjectMonth;
    }

    public BigDecimal getB200008222124() {
        return b200008222124;
    }

    public void setB200008222124(BigDecimal b200008222124) {
        this.b200008222124 = b200008222124;
    }

    public BigDecimal getB200008222125() {
        return b200008222125;
    }

    public void setB200008222125(BigDecimal b200008222125) {
        this.b200008222125 = b200008222125;
    }

    public BigDecimal getB20000867110501() {
        return b20000867110501;
    }

    public void setB20000867110501(BigDecimal b20000867110501) {
        this.b20000867110501 = b20000867110501;
    }

    public BigDecimal getB20000867110502() {
        return b20000867110502;
    }

    public void setB20000867110502(BigDecimal b20000867110502) {
        this.b20000867110502 = b20000867110502;
    }
}
