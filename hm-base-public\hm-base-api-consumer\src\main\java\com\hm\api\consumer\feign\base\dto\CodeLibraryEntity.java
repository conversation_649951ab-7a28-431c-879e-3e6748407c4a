package com.hm.api.consumer.feign.base.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @author: curry.wu
 * @create: 2020/6/11 15:27
 **/
@ApiModel(value = "字典表")
public class CodeLibraryEntity implements Serializable {

    private static final long serialVersionUID = -4522306939755499599L;

    private String entId;

    @NotBlank(message = "字典表codeNo不允许为空")
    @ApiModelProperty(value = "分类代码")
    private String codeNo;
    @ApiModelProperty(value = "分类项码值")
    private String itemNo;
    @ApiModelProperty(value = "分类项名称")
    private String itemName;

    @ApiModelProperty(value = "排序值")
    private String sortNo;

    @ApiModelProperty(value = "是否启用")
    private String enableFlag;
    private String relativeCode1;
    private String relativeCode2;
    private String relativeCode3;
    private String relativeCode4;
    private String relativeCode5;
    private String remark;
    private String createTime;
    private String updateTime;

    /** 本期金额  用户g113020 计算利润表合并的金额 */
    private BigDecimal currentAmount;
    @ApiModelProperty(value = "下级信息")
    private List<CodeLibraryEntity> nextInfo;

    @ApiModelProperty(value = "多个CodeNo")
    private List<String> codeNoList;

    /** 0：新增  1：更新 */
    private Integer type;

    public String getEntId() {
        return entId;
    }

    public void setEntId(String entId) {
        this.entId = entId;
    }

    public List<String> getCodeNoList() {
        return codeNoList;
    }

    public void setCodeNoList(List<String> codeNoList) {
        this.codeNoList = codeNoList;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getCodeNo() {
        return codeNo;
    }

    public void setCodeNo(String codeNo) {
        this.codeNo = codeNo;
    }

    public String getItemNo() {
        return itemNo;
    }

    public void setItemNo(String itemNo) {
        this.itemNo = itemNo;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getSortNo() {
        return sortNo;
    }

    public void setSortNo(String sortNo) {
        this.sortNo = sortNo;
    }

    public String getEnableFlag() {
        return enableFlag;
    }

    public void setEnableFlag(String enableFlag) {
        this.enableFlag = enableFlag;
    }

    public String getRelativeCode1() {
        return relativeCode1;
    }

    public void setRelativeCode1(String relativeCode1) {
        this.relativeCode1 = relativeCode1;
    }

    public String getRelativeCode2() {
        return relativeCode2;
    }

    public void setRelativeCode2(String relativeCode2) {
        this.relativeCode2 = relativeCode2;
    }

    public String getRelativeCode3() {
        return relativeCode3;
    }

    public void setRelativeCode3(String relativeCode3) {
        this.relativeCode3 = relativeCode3;
    }

    public String getRelativeCode4() {
        return relativeCode4;
    }

    public void setRelativeCode4(String relativeCode4) {
        this.relativeCode4 = relativeCode4;
    }

    public String getRelativeCode5() {
        return relativeCode5;
    }

    public void setRelativeCode5(String relativeCode5) {
        this.relativeCode5 = relativeCode5;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public List<CodeLibraryEntity> getNextInfo() {
        return nextInfo;
    }

    public void setNextInfo(List<CodeLibraryEntity> nextInfo) {
        this.nextInfo = nextInfo;
    }


    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public BigDecimal getCurrentAmount() {
        return currentAmount;
    }

    public void setCurrentAmount(BigDecimal currentAmount) {
        this.currentAmount = currentAmount;
    }

    @Override
    public String toString() {
        return "CodeLibraryEntity{" +
                "codeNo='" + codeNo + '\'' +
                ", itemNo='" + itemNo + '\'' +
                ", itemName='" + itemName + '\'' +
                ", sortNo='" + sortNo + '\'' +
                ", enableFlag='" + enableFlag + '\'' +
                ", relativeCode1='" + relativeCode1 + '\'' +
                ", relativeCode2='" + relativeCode2 + '\'' +
                ", relativeCode3='" + relativeCode3 + '\'' +
                ", relativeCode4='" + relativeCode4 + '\'' +
                ", relativeCode5='" + relativeCode5 + '\'' +
                ", remark='" + remark + '\'' +
                ", createTime='" + createTime + '\'' +
                ", updateTime='" + updateTime + '\'' +
                ", nextInfo=" + nextInfo +
                '}';
    }
}
