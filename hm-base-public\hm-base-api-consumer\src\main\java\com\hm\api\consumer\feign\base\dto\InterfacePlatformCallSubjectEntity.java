package com.hm.api.consumer.feign.base.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Map;

/**
 * @author: curry.wu
 * @create: 2021/3/12 11:24
 **/
@ApiModel(value = "接口平台调用-保存科目余额表")
@Data
public class InterfacePlatformCallSubjectEntity implements Serializable {

    private static final long serialVersionUID = 8289128090881586927L;

    @NotBlank(message = "企业ID,不能为空")
    @ApiModelProperty(value = "企业ID")
    private String entId;

    @NotBlank(message = "所属年份,不能为空")
    @ApiModelProperty(value = "所属年份")
    private String taxYear;

    @NotNull(message = "月份,不能为空")
    @ApiModelProperty(value = "月份(年度等于12)")
    private Integer subjectMonth;

    @ApiModelProperty(value = "操作人id")
    private String userId;

    @ApiModelProperty(value = "税种类别")
    private String taxType;

    @NotNull(message = "b200001本年期初借方余额,不能为空")
    @ApiModelProperty(value = "b200001本年期初借方余额")
    private Map<String, Object> b200001Entity;

    @NotNull(message = "b200002本年期初贷方余额,不能为空")
    @ApiModelProperty(value = "b200002本年期初贷方余额")
    private Map<String, Object> b200002Entity;

    @NotNull(message = "b200003本年借方累计发生额,不能为空")
    @ApiModelProperty(value = "b200003本年借方累计发生额")
    private Map<String, Object> b200003Entity;

    @NotNull(message = "b200004本年贷方累计发生额,不能为空")
    @ApiModelProperty(value = "b200004本年贷方累计发生额")
    private Map<String, Object> b200004Entity;

    @NotNull(message = "b200005本期借方发生额,不能为空")
    @ApiModelProperty(value = "b200005本期借方发生额")
    private Map<String, Object> b200005Entity;

    @NotNull(message = "b200006本期贷方发生额,不能为空")
    @ApiModelProperty(value = "b200006本期贷方发生额")
    private Map<String, Object> b200006Entity;

    @NotNull(message = "b200007本年期末借方余额,不能为空")
    @ApiModelProperty(value = "b200007本年期末借方余额")
    private Map<String, Object> b200007Entity;

    @NotNull(message = "b200008本年期末贷方余额,不能为空")
    @ApiModelProperty(value = "b200008本年期末贷方余额")
    private Map<String, Object> b200008Entity;

}
