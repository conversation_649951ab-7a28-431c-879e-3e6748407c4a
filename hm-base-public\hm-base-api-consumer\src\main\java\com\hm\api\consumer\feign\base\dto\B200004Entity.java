package com.hm.api.consumer.feign.base.dto;

import com.hm.api.common.aop.LogField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @author: curry.wu
 * @create: 2020/10/25 14:26
 **/
@ApiModel(value = "本年贷方累计发生额")
public class B200004Entity implements Serializable {

    private static final long serialVersionUID = -7583284091979024308L;

    @LogField(tableName = "b200004", value = "ent_id", valueName = "企业ID")
    @ApiModelProperty(value = "企业ID")
    private String entId;

    @LogField(tableName = "b200004", value = "tax_year", valueName = "所属时间")
    @ApiModelProperty(value = "所属时间")
    private String taxYear;

    @LogField(tableName = "b200001", value = "subject_month", valueName = "月份(年度等于12)")
    @ApiModelProperty(value = "月份(年度等于12)")
    private Integer subjectMonth;

    @LogField(tableName = "b200004", value = "b200004_1001", valueName = "库存现金")
    @ApiModelProperty(value = "库存现金")
    private BigDecimal b2000041001;

    @LogField(tableName = "b200004", value = "b200004_1002", valueName = "银行存款")
    @ApiModelProperty(value = "银行存款")
    private BigDecimal b2000041002;

    @LogField(tableName = "b200004", value = "b200004_1003", valueName = "存放中央银行款项")
    @ApiModelProperty(value = "存放中央银行款项")
    private BigDecimal b2000041003;

    @LogField(tableName = "b200004", value = "b200004_1011", valueName = "存放同业")
    @ApiModelProperty(value = "存放同业")
    private BigDecimal b2000041011;

    @LogField(tableName = "b200004", value = "b200004_1012", valueName = "其他货币资金")
    @ApiModelProperty(value = "其他货币资金")
    private BigDecimal b2000041012;

    @LogField(tableName = "b200004", value = "b200004_1021", valueName = "结算备付金")
    @ApiModelProperty(value = "结算备付金")
    private BigDecimal b2000041021;

    @LogField(tableName = "b200004", value = "b200004_1031", valueName = "存出保证金")
    @ApiModelProperty(value = "存出保证金")
    private BigDecimal b2000041031;

    @LogField(tableName = "b200004", value = "b200004_1101", valueName = "交易性金融资产")
    @ApiModelProperty(value = "交易性金融资产")
    private BigDecimal b2000041101;

    @LogField(tableName = "b200004", value = "b200004_1111", valueName = "买入返售金融资产")
    @ApiModelProperty(value = "买入返售金融资产")
    private BigDecimal b2000041111;

    @LogField(tableName = "b200004", value = "b200004_1121", valueName = "应收票据")
    @ApiModelProperty(value = "应收票据")
    private BigDecimal b2000041121;

    @LogField(tableName = "b200004", value = "b200004_1122", valueName = "应收账款")
    @ApiModelProperty(value = "应收账款")
    private BigDecimal b2000041122;

    @LogField(tableName = "b200004", value = "b200004_1123", valueName = "预付账款")
    @ApiModelProperty(value = "预付账款")
    private BigDecimal b2000041123;

    @LogField(tableName = "b200004", value = "b200004_1124", valueName = "合同资产")
    @ApiModelProperty(value = "合同资产")
    private BigDecimal b2000041124;

    @LogField(tableName = "b200004", value = "b200004_1125", valueName = "合同资产减值准备")
    @ApiModelProperty(value = "合同资产减值准备")
    private BigDecimal b2000041125;

    @LogField(tableName = "b200004", value = "b200004_1131", valueName = "应收股利")
    @ApiModelProperty(value = "应收股利")
    private BigDecimal b2000041131;

    @LogField(tableName = "b200004", value = "b200004_1132", valueName = "应收利息")
    @ApiModelProperty(value = "应收利息")
    private BigDecimal b2000041132;

    @LogField(tableName = "b200004", value = "b200004_1201", valueName = "应收代位追偿款")
    @ApiModelProperty(value = "应收代位追偿款")
    private BigDecimal b2000041201;

    @LogField(tableName = "b200004", value = "b200004_1211", valueName = "应收分保账款")
    @ApiModelProperty(value = "应收分保账款")
    private BigDecimal b2000041211;

    @LogField(tableName = "b200004", value = "b200004_1212", valueName = "应收分保合同准备金")
    @ApiModelProperty(value = "应收分保合同准备金")
    private BigDecimal b2000041212;

    @LogField(tableName = "b200004", value = "b200004_1221", valueName = "其他应收款")
    @ApiModelProperty(value = "其他应收款")
    private BigDecimal b2000041221;

    @LogField(tableName = "b200004", value = "b200004_1231", valueName = "坏账准备")
    @ApiModelProperty(value = "坏账准备")
    private BigDecimal b2000041231;

    @LogField(tableName = "b200004", value = "b200004_1301", valueName = "贴现资产")
    @ApiModelProperty(value = "贴现资产")
    private BigDecimal b2000041301;

    @LogField(tableName = "b200004", value = "b200004_1302", valueName = "拆出资金")
    @ApiModelProperty(value = "拆出资金")
    private BigDecimal b2000041302;

    @LogField(tableName = "b200004", value = "b200004_1303", valueName = "贷款")
    @ApiModelProperty(value = "贷款")
    private BigDecimal b2000041303;

    @LogField(tableName = "b200004", value = "b200004_1304", valueName = "贷款损失准备")
    @ApiModelProperty(value = "贷款损失准备")
    private BigDecimal b2000041304;

    @LogField(tableName = "b200004", value = "b200004_1311", valueName = "代理兑付证券")
    @ApiModelProperty(value = "代理兑付证券")
    private BigDecimal b2000041311;

    @LogField(tableName = "b200004", value = "b200004_1321", valueName = "代理业务资产")
    @ApiModelProperty(value = "代理业务资产")
    private BigDecimal b2000041321;

    @LogField(tableName = "b200004", value = "b200004_1401", valueName = "材料采购")
    @ApiModelProperty(value = "材料采购")
    private BigDecimal b2000041401;

    @LogField(tableName = "b200004", value = "b200004_1402", valueName = "在途物资")
    @ApiModelProperty(value = "在途物资")
    private BigDecimal b2000041402;

    @LogField(tableName = "b200004", value = "b200004_1403", valueName = "原材料")
    @ApiModelProperty(value = "原材料")
    private BigDecimal b2000041403;

    @LogField(tableName = "b200004", value = "b200004_1404", valueName = "材料成本差异")
    @ApiModelProperty(value = "材料成本差异")
    private BigDecimal b2000041404;

    @LogField(tableName = "b200004", value = "b200004_1405", valueName = "库存商品")
    @ApiModelProperty(value = "库存商品")
    private BigDecimal b2000041405;

    @LogField(tableName = "b200004", value = "b200004_1406", valueName = "发出商品")
    @ApiModelProperty(value = "发出商品")
    private BigDecimal b2000041406;

    @LogField(tableName = "b200004", value = "b200004_1407", valueName = "商品进销差价")
    @ApiModelProperty(value = "商品进销差价")
    private BigDecimal b2000041407;

    @LogField(tableName = "b200004", value = "b200004_1408", valueName = "委托加工物资")
    @ApiModelProperty(value = "委托加工物资")
    private BigDecimal b2000041408;

    @LogField(tableName = "b200004", value = "b200004_1411", valueName = "周转材料")
    @ApiModelProperty(value = "周转材料")
    private BigDecimal b2000041411;

    @LogField(tableName = "b200004", value = "b200004_1421", valueName = "消耗性生物资产")
    @ApiModelProperty(value = "消耗性生物资产")
    private BigDecimal b2000041421;

    @LogField(tableName = "b200004", value = "b200004_1431", valueName = "贵金属")
    @ApiModelProperty(value = "贵金属")
    private BigDecimal b2000041431;

    @LogField(tableName = "b200004", value = "b200004_1441", valueName = "抵债资产")
    @ApiModelProperty(value = "抵债资产")
    private BigDecimal b2000041441;

    @LogField(tableName = "b200004", value = "b200004_1451", valueName = "损余物资")
    @ApiModelProperty(value = "损余物资")
    private BigDecimal b2000041451;

    @LogField(tableName = "b200004", value = "b200004_1461", valueName = "融资租赁资产")
    @ApiModelProperty(value = "融资租赁资产")
    private BigDecimal b2000041461;

    @LogField(tableName = "b200004", value = "b200004_1471", valueName = "存货跌价准备")
    @ApiModelProperty(value = "存货跌价准备")
    private BigDecimal b2000041471;

    @LogField(tableName = "b200004", value = "b200004_1481", valueName = "持有待售资产")
    @ApiModelProperty(value = "持有待售资产")
    private BigDecimal b2000041481;

    @LogField(tableName = "b200004", value = "b200004_1482", valueName = "持有待售资产减值准备")
    @ApiModelProperty(value = "持有待售资产减值准备")
    private BigDecimal b2000041482;

    @LogField(tableName = "b200004", value = "b200004_1501", valueName = "持有至到期投资")
    @ApiModelProperty(value = "持有至到期投资")
    private BigDecimal b2000041501;

    @LogField(tableName = "b200004", value = "b200004_1502", valueName = "持有至到期投资减值准备")
    @ApiModelProperty(value = "持有至到期投资减值准备")
    private BigDecimal b2000041502;

    @LogField(tableName = "b200004", value = "b200004_1503", valueName = "可供出售金融资产")
    @ApiModelProperty(value = "可供出售金融资产")
    private BigDecimal b2000041503;

    @LogField(tableName = "b200004", value = "b200004_1511", valueName = "长期股权投资")
    @ApiModelProperty(value = "长期股权投资")
    private BigDecimal b2000041511;

    @LogField(tableName = "b200004", value = "b200004_1512", valueName = "长期股权投资减值准备")
    @ApiModelProperty(value = "长期股权投资减值准备")
    private BigDecimal b2000041512;

    @LogField(tableName = "b200004", value = "b200004_1521", valueName = "投资性房地产")
    @ApiModelProperty(value = "投资性房地产")
    private BigDecimal b2000041521;

    @LogField(tableName = "b200004", value = "b200004_1531", valueName = "长期应收款")
    @ApiModelProperty(value = "长期应收款")
    private BigDecimal b2000041531;

    @LogField(tableName = "b200004", value = "b200004_1532", valueName = "未实现融资收益")
    @ApiModelProperty(value = "未实现融资收益")
    private BigDecimal b2000041532;

    @LogField(tableName = "b200004", value = "b200004_1541", valueName = "存出资本保证金")
    @ApiModelProperty(value = "存出资本保证金")
    private BigDecimal b2000041541;

    @LogField(tableName = "b200004", value = "b200004_1601", valueName = "固定资产")
    @ApiModelProperty(value = "固定资产")
    private BigDecimal b2000041601;

    @LogField(tableName = "b200004", value = "b200004_1602", valueName = "累计折旧")
    @ApiModelProperty(value = "累计折旧")
    private BigDecimal b2000041602;

    @LogField(tableName = "b200004", value = "b200004_1603", valueName = "固定资产减值准备")
    @ApiModelProperty(value = "固定资产减值准备")
    private BigDecimal b2000041603;

    @LogField(tableName = "b200004", value = "b200004_1604", valueName = "在建工程")
    @ApiModelProperty(value = "在建工程")
    private BigDecimal b2000041604;

    @LogField(tableName = "b200004", value = "b200004_1605", valueName = "工程物资")
    @ApiModelProperty(value = "工程物资")
    private BigDecimal b2000041605;

    @LogField(tableName = "b200004", value = "b200004_1606", valueName = "固定资产清理")
    @ApiModelProperty(value = "固定资产清理")
    private BigDecimal b2000041606;

    @LogField(tableName = "b200004", value = "b200004_1611", valueName = "未担保余值")
    @ApiModelProperty(value = "未担保余值")
    private BigDecimal b2000041611;

    @LogField(tableName = "b200004", value = "b200004_1621", valueName = "生产性生物资产")
    @ApiModelProperty(value = "生产性生物资产")
    private BigDecimal b2000041621;

    @LogField(tableName = "b200004", value = "b200004_1622", valueName = "生产性生物资产累计折旧")
    @ApiModelProperty(value = "生产性生物资产累计折旧")
    private BigDecimal b2000041622;

    @LogField(tableName = "b200004", value = "b200004_1623", valueName = "公益性生物资产")
    @ApiModelProperty(value = "公益性生物资产")
    private BigDecimal b2000041623;

    @LogField(tableName = "b200004", value = "b200004_1631", valueName = "油气资产")
    @ApiModelProperty(value = "油气资产")
    private BigDecimal b2000041631;

    @LogField(tableName = "b200004", value = "b200004_1632", valueName = "累计折耗")
    @ApiModelProperty(value = "累计折耗")
    private BigDecimal b2000041632;

    @LogField(tableName = "b200004", value = "b200004_1701", valueName = "无形资产")
    @ApiModelProperty(value = "无形资产")
    private BigDecimal b2000041701;

    @LogField(tableName = "b200004", value = "b200004_1702", valueName = "累计摊销")
    @ApiModelProperty(value = "累计摊销")
    private BigDecimal b2000041702;

    @LogField(tableName = "b200004", value = "b200004_1703", valueName = "无形资产减值准备")
    @ApiModelProperty(value = "无形资产减值准备")
    private BigDecimal b2000041703;

    @LogField(tableName = "b200004", value = "b200004_1711", valueName = "商誉")
    @ApiModelProperty(value = "商誉")
    private BigDecimal b2000041711;

    @LogField(tableName = "b200004", value = "b200004_1801", valueName = "长期待摊费用")
    @ApiModelProperty(value = "长期待摊费用")
    private BigDecimal b2000041801;

    @LogField(tableName = "b200004", value = "b200004_1811", valueName = "递延所得税资产")
    @ApiModelProperty(value = "递延所得税资产")
    private BigDecimal b2000041811;

    @LogField(tableName = "b200004", value = "b200004_1821", valueName = "独立账户资产")
    @ApiModelProperty(value = "独立账户资产")
    private BigDecimal b2000041821;

    @LogField(tableName = "b200004", value = "b200004_1901", valueName = "待处理财产损溢")
    @ApiModelProperty(value = "待处理财产损溢")
    private BigDecimal b2000041901;

    @LogField(tableName = "b200004", value = "b200004_2001", valueName = "短期借款")
    @ApiModelProperty(value = "短期借款")
    private BigDecimal b2000042001;

    @LogField(tableName = "b200004", value = "b200004_2002", valueName = "存入保证金")
    @ApiModelProperty(value = "存入保证金")
    private BigDecimal b2000042002;

    @LogField(tableName = "b200004", value = "b200004_2003", valueName = "拆入资金")
    @ApiModelProperty(value = "拆入资金")
    private BigDecimal b2000042003;

    @LogField(tableName = "b200004", value = "b200004_2004", valueName = "向中央银行借款")
    @ApiModelProperty(value = "向中央银行借款")
    private BigDecimal b2000042004;

    @LogField(tableName = "b200004", value = "b200004_2011", valueName = "吸收存款")
    @ApiModelProperty(value = "吸收存款")
    private BigDecimal b2000042011;

    @LogField(tableName = "b200004", value = "b200004_2012", valueName = "同业存放")
    @ApiModelProperty(value = "同业存放")
    private BigDecimal b2000042012;

    @LogField(tableName = "b200004", value = "b200004_2021", valueName = "贴现负债")
    @ApiModelProperty(value = "贴现负债")
    private BigDecimal b2000042021;

    @LogField(tableName = "b200004", value = "b200004_2101", valueName = "交易性金融负债")
    @ApiModelProperty(value = "交易性金融负债")
    private BigDecimal b2000042101;

    @LogField(tableName = "b200004", value = "b200004_2111", valueName = "卖出回购金融资产款")
    @ApiModelProperty(value = "卖出回购金融资产款")
    private BigDecimal b2000042111;

    @LogField(tableName = "b200004", value = "b200004_2201", valueName = "应付票据")
    @ApiModelProperty(value = "应付票据")
    private BigDecimal b2000042201;

    @LogField(tableName = "b200004", value = "b200004_2202", valueName = "应付账款")
    @ApiModelProperty(value = "应付账款")
    private BigDecimal b2000042202;

    @LogField(tableName = "b200004", value = "b200004_2203", valueName = "预收账款")
    @ApiModelProperty(value = "预收账款")
    private BigDecimal b2000042203;

    @LogField(tableName = "b200004", value = "b200004_2204", valueName = "合同负债")
    @ApiModelProperty(value = "合同负债")
    private BigDecimal b2000042204;

    @LogField(tableName = "b200004", value = "b200004_2211", valueName = "应付职工薪酬")
    @ApiModelProperty(value = "应付职工薪酬")
    private BigDecimal b2000042211;

    @LogField(tableName = "b200004", value = "b200004_221101", valueName = "职工工资")
    @ApiModelProperty(value = "职工工资")
    private BigDecimal b200004221101;

    @LogField(tableName = "b200004", value = "b200004_221102", valueName = "职工福利费")
    @ApiModelProperty(value = "职工福利费")
    private BigDecimal b200004221102;

    @LogField(tableName = "b200004", value = "b200004_221103", valueName = "社会保险费")
    @ApiModelProperty(value = "社会保险费")
    private BigDecimal b200004221103;

    @LogField(tableName = "b200004", value = "b200004_221104", valueName = "住房公积金")
    @ApiModelProperty(value = "住房公积金")
    private BigDecimal b200004221104;

    @LogField(tableName = "b200004", value = "b200004_221105", valueName = "工会经费")
    @ApiModelProperty(value = "工会经费")
    private BigDecimal b200004221105;

    @LogField(tableName = "b200004", value = "b200004_221106", valueName = "职工教育经费")
    @ApiModelProperty(value = "职工教育经费")
    private BigDecimal b200004221106;

    @LogField(tableName = "b200004", value = "b200004_221107", valueName = "年终奖")
    @ApiModelProperty(value = "年终奖")
    private BigDecimal b200004221107;

    @LogField(tableName = "b200004", value = "b200004_221108", valueName = "职工培训费用")
    @ApiModelProperty(value = "职工培训费用")
    private BigDecimal b200004221108;

    @LogField(tableName = "b200004", value = "b200004_221109", valueName = "补充养老保险")
    @ApiModelProperty(value = "补充养老保险")
    private BigDecimal b200004221109;

    @LogField(tableName = "b200004", value = "b200004_221110", valueName = "补充医疗保险")
    @ApiModelProperty(value = "补充医疗保险")
    private BigDecimal b200004221110;

    @LogField(tableName = "b200004", value = "b200004_221111", valueName = "股权激励")
    @ApiModelProperty(value = "股权激励")
    private BigDecimal b200004221111;

    @LogField(tableName = "b200004", value = "b200004_221112", valueName = "商业保险费")
    @ApiModelProperty(value = "商业保险费")
    private BigDecimal b200004221112;

    @LogField(tableName = "b200004", value = "b200004_221113", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200004221113;

    @LogField(tableName = "b200004", value = "b200004_2221", valueName = "应交税费")
    @ApiModelProperty(value = "应交税费")
    private BigDecimal b2000042221;

    @LogField(tableName = "b200004", value = "b200004_222101", valueName = "应交增值税")
    @ApiModelProperty(value = "应交增值税")
    private BigDecimal b200004222101;

    @LogField(tableName = "b200004", value = "b200004_22210101", valueName = "进项税额")
    @ApiModelProperty(value = "进项税额")
    private BigDecimal b20000422210101;

    @LogField(tableName = "b200004", value = "b200004_22210102", valueName = "销项税额")
    @ApiModelProperty(value = "销项税额")
    private BigDecimal b20000422210102;

    @LogField(tableName = "b200004", value = "b200004_22210103", valueName = "已交税金")
    @ApiModelProperty(value = "已交税金")
    private BigDecimal b20000422210103;

    @LogField(tableName = "b200004", value = "b200004_22210104", valueName = "出口抵减内销产品应纳税额")
    @ApiModelProperty(value = "出口抵减内销产品应纳税额")
    private BigDecimal b20000422210104;

    @LogField(tableName = "b200004", value = "b200004_22210105", valueName = "转出未交增值税")
    @ApiModelProperty(value = "转出未交增值税")
    private BigDecimal b20000422210105;

    @LogField(tableName = "b200004", value = "b200004_22210106", valueName = "进项税额转出")
    @ApiModelProperty(value = "进项税额转出")
    private BigDecimal b20000422210106;

    @LogField(tableName = "b200004", value = "b200004_22210107", valueName = "减免税款")
    @ApiModelProperty(value = "减免税款")
    private BigDecimal b20000422210107;

    @LogField(tableName = "b200004", value = "b200004_22210108", valueName = "出口退税")
    @ApiModelProperty(value = "出口退税")
    private BigDecimal b20000422210108;

    @LogField(tableName = "b200004", value = "b200004_22210109", valueName = "转出多交增值税")
    @ApiModelProperty(value = "转出多交增值税")
    private BigDecimal b20000422210109;

    @LogField(tableName = "b200004", value = "b200004_22210110", valueName = "销项税额抵减")
    @ApiModelProperty(value = "销项税额抵减")
    private BigDecimal b20000422210110;

    @LogField(tableName = "b200004", value = "b200004_222102", valueName = "未交增值税")
    @ApiModelProperty(value = "未交增值税")
    private BigDecimal b200004222102;

    @LogField(tableName = "b200004", value = "b200004_222103", valueName = "应交营业税")
    @ApiModelProperty(value = "应交营业税")
    private BigDecimal b200004222103;

    @LogField(tableName = "b200004", value = "b200004_222104", valueName = "应交消费税")
    @ApiModelProperty(value = "应交消费税")
    private BigDecimal b200004222104;

    @LogField(tableName = "b200004", value = "b200004_222105", valueName = "应交资源税")
    @ApiModelProperty(value = "应交资源税")
    private BigDecimal b200004222105;

    @LogField(tableName = "b200004", value = "b200004_222106", valueName = "应交所得税")
    @ApiModelProperty(value = "应交所得税")
    private BigDecimal b200004222106;

    @LogField(tableName = "b200004", value = "b200004_222107", valueName = "应交土地增值税")
    @ApiModelProperty(value = "应交土地增值税")
    private BigDecimal b200004222107;

    @LogField(tableName = "b200004", value = "b200004_222108", valueName = "应交城市维护建设税")
    @ApiModelProperty(value = "应交城市维护建设税")
    private BigDecimal b200004222108;

    @LogField(tableName = "b200004", value = "b200004_222109", valueName = "应交房产税")
    @ApiModelProperty(value = "应交房产税")
    private BigDecimal b200004222109;

    @LogField(tableName = "b200004", value = "b200004_222110", valueName = "应交土地使用税")
    @ApiModelProperty(value = "应交土地使用税")
    private BigDecimal b200004222110;

    @LogField(tableName = "b200004", value = "b200004_222111", valueName = "应交车船税")
    @ApiModelProperty(value = "应交车船税")
    private BigDecimal b200004222111;

    @LogField(tableName = "b200004", value = "b200004_222112", valueName = "应交个人所得税")
    @ApiModelProperty(value = "应交个人所得税")
    private BigDecimal b200004222112;

    @LogField(tableName = "b200004", value = "b200004_222113", valueName = "教育费附加")
    @ApiModelProperty(value = "教育费附加")
    private BigDecimal b200004222113;

    @LogField(tableName = "b200004", value = "b200004_222114", valueName = "地方教育费附加")
    @ApiModelProperty(value = "地方教育费附加")
    private BigDecimal b200004222114;

    @LogField(tableName = "b200004", value = "b200004_222115", valueName = "印花税")
    @ApiModelProperty(value = "印花税")
    private BigDecimal b200004222115;

    @LogField(tableName = "b200004", value = "b200004_222116", valueName = "待抵扣进项税额")
    @ApiModelProperty(value = "待抵扣进项税额")
    private BigDecimal b200004222116;

    @LogField(tableName = "b200004", value = "b200004_222117", valueName = "待认证进项税额")
    @ApiModelProperty(value = "待认证进项税额")
    private BigDecimal b200004222117;

    @LogField(tableName = "b200004", value = "b200004_222118", valueName = "预交增值税")
    @ApiModelProperty(value = "预交增值税")
    private BigDecimal b200004222118;

    @LogField(tableName = "b200004", value = "b200004_222119", valueName = "待转销项税额")
    @ApiModelProperty(value = "待转销项税额")
    private BigDecimal b200004222119;

    @LogField(tableName = "b200004", value = "b200004_222120", valueName = "增值税留抵税额")
    @ApiModelProperty(value = "增值税留抵税额")
    private BigDecimal b200004222120;

    @LogField(tableName = "b200004", value = "b200004_222121", valueName = "简易计税")
    @ApiModelProperty(value = "简易计税")
    private BigDecimal b200004222121;

    @LogField(tableName = "b200004", value = "b200004_222122", valueName = "转让金融商品应交增值税")
    @ApiModelProperty(value = "转让金融商品应交增值税")
    private BigDecimal b200004222122;

    @LogField(tableName = "b200004", value = "b200004_222123", valueName = "代扣代缴增值税")
    @ApiModelProperty(value = "代扣代缴增值税")
    private BigDecimal b200004222123;

    @LogField(tableName = "b200004", value = "b200004_2231", valueName = "应付利息")
    @ApiModelProperty(value = "应付利息")
    private BigDecimal b2000042231;

    @LogField(tableName = "b200004", value = "b200004_2232", valueName = "应付股利")
    @ApiModelProperty(value = "应付股利")
    private BigDecimal b2000042232;

    @LogField(tableName = "b200004", value = "b200004_2241", valueName = "其他应付款")
    @ApiModelProperty(value = "其他应付款")
    private BigDecimal b2000042241;

    @LogField(tableName = "b200004", value = "b200004_2251", valueName = "应付保单红利")
    @ApiModelProperty(value = "应付保单红利")
    private BigDecimal b2000042251;

    @LogField(tableName = "b200004", value = "b200004_2261", valueName = "应付分保账款")
    @ApiModelProperty(value = "应付分保账款")
    private BigDecimal b2000042261;

    @LogField(tableName = "b200004", value = "b200004_2311", valueName = "代理买卖证券款")
    @ApiModelProperty(value = "代理买卖证券款")
    private BigDecimal b2000042311;

    @LogField(tableName = "b200004", value = "b200004_2312", valueName = "代理承销证券款")
    @ApiModelProperty(value = "代理承销证券款")
    private BigDecimal b2000042312;

    @LogField(tableName = "b200004", value = "b200004_2313", valueName = "代理兑付证券款")
    @ApiModelProperty(value = "代理兑付证券款")
    private BigDecimal b2000042313;

    @LogField(tableName = "b200004", value = "b200004_2314", valueName = "代理业务负债")
    @ApiModelProperty(value = "代理业务负债")
    private BigDecimal b2000042314;

    @LogField(tableName = "b200004", value = "b200004_2401", valueName = "递延收益")
    @ApiModelProperty(value = "递延收益")
    private BigDecimal b2000042401;

    @LogField(tableName = "b200004", value = "b200004_2245", valueName = "持有待售负债")
    @ApiModelProperty(value = "持有待售负债")
    private BigDecimal b2000042245;

    @LogField(tableName = "b200004", value = "b200004_2501", valueName = "长期借款")
    @ApiModelProperty(value = "长期借款")
    private BigDecimal b2000042501;

    @LogField(tableName = "b200004", value = "b200004_2502", valueName = "应付债券")
    @ApiModelProperty(value = "应付债券")
    private BigDecimal b2000042502;

    @LogField(tableName = "b200004", value = "b200004_2601", valueName = "未到期责任准备金")
    @ApiModelProperty(value = "未到期责任准备金")
    private BigDecimal b2000042601;

    @LogField(tableName = "b200004", value = "b200004_2602", valueName = "保险责任准备金")
    @ApiModelProperty(value = "保险责任准备金")
    private BigDecimal b2000042602;

    @LogField(tableName = "b200004", value = "b200004_2611", valueName = "保户储金")
    @ApiModelProperty(value = "保户储金")
    private BigDecimal b2000042611;

    @LogField(tableName = "b200004", value = "b200004_2621", valueName = "独立账户负债")
    @ApiModelProperty(value = "独立账户负债")
    private BigDecimal b2000042621;

    @LogField(tableName = "b200004", value = "b200004_2701", valueName = "长期应付款")
    @ApiModelProperty(value = "长期应付款")
    private BigDecimal b2000042701;

    @LogField(tableName = "b200004", value = "b200004_2702", valueName = "未确认融资费用")
    @ApiModelProperty(value = "未确认融资费用")
    private BigDecimal b2000042702;

    @LogField(tableName = "b200004", value = "b200004_2711", valueName = "专项应付款")
    @ApiModelProperty(value = "专项应付款")
    private BigDecimal b2000042711;

    @LogField(tableName = "b200004", value = "b200004_2801", valueName = "预计负债")
    @ApiModelProperty(value = "预计负债")
    private BigDecimal b2000042801;

    @LogField(tableName = "b200004", value = "b200004_2901", valueName = "递延所得税负债")
    @ApiModelProperty(value = "递延所得税负债")
    private BigDecimal b2000042901;

    @LogField(tableName = "b200004", value = "b200004_3001", valueName = "清算资金往来")
    @ApiModelProperty(value = "清算资金往来")
    private BigDecimal b2000043001;

    @LogField(tableName = "b200004", value = "b200004_3002", valueName = "货币兑换")
    @ApiModelProperty(value = "货币兑换")
    private BigDecimal b2000043002;

    @LogField(tableName = "b200004", value = "b200004_3101", valueName = "衍生工具")
    @ApiModelProperty(value = "衍生工具")
    private BigDecimal b2000043101;

    @LogField(tableName = "b200004", value = "b200004_3201", valueName = "套期工具")
    @ApiModelProperty(value = "套期工具")
    private BigDecimal b2000043201;

    @LogField(tableName = "b200004", value = "b200004_3202", valueName = "被套期项目")
    @ApiModelProperty(value = "被套期项目")
    private BigDecimal b2000043202;

    @LogField(tableName = "b200004", value = "b200004_4001", valueName = "实收资本")
    @ApiModelProperty(value = "实收资本")
    private BigDecimal b2000044001;

    @LogField(tableName = "b200004", value = "b200004_4002", valueName = "资本公积")
    @ApiModelProperty(value = "资本公积")
    private BigDecimal b2000044002;

    @LogField(tableName = "b200004", value = "b200004_4003", valueName = "其他综合收益")
    @ApiModelProperty(value = "其他综合收益")
    private BigDecimal b2000044003;

    @LogField(tableName = "b200004", value = "b200004_4101", valueName = "盈余公积")
    @ApiModelProperty(value = "盈余公积")
    private BigDecimal b2000044101;

    @LogField(tableName = "b200004", value = "b200004_4102", valueName = "一般风险准备")
    @ApiModelProperty(value = "一般风险准备")
    private BigDecimal b2000044102;

    @LogField(tableName = "b200004", value = "b200004_4103", valueName = "本年利润")
    @ApiModelProperty(value = "本年利润")
    private BigDecimal b2000044103;

    @LogField(tableName = "b200004", value = "b200004_4104", valueName = "利润分配")
    @ApiModelProperty(value = "利润分配")
    private BigDecimal b2000044104;

    @LogField(tableName = "b200004", value = "b200004_4201", valueName = "库存股")
    @ApiModelProperty(value = "库存股")
    private BigDecimal b2000044201;

    @LogField(tableName = "b200004", value = "b200004_4301", valueName = "专项储备")
    @ApiModelProperty(value = "专项储备")
    private BigDecimal b2000044301;

    @LogField(tableName = "b200004", value = "b200004_5001", valueName = "生产成本")
    @ApiModelProperty(value = "生产成本")
    private BigDecimal b2000045001;

    @LogField(tableName = "b200004", value = "b200004_500101", valueName = "直接人工")
    @ApiModelProperty(value = "直接人工")
    private BigDecimal b200004500101;

    @LogField(tableName = "b200004", value = "b200004_500102", valueName = "直接材料")
    @ApiModelProperty(value = "直接材料")
    private BigDecimal b200004500102;

    @LogField(tableName = "b200004", value = "b200004_500103", valueName = "制造费用")
    @ApiModelProperty(value = "制造费用")
    private BigDecimal b200004500103;

    @LogField(tableName = "b200004", value = "b200004_500104", valueName = "职工工资")
    @ApiModelProperty(value = "职工工资")
    private BigDecimal b200004500104;

    @LogField(tableName = "b200004", value = "b200004_500105", valueName = "年终奖")
    @ApiModelProperty(value = "年终奖")
    private BigDecimal b200004500105;

    @LogField(tableName = "b200004", value = "b200004_500106", valueName = "津贴与补贴")
    @ApiModelProperty(value = "津贴与补贴")
    private BigDecimal b200004500106;

    @LogField(tableName = "b200004", value = "b200004_500107", valueName = "股权激励")
    @ApiModelProperty(value = "股权激励")
    private BigDecimal b200004500107;

    @LogField(tableName = "b200004", value = "b200004_500108", valueName = "职工教育经费")
    @ApiModelProperty(value = "职工教育经费")
    private BigDecimal b200004500108;

    @LogField(tableName = "b200004", value = "b200004_500109", valueName = "职工培训费用")
    @ApiModelProperty(value = "职工培训费用")
    private BigDecimal b200004500109;

    @LogField(tableName = "b200004", value = "b200004_500110", valueName = "工会经费")
    @ApiModelProperty(value = "工会经费")
    private BigDecimal b200004500110;

    @LogField(tableName = "b200004", value = "b200004_500111", valueName = "社会保险费")
    @ApiModelProperty(value = "社会保险费")
    private BigDecimal b200004500111;

    @LogField(tableName = "b200004", value = "b200004_500112", valueName = "住房公积金")
    @ApiModelProperty(value = "住房公积金")
    private BigDecimal b200004500112;

    @LogField(tableName = "b200004", value = "b200004_500113", valueName = "职工福利费")
    @ApiModelProperty(value = "职工福利费")
    private BigDecimal b200004500113;

    @LogField(tableName = "b200004", value = "b200004_500114", valueName = "补充养老保险")
    @ApiModelProperty(value = "补充养老保险")
    private BigDecimal b200004500114;

    @LogField(tableName = "b200004", value = "b200004_500115", valueName = "补充医疗保险")
    @ApiModelProperty(value = "补充医疗保险")
    private BigDecimal b200004500115;

    @LogField(tableName = "b200004", value = "b200004_500116", valueName = "其他职工工资")
    @ApiModelProperty(value = "其他职工工资")
    private BigDecimal b200004500116;

    @LogField(tableName = "b200004", value = "b200004_500117", valueName = "商业保险费")
    @ApiModelProperty(value = "商业保险费")
    private BigDecimal b200004500117;

    @LogField(tableName = "b200004", value = "b200004_500118", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200004500118;

    @LogField(tableName = "b200004", value = "b200004_5101", valueName = "制造费用")
    @ApiModelProperty(value = "制造费用")
    private BigDecimal b2000045101;

    @LogField(tableName = "b200004", value = "b200004_510101", valueName = "职工工资")
    @ApiModelProperty(value = "职工工资")
    private BigDecimal b200004510101;

    @LogField(tableName = "b200004", value = "b200004_510102", valueName = "年终奖")
    @ApiModelProperty(value = "年终奖")
    private BigDecimal b200004510102;

    @LogField(tableName = "b200004", value = "b200004_510103", valueName = "津贴与补贴")
    @ApiModelProperty(value = "津贴与补贴")
    private BigDecimal b200004510103;

    @LogField(tableName = "b200004", value = "b200004_510104", valueName = "股权激励")
    @ApiModelProperty(value = "股权激励")
    private BigDecimal b200004510104;

    @LogField(tableName = "b200004", value = "b200004_510105", valueName = "社会保险费")
    @ApiModelProperty(value = "社会保险费")
    private BigDecimal b200004510105;

    @LogField(tableName = "b200004", value = "b200004_510106", valueName = "住房公积金")
    @ApiModelProperty(value = "住房公积金")
    private BigDecimal b200004510106;

    @LogField(tableName = "b200004", value = "b200004_510107", valueName = "补充养老保险")
    @ApiModelProperty(value = "补充养老保险")
    private BigDecimal b200004510107;

    @LogField(tableName = "b200004", value = "b200004_510108", valueName = "补充医疗保险")
    @ApiModelProperty(value = "补充医疗保险")
    private BigDecimal b200004510108;

    @LogField(tableName = "b200004", value = "b200004_510109", valueName = "职工教育经费")
    @ApiModelProperty(value = "职工教育经费")
    private BigDecimal b200004510109;

    @LogField(tableName = "b200004", value = "b200004_510110", valueName = "职工培训费用")
    @ApiModelProperty(value = "职工培训费用")
    private BigDecimal b200004510110;

    @LogField(tableName = "b200004", value = "b200004_510111", valueName = "其他职工工资")
    @ApiModelProperty(value = "其他职工工资")
    private BigDecimal b200004510111;

    @LogField(tableName = "b200004", value = "b200004_510112", valueName = "职工福利费")
    @ApiModelProperty(value = "职工福利费")
    private BigDecimal b200004510112;

    @LogField(tableName = "b200004", value = "b200004_510113", valueName = "工会经费")
    @ApiModelProperty(value = "工会经费")
    private BigDecimal b200004510113;

    @LogField(tableName = "b200004", value = "b200004_510114", valueName = "商业保险费")
    @ApiModelProperty(value = "商业保险费")
    private BigDecimal b200004510114;

    @LogField(tableName = "b200004", value = "b200004_510115", valueName = "折旧费")
    @ApiModelProperty(value = "折旧费")
    private BigDecimal b200004510115;

    @LogField(tableName = "b200004", value = "b200004_510116", valueName = "修理费")
    @ApiModelProperty(value = "修理费")
    private BigDecimal b200004510116;

    @LogField(tableName = "b200004", value = "b200004_510117", valueName = "机物料消耗")
    @ApiModelProperty(value = "机物料消耗")
    private BigDecimal b200004510117;

    @LogField(tableName = "b200004", value = "b200004_510118", valueName = "低值易耗品摊销")
    @ApiModelProperty(value = "低值易耗品摊销")
    private BigDecimal b200004510118;

    @LogField(tableName = "b200004", value = "b200004_510119", valueName = "租赁费")
    @ApiModelProperty(value = "租赁费")
    private BigDecimal b200004510119;

    @LogField(tableName = "b200004", value = "b200004_510120", valueName = "运杂费")
    @ApiModelProperty(value = "运杂费")
    private BigDecimal b200004510120;

    @LogField(tableName = "b200004", value = "b200004_510121", valueName = "保险费")
    @ApiModelProperty(value = "保险费")
    private BigDecimal b200004510121;

    @LogField(tableName = "b200004", value = "b200004_510122", valueName = "差旅费")
    @ApiModelProperty(value = "差旅费")
    private BigDecimal b200004510122;

    @LogField(tableName = "b200004", value = "b200004_510123", valueName = "水电费")
    @ApiModelProperty(value = "水电费")
    private BigDecimal b200004510123;

    @LogField(tableName = "b200004", value = "b200004_510124", valueName = "外部加工费")
    @ApiModelProperty(value = "外部加工费")
    private BigDecimal b200004510124;

    @LogField(tableName = "b200004", value = "b200004_510125", valueName = "厂房租金")
    @ApiModelProperty(value = "厂房租金")
    private BigDecimal b200004510125;

    @LogField(tableName = "b200004", value = "b200004_510126", valueName = "装卸费")
    @ApiModelProperty(value = "装卸费")
    private BigDecimal b200004510126;

    @LogField(tableName = "b200004", value = "b200004_510127", valueName = "设计制图费")
    @ApiModelProperty(value = "设计制图费")
    private BigDecimal b200004510127;

    @LogField(tableName = "b200004", value = "b200004_510128", valueName = "劳动保护费")
    @ApiModelProperty(value = "劳动保护费")
    private BigDecimal b200004510128;

    @LogField(tableName = "b200004", value = "b200004_510129", valueName = "无形资产摊销费")
    @ApiModelProperty(value = "无形资产摊销费")
    private BigDecimal b200004510129;

    @LogField(tableName = "b200004", value = "b200004_510130", valueName = "长期待摊费用摊销费")
    @ApiModelProperty(value = "长期待摊费用摊销费")
    private BigDecimal b200004510130;

    @LogField(tableName = "b200004", value = "b200004_510131", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200004510131;

    @LogField(tableName = "b200004", value = "b200004_5201", valueName = "劳务成本")
    @ApiModelProperty(value = "劳务成本")
    private BigDecimal b2000045201;

    @LogField(tableName = "b200004", value = "b200004_520101", valueName = "职工工资")
    @ApiModelProperty(value = "职工工资")
    private BigDecimal b200004520101;

    @LogField(tableName = "b200004", value = "b200004_520102", valueName = "年终奖")
    @ApiModelProperty(value = "年终奖")
    private BigDecimal b200004520102;

    @LogField(tableName = "b200004", value = "b200004_520103", valueName = "津贴与补贴")
    @ApiModelProperty(value = "津贴与补贴")
    private BigDecimal b200004520103;

    @LogField(tableName = "b200004", value = "b200004_520104", valueName = "股权激励")
    @ApiModelProperty(value = "股权激励")
    private BigDecimal b200004520104;

    @LogField(tableName = "b200004", value = "b200004_520105", valueName = "职工教育经费")
    @ApiModelProperty(value = "职工教育经费")
    private BigDecimal b200004520105;

    @LogField(tableName = "b200004", value = "b200004_520106", valueName = "职工培训费用")
    @ApiModelProperty(value = "职工培训费用")
    private BigDecimal b200004520106;

    @LogField(tableName = "b200004", value = "b200004_520107", valueName = "工会经费")
    @ApiModelProperty(value = "工会经费")
    private BigDecimal b200004520107;

    @LogField(tableName = "b200004", value = "b200004_520108", valueName = "社会保险费")
    @ApiModelProperty(value = "社会保险费")
    private BigDecimal b200004520108;

    @LogField(tableName = "b200004", value = "b200004_520109", valueName = "住房公积金")
    @ApiModelProperty(value = "住房公积金")
    private BigDecimal b200004520109;

    @LogField(tableName = "b200004", value = "b200004_520110", valueName = "职工福利费")
    @ApiModelProperty(value = "职工福利费")
    private BigDecimal b200004520110;

    @LogField(tableName = "b200004", value = "b200004_520111", valueName = "补充养老保险")
    @ApiModelProperty(value = "补充养老保险")
    private BigDecimal b200004520111;

    @LogField(tableName = "b200004", value = "b200004_520112", valueName = "补充医疗保险")
    @ApiModelProperty(value = "补充医疗保险")
    private BigDecimal b200004520112;

    @LogField(tableName = "b200004", value = "b200004_520113", valueName = "其他职工工资")
    @ApiModelProperty(value = "其他职工工资")
    private BigDecimal b200004520113;

    @LogField(tableName = "b200004", value = "b200004_520114", valueName = "商业保险费")
    @ApiModelProperty(value = "商业保险费")
    private BigDecimal b200004520114;

    @LogField(tableName = "b200004", value = "b200004_520115", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200004520115;

    @LogField(tableName = "b200004", value = "b200004_5301", valueName = "研发支出")
    @ApiModelProperty(value = "研发支出")
    private BigDecimal b2000045301;

    @LogField(tableName = "b200004", value = "b200004_530101", valueName = "资本化支出")
    @ApiModelProperty(value = "资本化支出")
    private BigDecimal b200004530101;

    @LogField(tableName = "b200004", value = "b200004_53010101", valueName = "人工成本")
    @ApiModelProperty(value = "人工成本")
    private BigDecimal b20000453010101;

    @LogField(tableName = "b200004", value = "b200004_5301010101", valueName = "职工工资")
    @ApiModelProperty(value = "职工工资")
    private BigDecimal b2000045301010101;

    @LogField(tableName = "b200004", value = "b200004_5301010102", valueName = "年终奖")
    @ApiModelProperty(value = "年终奖")
    private BigDecimal b2000045301010102;

    @LogField(tableName = "b200004", value = "b200004_5301010103", valueName = "津贴与补贴")
    @ApiModelProperty(value = "津贴与补贴")
    private BigDecimal b2000045301010103;

    @LogField(tableName = "b200004", value = "b200004_5301010104", valueName = "股权激励")
    @ApiModelProperty(value = "股权激励")
    private BigDecimal b2000045301010104;

    @LogField(tableName = "b200004", value = "b200004_5301010105", valueName = "职工教育经费")
    @ApiModelProperty(value = "职工教育经费")
    private BigDecimal b2000045301010105;

    @LogField(tableName = "b200004", value = "b200004_5301010106", valueName = "工会经费")
    @ApiModelProperty(value = "工会经费")
    private BigDecimal b2000045301010106;

    @LogField(tableName = "b200004", value = "b200004_5301010107", valueName = "社会保险费")
    @ApiModelProperty(value = "社会保险费")
    private BigDecimal b2000045301010107;

    @LogField(tableName = "b200004", value = "b200004_5301010108", valueName = "商业保险费")
    @ApiModelProperty(value = "商业保险费")
    private BigDecimal b2000045301010108;

    @LogField(tableName = "b200004", value = "b200004_5301010109", valueName = "住房公积金")
    @ApiModelProperty(value = "住房公积金")
    private BigDecimal b2000045301010109;

    @LogField(tableName = "b200004", value = "b200004_5301010110", valueName = "职工福利费")
    @ApiModelProperty(value = "职工福利费")
    private BigDecimal b2000045301010110;

    @LogField(tableName = "b200004", value = "b200004_5301010111", valueName = "补充养老保险")
    @ApiModelProperty(value = "补充养老保险")
    private BigDecimal b2000045301010111;

    @LogField(tableName = "b200004", value = "b200004_5301010112", valueName = "补充医疗保险")
    @ApiModelProperty(value = "补充医疗保险")
    private BigDecimal b2000045301010112;

    @LogField(tableName = "b200004", value = "b200004_5301010113", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000045301010113;

    @LogField(tableName = "b200004", value = "b200004_53010102", valueName = "劳务费")
    @ApiModelProperty(value = "劳务费")
    private BigDecimal b20000453010102;

    @LogField(tableName = "b200004", value = "b200004_53010103", valueName = "直接投入")
    @ApiModelProperty(value = "直接投入")
    private BigDecimal b20000453010103;

    @LogField(tableName = "b200004", value = "b200004_5301010301", valueName = "材料费用")
    @ApiModelProperty(value = "材料费用")
    private BigDecimal b2000045301010301;

    @LogField(tableName = "b200004", value = "b200004_5301010302", valueName = "燃料费用")
    @ApiModelProperty(value = "燃料费用")
    private BigDecimal b2000045301010302;

    @LogField(tableName = "b200004", value = "b200004_5301010303", valueName = "动力费用")
    @ApiModelProperty(value = "动力费用")
    private BigDecimal b2000045301010303;

    @LogField(tableName = "b200004", value = "b200004_5301010304", valueName = "试用品开发及制造费")
    @ApiModelProperty(value = "试用品开发及制造费")
    private BigDecimal b2000045301010304;

    @LogField(tableName = "b200004", value = "b200004_5301010305", valueName = "样品测试购置费")
    @ApiModelProperty(value = "样品测试购置费")
    private BigDecimal b2000045301010305;

    @LogField(tableName = "b200004", value = "b200004_5301010306", valueName = "试制品检验费")
    @ApiModelProperty(value = "试制品检验费")
    private BigDecimal b2000045301010306;

    @LogField(tableName = "b200004", value = "b200004_5301010307", valueName = "运行维护维修费")
    @ApiModelProperty(value = "运行维护维修费")
    private BigDecimal b2000045301010307;

    @LogField(tableName = "b200004", value = "b200004_5301010308", valueName = "运行调整费")
    @ApiModelProperty(value = "运行调整费")
    private BigDecimal b2000045301010308;

    @LogField(tableName = "b200004", value = "b200004_5301010309", valueName = "运行检测费")
    @ApiModelProperty(value = "运行检测费")
    private BigDecimal b2000045301010309;

    @LogField(tableName = "b200004", value = "b200004_5301010310", valueName = "仪器租赁费")
    @ApiModelProperty(value = "仪器租赁费")
    private BigDecimal b2000045301010310;

    @LogField(tableName = "b200004", value = "b200004_5301010311", valueName = "设备租赁费")
    @ApiModelProperty(value = "设备租赁费")
    private BigDecimal b2000045301010311;

    @LogField(tableName = "b200004", value = "b200004_5301010312", valueName = "房屋租赁费")
    @ApiModelProperty(value = "房屋租赁费")
    private BigDecimal b2000045301010312;

    @LogField(tableName = "b200004", value = "b200004_5301010313", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000045301010313;

    @LogField(tableName = "b200004", value = "b200004_53010104", valueName = "低值易耗品摊销")
    @ApiModelProperty(value = "低值易耗品摊销")
    private BigDecimal b20000453010104;

    @LogField(tableName = "b200004", value = "b200004_5301010401", valueName = "直接消耗")
    @ApiModelProperty(value = "直接消耗")
    private BigDecimal b2000045301010401;

    @LogField(tableName = "b200004", value = "b200004_5301010402", valueName = "其他消耗")
    @ApiModelProperty(value = "其他消耗")
    private BigDecimal b2000045301010402;

    @LogField(tableName = "b200004", value = "b200004_53010105", valueName = "物料消耗")
    @ApiModelProperty(value = "物料消耗")
    private BigDecimal b20000453010105;

    @LogField(tableName = "b200004", value = "b200004_5301010501", valueName = "直接消耗")
    @ApiModelProperty(value = "直接消耗")
    private BigDecimal b2000045301010501;

    @LogField(tableName = "b200004", value = "b200004_5301010502", valueName = "其他消耗")
    @ApiModelProperty(value = "其他消耗")
    private BigDecimal b2000045301010502;

    @LogField(tableName = "b200004", value = "b200004_53010106", valueName = "折旧费用")
    @ApiModelProperty(value = "折旧费用")
    private BigDecimal b20000453010106;

    @LogField(tableName = "b200004", value = "b200004_5301010601", valueName = "仪器折旧费")
    @ApiModelProperty(value = "仪器折旧费")
    private BigDecimal b2000045301010601;

    @LogField(tableName = "b200004", value = "b200004_5301010602", valueName = "设备折旧费")
    @ApiModelProperty(value = "设备折旧费")
    private BigDecimal b2000045301010602;

    @LogField(tableName = "b200004", value = "b200004_5301010603", valueName = "房屋折旧费")
    @ApiModelProperty(value = "房屋折旧费")
    private BigDecimal b2000045301010603;

    @LogField(tableName = "b200004", value = "b200004_5301010604", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000045301010604;

    @LogField(tableName = "b200004", value = "b200004_53010107", valueName = "长期待摊费用")
    @ApiModelProperty(value = "长期待摊费用")
    private BigDecimal b20000453010107;

    @LogField(tableName = "b200004", value = "b200004_5301010701", valueName = "设施改建及改装费")
    @ApiModelProperty(value = "设施改建及改装费")
    private BigDecimal b2000045301010701;

    @LogField(tableName = "b200004", value = "b200004_5301010702", valueName = "设施装修及修理费")
    @ApiModelProperty(value = "设施装修及修理费")
    private BigDecimal b2000045301010702;

    @LogField(tableName = "b200004", value = "b200004_5301010703", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000045301010703;

    @LogField(tableName = "b200004", value = "b200004_53010108", valueName = "无形资产摊销")
    @ApiModelProperty(value = "无形资产摊销")
    private BigDecimal b20000453010108;

    @LogField(tableName = "b200004", value = "b200004_5301010801", valueName = "软件")
    @ApiModelProperty(value = "软件")
    private BigDecimal b2000045301010801;

    @LogField(tableName = "b200004", value = "b200004_5301010802", valueName = "专利权")
    @ApiModelProperty(value = "专利权")
    private BigDecimal b2000045301010802;

    @LogField(tableName = "b200004", value = "b200004_5301010803", valueName = "商标")
    @ApiModelProperty(value = "商标")
    private BigDecimal b2000045301010803;

    @LogField(tableName = "b200004", value = "b200004_5301010804", valueName = "非专利技术")
    @ApiModelProperty(value = "非专利技术")
    private BigDecimal b2000045301010804;

    @LogField(tableName = "b200004", value = "b200004_5301010805", valueName = "知识产权")
    @ApiModelProperty(value = "知识产权")
    private BigDecimal b2000045301010805;

    @LogField(tableName = "b200004", value = "b200004_5301010806", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000045301010806;

    @LogField(tableName = "b200004", value = "b200004_53010109", valueName = "设计费用")
    @ApiModelProperty(value = "设计费用")
    private BigDecimal b20000453010109;

    @LogField(tableName = "b200004", value = "b200004_5301010901", valueName = "新产品设计费")
    @ApiModelProperty(value = "新产品设计费")
    private BigDecimal b2000045301010901;

    @LogField(tableName = "b200004", value = "b200004_5301010902", valueName = "新工艺规程制定费")
    @ApiModelProperty(value = "新工艺规程制定费")
    private BigDecimal b2000045301010902;

    @LogField(tableName = "b200004", value = "b200004_5301010903", valueName = "新产品与新工艺工序设计费")
    @ApiModelProperty(value = "新产品与新工艺工序设计费")
    private BigDecimal b2000045301010903;

    @LogField(tableName = "b200004", value = "b200004_5301010904", valueName = "新产品与新工艺技术规范费")
    @ApiModelProperty(value = "新产品与新工艺技术规范费")
    private BigDecimal b2000045301010904;

    @LogField(tableName = "b200004", value = "b200004_5301010905", valueName = "新产品与新工艺操作设计费")
    @ApiModelProperty(value = "新产品与新工艺操作设计费")
    private BigDecimal b2000045301010905;

    @LogField(tableName = "b200004", value = "b200004_5301010906", valueName = "创意设计活动费")
    @ApiModelProperty(value = "创意设计活动费")
    private BigDecimal b2000045301010906;

    @LogField(tableName = "b200004", value = "b200004_5301010907", valueName = "其他设计费用")
    @ApiModelProperty(value = "其他设计费用")
    private BigDecimal b2000045301010907;

    @LogField(tableName = "b200004", value = "b200004_53010110", valueName = "装备调试费")
    @ApiModelProperty(value = "装备调试费")
    private BigDecimal b20000453010110;

    @LogField(tableName = "b200004", value = "b200004_53010111", valueName = "试验费")
    @ApiModelProperty(value = "试验费")
    private BigDecimal b20000453010111;

    @LogField(tableName = "b200004", value = "b200004_5301011101", valueName = "新药临床试验费")
    @ApiModelProperty(value = "新药临床试验费")
    private BigDecimal b2000045301011101;

    @LogField(tableName = "b200004", value = "b200004_5301011102", valueName = "勘探开发技术现场试验费")
    @ApiModelProperty(value = "勘探开发技术现场试验费")
    private BigDecimal b2000045301011102;

    @LogField(tableName = "b200004", value = "b200004_5301011103", valueName = "田间试验费")
    @ApiModelProperty(value = "田间试验费")
    private BigDecimal b2000045301011103;

    @LogField(tableName = "b200004", value = "b200004_5301011104", valueName = "其他试验费用")
    @ApiModelProperty(value = "其他试验费用")
    private BigDecimal b2000045301011104;

    @LogField(tableName = "b200004", value = "b200004_53010112", valueName = "图书资料费")
    @ApiModelProperty(value = "图书资料费")
    private BigDecimal b20000453010112;

    @LogField(tableName = "b200004", value = "b200004_53010113", valueName = "资料翻译费")
    @ApiModelProperty(value = "资料翻译费")
    private BigDecimal b20000453010113;

    @LogField(tableName = "b200004", value = "b200004_53010114", valueName = "专家咨询费")
    @ApiModelProperty(value = "专家咨询费")
    private BigDecimal b20000453010114;

    @LogField(tableName = "b200004", value = "b200004_53010115", valueName = "高新科技研发保险费")
    @ApiModelProperty(value = "高新科技研发保险费")
    private BigDecimal b20000453010115;

    @LogField(tableName = "b200004", value = "b200004_53010116", valueName = "成果费用")
    @ApiModelProperty(value = "成果费用")
    private BigDecimal b20000453010116;

    @LogField(tableName = "b200004", value = "b200004_5301011601", valueName = "检索费")
    @ApiModelProperty(value = "检索费")
    private BigDecimal b2000045301011601;

    @LogField(tableName = "b200004", value = "b200004_5301011602", valueName = "分析费")
    @ApiModelProperty(value = "分析费")
    private BigDecimal b2000045301011602;

    @LogField(tableName = "b200004", value = "b200004_5301011603", valueName = "评议费")
    @ApiModelProperty(value = "评议费")
    private BigDecimal b2000045301011603;

    @LogField(tableName = "b200004", value = "b200004_5301011604", valueName = "论证费")
    @ApiModelProperty(value = "论证费")
    private BigDecimal b2000045301011604;

    @LogField(tableName = "b200004", value = "b200004_5301011605", valueName = "鉴定费")
    @ApiModelProperty(value = "鉴定费")
    private BigDecimal b2000045301011605;

    @LogField(tableName = "b200004", value = "b200004_5301011606", valueName = "评审费")
    @ApiModelProperty(value = "评审费")
    private BigDecimal b2000045301011606;

    @LogField(tableName = "b200004", value = "b200004_5301011607", valueName = "评估费")
    @ApiModelProperty(value = "评估费")
    private BigDecimal b2000045301011607;

    @LogField(tableName = "b200004", value = "b200004_5301011608", valueName = "验收费")
    @ApiModelProperty(value = "验收费")
    private BigDecimal b2000045301011608;

    @LogField(tableName = "b200004", value = "b200004_5301011609", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000045301011609;

    @LogField(tableName = "b200004", value = "b200004_53010117", valueName = "知识产权费用")
    @ApiModelProperty(value = "知识产权费用")
    private BigDecimal b20000453010117;

    @LogField(tableName = "b200004", value = "b200004_5301011701", valueName = "申请费")
    @ApiModelProperty(value = "申请费")
    private BigDecimal b2000045301011701;

    @LogField(tableName = "b200004", value = "b200004_5301011702", valueName = "注册费")
    @ApiModelProperty(value = "注册费")
    private BigDecimal b2000045301011702;

    @LogField(tableName = "b200004", value = "b200004_5301011703", valueName = "代理费")
    @ApiModelProperty(value = "代理费")
    private BigDecimal b2000045301011703;

    @LogField(tableName = "b200004", value = "b200004_5301011704", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000045301011704;

    @LogField(tableName = "b200004", value = "b200004_53010118", valueName = "差旅费")
    @ApiModelProperty(value = "差旅费")
    private BigDecimal b20000453010118;

    @LogField(tableName = "b200004", value = "b200004_53010119", valueName = "会议费")
    @ApiModelProperty(value = "会议费")
    private BigDecimal b20000453010119;

    @LogField(tableName = "b200004", value = "b200004_53010120", valueName = "通讯费")
    @ApiModelProperty(value = "通讯费")
    private BigDecimal b20000453010120;

    @LogField(tableName = "b200004", value = "b200004_53010121", valueName = "办公费")
    @ApiModelProperty(value = "办公费")
    private BigDecimal b20000453010121;

    @LogField(tableName = "b200004", value = "b200004_53010122", valueName = "外事费")
    @ApiModelProperty(value = "外事费")
    private BigDecimal b20000453010122;

    @LogField(tableName = "b200004", value = "b200004_53010123", valueName = "研发人员培训费")
    @ApiModelProperty(value = "研发人员培训费")
    private BigDecimal b20000453010123;

    @LogField(tableName = "b200004", value = "b200004_53010124", valueName = "研发人员培养费")
    @ApiModelProperty(value = "研发人员培养费")
    private BigDecimal b20000453010124;

    @LogField(tableName = "b200004", value = "b200004_53010125", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b20000453010125;

    @LogField(tableName = "b200004", value = "b200004_53010126", valueName = "外包费用")
    @ApiModelProperty(value = "外包费用")
    private BigDecimal b20000453010126;

    @LogField(tableName = "b200004", value = "b200004_5301012601", valueName = "委托研发（境内进行）")
    @ApiModelProperty(value = "委托研发（境内进行）")
    private BigDecimal b2000045301012601;

    @LogField(tableName = "b200004", value = "b200004_5301012602", valueName = "委托研发（境外机构）")
    @ApiModelProperty(value = "委托研发（境外机构）")
    private BigDecimal b2000045301012602;

    @LogField(tableName = "b200004", value = "b200004_5301012603", valueName = "委托研发（境外个人）")
    @ApiModelProperty(value = "委托研发（境外个人）")
    private BigDecimal b2000045301012603;

    @LogField(tableName = "b200004", value = "b200004_5301012604", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000045301012604;

    @LogField(tableName = "b200004", value = "b200004_530102", valueName = "费用化支出")
    @ApiModelProperty(value = "费用化支出")
    private BigDecimal b200004530102;

    @LogField(tableName = "b200004", value = "b200004_53010201", valueName = "人工成本")
    @ApiModelProperty(value = "人工成本")
    private BigDecimal b20000453010201;

    @LogField(tableName = "b200004", value = "b200004_5301020101", valueName = "职工工资")
    @ApiModelProperty(value = "职工工资")
    private BigDecimal b2000045301020101;

    @LogField(tableName = "b200004", value = "b200004_5301020102", valueName = "年终奖")
    @ApiModelProperty(value = "年终奖")
    private BigDecimal b2000045301020102;

    @LogField(tableName = "b200004", value = "b200004_5301020103", valueName = "津贴与补贴")
    @ApiModelProperty(value = "津贴与补贴")
    private BigDecimal b2000045301020103;

    @LogField(tableName = "b200004", value = "b200004_5301020104", valueName = "股权激励")
    @ApiModelProperty(value = "股权激励")
    private BigDecimal b2000045301020104;

    @LogField(tableName = "b200004", value = "b200004_5301020105", valueName = "职工教育经费")
    @ApiModelProperty(value = "职工教育经费")
    private BigDecimal b2000045301020105;

    @LogField(tableName = "b200004", value = "b200004_5301020106", valueName = "工会经费")
    @ApiModelProperty(value = "工会经费")
    private BigDecimal b2000045301020106;

    @LogField(tableName = "b200004", value = "b200004_5301020107", valueName = "社会保险费")
    @ApiModelProperty(value = "社会保险费")
    private BigDecimal b2000045301020107;

    @LogField(tableName = "b200004", value = "b200004_5301020108", valueName = "商业保险费")
    @ApiModelProperty(value = "商业保险费")
    private BigDecimal b2000045301020108;

    @LogField(tableName = "b200004", value = "b200004_5301020109", valueName = "住房公积金")
    @ApiModelProperty(value = "住房公积金")
    private BigDecimal b2000045301020109;

    @LogField(tableName = "b200004", value = "b200004_5301020110", valueName = "职工福利费")
    @ApiModelProperty(value = "职工福利费")
    private BigDecimal b2000045301020110;

    @LogField(tableName = "b200004", value = "b200004_5301020111", valueName = "补充养老保险")
    @ApiModelProperty(value = "补充养老保险")
    private BigDecimal b2000045301020111;

    @LogField(tableName = "b200004", value = "b200004_5301020112", valueName = "补充养老保险")
    @ApiModelProperty(value = "补充养老保险")
    private BigDecimal b2000045301020112;

    @LogField(tableName = "b200004", value = "b200004_5301020113", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000045301020113;

    @LogField(tableName = "b200004", value = "b200004_53010202", valueName = "劳务费")
    @ApiModelProperty(value = "劳务费")
    private BigDecimal b20000453010202;

    @LogField(tableName = "b200004", value = "b200004_53010203", valueName = "直接投入")
    @ApiModelProperty(value = "直接投入")
    private BigDecimal b20000453010203;

    @LogField(tableName = "b200004", value = "b200004_5301020301", valueName = "材料费用")
    @ApiModelProperty(value = "材料费用")
    private BigDecimal b2000045301020301;

    @LogField(tableName = "b200004", value = "b200004_5301020302", valueName = "燃料费用")
    @ApiModelProperty(value = "燃料费用")
    private BigDecimal b2000045301020302;

    @LogField(tableName = "b200004", value = "b200004_5301020303", valueName = "动力费用")
    @ApiModelProperty(value = "动力费用")
    private BigDecimal b2000045301020303;

    @LogField(tableName = "b200004", value = "b200004_5301020304", valueName = "试用品开发及制造费")
    @ApiModelProperty(value = "试用品开发及制造费")
    private BigDecimal b2000045301020304;

    @LogField(tableName = "b200004", value = "b200004_5301020305", valueName = "样品测试购置费")
    @ApiModelProperty(value = "样品测试购置费")
    private BigDecimal b2000045301020305;

    @LogField(tableName = "b200004", value = "b200004_5301020306", valueName = "试制品检验费")
    @ApiModelProperty(value = "试制品检验费")
    private BigDecimal b2000045301020306;

    @LogField(tableName = "b200004", value = "b200004_5301020307", valueName = "运行维护维修费")
    @ApiModelProperty(value = "运行维护维修费")
    private BigDecimal b2000045301020307;

    @LogField(tableName = "b200004", value = "b200004_5301020308", valueName = "运行调整费")
    @ApiModelProperty(value = "运行调整费")
    private BigDecimal b2000045301020308;

    @LogField(tableName = "b200004", value = "b200004_5301020309", valueName = "运行检测费")
    @ApiModelProperty(value = "运行检测费")
    private BigDecimal b2000045301020309;

    @LogField(tableName = "b200004", value = "b200004_5301020310", valueName = "仪器租赁费")
    @ApiModelProperty(value = "仪器租赁费")
    private BigDecimal b2000045301020310;

    @LogField(tableName = "b200004", value = "b200004_5301020311", valueName = "设备租赁费")
    @ApiModelProperty(value = "设备租赁费")
    private BigDecimal b2000045301020311;

    @LogField(tableName = "b200004", value = "b200004_5301020312", valueName = "房屋租赁费")
    @ApiModelProperty(value = "房屋租赁费")
    private BigDecimal b2000045301020312;

    @LogField(tableName = "b200004", value = "b200004_5301020313", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000045301020313;

    @LogField(tableName = "b200004", value = "b200004_53010204", valueName = "低值易耗品摊销")
    @ApiModelProperty(value = "低值易耗品摊销")
    private BigDecimal b20000453010204;

    @LogField(tableName = "b200004", value = "b200004_5301020401", valueName = "直接消耗")
    @ApiModelProperty(value = "直接消耗")
    private BigDecimal b2000045301020401;

    @LogField(tableName = "b200004", value = "b200004_5301020402", valueName = "其他消耗")
    @ApiModelProperty(value = "其他消耗")
    private BigDecimal b2000045301020402;

    @LogField(tableName = "b200004", value = "b200004_53010205", valueName = "物料消耗")
    @ApiModelProperty(value = "物料消耗")
    private BigDecimal b20000453010205;

    @LogField(tableName = "b200004", value = "b200004_5301020501", valueName = "直接消耗")
    @ApiModelProperty(value = "直接消耗")
    private BigDecimal b2000045301020501;

    @LogField(tableName = "b200004", value = "b200004_5301020502", valueName = "其他消耗")
    @ApiModelProperty(value = "其他消耗")
    private BigDecimal b2000045301020502;

    @LogField(tableName = "b200004", value = "b200004_53010206", valueName = "折旧费用")
    @ApiModelProperty(value = "折旧费用")
    private BigDecimal b20000453010206;

    @LogField(tableName = "b200004", value = "b200004_5301020601", valueName = "仪器折旧费")
    @ApiModelProperty(value = "仪器折旧费")
    private BigDecimal b2000045301020601;

    @LogField(tableName = "b200004", value = "b200004_5301020602", valueName = "设备折旧费")
    @ApiModelProperty(value = "设备折旧费")
    private BigDecimal b2000045301020602;

    @LogField(tableName = "b200004", value = "b200004_5301020603", valueName = "房屋折旧费")
    @ApiModelProperty(value = "房屋折旧费")
    private BigDecimal b2000045301020603;

    @LogField(tableName = "b200004", value = "b200004_5301020604", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000045301020604;

    @LogField(tableName = "b200004", value = "b200004_53010207", valueName = "长期待摊费用")
    @ApiModelProperty(value = "长期待摊费用")
    private BigDecimal b20000453010207;

    @LogField(tableName = "b200004", value = "b200004_5301020701", valueName = "设施改建及改装费")
    @ApiModelProperty(value = "设施改建及改装费")
    private BigDecimal b2000045301020701;

    @LogField(tableName = "b200004", value = "b200004_5301020702", valueName = "设施装修及修理费")
    @ApiModelProperty(value = "设施装修及修理费")
    private BigDecimal b2000045301020702;

    @LogField(tableName = "b200004", value = "b200004_5301020703", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000045301020703;

    @LogField(tableName = "b200004", value = "b200004_53010208", valueName = "无形资产摊销")
    @ApiModelProperty(value = "无形资产摊销")
    private BigDecimal b20000453010208;

    @LogField(tableName = "b200004", value = "b200004_5301020801", valueName = "软件")
    @ApiModelProperty(value = "软件")
    private BigDecimal b2000045301020801;

    @LogField(tableName = "b200004", value = "b200004_5301020802", valueName = "专利权")
    @ApiModelProperty(value = "专利权")
    private BigDecimal b2000045301020802;

    @LogField(tableName = "b200004", value = "b200004_5301020803", valueName = "商标")
    @ApiModelProperty(value = "商标")
    private BigDecimal b2000045301020803;

    @LogField(tableName = "b200004", value = "b200004_5301020804", valueName = "非专利技术")
    @ApiModelProperty(value = "非专利技术")
    private BigDecimal b2000045301020804;

    @LogField(tableName = "b200004", value = "b200004_5301020805", valueName = "知识产权")
    @ApiModelProperty(value = "知识产权")
    private BigDecimal b2000045301020805;

    @LogField(tableName = "b200004", value = "b200004_5301020806", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000045301020806;

    @LogField(tableName = "b200004", value = "b200004_53010209", valueName = "设计费用")
    @ApiModelProperty(value = "设计费用")
    private BigDecimal b20000453010209;

    @LogField(tableName = "b200004", value = "b200004_5301020901", valueName = "新产品设计费")
    @ApiModelProperty(value = "新产品设计费")
    private BigDecimal b2000045301020901;

    @LogField(tableName = "b200004", value = "b200004_5301020902", valueName = "新工艺规程制定费")
    @ApiModelProperty(value = "新工艺规程制定费")
    private BigDecimal b2000045301020902;

    @LogField(tableName = "b200004", value = "b200004_5301020903", valueName = "新产品与新工艺工序设计费")
    @ApiModelProperty(value = "新产品与新工艺工序设计费")
    private BigDecimal b2000045301020903;

    @LogField(tableName = "b200004", value = "b200004_5301020904", valueName = "新产品与新工艺技术规范费")
    @ApiModelProperty(value = "新产品与新工艺技术规范费")
    private BigDecimal b2000045301020904;

    @LogField(tableName = "b200004", value = "b200004_5301020905", valueName = "新产品与新工艺操作设计费")
    @ApiModelProperty(value = "新产品与新工艺操作设计费")
    private BigDecimal b2000045301020905;

    @LogField(tableName = "b200004", value = "b200004_5301020906", valueName = "创意设计活动费")
    @ApiModelProperty(value = "创意设计活动费")
    private BigDecimal b2000045301020906;

    @LogField(tableName = "b200004", value = "b200004_5301020907", valueName = "其他设计费用")
    @ApiModelProperty(value = "其他设计费用")
    private BigDecimal b2000045301020907;

    @LogField(tableName = "b200004", value = "b200004_53010210", valueName = "装备调试费")
    @ApiModelProperty(value = "装备调试费")
    private BigDecimal b20000453010210;

    @LogField(tableName = "b200004", value = "b200004_53010211", valueName = "试验费")
    @ApiModelProperty(value = "试验费")
    private BigDecimal b20000453010211;

    @LogField(tableName = "b200004", value = "b200004_5301021101", valueName = "新药临床试验费")
    @ApiModelProperty(value = "新药临床试验费")
    private BigDecimal b2000045301021101;

    @LogField(tableName = "b200004", value = "b200004_5301021102", valueName = "勘探开发技术现场试验费")
    @ApiModelProperty(value = "勘探开发技术现场试验费")
    private BigDecimal b2000045301021102;

    @LogField(tableName = "b200004", value = "b200004_5301021103", valueName = "田间试验费")
    @ApiModelProperty(value = "田间试验费")
    private BigDecimal b2000045301021103;

    @LogField(tableName = "b200004", value = "b200004_5301021104", valueName = "其他试验费用")
    @ApiModelProperty(value = "其他试验费用")
    private BigDecimal b2000045301021104;

    @LogField(tableName = "b200004", value = "b200004_53010212", valueName = "图书资料费")
    @ApiModelProperty(value = "图书资料费")
    private BigDecimal b20000453010212;

    @LogField(tableName = "b200004", value = "b200004_53010213", valueName = "资料翻译费")
    @ApiModelProperty(value = "资料翻译费")
    private BigDecimal b20000453010213;

    @LogField(tableName = "b200004", value = "b200004_53010214", valueName = "专家咨询费")
    @ApiModelProperty(value = "专家咨询费")
    private BigDecimal b20000453010214;

    @LogField(tableName = "b200004", value = "b200004_53010215", valueName = "高新科技研发保险费")
    @ApiModelProperty(value = "高新科技研发保险费")
    private BigDecimal b20000453010215;

    @LogField(tableName = "b200004", value = "b200004_53010216", valueName = "成果费用")
    @ApiModelProperty(value = "成果费用")
    private BigDecimal b20000453010216;

    @LogField(tableName = "b200004", value = "b200004_5301021601", valueName = "检索费")
    @ApiModelProperty(value = "检索费")
    private BigDecimal b2000045301021601;

    @LogField(tableName = "b200004", value = "b200004_5301021602", valueName = "分析费")
    @ApiModelProperty(value = "分析费")
    private BigDecimal b2000045301021602;

    @LogField(tableName = "b200004", value = "b200004_5301021603", valueName = "评议费")
    @ApiModelProperty(value = "评议费")
    private BigDecimal b2000045301021603;

    @LogField(tableName = "b200004", value = "b200004_5301021604", valueName = "论证费")
    @ApiModelProperty(value = "论证费")
    private BigDecimal b2000045301021604;

    @LogField(tableName = "b200004", value = "b200004_5301021605", valueName = "鉴定费")
    @ApiModelProperty(value = "鉴定费")
    private BigDecimal b2000045301021605;

    @LogField(tableName = "b200004", value = "b200004_5301021606", valueName = "评审费")
    @ApiModelProperty(value = "评审费")
    private BigDecimal b2000045301021606;

    @LogField(tableName = "b200004", value = "b200004_5301021607", valueName = "评估费")
    @ApiModelProperty(value = "评估费")
    private BigDecimal b2000045301021607;

    @LogField(tableName = "b200004", value = "b200004_5301021608", valueName = "验收费")
    @ApiModelProperty(value = "验收费")
    private BigDecimal b2000045301021608;

    @LogField(tableName = "b200004", value = "b200004_5301021609", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000045301021609;

    @LogField(tableName = "b200004", value = "b200004_53010217", valueName = "知识产权费用")
    @ApiModelProperty(value = "知识产权费用")
    private BigDecimal b20000453010217;

    @LogField(tableName = "b200004", value = "b200004_5301021701", valueName = "申请费")
    @ApiModelProperty(value = "申请费")
    private BigDecimal b2000045301021701;

    @LogField(tableName = "b200004", value = "b200004_5301021702", valueName = "注册费")
    @ApiModelProperty(value = "注册费")
    private BigDecimal b2000045301021702;

    @LogField(tableName = "b200004", value = "b200004_5301021703", valueName = "代理费")
    @ApiModelProperty(value = "代理费")
    private BigDecimal b2000045301021703;

    @LogField(tableName = "b200004", value = "b200004_5301021704", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000045301021704;

    @LogField(tableName = "b200004", value = "b200004_53010218", valueName = "差旅费")
    @ApiModelProperty(value = "差旅费")
    private BigDecimal b20000453010218;

    @LogField(tableName = "b200004", value = "b200004_53010219", valueName = "会议费")
    @ApiModelProperty(value = "会议费")
    private BigDecimal b20000453010219;

    @LogField(tableName = "b200004", value = "b200004_53010220", valueName = "通讯费")
    @ApiModelProperty(value = "通讯费")
    private BigDecimal b20000453010220;

    @LogField(tableName = "b200004", value = "b200004_53010221", valueName = "办公费")
    @ApiModelProperty(value = "办公费")
    private BigDecimal b20000453010221;

    @LogField(tableName = "b200004", value = "b200004_53010222", valueName = "外事费")
    @ApiModelProperty(value = "外事费")
    private BigDecimal b20000453010222;

    @LogField(tableName = "b200004", value = "b200004_53010223", valueName = "研发人员培训费")
    @ApiModelProperty(value = "研发人员培训费")
    private BigDecimal b20000453010223;

    @LogField(tableName = "b200004", value = "b200004_53010224", valueName = "研发人员培养费")
    @ApiModelProperty(value = "研发人员培养费")
    private BigDecimal b20000453010224;

    @LogField(tableName = "b200004", value = "b200004_53010225", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b20000453010225;

    @LogField(tableName = "b200004", value = "b200004_53010226", valueName = "外包费用")
    @ApiModelProperty(value = "外包费用")
    private BigDecimal b20000453010226;

    @LogField(tableName = "b200004", value = "b200004_5301022601", valueName = "委托研发（境内进行）")
    @ApiModelProperty(value = "委托研发（境内进行）")
    private BigDecimal b2000045301022601;

    @LogField(tableName = "b200004", value = "b200004_5301022602", valueName = "委托研发（境外机构）")
    @ApiModelProperty(value = "委托研发（境外机构）")
    private BigDecimal b2000045301022602;

    @LogField(tableName = "b200004", value = "b200004_5301022603", valueName = "委托研发（境外个人）")
    @ApiModelProperty(value = "委托研发（境外个人）")
    private BigDecimal b2000045301022603;

    @LogField(tableName = "b200004", value = "b200004_5301022604", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000045301022604;

    @LogField(tableName = "b200004", value = "b200004_5401", valueName = "工程施工")
    @ApiModelProperty(value = "工程施工")
    private BigDecimal b2000045401;

    @LogField(tableName = "b200004", value = "b200004_5402", valueName = "工程结算")
    @ApiModelProperty(value = "工程结算")
    private BigDecimal b2000045402;

    @LogField(tableName = "b200004", value = "b200004_5403", valueName = "机械作业")
    @ApiModelProperty(value = "机械作业")
    private BigDecimal b2000045403;

    @LogField(tableName = "b200004", value = "b200004_5501", valueName = "合同履约成本")
    @ApiModelProperty(value = "合同履约成本")
    private BigDecimal b2000045501;

    @LogField(tableName = "b200004", value = "b200004_550101", valueName = "职工工资")
    @ApiModelProperty(value = "职工工资")
    private BigDecimal b200004550101;

    @LogField(tableName = "b200004", value = "b200004_550102", valueName = "年终奖")
    @ApiModelProperty(value = "年终奖")
    private BigDecimal b200004550102;

    @LogField(tableName = "b200004", value = "b200004_550103", valueName = "津贴与补贴")
    @ApiModelProperty(value = "津贴与补贴")
    private BigDecimal b200004550103;

    @LogField(tableName = "b200004", value = "b200004_550104", valueName = "股权激励")
    @ApiModelProperty(value = "股权激励")
    private BigDecimal b200004550104;

    @LogField(tableName = "b200004", value = "b200004_550105", valueName = "职工教育经费")
    @ApiModelProperty(value = "职工教育经费")
    private BigDecimal b200004550105;

    @LogField(tableName = "b200004", value = "b200004_550106", valueName = "职工培训费用")
    @ApiModelProperty(value = "职工培训费用")
    private BigDecimal b200004550106;

    @LogField(tableName = "b200004", value = "b200004_550107", valueName = "工会经费")
    @ApiModelProperty(value = "工会经费")
    private BigDecimal b200004550107;

    @LogField(tableName = "b200004", value = "b200004_550108", valueName = "社会保险费")
    @ApiModelProperty(value = "社会保险费")
    private BigDecimal b200004550108;

    @LogField(tableName = "b200004", value = "b200004_550109", valueName = "住房公积金")
    @ApiModelProperty(value = "住房公积金")
    private BigDecimal b200004550109;

    @LogField(tableName = "b200004", value = "b200004_550110", valueName = "职工福利费")
    @ApiModelProperty(value = "职工福利费")
    private BigDecimal b200004550110;

    @LogField(tableName = "b200004", value = "b200004_550111", valueName = "补充养老保险")
    @ApiModelProperty(value = "补充养老保险")
    private BigDecimal b200004550111;

    @LogField(tableName = "b200004", value = "b200004_550112", valueName = "补充医疗保险")
    @ApiModelProperty(value = "补充医疗保险")
    private BigDecimal b200004550112;

    @LogField(tableName = "b200004", value = "b200004_550113", valueName = "其他职工工资")
    @ApiModelProperty(value = "其他职工工资")
    private BigDecimal b200004550113;

    @LogField(tableName = "b200004", value = "b200004_550114", valueName = "商业保险费")
    @ApiModelProperty(value = "商业保险费")
    private BigDecimal b200004550114;

    @LogField(tableName = "b200004", value = "b200004_550115", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200004550115;

    @LogField(tableName = "b200004", value = "b200004_5502", valueName = "合同履约成本减值准备")
    @ApiModelProperty(value = "合同履约成本减值准备")
    private BigDecimal b2000045502;

    @LogField(tableName = "b200004", value = "b200004_5503", valueName = "合同取得成本")
    @ApiModelProperty(value = "合同取得成本")
    private BigDecimal b2000045503;

    @LogField(tableName = "b200004", value = "b200004_5504", valueName = "合同取得成本减值准备")
    @ApiModelProperty(value = "合同取得成本减值准备")
    private BigDecimal b2000045504;

    @LogField(tableName = "b200004", value = "b200004_6001", valueName = "主营业务收入")
    @ApiModelProperty(value = "主营业务收入")
    private BigDecimal b2000046001;

    @LogField(tableName = "b200004", value = "b200004_600101", valueName = "销售商品收入")
    @ApiModelProperty(value = "销售商品收入")
    private BigDecimal b200004600101;

    @LogField(tableName = "b200004", value = "b200004_600102", valueName = "提供劳务收入")
    @ApiModelProperty(value = "提供劳务收入")
    private BigDecimal b200004600102;

    @LogField(tableName = "b200004", value = "b200004_600103", valueName = "建造合同收入")
    @ApiModelProperty(value = "建造合同收入")
    private BigDecimal b200004600103;

    @LogField(tableName = "b200004", value = "b200004_600104", valueName = "让渡资产使用权收入")
    @ApiModelProperty(value = "让渡资产使用权收入")
    private BigDecimal b200004600104;

    @LogField(tableName = "b200004", value = "b200004_600105", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200004600105;

    @LogField(tableName = "b200004", value = "b200004_6011", valueName = "利息收入")
    @ApiModelProperty(value = "利息收入")
    private BigDecimal b2000046011;

    @LogField(tableName = "b200004", value = "b200004_6021", valueName = "手续费及佣金收入")
    @ApiModelProperty(value = "手续费及佣金收入")
    private BigDecimal b2000046021;

    @LogField(tableName = "b200004", value = "b200004_6031", valueName = "保费收入")
    @ApiModelProperty(value = "保费收入")
    private BigDecimal b2000046031;

    @LogField(tableName = "b200004", value = "b200004_6041", valueName = "租赁收入")
    @ApiModelProperty(value = "租赁收入")
    private BigDecimal b2000046041;

    @LogField(tableName = "b200004", value = "b200004_6051", valueName = "其他业务收入")
    @ApiModelProperty(value = "其他业务收入")
    private BigDecimal b2000046051;

    @LogField(tableName = "b200004", value = "b200004_605101", valueName = "销售材料收入")
    @ApiModelProperty(value = "销售材料收入")
    private BigDecimal b200004605101;

    @LogField(tableName = "b200004", value = "b200004_605102", valueName = "出租固定资产收入")
    @ApiModelProperty(value = "出租固定资产收入")
    private BigDecimal b200004605102;

    @LogField(tableName = "b200004", value = "b200004_605103", valueName = "出租无形资产收入")
    @ApiModelProperty(value = "出租无形资产收入")
    private BigDecimal b200004605103;

    @LogField(tableName = "b200004", value = "b200004_605104", valueName = "出租包装物和商品收入")
    @ApiModelProperty(value = "出租包装物和商品收入")
    private BigDecimal b200004605104;

    @LogField(tableName = "b200004", value = "b200004_605105", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200004605105;

    @LogField(tableName = "b200004", value = "b200004_6061", valueName = "汇兑损益")
    @ApiModelProperty(value = "汇兑损益")
    private BigDecimal b2000046061;

    @LogField(tableName = "b200004", value = "b200004_6101", valueName = "公允价值变动损益")
    @ApiModelProperty(value = "公允价值变动损益")
    private BigDecimal b2000046101;

    @LogField(tableName = "b200004", value = "b200004_6111", valueName = "投资收益")
    @ApiModelProperty(value = "投资收益")
    private BigDecimal b2000046111;

    @LogField(tableName = "b200004", value = "b200004_611101", valueName = "交易性金融资产")
    @ApiModelProperty(value = "交易性金融资产")
    private BigDecimal b200004611101;

    @LogField(tableName = "b200004", value = "b200004_61110101", valueName = "国债")
    @ApiModelProperty(value = "国债")
    private BigDecimal b20000461110101;

    @LogField(tableName = "b200004", value = "b200004_6111010101", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000046111010101;

    @LogField(tableName = "b200004", value = "b200004_6111010102", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000046111010102;

    @LogField(tableName = "b200004", value = "b200004_61110102", valueName = "证券投资基金")
    @ApiModelProperty(value = "证券投资基金")
    private BigDecimal b20000461110102;

    @LogField(tableName = "b200004", value = "b200004_6111010201", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000046111010201;

    @LogField(tableName = "b200004", value = "b200004_6111010202", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000046111010202;

    @LogField(tableName = "b200004", value = "b200004_61110103", valueName = "地方政府债券")
    @ApiModelProperty(value = "地方政府债券")
    private BigDecimal b20000461110103;

    @LogField(tableName = "b200004", value = "b200004_6111010301", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000046111010301;

    @LogField(tableName = "b200004", value = "b200004_6111010302", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000046111010302;

    @LogField(tableName = "b200004", value = "b200004_61110104", valueName = "铁路债券")
    @ApiModelProperty(value = "铁路债券")
    private BigDecimal b20000461110104;

    @LogField(tableName = "b200004", value = "b200004_6111010401", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000046111010401;

    @LogField(tableName = "b200004", value = "b200004_6111010402", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000046111010402;

    @LogField(tableName = "b200004", value = "b200004_61110105", valueName = "连续持有公开发行并上市流通12个月及以上的股票")
    @ApiModelProperty(value = "连续持有公开发行并上市流通12个月及以上的股票")
    private BigDecimal b20000461110105;

    @LogField(tableName = "b200004", value = "b200004_6111010501", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000046111010501;

    @LogField(tableName = "b200004", value = "b200004_6111010502", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000046111010502;

    @LogField(tableName = "b200004", value = "b200004_61110106", valueName = "永续债")
    @ApiModelProperty(value = "永续债")
    private BigDecimal b20000461110106;

    @LogField(tableName = "b200004", value = "b200004_6111010601", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000046111010601;

    @LogField(tableName = "b200004", value = "b200004_6111010602", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000046111010602;

    @LogField(tableName = "b200004", value = "b200004_61110107", valueName = "创新企业CDR")
    @ApiModelProperty(value = "创新企业CDR")
    private BigDecimal b20000461110107;

    @LogField(tableName = "b200004", value = "b200004_6111010701", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000046111010701;

    @LogField(tableName = "b200004", value = "b200004_6111010702", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000046111010702;

    @LogField(tableName = "b200004", value = "b200004_61110108", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b20000461110108;

    @LogField(tableName = "b200004", value = "b200004_6111010801", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000046111010801;

    @LogField(tableName = "b200004", value = "b200004_6111010802", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000046111010802;

    @LogField(tableName = "b200004", value = "b200004_611102", valueName = "可供出售金融资产")
    @ApiModelProperty(value = "可供出售金融资产")
    private BigDecimal b200004611102;

    @LogField(tableName = "b200004", value = "b200004_61110201", valueName = "国债")
    @ApiModelProperty(value = "国债")
    private BigDecimal b20000461110201;

    @LogField(tableName = "b200004", value = "b200004_6111020101", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000046111020101;

    @LogField(tableName = "b200004", value = "b200004_6111020102", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000046111020102;

    @LogField(tableName = "b200004", value = "b200004_61110202", valueName = "证券投资基金")
    @ApiModelProperty(value = "证券投资基金")
    private BigDecimal b20000461110202;

    @LogField(tableName = "b200004", value = "b200004_6111020201", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000046111020201;

    @LogField(tableName = "b200004", value = "b200004_6111020202", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000046111020202;

    @LogField(tableName = "b200004", value = "b200004_61110203", valueName = "地方政府债券")
    @ApiModelProperty(value = "地方政府债券")
    private BigDecimal b20000461110203;

    @LogField(tableName = "b200004", value = "b200004_6111020301", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000046111020301;

    @LogField(tableName = "b200004", value = "b200004_6111020302", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000046111020302;

    @LogField(tableName = "b200004", value = "b200004_61110204", valueName = "铁路债券")
    @ApiModelProperty(value = "铁路债券")
    private BigDecimal b20000461110204;

    @LogField(tableName = "b200004", value = "b200004_6111020401", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000046111020401;

    @LogField(tableName = "b200004", value = "b200004_6111020402", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000046111020402;

    @LogField(tableName = "b200004", value = "b200004_61110205", valueName = "连续持有公开发行并上市流通12个月及以上的股票")
    @ApiModelProperty(value = "连续持有公开发行并上市流通12个月及以上的股票")
    private BigDecimal b20000461110205;

    @LogField(tableName = "b200004", value = "b200004_6111020501", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000046111020501;

    @LogField(tableName = "b200004", value = "b200004_6111020502", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000046111020502;

    @LogField(tableName = "b200004", value = "b200004_61110206", valueName = "永续债")
    @ApiModelProperty(value = "永续债")
    private BigDecimal b20000461110206;

    @LogField(tableName = "b200004", value = "b200004_6111020601", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000046111020601;

    @LogField(tableName = "b200004", value = "b200004_6111020602", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000046111020602;

    @LogField(tableName = "b200004", value = "b200004_61110207", valueName = "创新企业CDR")
    @ApiModelProperty(value = "创新企业CDR")
    private BigDecimal b20000461110207;

    @LogField(tableName = "b200004", value = "b200004_6111020701", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000046111020701;

    @LogField(tableName = "b200004", value = "b200004_6111020702", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000046111020702;

    @LogField(tableName = "b200004", value = "b200004_61110208", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b20000461110208;

    @LogField(tableName = "b200004", value = "b200004_6111020801", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000046111020801;

    @LogField(tableName = "b200004", value = "b200004_6111020802", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000046111020802;

    @LogField(tableName = "b200004", value = "b200004_611103", valueName = "持有至到期投资")
    @ApiModelProperty(value = "持有至到期投资")
    private BigDecimal b200004611103;

    @LogField(tableName = "b200004", value = "b200004_61110301", valueName = "国债")
    @ApiModelProperty(value = "国债")
    private BigDecimal b20000461110301;

    @LogField(tableName = "b200004", value = "b200004_6111030101", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000046111030101;

    @LogField(tableName = "b200004", value = "b200004_6111030102", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000046111030102;

    @LogField(tableName = "b200004", value = "b200004_61110302", valueName = "地方政府债券")
    @ApiModelProperty(value = "地方政府债券")
    private BigDecimal b20000461110302;

    @LogField(tableName = "b200004", value = "b200004_6111030201", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000046111030201;

    @LogField(tableName = "b200004", value = "b200004_6111030202", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000046111030202;

    @LogField(tableName = "b200004", value = "b200004_61110303", valueName = "铁路债券")
    @ApiModelProperty(value = "铁路债券")
    private BigDecimal b20000461110303;

    @LogField(tableName = "b200004", value = "b200004_6111030301", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000046111030301;

    @LogField(tableName = "b200004", value = "b200004_6111030302", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000046111030302;

    @LogField(tableName = "b200004", value = "b200004_61110304", valueName = "永续债")
    @ApiModelProperty(value = "永续债")
    private BigDecimal b20000461110304;

    @LogField(tableName = "b200004", value = "b200004_6111030401", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000046111030401;

    @LogField(tableName = "b200004", value = "b200004_6111030402", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000046111030402;

    @LogField(tableName = "b200004", value = "b200004_61110305", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b20000461110305;

    @LogField(tableName = "b200004", value = "b200004_6111030501", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000046111030501;

    @LogField(tableName = "b200004", value = "b200004_6111030502", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000046111030502;

    @LogField(tableName = "b200004", value = "b200004_611104", valueName = "衍生工具")
    @ApiModelProperty(value = "衍生工具")
    private BigDecimal b200004611104;

    @LogField(tableName = "b200004", value = "b200004_61110401", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b20000461110401;

    @LogField(tableName = "b200004", value = "b200004_61110402", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b20000461110402;

    @LogField(tableName = "b200004", value = "b200004_611105", valueName = "交易性金融负债")
    @ApiModelProperty(value = "交易性金融负债")
    private BigDecimal b200004611105;

    @LogField(tableName = "b200004", value = "b200004_61110501", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b20000461110501;

    @LogField(tableName = "b200004", value = "b200004_61110502", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b20000461110502;

    @LogField(tableName = "b200004", value = "b200004_611106", valueName = "长期股权投资")
    @ApiModelProperty(value = "长期股权投资")
    private BigDecimal b200004611106;

    @LogField(tableName = "b200004", value = "b200004_61110601", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b20000461110601;

    @LogField(tableName = "b200004", value = "b200004_61110602", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b20000461110602;

    @LogField(tableName = "b200004", value = "b200004_611107", valueName = "长期债券投资")
    @ApiModelProperty(value = "长期债券投资")
    private BigDecimal b200004611107;

    @LogField(tableName = "b200004", value = "b200004_61110701", valueName = "国债")
    @ApiModelProperty(value = "国债")
    private BigDecimal b20000461110701;

    @LogField(tableName = "b200004", value = "b200004_6111070101", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000046111070101;

    @LogField(tableName = "b200004", value = "b200004_6111070102", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000046111070102;

    @LogField(tableName = "b200004", value = "b200004_61110702", valueName = "地方政府债券")
    @ApiModelProperty(value = "地方政府债券")
    private BigDecimal b20000461110702;

    @LogField(tableName = "b200004", value = "b200004_6111070201", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000046111070201;

    @LogField(tableName = "b200004", value = "b200004_6111070202", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000046111070202;

    @LogField(tableName = "b200004", value = "b200004_61110703", valueName = "铁路债券")
    @ApiModelProperty(value = "铁路债券")
    private BigDecimal b20000461110703;

    @LogField(tableName = "b200004", value = "b200004_6111070301", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000046111070301;

    @LogField(tableName = "b200004", value = "b200004_6111070302", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000046111070302;

    @LogField(tableName = "b200004", value = "b200004_61110704", valueName = "永续债")
    @ApiModelProperty(value = "永续债")
    private BigDecimal b20000461110704;

    @LogField(tableName = "b200004", value = "b200004_6111070401", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000046111070401;

    @LogField(tableName = "b200004", value = "b200004_6111070402", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000046111070402;

    @LogField(tableName = "b200004", value = "b200004_61110705", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b20000461110705;

    @LogField(tableName = "b200004", value = "b200004_6111070501", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000046111070501;

    @LogField(tableName = "b200004", value = "b200004_6111070502", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000046111070502;

    @LogField(tableName = "b200004", value = "b200004_611108", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200004611108;

    @LogField(tableName = "b200004", value = "b200004_61110801", valueName = "国债")
    @ApiModelProperty(value = "国债")
    private BigDecimal b20000461110801;

    @LogField(tableName = "b200004", value = "b200004_6111080101", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000046111080101;

    @LogField(tableName = "b200004", value = "b200004_6111080102", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000046111080102;

    @LogField(tableName = "b200004", value = "b200004_61110802", valueName = "证券投资基金")
    @ApiModelProperty(value = "证券投资基金")
    private BigDecimal b20000461110802;

    @LogField(tableName = "b200004", value = "b200004_6111080201", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000046111080201;

    @LogField(tableName = "b200004", value = "b200004_6111080202", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000046111080202;

    @LogField(tableName = "b200004", value = "b200004_61110803", valueName = "地方政府债券")
    @ApiModelProperty(value = "地方政府债券")
    private BigDecimal b20000461110803;

    @LogField(tableName = "b200004", value = "b200004_6111080301", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000046111080301;

    @LogField(tableName = "b200004", value = "b200004_6111080302", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000046111080302;

    @LogField(tableName = "b200004", value = "b200004_61110804", valueName = "铁路债券")
    @ApiModelProperty(value = "铁路债券")
    private BigDecimal b20000461110804;

    @LogField(tableName = "b200004", value = "b200004_6111080401", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000046111080401;

    @LogField(tableName = "b200004", value = "b200004_6111080402", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000046111080402;

    @LogField(tableName = "b200004", value = "b200004_61110805", valueName = "连续持有公开发行并上市流通12个月及以上的股票")
    @ApiModelProperty(value = "连续持有公开发行并上市流通12个月及以上的股票")
    private BigDecimal b20000461110805;

    @LogField(tableName = "b200004", value = "b200004_6111080501", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000046111080501;

    @LogField(tableName = "b200004", value = "b200004_6111080502", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000046111080502;

    @LogField(tableName = "b200004", value = "b200004_61110806", valueName = "永续债")
    @ApiModelProperty(value = "永续债")
    private BigDecimal b20000461110806;

    @LogField(tableName = "b200004", value = "b200004_6111080601", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000046111080601;

    @LogField(tableName = "b200004", value = "b200004_6111080602", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000046111080602;

    @LogField(tableName = "b200004", value = "b200004_61110807", valueName = "创新企业CDR")
    @ApiModelProperty(value = "创新企业CDR")
    private BigDecimal b20000461110807;

    @LogField(tableName = "b200004", value = "b200004_6111080701", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000046111080701;

    @LogField(tableName = "b200004", value = "b200004_6111080702", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000046111080702;

    @LogField(tableName = "b200004", value = "b200004_61110808", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b20000461110808;

    @LogField(tableName = "b200004", value = "b200004_6111080801", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000046111080801;

    @LogField(tableName = "b200004", value = "b200004_6111080802", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000046111080802;

    @LogField(tableName = "b200004", value = "b200004_6115", valueName = "资产处置损益")
    @ApiModelProperty(value = "资产处置损益")
    private BigDecimal b2000046115;

    @LogField(tableName = "b200004", value = "b200004_611501", valueName = "固定资产损失")
    @ApiModelProperty(value = "固定资产损失")
    private BigDecimal b200004611501;

    @LogField(tableName = "b200004", value = "b200004_611502", valueName = "无形资产损失")
    @ApiModelProperty(value = "无形资产损失")
    private BigDecimal b200004611502;

    @LogField(tableName = "b200004", value = "b200004_611503", valueName = "在建工程损失")
    @ApiModelProperty(value = "在建工程损失")
    private BigDecimal b200004611503;

    @LogField(tableName = "b200004", value = "b200004_611504", valueName = "生产性生物资产损失")
    @ApiModelProperty(value = "生产性生物资产损失")
    private BigDecimal b200004611504;

    @LogField(tableName = "b200004", value = "b200004_611505", valueName = "打包出售资产损失")
    @ApiModelProperty(value = "打包出售资产损失")
    private BigDecimal b200004611505;

    @LogField(tableName = "b200004", value = "b200004_611506", valueName = "其他资产损失")
    @ApiModelProperty(value = "其他资产损失")
    private BigDecimal b200004611506;

    @LogField(tableName = "b200004", value = "b200004_611507", valueName = "资产处置收益")
    @ApiModelProperty(value = "资产处置收益")
    private BigDecimal b200004611507;

    @LogField(tableName = "b200004", value = "b200004_6117", valueName = "其他收益")
    @ApiModelProperty(value = "其他收益")
    private BigDecimal b2000046117;

    @LogField(tableName = "b200004", value = "b200004_6201", valueName = "摊回保险责任准备金")
    @ApiModelProperty(value = "摊回保险责任准备金")
    private BigDecimal b2000046201;

    @LogField(tableName = "b200004", value = "b200004_6202", valueName = "摊回赔付支出")
    @ApiModelProperty(value = "摊回赔付支出")
    private BigDecimal b2000046202;

    @LogField(tableName = "b200004", value = "b200004_6203", valueName = "摊回分保费用")
    @ApiModelProperty(value = "摊回分保费用")
    private BigDecimal b2000046203;

    @LogField(tableName = "b200004", value = "b200004_6301", valueName = "营业外收入")
    @ApiModelProperty(value = "营业外收入")
    private BigDecimal b2000046301;

    @LogField(tableName = "b200004", value = "b200004_630101", valueName = "非流动资产处置利得")
    @ApiModelProperty(value = "非流动资产处置利得")
    private BigDecimal b200004630101;

    @LogField(tableName = "b200004", value = "b200004_630102", valueName = "非货币性资产交换利得")
    @ApiModelProperty(value = "非货币性资产交换利得")
    private BigDecimal b200004630102;

    @LogField(tableName = "b200004", value = "b200004_630103", valueName = "债务重组利得")
    @ApiModelProperty(value = "债务重组利得")
    private BigDecimal b200004630103;

    @LogField(tableName = "b200004", value = "b200004_630104", valueName = "政府补助利得")
    @ApiModelProperty(value = "政府补助利得")
    private BigDecimal b200004630104;

    @LogField(tableName = "b200004", value = "b200004_630105", valueName = "盘盈利得")
    @ApiModelProperty(value = "盘盈利得")
    private BigDecimal b200004630105;

    @LogField(tableName = "b200004", value = "b200004_630106", valueName = "捐赠利得")
    @ApiModelProperty(value = "捐赠利得")
    private BigDecimal b200004630106;

    @LogField(tableName = "b200004", value = "b200004_630107", valueName = "罚没利得")
    @ApiModelProperty(value = "罚没利得")
    private BigDecimal b200004630107;

    @LogField(tableName = "b200004", value = "b200004_630108", valueName = "确实无法偿付的应付款项")
    @ApiModelProperty(value = "确实无法偿付的应付款项")
    private BigDecimal b200004630108;

    @LogField(tableName = "b200004", value = "b200004_630109", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200004630109;

    @LogField(tableName = "b200004", value = "b200004_6401", valueName = "主营业务成本")
    @ApiModelProperty(value = "主营业务成本")
    private BigDecimal b2000046401;

    @LogField(tableName = "b200004", value = "b200004_640101", valueName = "销售商品成本")
    @ApiModelProperty(value = "销售商品成本")
    private BigDecimal b200004640101;

    @LogField(tableName = "b200004", value = "b200004_640102", valueName = "提供劳务成本")
    @ApiModelProperty(value = "提供劳务成本")
    private BigDecimal b200004640102;

    @LogField(tableName = "b200004", value = "b200004_640103", valueName = "职工工资")
    @ApiModelProperty(value = "职工工资")
    private BigDecimal b200004640103;

    @LogField(tableName = "b200004", value = "b200004_640104", valueName = "年终奖")
    @ApiModelProperty(value = "年终奖")
    private BigDecimal b200004640104;

    @LogField(tableName = "b200004", value = "b200004_640105", valueName = "津贴与补贴")
    @ApiModelProperty(value = "津贴与补贴")
    private BigDecimal b200004640105;

    @LogField(tableName = "b200004", value = "b200004_640106", valueName = "股权激励")
    @ApiModelProperty(value = "股权激励")
    private BigDecimal b200004640106;

    @LogField(tableName = "b200004", value = "b200004_640107", valueName = "社会保险费")
    @ApiModelProperty(value = "社会保险费")
    private BigDecimal b200004640107;

    @LogField(tableName = "b200004", value = "b200004_640108", valueName = "住房公积金")
    @ApiModelProperty(value = "住房公积金")
    private BigDecimal b200004640108;

    @LogField(tableName = "b200004", value = "b200004_640109", valueName = "补充养老保险")
    @ApiModelProperty(value = "补充养老保险")
    private BigDecimal b200004640109;

    @LogField(tableName = "b200004", value = "b200004_640110", valueName = "补充医疗保险")
    @ApiModelProperty(value = "补充医疗保险")
    private BigDecimal b200004640110;

    @LogField(tableName = "b200004", value = "b200004_640111", valueName = "职工福利费")
    @ApiModelProperty(value = "职工福利费")
    private BigDecimal b200004640111;

    @LogField(tableName = "b200004", value = "b200004_640112", valueName = "职工教育经费")
    @ApiModelProperty(value = "职工教育经费")
    private BigDecimal b200004640112;

    @LogField(tableName = "b200004", value = "b200004_640113", valueName = "职工培训费用")
    @ApiModelProperty(value = "职工培训费用")
    private BigDecimal b200004640113;

    @LogField(tableName = "b200004", value = "b200004_640114", valueName = "工会经费")
    @ApiModelProperty(value = "工会经费")
    private BigDecimal b200004640114;

    @LogField(tableName = "b200004", value = "b200004_640115", valueName = "其他职工工资")
    @ApiModelProperty(value = "其他职工工资")
    private BigDecimal b200004640115;

    @LogField(tableName = "b200004", value = "b200004_640116", valueName = "商业保险费")
    @ApiModelProperty(value = "商业保险费")
    private BigDecimal b200004640116;

    @LogField(tableName = "b200004", value = "b200004_640117", valueName = "建造合同成本")
    @ApiModelProperty(value = "建造合同成本")
    private BigDecimal b200004640117;

    @LogField(tableName = "b200004", value = "b200004_640118", valueName = "让渡资产使用权成本")
    @ApiModelProperty(value = "让渡资产使用权成本")
    private BigDecimal b200004640118;

    @LogField(tableName = "b200004", value = "b200004_640119", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200004640119;

    @LogField(tableName = "b200004", value = "b200004_6402", valueName = "其他业务成本")
    @ApiModelProperty(value = "其他业务成本")
    private BigDecimal b2000046402;

    @LogField(tableName = "b200004", value = "b200004_640201", valueName = "销售材料成本")
    @ApiModelProperty(value = "销售材料成本")
    private BigDecimal b200004640201;

    @LogField(tableName = "b200004", value = "b200004_640202", valueName = "出租固定资产成本")
    @ApiModelProperty(value = "出租固定资产成本")
    private BigDecimal b200004640202;

    @LogField(tableName = "b200004", value = "b200004_640203", valueName = "出租无形资产成本")
    @ApiModelProperty(value = "出租无形资产成本")
    private BigDecimal b200004640203;

    @LogField(tableName = "b200004", value = "b200004_640204", valueName = "出租包装物和商品成本")
    @ApiModelProperty(value = "出租包装物和商品成本")
    private BigDecimal b200004640204;

    @LogField(tableName = "b200004", value = "b200004_640205", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200004640205;

    @LogField(tableName = "b200004", value = "b200004_6403", valueName = "税金及附加")
    @ApiModelProperty(value = "税金及附加")
    private BigDecimal b2000046403;

    @LogField(tableName = "b200004", value = "b200004_6411", valueName = "利息支出")
    @ApiModelProperty(value = "利息支出")
    private BigDecimal b2000046411;

    @LogField(tableName = "b200004", value = "b200004_6421", valueName = "手续费及佣金支出")
    @ApiModelProperty(value = "手续费及佣金支出")
    private BigDecimal b2000046421;

    @LogField(tableName = "b200004", value = "b200004_6501", valueName = "提取未到期责任准备金")
    @ApiModelProperty(value = "提取未到期责任准备金")
    private BigDecimal b2000046501;

    @LogField(tableName = "b200004", value = "b200004_6502", valueName = "提取保险责任准备金")
    @ApiModelProperty(value = "提取保险责任准备金")
    private BigDecimal b2000046502;

    @LogField(tableName = "b200004", value = "b200004_6511", valueName = "赔付支出")
    @ApiModelProperty(value = "赔付支出")
    private BigDecimal b2000046511;

    @LogField(tableName = "b200004", value = "b200004_6521", valueName = "保单红利支出")
    @ApiModelProperty(value = "保单红利支出")
    private BigDecimal b2000046521;

    @LogField(tableName = "b200004", value = "b200004_6531", valueName = "退保金")
    @ApiModelProperty(value = "退保金")
    private BigDecimal b2000046531;

    @LogField(tableName = "b200004", value = "b200004_6541", valueName = "分出保费")
    @ApiModelProperty(value = "分出保费")
    private BigDecimal b2000046541;

    @LogField(tableName = "b200004", value = "b200004_6542", valueName = "分保费用")
    @ApiModelProperty(value = "分保费用")
    private BigDecimal b2000046542;

    @LogField(tableName = "b200004", value = "b200004_6601", valueName = "销售费用")
    @ApiModelProperty(value = "销售费用")
    private BigDecimal b2000046601;

    @LogField(tableName = "b200004", value = "b200004_660101", valueName = "职工工资")
    @ApiModelProperty(value = "职工工资")
    private BigDecimal b200004660101;

    @LogField(tableName = "b200004", value = "b200004_660102", valueName = "年终奖")
    @ApiModelProperty(value = "年终奖")
    private BigDecimal b200004660102;

    @LogField(tableName = "b200004", value = "b200004_660103", valueName = "津贴与补贴")
    @ApiModelProperty(value = "津贴与补贴")
    private BigDecimal b200004660103;

    @LogField(tableName = "b200004", value = "b200004_660104", valueName = "股权激励")
    @ApiModelProperty(value = "股权激励")
    private BigDecimal b200004660104;

    @LogField(tableName = "b200004", value = "b200004_660105", valueName = "社会保险费")
    @ApiModelProperty(value = "社会保险费")
    private BigDecimal b200004660105;

    @LogField(tableName = "b200004", value = "b200004_660106", valueName = "住房公积金")
    @ApiModelProperty(value = "住房公积金")
    private BigDecimal b200004660106;

    @LogField(tableName = "b200004", value = "b200004_660107", valueName = "补充医疗保险")
    @ApiModelProperty(value = "补充医疗保险")
    private BigDecimal b200004660107;

    @LogField(tableName = "b200004", value = "b200004_660108", valueName = "补充养老保险")
    @ApiModelProperty(value = "补充养老保险")
    private BigDecimal b200004660108;

    @LogField(tableName = "b200004", value = "b200004_660109", valueName = "职工福利费")
    @ApiModelProperty(value = "职工福利费")
    private BigDecimal b200004660109;

    @LogField(tableName = "b200004", value = "b200004_660110", valueName = "职工教育经费")
    @ApiModelProperty(value = "职工教育经费")
    private BigDecimal b200004660110;

    @LogField(tableName = "b200004", value = "b200004_660111", valueName = "职工培训费用")
    @ApiModelProperty(value = "职工培训费用")
    private BigDecimal b200004660111;

    @LogField(tableName = "b200004", value = "b200004_660112", valueName = "工会经费")
    @ApiModelProperty(value = "工会经费")
    private BigDecimal b200004660112;

    @LogField(tableName = "b200004", value = "b200004_660113", valueName = "其他职工工资")
    @ApiModelProperty(value = "其他职工工资")
    private BigDecimal b200004660113;

    @LogField(tableName = "b200004", value = "b200004_660114", valueName = "商业保险费")
    @ApiModelProperty(value = "商业保险费")
    private BigDecimal b200004660114;

    @LogField(tableName = "b200004", value = "b200004_660115", valueName = "折旧费")
    @ApiModelProperty(value = "折旧费")
    private BigDecimal b200004660115;

    @LogField(tableName = "b200004", value = "b200004_660116", valueName = "无形资产摊销费")
    @ApiModelProperty(value = "无形资产摊销费")
    private BigDecimal b200004660116;

    @LogField(tableName = "b200004", value = "b200004_660117", valueName = "长期待摊费用摊销费")
    @ApiModelProperty(value = "长期待摊费用摊销费")
    private BigDecimal b200004660117;

    @LogField(tableName = "b200004", value = "b200004_660118", valueName = "修理费")
    @ApiModelProperty(value = "修理费")
    private BigDecimal b200004660118;

    @LogField(tableName = "b200004", value = "b200004_660119", valueName = "物料消耗")
    @ApiModelProperty(value = "物料消耗")
    private BigDecimal b200004660119;

    @LogField(tableName = "b200004", value = "b200004_660120", valueName = "低值易耗品摊销")
    @ApiModelProperty(value = "低值易耗品摊销")
    private BigDecimal b200004660120;

    @LogField(tableName = "b200004", value = "b200004_660121", valueName = "办公费")
    @ApiModelProperty(value = "办公费")
    private BigDecimal b200004660121;

    @LogField(tableName = "b200004", value = "b200004_660122", valueName = "差旅费")
    @ApiModelProperty(value = "差旅费")
    private BigDecimal b200004660122;

    @LogField(tableName = "b200004", value = "b200004_660123", valueName = "业务招待费")
    @ApiModelProperty(value = "业务招待费")
    private BigDecimal b200004660123;

    @LogField(tableName = "b200004", value = "b200004_660124", valueName = "通信费")
    @ApiModelProperty(value = "通信费")
    private BigDecimal b200004660124;

    @LogField(tableName = "b200004", value = "b200004_660125", valueName = "车辆费")
    @ApiModelProperty(value = "车辆费")
    private BigDecimal b200004660125;

    @LogField(tableName = "b200004", value = "b200004_660126", valueName = "能源费")
    @ApiModelProperty(value = "能源费")
    private BigDecimal b200004660126;

    @LogField(tableName = "b200004", value = "b200004_660127", valueName = "运杂费")
    @ApiModelProperty(value = "运杂费")
    private BigDecimal b200004660127;

    @LogField(tableName = "b200004", value = "b200004_660128", valueName = "交通费")
    @ApiModelProperty(value = "交通费")
    private BigDecimal b200004660128;

    @LogField(tableName = "b200004", value = "b200004_660129", valueName = "房租")
    @ApiModelProperty(value = "房租")
    private BigDecimal b200004660129;

    @LogField(tableName = "b200004", value = "b200004_660130", valueName = "物业管理费")
    @ApiModelProperty(value = "物业管理费")
    private BigDecimal b200004660130;

    @LogField(tableName = "b200004", value = "b200004_660131", valueName = "水电费")
    @ApiModelProperty(value = "水电费")
    private BigDecimal b200004660131;

    @LogField(tableName = "b200004", value = "b200004_660132", valueName = "中介服务费")
    @ApiModelProperty(value = "中介服务费")
    private BigDecimal b200004660132;

    @LogField(tableName = "b200004", value = "b200004_660133", valueName = "快递费")
    @ApiModelProperty(value = "快递费")
    private BigDecimal b200004660133;

    @LogField(tableName = "b200004", value = "b200004_660134", valueName = "保险费")
    @ApiModelProperty(value = "保险费")
    private BigDecimal b200004660134;

    @LogField(tableName = "b200004", value = "b200004_660135", valueName = "租赁费")
    @ApiModelProperty(value = "租赁费")
    private BigDecimal b200004660135;

    @LogField(tableName = "b200004", value = "b200004_660136", valueName = "装卸费")
    @ApiModelProperty(value = "装卸费")
    private BigDecimal b200004660136;

    @LogField(tableName = "b200004", value = "b200004_660137", valueName = "包装费")
    @ApiModelProperty(value = "包装费")
    private BigDecimal b200004660137;

    @LogField(tableName = "b200004", value = "b200004_660138", valueName = "通关费用")
    @ApiModelProperty(value = "通关费用")
    private BigDecimal b200004660138;

    @LogField(tableName = "b200004", value = "b200004_660139", valueName = "宣传展览费")
    @ApiModelProperty(value = "宣传展览费")
    private BigDecimal b200004660139;

    @LogField(tableName = "b200004", value = "b200004_660140", valueName = "仓储费")
    @ApiModelProperty(value = "仓储费")
    private BigDecimal b200004660140;

    @LogField(tableName = "b200004", value = "b200004_660141", valueName = "调试费")
    @ApiModelProperty(value = "调试费")
    private BigDecimal b200004660141;

    @LogField(tableName = "b200004", value = "b200004_660142", valueName = "广告费")
    @ApiModelProperty(value = "广告费")
    private BigDecimal b200004660142;

    @LogField(tableName = "b200004", value = "b200004_660143", valueName = "业务提成/佣金（销售服务费）")
    @ApiModelProperty(value = "业务提成/佣金（销售服务费）")
    private BigDecimal b200004660143;

    @LogField(tableName = "b200004", value = "b200004_660144", valueName = "投标费")
    @ApiModelProperty(value = "投标费")
    private BigDecimal b200004660144;

    @LogField(tableName = "b200004", value = "b200004_660145", valueName = "售后服务费")
    @ApiModelProperty(value = "售后服务费")
    private BigDecimal b200004660145;

    @LogField(tableName = "b200004", value = "b200004_660146", valueName = "其他经营费用")
    @ApiModelProperty(value = "其他经营费用")
    private BigDecimal b200004660146;

    @LogField(tableName = "b200004", value = "b200004_660147", valueName = "劳务费")
    @ApiModelProperty(value = "劳务费")
    private BigDecimal b200004660147;

    @LogField(tableName = "b200004", value = "b200004_660148", valueName = "董事会费")
    @ApiModelProperty(value = "董事会费")
    private BigDecimal b200004660148;

    @LogField(tableName = "b200004", value = "b200004_660149", valueName = "诉讼费")
    @ApiModelProperty(value = "诉讼费")
    private BigDecimal b200004660149;

    @LogField(tableName = "b200004", value = "b200004_660150", valueName = "技术转让费")
    @ApiModelProperty(value = "技术转让费")
    private BigDecimal b200004660150;

    @LogField(tableName = "b200004", value = "b200004_660151", valueName = "研究费用")
    @ApiModelProperty(value = "研究费用")
    private BigDecimal b200004660151;

    @LogField(tableName = "b200004", value = "b200004_660152", valueName = "税金")
    @ApiModelProperty(value = "税金")
    private BigDecimal b200004660152;

    @LogField(tableName = "b200004", value = "b200004_660153", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200004660153;

    @LogField(tableName = "b200004", value = "b200004_6602", valueName = "管理费用")
    @ApiModelProperty(value = "管理费用")
    private BigDecimal b2000046602;

    @LogField(tableName = "b200004", value = "b200004_660201", valueName = "职工工资")
    @ApiModelProperty(value = "职工工资")
    private BigDecimal b200004660201;

    @LogField(tableName = "b200004", value = "b200004_660202", valueName = "年终奖")
    @ApiModelProperty(value = "年终奖")
    private BigDecimal b200004660202;

    @LogField(tableName = "b200004", value = "b200004_660203", valueName = "津贴与补贴")
    @ApiModelProperty(value = "津贴与补贴")
    private BigDecimal b200004660203;

    @LogField(tableName = "b200004", value = "b200004_660204", valueName = "股权激励")
    @ApiModelProperty(value = "股权激励")
    private BigDecimal b200004660204;

    @LogField(tableName = "b200004", value = "b200004_660205", valueName = "社会保险费")
    @ApiModelProperty(value = "社会保险费")
    private BigDecimal b200004660205;

    @LogField(tableName = "b200004", value = "b200004_660206", valueName = "住房公积金")
    @ApiModelProperty(value = "住房公积金")
    private BigDecimal b200004660206;

    @LogField(tableName = "b200004", value = "b200004_660207", valueName = "补充医疗保险")
    @ApiModelProperty(value = "补充医疗保险")
    private BigDecimal b200004660207;

    @LogField(tableName = "b200004", value = "b200004_660208", valueName = "补充养老保险")
    @ApiModelProperty(value = "补充养老保险")
    private BigDecimal b200004660208;

    @LogField(tableName = "b200004", value = "b200004_660209", valueName = "职工福利费")
    @ApiModelProperty(value = "职工福利费")
    private BigDecimal b200004660209;

    @LogField(tableName = "b200004", value = "b200004_660210", valueName = "职工教育经费")
    @ApiModelProperty(value = "职工教育经费")
    private BigDecimal b200004660210;

    @LogField(tableName = "b200004", value = "b200004_660211", valueName = "职工培训费用")
    @ApiModelProperty(value = "职工培训费用")
    private BigDecimal b200004660211;

    @LogField(tableName = "b200004", value = "b200004_660212", valueName = "工会经费")
    @ApiModelProperty(value = "工会经费")
    private BigDecimal b200004660212;

    @LogField(tableName = "b200004", value = "b200004_660213", valueName = "其他职工工资")
    @ApiModelProperty(value = "其他职工工资")
    private BigDecimal b200004660213;

    @LogField(tableName = "b200004", value = "b200004_660214", valueName = "商业保险费")
    @ApiModelProperty(value = "商业保险费")
    private BigDecimal b200004660214;

    @LogField(tableName = "b200004", value = "b200004_660215", valueName = "差旅费")
    @ApiModelProperty(value = "差旅费")
    private BigDecimal b200004660215;

    @LogField(tableName = "b200004", value = "b200004_660216", valueName = "办公费")
    @ApiModelProperty(value = "办公费")
    private BigDecimal b200004660216;

    @LogField(tableName = "b200004", value = "b200004_660217", valueName = "折旧费")
    @ApiModelProperty(value = "折旧费")
    private BigDecimal b200004660217;

    @LogField(tableName = "b200004", value = "b200004_660218", valueName = "修理费")
    @ApiModelProperty(value = "修理费")
    private BigDecimal b200004660218;

    @LogField(tableName = "b200004", value = "b200004_660219", valueName = "物料消耗")
    @ApiModelProperty(value = "物料消耗")
    private BigDecimal b200004660219;

    @LogField(tableName = "b200004", value = "b200004_660220", valueName = "低值易耗品摊销")
    @ApiModelProperty(value = "低值易耗品摊销")
    private BigDecimal b200004660220;

    @LogField(tableName = "b200004", value = "b200004_660221", valueName = "会议费")
    @ApiModelProperty(value = "会议费")
    private BigDecimal b200004660221;

    @LogField(tableName = "b200004", value = "b200004_660222", valueName = "咨询费")
    @ApiModelProperty(value = "咨询费")
    private BigDecimal b200004660222;

    @LogField(tableName = "b200004", value = "b200004_660223", valueName = "软件使用费")
    @ApiModelProperty(value = "软件使用费")
    private BigDecimal b200004660223;

    @LogField(tableName = "b200004", value = "b200004_660224", valueName = "招聘费")
    @ApiModelProperty(value = "招聘费")
    private BigDecimal b200004660224;

    @LogField(tableName = "b200004", value = "b200004_660225", valueName = "专业服务费")
    @ApiModelProperty(value = "专业服务费")
    private BigDecimal b200004660225;

    @LogField(tableName = "b200004", value = "b200004_660226", valueName = "税金")
    @ApiModelProperty(value = "税金")
    private BigDecimal b200004660226;

    @LogField(tableName = "b200004", value = "b200004_660227", valueName = "技术开发费")
    @ApiModelProperty(value = "技术开发费")
    private BigDecimal b200004660227;

    @LogField(tableName = "b200004", value = "b200004_660228", valueName = "技术转让费")
    @ApiModelProperty(value = "技术转让费")
    private BigDecimal b200004660228;

    @LogField(tableName = "b200004", value = "b200004_660229", valueName = "无形资产摊销费")
    @ApiModelProperty(value = "无形资产摊销费")
    private BigDecimal b200004660229;

    @LogField(tableName = "b200004", value = "b200004_660230", valueName = "业务招待费")
    @ApiModelProperty(value = "业务招待费")
    private BigDecimal b200004660230;

    @LogField(tableName = "b200004", value = "b200004_660231", valueName = "研发费用")
    @ApiModelProperty(value = "研发费用")
    private BigDecimal b200004660231;

    @LogField(tableName = "b200004", value = "b200004_660232", valueName = "仓储费用")
    @ApiModelProperty(value = "仓储费用")
    private BigDecimal b200004660232;

    @LogField(tableName = "b200004", value = "b200004_660233", valueName = "保险费")
    @ApiModelProperty(value = "保险费")
    private BigDecimal b200004660233;

    @LogField(tableName = "b200004", value = "b200004_660234", valueName = "房租")
    @ApiModelProperty(value = "房租")
    private BigDecimal b200004660234;

    @LogField(tableName = "b200004", value = "b200004_660235", valueName = "物业管理费")
    @ApiModelProperty(value = "物业管理费")
    private BigDecimal b200004660235;

    @LogField(tableName = "b200004", value = "b200004_660236", valueName = "快递费")
    @ApiModelProperty(value = "快递费")
    private BigDecimal b200004660236;

    @LogField(tableName = "b200004", value = "b200004_660237", valueName = "中介服务费")
    @ApiModelProperty(value = "中介服务费")
    private BigDecimal b200004660237;

    @LogField(tableName = "b200004", value = "b200004_660238", valueName = "开办费")
    @ApiModelProperty(value = "开办费")
    private BigDecimal b200004660238;

    @LogField(tableName = "b200004", value = "b200004_660239", valueName = "水电费")
    @ApiModelProperty(value = "水电费")
    private BigDecimal b200004660239;

    @LogField(tableName = "b200004", value = "b200004_660240", valueName = "运杂费")
    @ApiModelProperty(value = "运杂费")
    private BigDecimal b200004660240;

    @LogField(tableName = "b200004", value = "b200004_660241", valueName = "劳务费")
    @ApiModelProperty(value = "劳务费")
    private BigDecimal b200004660241;

    @LogField(tableName = "b200004", value = "b200004_660242", valueName = "广告费")
    @ApiModelProperty(value = "广告费")
    private BigDecimal b200004660242;

    @LogField(tableName = "b200004", value = "b200004_660243", valueName = "佣金和手续费")
    @ApiModelProperty(value = "佣金和手续费")
    private BigDecimal b200004660243;

    @LogField(tableName = "b200004", value = "b200004_660244", valueName = "董事会费")
    @ApiModelProperty(value = "董事会费")
    private BigDecimal b200004660244;

    @LogField(tableName = "b200004", value = "b200004_660245", valueName = "诉讼费")
    @ApiModelProperty(value = "诉讼费")
    private BigDecimal b200004660245;

    @LogField(tableName = "b200004", value = "b200004_660246", valueName = "包装费")
    @ApiModelProperty(value = "包装费")
    private BigDecimal b200004660246;

    @LogField(tableName = "b200004", value = "b200004_660247", valueName = "党组织工作经费")
    @ApiModelProperty(value = "党组织工作经费")
    private BigDecimal b200004660247;

    @LogField(tableName = "b200004", value = "b200004_660248", valueName = "长期待摊费用摊销费")
    @ApiModelProperty(value = "长期待摊费用摊销费")
    private BigDecimal b200004660248;

    @LogField(tableName = "b200004", value = "b200004_660249", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200004660249;

    @LogField(tableName = "b200004", value = "b200004_660250", valueName = "现金及银行存款损失")
    @ApiModelProperty(value = "现金及银行存款损失")
    private BigDecimal b200004660250;

    @LogField(tableName = "b200004", value = "b200004_660251", valueName = "应收及预付款项坏账损失")
    @ApiModelProperty(value = "应收及预付款项坏账损失")
    private BigDecimal b200004660251;

    @LogField(tableName = "b200004", value = "b200004_660252", valueName = "存货损失")
    @ApiModelProperty(value = "存货损失")
    private BigDecimal b200004660252;

    @LogField(tableName = "b200004", value = "b200004_660253", valueName = "固定资产损失")
    @ApiModelProperty(value = "固定资产损失")
    private BigDecimal b200004660253;

    @LogField(tableName = "b200004", value = "b200004_660254", valueName = "无形资产损失")
    @ApiModelProperty(value = "无形资产损失")
    private BigDecimal b200004660254;

    @LogField(tableName = "b200004", value = "b200004_660255", valueName = "在建工程损失")
    @ApiModelProperty(value = "在建工程损失")
    private BigDecimal b200004660255;

    @LogField(tableName = "b200004", value = "b200004_660256", valueName = "生产性生物资产损失")
    @ApiModelProperty(value = "生产性生物资产损失")
    private BigDecimal b200004660256;

    @LogField(tableName = "b200004", value = "b200004_660257", valueName = "非金融企业债权性投资损失")
    @ApiModelProperty(value = "非金融企业债权性投资损失")
    private BigDecimal b200004660257;

    @LogField(tableName = "b200004", value = "b200004_660258", valueName = "股权（权益）性投资损失")
    @ApiModelProperty(value = "股权（权益）性投资损失")
    private BigDecimal b200004660258;

    @LogField(tableName = "b200004", value = "b200004_660259", valueName = "通过各种交易场所、市场买卖债券、股票、期货、基金以及金融衍生产品等发生的损失")
    @ApiModelProperty(value = "通过各种交易场所、市场买卖债券、股票、期货、基金以及金融衍生产品等发生的损失")
    private BigDecimal b200004660259;

    @LogField(tableName = "b200004", value = "b200004_660260", valueName = "打包出售资产损失")
    @ApiModelProperty(value = "打包出售资产损失")
    private BigDecimal b200004660260;

    @LogField(tableName = "b200004", value = "b200004_660261", valueName = "其他资产损失")
    @ApiModelProperty(value = "其他资产损失")
    private BigDecimal b200004660261;

    @LogField(tableName = "b200004", value = "b200004_6603", valueName = "财务费用")
    @ApiModelProperty(value = "财务费用")
    private BigDecimal b2000046603;

    @LogField(tableName = "b200004", value = "b200004_660301", valueName = "汇兑损益")
    @ApiModelProperty(value = "汇兑损益")
    private BigDecimal b200004660301;

    @LogField(tableName = "b200004", value = "b200004_660302", valueName = "利息费用")
    @ApiModelProperty(value = "利息费用")
    private BigDecimal b200004660302;

    @LogField(tableName = "b200004", value = "b200004_660303", valueName = "佣金和手续费")
    @ApiModelProperty(value = "佣金和手续费")
    private BigDecimal b200004660303;

    @LogField(tableName = "b200004", value = "b200004_660304", valueName = "账户管理费")
    @ApiModelProperty(value = "账户管理费")
    private BigDecimal b200004660304;

    @LogField(tableName = "b200004", value = "b200004_660305", valueName = "利息收入")
    @ApiModelProperty(value = "利息收入")
    private BigDecimal b200004660305;

    @LogField(tableName = "b200004", value = "b200004_66030501", valueName = "因未实现融资收益确认的利息收入")
    @ApiModelProperty(value = "因未实现融资收益确认的利息收入")
    private BigDecimal b20000466030501;

    @LogField(tableName = "b200004", value = "b200004_66030502", valueName = "其他利息收入")
    @ApiModelProperty(value = "其他利息收入")
    private BigDecimal b20000466030502;

    @LogField(tableName = "b200004", value = "b200004_660306", valueName = "现金折扣")
    @ApiModelProperty(value = "现金折扣")
    private BigDecimal b200004660306;

    @LogField(tableName = "b200004", value = "b200004_660307", valueName = "银行手续费")
    @ApiModelProperty(value = "银行手续费")
    private BigDecimal b200004660307;

    @LogField(tableName = "b200004", value = "b200004_660308", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200004660308;

    @LogField(tableName = "b200004", value = "b200004_6604", valueName = "勘探费用")
    @ApiModelProperty(value = "勘探费用")
    private BigDecimal b2000046604;

    @LogField(tableName = "b200004", value = "b200004_6701", valueName = "资产减值损失")
    @ApiModelProperty(value = "资产减值损失")
    private BigDecimal b2000046701;

    @LogField(tableName = "b200004", value = "b200004_6702", valueName = "信用减值损失")
    @ApiModelProperty(value = "信用减值损失")
    private BigDecimal b2000046702;

    @LogField(tableName = "b200004", value = "b200004_6711", valueName = "营业外支出")
    @ApiModelProperty(value = "营业外支出")
    private BigDecimal b2000046711;

    @LogField(tableName = "b200004", value = "b200004_671101", valueName = "非流动资产处置净损失")
    @ApiModelProperty(value = "非流动资产处置净损失")
    private BigDecimal b200004671101;

    @LogField(tableName = "b200004", value = "b200004_67110101", valueName = "存货损失")
    @ApiModelProperty(value = "存货损失")
    private BigDecimal b20000467110101;

    @LogField(tableName = "b200004", value = "b200004_67110102", valueName = "固定资产损失")
    @ApiModelProperty(value = "固定资产损失")
    private BigDecimal b20000467110102;

    @LogField(tableName = "b200004", value = "b200004_67110103", valueName = "无形资产损失")
    @ApiModelProperty(value = "无形资产损失")
    private BigDecimal b20000467110103;

    @LogField(tableName = "b200004", value = "b200004_67110104", valueName = "在建工程损失")
    @ApiModelProperty(value = "在建工程损失")
    private BigDecimal b20000467110104;

    @LogField(tableName = "b200004", value = "b200004_67110105", valueName = "生产性生物资产损失")
    @ApiModelProperty(value = "生产性生物资产损失")
    private BigDecimal b20000467110105;

    @LogField(tableName = "b200004", value = "b200004_67110106", valueName = "打包出售资产损失")
    @ApiModelProperty(value = "打包出售资产损失")
    private BigDecimal b20000467110106;

    @LogField(tableName = "b200004", value = "b200004_67110107", valueName = "其他资产损失")
    @ApiModelProperty(value = "其他资产损失")
    private BigDecimal b20000467110107;

    @LogField(tableName = "b200004", value = "b200004_671102", valueName = "非货币性资产交换损失")
    @ApiModelProperty(value = "非货币性资产交换损失")
    private BigDecimal b200004671102;

    @LogField(tableName = "b200004", value = "b200004_67110201", valueName = "存货损失")
    @ApiModelProperty(value = "存货损失")
    private BigDecimal b20000467110201;

    @LogField(tableName = "b200004", value = "b200004_67110202", valueName = "固定资产损失")
    @ApiModelProperty(value = "固定资产损失")
    private BigDecimal b20000467110202;

    @LogField(tableName = "b200004", value = "b200004_67110203", valueName = "无形资产损失")
    @ApiModelProperty(value = "无形资产损失")
    private BigDecimal b20000467110203;

    @LogField(tableName = "b200004", value = "b200004_67110204", valueName = "在建工程损失")
    @ApiModelProperty(value = "在建工程损失")
    private BigDecimal b20000467110204;

    @LogField(tableName = "b200004", value = "b200004_67110205", valueName = "生产性生物资产损失")
    @ApiModelProperty(value = "生产性生物资产损失")
    private BigDecimal b20000467110205;

    @LogField(tableName = "b200004", value = "b200004_67110206", valueName = "打包出售资产损失")
    @ApiModelProperty(value = "打包出售资产损失")
    private BigDecimal b20000467110206;

    @LogField(tableName = "b200004", value = "b200004_67110207", valueName = "其他资产损失")
    @ApiModelProperty(value = "其他资产损失")
    private BigDecimal b20000467110207;

    @LogField(tableName = "b200004", value = "b200004_671103", valueName = "债务重组损失")
    @ApiModelProperty(value = "债务重组损失")
    private BigDecimal b200004671103;

    @LogField(tableName = "b200004", value = "b200004_671104", valueName = "非常损失")
    @ApiModelProperty(value = "非常损失")
    private BigDecimal b200004671104;

    @LogField(tableName = "b200004", value = "b200004_67110401", valueName = "存货损失")
    @ApiModelProperty(value = "存货损失")
    private BigDecimal b20000467110401;

    @LogField(tableName = "b200004", value = "b200004_67110402", valueName = "固定资产损失")
    @ApiModelProperty(value = "固定资产损失")
    private BigDecimal b20000467110402;

    @LogField(tableName = "b200004", value = "b200004_67110403", valueName = "无形资产损失")
    @ApiModelProperty(value = "无形资产损失")
    private BigDecimal b20000467110403;

    @LogField(tableName = "b200004", value = "b200004_67110404", valueName = "在建工程损失")
    @ApiModelProperty(value = "在建工程损失")
    private BigDecimal b20000467110404;

    @LogField(tableName = "b200004", value = "b200004_67110405", valueName = "生产性生物资产损失")
    @ApiModelProperty(value = "生产性生物资产损失")
    private BigDecimal b20000467110405;

    @LogField(tableName = "b200004", value = "b200004_67110406", valueName = "打包出售资产损失")
    @ApiModelProperty(value = "打包出售资产损失")
    private BigDecimal b20000467110406;

    @LogField(tableName = "b200004", value = "b200004_67110407", valueName = "其他资产损失")
    @ApiModelProperty(value = "其他资产损失")
    private BigDecimal b20000467110407;

    @LogField(tableName = "b200004", value = "b200004_671105", valueName = "捐赠支出")
    @ApiModelProperty(value = "捐赠支出")
    private BigDecimal b200004671105;

    @LogField(tableName = "b200004", value = "b200004_671106", valueName = "赞助支出")
    @ApiModelProperty(value = "赞助支出")
    private BigDecimal b200004671106;

    @LogField(tableName = "b200004", value = "b200004_671107", valueName = "罚没支出")
    @ApiModelProperty(value = "罚没支出")
    private BigDecimal b200004671107;

    @LogField(tableName = "b200004", value = "b200004_67110701", valueName = "经营性处罚")
    @ApiModelProperty(value = "经营性处罚")
    private BigDecimal b20000467110701;

    @LogField(tableName = "b200004", value = "b200004_67110702", valueName = "税收滞纳金")
    @ApiModelProperty(value = "税收滞纳金")
    private BigDecimal b20000467110702;

    @LogField(tableName = "b200004", value = "b200004_67110703", valueName = "行政性处罚")
    @ApiModelProperty(value = "行政性处罚")
    private BigDecimal b20000467110703;

    @LogField(tableName = "b200004", value = "b200004_671108", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200004671108;

    @LogField(tableName = "b200004", value = "b200004_67110801", valueName = "现金及银行存款损失")
    @ApiModelProperty(value = "现金及银行存款损失")
    private BigDecimal b20000467110801;

    @LogField(tableName = "b200004", value = "b200004_67110802", valueName = "应收及预付款项坏账损失")
    @ApiModelProperty(value = "应收及预付款项坏账损失")
    private BigDecimal b20000467110802;

    @LogField(tableName = "b200004", value = "b200004_67110803", valueName = "存货损失")
    @ApiModelProperty(value = "存货损失")
    private BigDecimal b20000467110803;

    @LogField(tableName = "b200004", value = "b200004_67110804", valueName = "固定资产损失")
    @ApiModelProperty(value = "固定资产损失")
    private BigDecimal b20000467110804;

    @LogField(tableName = "b200004", value = "b200004_67110805", valueName = "无形资产损失")
    @ApiModelProperty(value = "无形资产损失")
    private BigDecimal b20000467110805;

    @LogField(tableName = "b200004", value = "b200004_67110806", valueName = "在建工程损失")
    @ApiModelProperty(value = "在建工程损失")
    private BigDecimal b20000467110806;

    @LogField(tableName = "b200004", value = "b200004_67110807", valueName = "生产性生物资产损失")
    @ApiModelProperty(value = "生产性生物资产损失")
    private BigDecimal b20000467110807;

    @LogField(tableName = "b200004", value = "b200004_67110808", valueName = "非金融企业债权性投资损失")
    @ApiModelProperty(value = "非金融企业债权性投资损失")
    private BigDecimal b20000467110808;

    @LogField(tableName = "b200004", value = "b200004_67110809", valueName = "股权（权益）性投资损失")
    @ApiModelProperty(value = "股权（权益）性投资损失")
    private BigDecimal b20000467110809;

    @LogField(tableName = "b200004", value = "b200004_67110810", valueName = "通过各种交易场所、市场买卖债券、股票、期货、基金以及金融衍生产品等发生的损失")
    @ApiModelProperty(value = "通过各种交易场所、市场买卖债券、股票、期货、基金以及金融衍生产品等发生的损失")
    private BigDecimal b20000467110810;

    @LogField(tableName = "b200004", value = "b200004_67110811", valueName = "打包出售资产损失")
    @ApiModelProperty(value = "打包出售资产损失")
    private BigDecimal b20000467110811;

    @LogField(tableName = "b200004", value = "b200004_67110812", valueName = "其他资产损失")
    @ApiModelProperty(value = "其他资产损失")
    private BigDecimal b20000467110812;

    @LogField(tableName = "b200004", value = "b200004_67110813", valueName = "其他支出")
    @ApiModelProperty(value = "其他支出")
    private BigDecimal b20000467110813;

    @LogField(tableName = "b200004", value = "b200004_6801", valueName = "所得税费用")
    @ApiModelProperty(value = "所得税费用")
    private BigDecimal b2000046801;

    @LogField(tableName = "b200004", value = "b200004_6901", valueName = "以前年度损益调整")
    @ApiModelProperty(value = "以前年度损益调整")
    private BigDecimal b2000046901;

    @LogField(tableName = "b200004", value = "b200004_222124", valueName = "应交税费-纳税检查调整")
    @ApiModelProperty(value = "应交税费-纳税检查调整")
    private BigDecimal b200004222124;

    @LogField(tableName = "b200004", value = "b200004_222125", valueName = "应交税费-加计抵减进项税额")
    @ApiModelProperty(value = "应交税费-加计抵减进项税额")
    private BigDecimal b200004222125;

    @LogField(tableName = "b200004", value = "b200004_67110501", valueName = "营业外支出—捐赠支出—公益性捐赠")
    @ApiModelProperty(value = "营业外支出—捐赠支出—公益性捐赠")
    private BigDecimal b20000467110501;

    @LogField(tableName = "b200004", value = "b200004_67110502", valueName = "营业外支出—捐赠支出—非公益性捐赠")
    @ApiModelProperty(value = "营业外支出—捐赠支出—非公益性捐赠")
    private BigDecimal b20000467110502;

    @ApiModelProperty(value = "创建时间")
    private String createTime;

    @ApiModelProperty(value = "更新时间")
    private String updateTime;

    public String getEntId() {
        return entId;
    }

    public void setEntId(String entId) {
        this.entId = entId;
    }

    public String getTaxYear() {
        return taxYear;
    }

    public void setTaxYear(String taxYear) {
        this.taxYear = taxYear;
    }

    public BigDecimal getB2000041001() {
        return b2000041001;
    }

    public void setB2000041001(BigDecimal b2000041001) {
        this.b2000041001 = b2000041001;
    }

    public BigDecimal getB2000041002() {
        return b2000041002;
    }

    public void setB2000041002(BigDecimal b2000041002) {
        this.b2000041002 = b2000041002;
    }

    public BigDecimal getB2000041003() {
        return b2000041003;
    }

    public void setB2000041003(BigDecimal b2000041003) {
        this.b2000041003 = b2000041003;
    }

    public BigDecimal getB2000041011() {
        return b2000041011;
    }

    public void setB2000041011(BigDecimal b2000041011) {
        this.b2000041011 = b2000041011;
    }

    public BigDecimal getB2000041012() {
        return b2000041012;
    }

    public void setB2000041012(BigDecimal b2000041012) {
        this.b2000041012 = b2000041012;
    }

    public BigDecimal getB2000041021() {
        return b2000041021;
    }

    public void setB2000041021(BigDecimal b2000041021) {
        this.b2000041021 = b2000041021;
    }

    public BigDecimal getB2000041031() {
        return b2000041031;
    }

    public void setB2000041031(BigDecimal b2000041031) {
        this.b2000041031 = b2000041031;
    }

    public BigDecimal getB2000041101() {
        return b2000041101;
    }

    public void setB2000041101(BigDecimal b2000041101) {
        this.b2000041101 = b2000041101;
    }

    public BigDecimal getB2000041111() {
        return b2000041111;
    }

    public void setB2000041111(BigDecimal b2000041111) {
        this.b2000041111 = b2000041111;
    }

    public BigDecimal getB2000041121() {
        return b2000041121;
    }

    public void setB2000041121(BigDecimal b2000041121) {
        this.b2000041121 = b2000041121;
    }

    public BigDecimal getB2000041122() {
        return b2000041122;
    }

    public void setB2000041122(BigDecimal b2000041122) {
        this.b2000041122 = b2000041122;
    }

    public BigDecimal getB2000041123() {
        return b2000041123;
    }

    public void setB2000041123(BigDecimal b2000041123) {
        this.b2000041123 = b2000041123;
    }

    public BigDecimal getB2000041124() {
        return b2000041124;
    }

    public void setB2000041124(BigDecimal b2000041124) {
        this.b2000041124 = b2000041124;
    }

    public BigDecimal getB2000041125() {
        return b2000041125;
    }

    public void setB2000041125(BigDecimal b2000041125) {
        this.b2000041125 = b2000041125;
    }

    public BigDecimal getB2000041131() {
        return b2000041131;
    }

    public void setB2000041131(BigDecimal b2000041131) {
        this.b2000041131 = b2000041131;
    }

    public BigDecimal getB2000041132() {
        return b2000041132;
    }

    public void setB2000041132(BigDecimal b2000041132) {
        this.b2000041132 = b2000041132;
    }

    public BigDecimal getB2000041201() {
        return b2000041201;
    }

    public void setB2000041201(BigDecimal b2000041201) {
        this.b2000041201 = b2000041201;
    }

    public BigDecimal getB2000041211() {
        return b2000041211;
    }

    public void setB2000041211(BigDecimal b2000041211) {
        this.b2000041211 = b2000041211;
    }

    public BigDecimal getB2000041212() {
        return b2000041212;
    }

    public void setB2000041212(BigDecimal b2000041212) {
        this.b2000041212 = b2000041212;
    }

    public BigDecimal getB2000041221() {
        return b2000041221;
    }

    public void setB2000041221(BigDecimal b2000041221) {
        this.b2000041221 = b2000041221;
    }

    public BigDecimal getB2000041231() {
        return b2000041231;
    }

    public void setB2000041231(BigDecimal b2000041231) {
        this.b2000041231 = b2000041231;
    }

    public BigDecimal getB2000041301() {
        return b2000041301;
    }

    public void setB2000041301(BigDecimal b2000041301) {
        this.b2000041301 = b2000041301;
    }

    public BigDecimal getB2000041302() {
        return b2000041302;
    }

    public void setB2000041302(BigDecimal b2000041302) {
        this.b2000041302 = b2000041302;
    }

    public BigDecimal getB2000041303() {
        return b2000041303;
    }

    public void setB2000041303(BigDecimal b2000041303) {
        this.b2000041303 = b2000041303;
    }

    public BigDecimal getB2000041304() {
        return b2000041304;
    }

    public void setB2000041304(BigDecimal b2000041304) {
        this.b2000041304 = b2000041304;
    }

    public BigDecimal getB2000041311() {
        return b2000041311;
    }

    public void setB2000041311(BigDecimal b2000041311) {
        this.b2000041311 = b2000041311;
    }

    public BigDecimal getB2000041321() {
        return b2000041321;
    }

    public void setB2000041321(BigDecimal b2000041321) {
        this.b2000041321 = b2000041321;
    }

    public BigDecimal getB2000041401() {
        return b2000041401;
    }

    public void setB2000041401(BigDecimal b2000041401) {
        this.b2000041401 = b2000041401;
    }

    public BigDecimal getB2000041402() {
        return b2000041402;
    }

    public void setB2000041402(BigDecimal b2000041402) {
        this.b2000041402 = b2000041402;
    }

    public BigDecimal getB2000041403() {
        return b2000041403;
    }

    public void setB2000041403(BigDecimal b2000041403) {
        this.b2000041403 = b2000041403;
    }

    public BigDecimal getB2000041404() {
        return b2000041404;
    }

    public void setB2000041404(BigDecimal b2000041404) {
        this.b2000041404 = b2000041404;
    }

    public BigDecimal getB2000041405() {
        return b2000041405;
    }

    public void setB2000041405(BigDecimal b2000041405) {
        this.b2000041405 = b2000041405;
    }

    public BigDecimal getB2000041406() {
        return b2000041406;
    }

    public void setB2000041406(BigDecimal b2000041406) {
        this.b2000041406 = b2000041406;
    }

    public BigDecimal getB2000041407() {
        return b2000041407;
    }

    public void setB2000041407(BigDecimal b2000041407) {
        this.b2000041407 = b2000041407;
    }

    public BigDecimal getB2000041408() {
        return b2000041408;
    }

    public void setB2000041408(BigDecimal b2000041408) {
        this.b2000041408 = b2000041408;
    }

    public BigDecimal getB2000041411() {
        return b2000041411;
    }

    public void setB2000041411(BigDecimal b2000041411) {
        this.b2000041411 = b2000041411;
    }

    public BigDecimal getB2000041421() {
        return b2000041421;
    }

    public void setB2000041421(BigDecimal b2000041421) {
        this.b2000041421 = b2000041421;
    }

    public BigDecimal getB2000041431() {
        return b2000041431;
    }

    public void setB2000041431(BigDecimal b2000041431) {
        this.b2000041431 = b2000041431;
    }

    public BigDecimal getB2000041441() {
        return b2000041441;
    }

    public void setB2000041441(BigDecimal b2000041441) {
        this.b2000041441 = b2000041441;
    }

    public BigDecimal getB2000041451() {
        return b2000041451;
    }

    public void setB2000041451(BigDecimal b2000041451) {
        this.b2000041451 = b2000041451;
    }

    public BigDecimal getB2000041461() {
        return b2000041461;
    }

    public void setB2000041461(BigDecimal b2000041461) {
        this.b2000041461 = b2000041461;
    }

    public BigDecimal getB2000041471() {
        return b2000041471;
    }

    public void setB2000041471(BigDecimal b2000041471) {
        this.b2000041471 = b2000041471;
    }

    public BigDecimal getB2000041481() {
        return b2000041481;
    }

    public void setB2000041481(BigDecimal b2000041481) {
        this.b2000041481 = b2000041481;
    }

    public BigDecimal getB2000041482() {
        return b2000041482;
    }

    public void setB2000041482(BigDecimal b2000041482) {
        this.b2000041482 = b2000041482;
    }

    public BigDecimal getB2000041501() {
        return b2000041501;
    }

    public void setB2000041501(BigDecimal b2000041501) {
        this.b2000041501 = b2000041501;
    }

    public BigDecimal getB2000041502() {
        return b2000041502;
    }

    public void setB2000041502(BigDecimal b2000041502) {
        this.b2000041502 = b2000041502;
    }

    public BigDecimal getB2000041503() {
        return b2000041503;
    }

    public void setB2000041503(BigDecimal b2000041503) {
        this.b2000041503 = b2000041503;
    }

    public BigDecimal getB2000041511() {
        return b2000041511;
    }

    public void setB2000041511(BigDecimal b2000041511) {
        this.b2000041511 = b2000041511;
    }

    public BigDecimal getB2000041512() {
        return b2000041512;
    }

    public void setB2000041512(BigDecimal b2000041512) {
        this.b2000041512 = b2000041512;
    }

    public BigDecimal getB2000041521() {
        return b2000041521;
    }

    public void setB2000041521(BigDecimal b2000041521) {
        this.b2000041521 = b2000041521;
    }

    public BigDecimal getB2000041531() {
        return b2000041531;
    }

    public void setB2000041531(BigDecimal b2000041531) {
        this.b2000041531 = b2000041531;
    }

    public BigDecimal getB2000041532() {
        return b2000041532;
    }

    public void setB2000041532(BigDecimal b2000041532) {
        this.b2000041532 = b2000041532;
    }

    public BigDecimal getB2000041541() {
        return b2000041541;
    }

    public void setB2000041541(BigDecimal b2000041541) {
        this.b2000041541 = b2000041541;
    }

    public BigDecimal getB2000041601() {
        return b2000041601;
    }

    public void setB2000041601(BigDecimal b2000041601) {
        this.b2000041601 = b2000041601;
    }

    public BigDecimal getB2000041602() {
        return b2000041602;
    }

    public void setB2000041602(BigDecimal b2000041602) {
        this.b2000041602 = b2000041602;
    }

    public BigDecimal getB2000041603() {
        return b2000041603;
    }

    public void setB2000041603(BigDecimal b2000041603) {
        this.b2000041603 = b2000041603;
    }

    public BigDecimal getB2000041604() {
        return b2000041604;
    }

    public void setB2000041604(BigDecimal b2000041604) {
        this.b2000041604 = b2000041604;
    }

    public BigDecimal getB2000041605() {
        return b2000041605;
    }

    public void setB2000041605(BigDecimal b2000041605) {
        this.b2000041605 = b2000041605;
    }

    public BigDecimal getB2000041606() {
        return b2000041606;
    }

    public void setB2000041606(BigDecimal b2000041606) {
        this.b2000041606 = b2000041606;
    }

    public BigDecimal getB2000041611() {
        return b2000041611;
    }

    public void setB2000041611(BigDecimal b2000041611) {
        this.b2000041611 = b2000041611;
    }

    public BigDecimal getB2000041621() {
        return b2000041621;
    }

    public void setB2000041621(BigDecimal b2000041621) {
        this.b2000041621 = b2000041621;
    }

    public BigDecimal getB2000041622() {
        return b2000041622;
    }

    public void setB2000041622(BigDecimal b2000041622) {
        this.b2000041622 = b2000041622;
    }

    public BigDecimal getB2000041623() {
        return b2000041623;
    }

    public void setB2000041623(BigDecimal b2000041623) {
        this.b2000041623 = b2000041623;
    }

    public BigDecimal getB2000041631() {
        return b2000041631;
    }

    public void setB2000041631(BigDecimal b2000041631) {
        this.b2000041631 = b2000041631;
    }

    public BigDecimal getB2000041632() {
        return b2000041632;
    }

    public void setB2000041632(BigDecimal b2000041632) {
        this.b2000041632 = b2000041632;
    }

    public BigDecimal getB2000041701() {
        return b2000041701;
    }

    public void setB2000041701(BigDecimal b2000041701) {
        this.b2000041701 = b2000041701;
    }

    public BigDecimal getB2000041702() {
        return b2000041702;
    }

    public void setB2000041702(BigDecimal b2000041702) {
        this.b2000041702 = b2000041702;
    }

    public BigDecimal getB2000041703() {
        return b2000041703;
    }

    public void setB2000041703(BigDecimal b2000041703) {
        this.b2000041703 = b2000041703;
    }

    public BigDecimal getB2000041711() {
        return b2000041711;
    }

    public void setB2000041711(BigDecimal b2000041711) {
        this.b2000041711 = b2000041711;
    }

    public BigDecimal getB2000041801() {
        return b2000041801;
    }

    public void setB2000041801(BigDecimal b2000041801) {
        this.b2000041801 = b2000041801;
    }

    public BigDecimal getB2000041811() {
        return b2000041811;
    }

    public void setB2000041811(BigDecimal b2000041811) {
        this.b2000041811 = b2000041811;
    }

    public BigDecimal getB2000041821() {
        return b2000041821;
    }

    public void setB2000041821(BigDecimal b2000041821) {
        this.b2000041821 = b2000041821;
    }

    public BigDecimal getB2000041901() {
        return b2000041901;
    }

    public void setB2000041901(BigDecimal b2000041901) {
        this.b2000041901 = b2000041901;
    }

    public BigDecimal getB2000042001() {
        return b2000042001;
    }

    public void setB2000042001(BigDecimal b2000042001) {
        this.b2000042001 = b2000042001;
    }

    public BigDecimal getB2000042002() {
        return b2000042002;
    }

    public void setB2000042002(BigDecimal b2000042002) {
        this.b2000042002 = b2000042002;
    }

    public BigDecimal getB2000042003() {
        return b2000042003;
    }

    public void setB2000042003(BigDecimal b2000042003) {
        this.b2000042003 = b2000042003;
    }

    public BigDecimal getB2000042004() {
        return b2000042004;
    }

    public void setB2000042004(BigDecimal b2000042004) {
        this.b2000042004 = b2000042004;
    }

    public BigDecimal getB2000042011() {
        return b2000042011;
    }

    public void setB2000042011(BigDecimal b2000042011) {
        this.b2000042011 = b2000042011;
    }

    public BigDecimal getB2000042012() {
        return b2000042012;
    }

    public void setB2000042012(BigDecimal b2000042012) {
        this.b2000042012 = b2000042012;
    }

    public BigDecimal getB2000042021() {
        return b2000042021;
    }

    public void setB2000042021(BigDecimal b2000042021) {
        this.b2000042021 = b2000042021;
    }

    public BigDecimal getB2000042101() {
        return b2000042101;
    }

    public void setB2000042101(BigDecimal b2000042101) {
        this.b2000042101 = b2000042101;
    }

    public BigDecimal getB2000042111() {
        return b2000042111;
    }

    public void setB2000042111(BigDecimal b2000042111) {
        this.b2000042111 = b2000042111;
    }

    public BigDecimal getB2000042201() {
        return b2000042201;
    }

    public void setB2000042201(BigDecimal b2000042201) {
        this.b2000042201 = b2000042201;
    }

    public BigDecimal getB2000042202() {
        return b2000042202;
    }

    public void setB2000042202(BigDecimal b2000042202) {
        this.b2000042202 = b2000042202;
    }

    public BigDecimal getB2000042203() {
        return b2000042203;
    }

    public void setB2000042203(BigDecimal b2000042203) {
        this.b2000042203 = b2000042203;
    }

    public BigDecimal getB2000042204() {
        return b2000042204;
    }

    public void setB2000042204(BigDecimal b2000042204) {
        this.b2000042204 = b2000042204;
    }

    public BigDecimal getB2000042211() {
        return b2000042211;
    }

    public void setB2000042211(BigDecimal b2000042211) {
        this.b2000042211 = b2000042211;
    }

    public BigDecimal getB200004221101() {
        return b200004221101;
    }

    public void setB200004221101(BigDecimal b200004221101) {
        this.b200004221101 = b200004221101;
    }

    public BigDecimal getB200004221102() {
        return b200004221102;
    }

    public void setB200004221102(BigDecimal b200004221102) {
        this.b200004221102 = b200004221102;
    }

    public BigDecimal getB200004221103() {
        return b200004221103;
    }

    public void setB200004221103(BigDecimal b200004221103) {
        this.b200004221103 = b200004221103;
    }

    public BigDecimal getB200004221104() {
        return b200004221104;
    }

    public void setB200004221104(BigDecimal b200004221104) {
        this.b200004221104 = b200004221104;
    }

    public BigDecimal getB200004221105() {
        return b200004221105;
    }

    public void setB200004221105(BigDecimal b200004221105) {
        this.b200004221105 = b200004221105;
    }

    public BigDecimal getB200004221106() {
        return b200004221106;
    }

    public void setB200004221106(BigDecimal b200004221106) {
        this.b200004221106 = b200004221106;
    }

    public BigDecimal getB200004221107() {
        return b200004221107;
    }

    public void setB200004221107(BigDecimal b200004221107) {
        this.b200004221107 = b200004221107;
    }

    public BigDecimal getB200004221108() {
        return b200004221108;
    }

    public void setB200004221108(BigDecimal b200004221108) {
        this.b200004221108 = b200004221108;
    }

    public BigDecimal getB200004221109() {
        return b200004221109;
    }

    public void setB200004221109(BigDecimal b200004221109) {
        this.b200004221109 = b200004221109;
    }

    public BigDecimal getB200004221110() {
        return b200004221110;
    }

    public void setB200004221110(BigDecimal b200004221110) {
        this.b200004221110 = b200004221110;
    }

    public BigDecimal getB200004221111() {
        return b200004221111;
    }

    public void setB200004221111(BigDecimal b200004221111) {
        this.b200004221111 = b200004221111;
    }

    public BigDecimal getB200004221112() {
        return b200004221112;
    }

    public void setB200004221112(BigDecimal b200004221112) {
        this.b200004221112 = b200004221112;
    }

    public BigDecimal getB200004221113() {
        return b200004221113;
    }

    public void setB200004221113(BigDecimal b200004221113) {
        this.b200004221113 = b200004221113;
    }

    public BigDecimal getB2000042221() {
        return b2000042221;
    }

    public void setB2000042221(BigDecimal b2000042221) {
        this.b2000042221 = b2000042221;
    }

    public BigDecimal getB200004222101() {
        return b200004222101;
    }

    public void setB200004222101(BigDecimal b200004222101) {
        this.b200004222101 = b200004222101;
    }

    public BigDecimal getB20000422210101() {
        return b20000422210101;
    }

    public void setB20000422210101(BigDecimal b20000422210101) {
        this.b20000422210101 = b20000422210101;
    }

    public BigDecimal getB20000422210102() {
        return b20000422210102;
    }

    public void setB20000422210102(BigDecimal b20000422210102) {
        this.b20000422210102 = b20000422210102;
    }

    public BigDecimal getB20000422210103() {
        return b20000422210103;
    }

    public void setB20000422210103(BigDecimal b20000422210103) {
        this.b20000422210103 = b20000422210103;
    }

    public BigDecimal getB20000422210104() {
        return b20000422210104;
    }

    public void setB20000422210104(BigDecimal b20000422210104) {
        this.b20000422210104 = b20000422210104;
    }

    public BigDecimal getB20000422210105() {
        return b20000422210105;
    }

    public void setB20000422210105(BigDecimal b20000422210105) {
        this.b20000422210105 = b20000422210105;
    }

    public BigDecimal getB20000422210106() {
        return b20000422210106;
    }

    public void setB20000422210106(BigDecimal b20000422210106) {
        this.b20000422210106 = b20000422210106;
    }

    public BigDecimal getB20000422210107() {
        return b20000422210107;
    }

    public void setB20000422210107(BigDecimal b20000422210107) {
        this.b20000422210107 = b20000422210107;
    }

    public BigDecimal getB20000422210108() {
        return b20000422210108;
    }

    public void setB20000422210108(BigDecimal b20000422210108) {
        this.b20000422210108 = b20000422210108;
    }

    public BigDecimal getB20000422210109() {
        return b20000422210109;
    }

    public void setB20000422210109(BigDecimal b20000422210109) {
        this.b20000422210109 = b20000422210109;
    }

    public BigDecimal getB20000422210110() {
        return b20000422210110;
    }

    public void setB20000422210110(BigDecimal b20000422210110) {
        this.b20000422210110 = b20000422210110;
    }

    public BigDecimal getB200004222102() {
        return b200004222102;
    }

    public void setB200004222102(BigDecimal b200004222102) {
        this.b200004222102 = b200004222102;
    }

    public BigDecimal getB200004222103() {
        return b200004222103;
    }

    public void setB200004222103(BigDecimal b200004222103) {
        this.b200004222103 = b200004222103;
    }

    public BigDecimal getB200004222104() {
        return b200004222104;
    }

    public void setB200004222104(BigDecimal b200004222104) {
        this.b200004222104 = b200004222104;
    }

    public BigDecimal getB200004222105() {
        return b200004222105;
    }

    public void setB200004222105(BigDecimal b200004222105) {
        this.b200004222105 = b200004222105;
    }

    public BigDecimal getB200004222106() {
        return b200004222106;
    }

    public void setB200004222106(BigDecimal b200004222106) {
        this.b200004222106 = b200004222106;
    }

    public BigDecimal getB200004222107() {
        return b200004222107;
    }

    public void setB200004222107(BigDecimal b200004222107) {
        this.b200004222107 = b200004222107;
    }

    public BigDecimal getB200004222108() {
        return b200004222108;
    }

    public void setB200004222108(BigDecimal b200004222108) {
        this.b200004222108 = b200004222108;
    }

    public BigDecimal getB200004222109() {
        return b200004222109;
    }

    public void setB200004222109(BigDecimal b200004222109) {
        this.b200004222109 = b200004222109;
    }

    public BigDecimal getB200004222110() {
        return b200004222110;
    }

    public void setB200004222110(BigDecimal b200004222110) {
        this.b200004222110 = b200004222110;
    }

    public BigDecimal getB200004222111() {
        return b200004222111;
    }

    public void setB200004222111(BigDecimal b200004222111) {
        this.b200004222111 = b200004222111;
    }

    public BigDecimal getB200004222112() {
        return b200004222112;
    }

    public void setB200004222112(BigDecimal b200004222112) {
        this.b200004222112 = b200004222112;
    }

    public BigDecimal getB200004222113() {
        return b200004222113;
    }

    public void setB200004222113(BigDecimal b200004222113) {
        this.b200004222113 = b200004222113;
    }

    public BigDecimal getB200004222114() {
        return b200004222114;
    }

    public void setB200004222114(BigDecimal b200004222114) {
        this.b200004222114 = b200004222114;
    }

    public BigDecimal getB200004222115() {
        return b200004222115;
    }

    public void setB200004222115(BigDecimal b200004222115) {
        this.b200004222115 = b200004222115;
    }

    public BigDecimal getB200004222116() {
        return b200004222116;
    }

    public void setB200004222116(BigDecimal b200004222116) {
        this.b200004222116 = b200004222116;
    }

    public BigDecimal getB200004222117() {
        return b200004222117;
    }

    public void setB200004222117(BigDecimal b200004222117) {
        this.b200004222117 = b200004222117;
    }

    public BigDecimal getB200004222118() {
        return b200004222118;
    }

    public void setB200004222118(BigDecimal b200004222118) {
        this.b200004222118 = b200004222118;
    }

    public BigDecimal getB200004222119() {
        return b200004222119;
    }

    public void setB200004222119(BigDecimal b200004222119) {
        this.b200004222119 = b200004222119;
    }

    public BigDecimal getB200004222120() {
        return b200004222120;
    }

    public void setB200004222120(BigDecimal b200004222120) {
        this.b200004222120 = b200004222120;
    }

    public BigDecimal getB200004222121() {
        return b200004222121;
    }

    public void setB200004222121(BigDecimal b200004222121) {
        this.b200004222121 = b200004222121;
    }

    public BigDecimal getB200004222122() {
        return b200004222122;
    }

    public void setB200004222122(BigDecimal b200004222122) {
        this.b200004222122 = b200004222122;
    }

    public BigDecimal getB200004222123() {
        return b200004222123;
    }

    public void setB200004222123(BigDecimal b200004222123) {
        this.b200004222123 = b200004222123;
    }

    public BigDecimal getB2000042231() {
        return b2000042231;
    }

    public void setB2000042231(BigDecimal b2000042231) {
        this.b2000042231 = b2000042231;
    }

    public BigDecimal getB2000042232() {
        return b2000042232;
    }

    public void setB2000042232(BigDecimal b2000042232) {
        this.b2000042232 = b2000042232;
    }

    public BigDecimal getB2000042241() {
        return b2000042241;
    }

    public void setB2000042241(BigDecimal b2000042241) {
        this.b2000042241 = b2000042241;
    }

    public BigDecimal getB2000042251() {
        return b2000042251;
    }

    public void setB2000042251(BigDecimal b2000042251) {
        this.b2000042251 = b2000042251;
    }

    public BigDecimal getB2000042261() {
        return b2000042261;
    }

    public void setB2000042261(BigDecimal b2000042261) {
        this.b2000042261 = b2000042261;
    }

    public BigDecimal getB2000042311() {
        return b2000042311;
    }

    public void setB2000042311(BigDecimal b2000042311) {
        this.b2000042311 = b2000042311;
    }

    public BigDecimal getB2000042312() {
        return b2000042312;
    }

    public void setB2000042312(BigDecimal b2000042312) {
        this.b2000042312 = b2000042312;
    }

    public BigDecimal getB2000042313() {
        return b2000042313;
    }

    public void setB2000042313(BigDecimal b2000042313) {
        this.b2000042313 = b2000042313;
    }

    public BigDecimal getB2000042314() {
        return b2000042314;
    }

    public void setB2000042314(BigDecimal b2000042314) {
        this.b2000042314 = b2000042314;
    }

    public BigDecimal getB2000042401() {
        return b2000042401;
    }

    public void setB2000042401(BigDecimal b2000042401) {
        this.b2000042401 = b2000042401;
    }

    public BigDecimal getB2000042245() {
        return b2000042245;
    }

    public void setB2000042245(BigDecimal b2000042245) {
        this.b2000042245 = b2000042245;
    }

    public BigDecimal getB2000042501() {
        return b2000042501;
    }

    public void setB2000042501(BigDecimal b2000042501) {
        this.b2000042501 = b2000042501;
    }

    public BigDecimal getB2000042502() {
        return b2000042502;
    }

    public void setB2000042502(BigDecimal b2000042502) {
        this.b2000042502 = b2000042502;
    }

    public BigDecimal getB2000042601() {
        return b2000042601;
    }

    public void setB2000042601(BigDecimal b2000042601) {
        this.b2000042601 = b2000042601;
    }

    public BigDecimal getB2000042602() {
        return b2000042602;
    }

    public void setB2000042602(BigDecimal b2000042602) {
        this.b2000042602 = b2000042602;
    }

    public BigDecimal getB2000042611() {
        return b2000042611;
    }

    public void setB2000042611(BigDecimal b2000042611) {
        this.b2000042611 = b2000042611;
    }

    public BigDecimal getB2000042621() {
        return b2000042621;
    }

    public void setB2000042621(BigDecimal b2000042621) {
        this.b2000042621 = b2000042621;
    }

    public BigDecimal getB2000042701() {
        return b2000042701;
    }

    public void setB2000042701(BigDecimal b2000042701) {
        this.b2000042701 = b2000042701;
    }

    public BigDecimal getB2000042702() {
        return b2000042702;
    }

    public void setB2000042702(BigDecimal b2000042702) {
        this.b2000042702 = b2000042702;
    }

    public BigDecimal getB2000042711() {
        return b2000042711;
    }

    public void setB2000042711(BigDecimal b2000042711) {
        this.b2000042711 = b2000042711;
    }

    public BigDecimal getB2000042801() {
        return b2000042801;
    }

    public void setB2000042801(BigDecimal b2000042801) {
        this.b2000042801 = b2000042801;
    }

    public BigDecimal getB2000042901() {
        return b2000042901;
    }

    public void setB2000042901(BigDecimal b2000042901) {
        this.b2000042901 = b2000042901;
    }

    public BigDecimal getB2000043001() {
        return b2000043001;
    }

    public void setB2000043001(BigDecimal b2000043001) {
        this.b2000043001 = b2000043001;
    }

    public BigDecimal getB2000043002() {
        return b2000043002;
    }

    public void setB2000043002(BigDecimal b2000043002) {
        this.b2000043002 = b2000043002;
    }

    public BigDecimal getB2000043101() {
        return b2000043101;
    }

    public void setB2000043101(BigDecimal b2000043101) {
        this.b2000043101 = b2000043101;
    }

    public BigDecimal getB2000043201() {
        return b2000043201;
    }

    public void setB2000043201(BigDecimal b2000043201) {
        this.b2000043201 = b2000043201;
    }

    public BigDecimal getB2000043202() {
        return b2000043202;
    }

    public void setB2000043202(BigDecimal b2000043202) {
        this.b2000043202 = b2000043202;
    }

    public BigDecimal getB2000044001() {
        return b2000044001;
    }

    public void setB2000044001(BigDecimal b2000044001) {
        this.b2000044001 = b2000044001;
    }

    public BigDecimal getB2000044002() {
        return b2000044002;
    }

    public void setB2000044002(BigDecimal b2000044002) {
        this.b2000044002 = b2000044002;
    }

    public BigDecimal getB2000044003() {
        return b2000044003;
    }

    public void setB2000044003(BigDecimal b2000044003) {
        this.b2000044003 = b2000044003;
    }

    public BigDecimal getB2000044101() {
        return b2000044101;
    }

    public void setB2000044101(BigDecimal b2000044101) {
        this.b2000044101 = b2000044101;
    }

    public BigDecimal getB2000044102() {
        return b2000044102;
    }

    public void setB2000044102(BigDecimal b2000044102) {
        this.b2000044102 = b2000044102;
    }

    public BigDecimal getB2000044103() {
        return b2000044103;
    }

    public void setB2000044103(BigDecimal b2000044103) {
        this.b2000044103 = b2000044103;
    }

    public BigDecimal getB2000044104() {
        return b2000044104;
    }

    public void setB2000044104(BigDecimal b2000044104) {
        this.b2000044104 = b2000044104;
    }

    public BigDecimal getB2000044201() {
        return b2000044201;
    }

    public void setB2000044201(BigDecimal b2000044201) {
        this.b2000044201 = b2000044201;
    }

    public BigDecimal getB2000044301() {
        return b2000044301;
    }

    public void setB2000044301(BigDecimal b2000044301) {
        this.b2000044301 = b2000044301;
    }

    public BigDecimal getB2000045001() {
        return b2000045001;
    }

    public void setB2000045001(BigDecimal b2000045001) {
        this.b2000045001 = b2000045001;
    }

    public BigDecimal getB200004500101() {
        return b200004500101;
    }

    public void setB200004500101(BigDecimal b200004500101) {
        this.b200004500101 = b200004500101;
    }

    public BigDecimal getB200004500102() {
        return b200004500102;
    }

    public void setB200004500102(BigDecimal b200004500102) {
        this.b200004500102 = b200004500102;
    }

    public BigDecimal getB200004500103() {
        return b200004500103;
    }

    public void setB200004500103(BigDecimal b200004500103) {
        this.b200004500103 = b200004500103;
    }

    public BigDecimal getB200004500104() {
        return b200004500104;
    }

    public void setB200004500104(BigDecimal b200004500104) {
        this.b200004500104 = b200004500104;
    }

    public BigDecimal getB200004500105() {
        return b200004500105;
    }

    public void setB200004500105(BigDecimal b200004500105) {
        this.b200004500105 = b200004500105;
    }

    public BigDecimal getB200004500106() {
        return b200004500106;
    }

    public void setB200004500106(BigDecimal b200004500106) {
        this.b200004500106 = b200004500106;
    }

    public BigDecimal getB200004500107() {
        return b200004500107;
    }

    public void setB200004500107(BigDecimal b200004500107) {
        this.b200004500107 = b200004500107;
    }

    public BigDecimal getB200004500108() {
        return b200004500108;
    }

    public void setB200004500108(BigDecimal b200004500108) {
        this.b200004500108 = b200004500108;
    }

    public BigDecimal getB200004500109() {
        return b200004500109;
    }

    public void setB200004500109(BigDecimal b200004500109) {
        this.b200004500109 = b200004500109;
    }

    public BigDecimal getB200004500110() {
        return b200004500110;
    }

    public void setB200004500110(BigDecimal b200004500110) {
        this.b200004500110 = b200004500110;
    }

    public BigDecimal getB200004500111() {
        return b200004500111;
    }

    public void setB200004500111(BigDecimal b200004500111) {
        this.b200004500111 = b200004500111;
    }

    public BigDecimal getB200004500112() {
        return b200004500112;
    }

    public void setB200004500112(BigDecimal b200004500112) {
        this.b200004500112 = b200004500112;
    }

    public BigDecimal getB200004500113() {
        return b200004500113;
    }

    public void setB200004500113(BigDecimal b200004500113) {
        this.b200004500113 = b200004500113;
    }

    public BigDecimal getB200004500114() {
        return b200004500114;
    }

    public void setB200004500114(BigDecimal b200004500114) {
        this.b200004500114 = b200004500114;
    }

    public BigDecimal getB200004500115() {
        return b200004500115;
    }

    public void setB200004500115(BigDecimal b200004500115) {
        this.b200004500115 = b200004500115;
    }

    public BigDecimal getB200004500116() {
        return b200004500116;
    }

    public void setB200004500116(BigDecimal b200004500116) {
        this.b200004500116 = b200004500116;
    }

    public BigDecimal getB200004500117() {
        return b200004500117;
    }

    public void setB200004500117(BigDecimal b200004500117) {
        this.b200004500117 = b200004500117;
    }

    public BigDecimal getB200004500118() {
        return b200004500118;
    }

    public void setB200004500118(BigDecimal b200004500118) {
        this.b200004500118 = b200004500118;
    }

    public BigDecimal getB2000045101() {
        return b2000045101;
    }

    public void setB2000045101(BigDecimal b2000045101) {
        this.b2000045101 = b2000045101;
    }

    public BigDecimal getB200004510101() {
        return b200004510101;
    }

    public void setB200004510101(BigDecimal b200004510101) {
        this.b200004510101 = b200004510101;
    }

    public BigDecimal getB200004510102() {
        return b200004510102;
    }

    public void setB200004510102(BigDecimal b200004510102) {
        this.b200004510102 = b200004510102;
    }

    public BigDecimal getB200004510103() {
        return b200004510103;
    }

    public void setB200004510103(BigDecimal b200004510103) {
        this.b200004510103 = b200004510103;
    }

    public BigDecimal getB200004510104() {
        return b200004510104;
    }

    public void setB200004510104(BigDecimal b200004510104) {
        this.b200004510104 = b200004510104;
    }

    public BigDecimal getB200004510105() {
        return b200004510105;
    }

    public void setB200004510105(BigDecimal b200004510105) {
        this.b200004510105 = b200004510105;
    }

    public BigDecimal getB200004510106() {
        return b200004510106;
    }

    public void setB200004510106(BigDecimal b200004510106) {
        this.b200004510106 = b200004510106;
    }

    public BigDecimal getB200004510107() {
        return b200004510107;
    }

    public void setB200004510107(BigDecimal b200004510107) {
        this.b200004510107 = b200004510107;
    }

    public BigDecimal getB200004510108() {
        return b200004510108;
    }

    public void setB200004510108(BigDecimal b200004510108) {
        this.b200004510108 = b200004510108;
    }

    public BigDecimal getB200004510109() {
        return b200004510109;
    }

    public void setB200004510109(BigDecimal b200004510109) {
        this.b200004510109 = b200004510109;
    }

    public BigDecimal getB200004510110() {
        return b200004510110;
    }

    public void setB200004510110(BigDecimal b200004510110) {
        this.b200004510110 = b200004510110;
    }

    public BigDecimal getB200004510111() {
        return b200004510111;
    }

    public void setB200004510111(BigDecimal b200004510111) {
        this.b200004510111 = b200004510111;
    }

    public BigDecimal getB200004510112() {
        return b200004510112;
    }

    public void setB200004510112(BigDecimal b200004510112) {
        this.b200004510112 = b200004510112;
    }

    public BigDecimal getB200004510113() {
        return b200004510113;
    }

    public void setB200004510113(BigDecimal b200004510113) {
        this.b200004510113 = b200004510113;
    }

    public BigDecimal getB200004510114() {
        return b200004510114;
    }

    public void setB200004510114(BigDecimal b200004510114) {
        this.b200004510114 = b200004510114;
    }

    public BigDecimal getB200004510115() {
        return b200004510115;
    }

    public void setB200004510115(BigDecimal b200004510115) {
        this.b200004510115 = b200004510115;
    }

    public BigDecimal getB200004510116() {
        return b200004510116;
    }

    public void setB200004510116(BigDecimal b200004510116) {
        this.b200004510116 = b200004510116;
    }

    public BigDecimal getB200004510117() {
        return b200004510117;
    }

    public void setB200004510117(BigDecimal b200004510117) {
        this.b200004510117 = b200004510117;
    }

    public BigDecimal getB200004510118() {
        return b200004510118;
    }

    public void setB200004510118(BigDecimal b200004510118) {
        this.b200004510118 = b200004510118;
    }

    public BigDecimal getB200004510119() {
        return b200004510119;
    }

    public void setB200004510119(BigDecimal b200004510119) {
        this.b200004510119 = b200004510119;
    }

    public BigDecimal getB200004510120() {
        return b200004510120;
    }

    public void setB200004510120(BigDecimal b200004510120) {
        this.b200004510120 = b200004510120;
    }

    public BigDecimal getB200004510121() {
        return b200004510121;
    }

    public void setB200004510121(BigDecimal b200004510121) {
        this.b200004510121 = b200004510121;
    }

    public BigDecimal getB200004510122() {
        return b200004510122;
    }

    public void setB200004510122(BigDecimal b200004510122) {
        this.b200004510122 = b200004510122;
    }

    public BigDecimal getB200004510123() {
        return b200004510123;
    }

    public void setB200004510123(BigDecimal b200004510123) {
        this.b200004510123 = b200004510123;
    }

    public BigDecimal getB200004510124() {
        return b200004510124;
    }

    public void setB200004510124(BigDecimal b200004510124) {
        this.b200004510124 = b200004510124;
    }

    public BigDecimal getB200004510125() {
        return b200004510125;
    }

    public void setB200004510125(BigDecimal b200004510125) {
        this.b200004510125 = b200004510125;
    }

    public BigDecimal getB200004510126() {
        return b200004510126;
    }

    public void setB200004510126(BigDecimal b200004510126) {
        this.b200004510126 = b200004510126;
    }

    public BigDecimal getB200004510127() {
        return b200004510127;
    }

    public void setB200004510127(BigDecimal b200004510127) {
        this.b200004510127 = b200004510127;
    }

    public BigDecimal getB200004510128() {
        return b200004510128;
    }

    public void setB200004510128(BigDecimal b200004510128) {
        this.b200004510128 = b200004510128;
    }

    public BigDecimal getB200004510129() {
        return b200004510129;
    }

    public void setB200004510129(BigDecimal b200004510129) {
        this.b200004510129 = b200004510129;
    }

    public BigDecimal getB200004510130() {
        return b200004510130;
    }

    public void setB200004510130(BigDecimal b200004510130) {
        this.b200004510130 = b200004510130;
    }

    public BigDecimal getB200004510131() {
        return b200004510131;
    }

    public void setB200004510131(BigDecimal b200004510131) {
        this.b200004510131 = b200004510131;
    }

    public BigDecimal getB2000045201() {
        return b2000045201;
    }

    public void setB2000045201(BigDecimal b2000045201) {
        this.b2000045201 = b2000045201;
    }

    public BigDecimal getB200004520101() {
        return b200004520101;
    }

    public void setB200004520101(BigDecimal b200004520101) {
        this.b200004520101 = b200004520101;
    }

    public BigDecimal getB200004520102() {
        return b200004520102;
    }

    public void setB200004520102(BigDecimal b200004520102) {
        this.b200004520102 = b200004520102;
    }

    public BigDecimal getB200004520103() {
        return b200004520103;
    }

    public void setB200004520103(BigDecimal b200004520103) {
        this.b200004520103 = b200004520103;
    }

    public BigDecimal getB200004520104() {
        return b200004520104;
    }

    public void setB200004520104(BigDecimal b200004520104) {
        this.b200004520104 = b200004520104;
    }

    public BigDecimal getB200004520105() {
        return b200004520105;
    }

    public void setB200004520105(BigDecimal b200004520105) {
        this.b200004520105 = b200004520105;
    }

    public BigDecimal getB200004520106() {
        return b200004520106;
    }

    public void setB200004520106(BigDecimal b200004520106) {
        this.b200004520106 = b200004520106;
    }

    public BigDecimal getB200004520107() {
        return b200004520107;
    }

    public void setB200004520107(BigDecimal b200004520107) {
        this.b200004520107 = b200004520107;
    }

    public BigDecimal getB200004520108() {
        return b200004520108;
    }

    public void setB200004520108(BigDecimal b200004520108) {
        this.b200004520108 = b200004520108;
    }

    public BigDecimal getB200004520109() {
        return b200004520109;
    }

    public void setB200004520109(BigDecimal b200004520109) {
        this.b200004520109 = b200004520109;
    }

    public BigDecimal getB200004520110() {
        return b200004520110;
    }

    public void setB200004520110(BigDecimal b200004520110) {
        this.b200004520110 = b200004520110;
    }

    public BigDecimal getB200004520111() {
        return b200004520111;
    }

    public void setB200004520111(BigDecimal b200004520111) {
        this.b200004520111 = b200004520111;
    }

    public BigDecimal getB200004520112() {
        return b200004520112;
    }

    public void setB200004520112(BigDecimal b200004520112) {
        this.b200004520112 = b200004520112;
    }

    public BigDecimal getB200004520113() {
        return b200004520113;
    }

    public void setB200004520113(BigDecimal b200004520113) {
        this.b200004520113 = b200004520113;
    }

    public BigDecimal getB200004520114() {
        return b200004520114;
    }

    public void setB200004520114(BigDecimal b200004520114) {
        this.b200004520114 = b200004520114;
    }

    public BigDecimal getB200004520115() {
        return b200004520115;
    }

    public void setB200004520115(BigDecimal b200004520115) {
        this.b200004520115 = b200004520115;
    }

    public BigDecimal getB2000045301() {
        return b2000045301;
    }

    public void setB2000045301(BigDecimal b2000045301) {
        this.b2000045301 = b2000045301;
    }

    public BigDecimal getB200004530101() {
        return b200004530101;
    }

    public void setB200004530101(BigDecimal b200004530101) {
        this.b200004530101 = b200004530101;
    }

    public BigDecimal getB20000453010101() {
        return b20000453010101;
    }

    public void setB20000453010101(BigDecimal b20000453010101) {
        this.b20000453010101 = b20000453010101;
    }

    public BigDecimal getB2000045301010101() {
        return b2000045301010101;
    }

    public void setB2000045301010101(BigDecimal b2000045301010101) {
        this.b2000045301010101 = b2000045301010101;
    }

    public BigDecimal getB2000045301010102() {
        return b2000045301010102;
    }

    public void setB2000045301010102(BigDecimal b2000045301010102) {
        this.b2000045301010102 = b2000045301010102;
    }

    public BigDecimal getB2000045301010103() {
        return b2000045301010103;
    }

    public void setB2000045301010103(BigDecimal b2000045301010103) {
        this.b2000045301010103 = b2000045301010103;
    }

    public BigDecimal getB2000045301010104() {
        return b2000045301010104;
    }

    public void setB2000045301010104(BigDecimal b2000045301010104) {
        this.b2000045301010104 = b2000045301010104;
    }

    public BigDecimal getB2000045301010105() {
        return b2000045301010105;
    }

    public void setB2000045301010105(BigDecimal b2000045301010105) {
        this.b2000045301010105 = b2000045301010105;
    }

    public BigDecimal getB2000045301010106() {
        return b2000045301010106;
    }

    public void setB2000045301010106(BigDecimal b2000045301010106) {
        this.b2000045301010106 = b2000045301010106;
    }

    public BigDecimal getB2000045301010107() {
        return b2000045301010107;
    }

    public void setB2000045301010107(BigDecimal b2000045301010107) {
        this.b2000045301010107 = b2000045301010107;
    }

    public BigDecimal getB2000045301010108() {
        return b2000045301010108;
    }

    public void setB2000045301010108(BigDecimal b2000045301010108) {
        this.b2000045301010108 = b2000045301010108;
    }

    public BigDecimal getB2000045301010109() {
        return b2000045301010109;
    }

    public void setB2000045301010109(BigDecimal b2000045301010109) {
        this.b2000045301010109 = b2000045301010109;
    }

    public BigDecimal getB2000045301010110() {
        return b2000045301010110;
    }

    public void setB2000045301010110(BigDecimal b2000045301010110) {
        this.b2000045301010110 = b2000045301010110;
    }

    public BigDecimal getB2000045301010111() {
        return b2000045301010111;
    }

    public void setB2000045301010111(BigDecimal b2000045301010111) {
        this.b2000045301010111 = b2000045301010111;
    }

    public BigDecimal getB2000045301010112() {
        return b2000045301010112;
    }

    public void setB2000045301010112(BigDecimal b2000045301010112) {
        this.b2000045301010112 = b2000045301010112;
    }

    public BigDecimal getB2000045301010113() {
        return b2000045301010113;
    }

    public void setB2000045301010113(BigDecimal b2000045301010113) {
        this.b2000045301010113 = b2000045301010113;
    }

    public BigDecimal getB20000453010102() {
        return b20000453010102;
    }

    public void setB20000453010102(BigDecimal b20000453010102) {
        this.b20000453010102 = b20000453010102;
    }

    public BigDecimal getB20000453010103() {
        return b20000453010103;
    }

    public void setB20000453010103(BigDecimal b20000453010103) {
        this.b20000453010103 = b20000453010103;
    }

    public BigDecimal getB2000045301010301() {
        return b2000045301010301;
    }

    public void setB2000045301010301(BigDecimal b2000045301010301) {
        this.b2000045301010301 = b2000045301010301;
    }

    public BigDecimal getB2000045301010302() {
        return b2000045301010302;
    }

    public void setB2000045301010302(BigDecimal b2000045301010302) {
        this.b2000045301010302 = b2000045301010302;
    }

    public BigDecimal getB2000045301010303() {
        return b2000045301010303;
    }

    public void setB2000045301010303(BigDecimal b2000045301010303) {
        this.b2000045301010303 = b2000045301010303;
    }

    public BigDecimal getB2000045301010304() {
        return b2000045301010304;
    }

    public void setB2000045301010304(BigDecimal b2000045301010304) {
        this.b2000045301010304 = b2000045301010304;
    }

    public BigDecimal getB2000045301010305() {
        return b2000045301010305;
    }

    public void setB2000045301010305(BigDecimal b2000045301010305) {
        this.b2000045301010305 = b2000045301010305;
    }

    public BigDecimal getB2000045301010306() {
        return b2000045301010306;
    }

    public void setB2000045301010306(BigDecimal b2000045301010306) {
        this.b2000045301010306 = b2000045301010306;
    }

    public BigDecimal getB2000045301010307() {
        return b2000045301010307;
    }

    public void setB2000045301010307(BigDecimal b2000045301010307) {
        this.b2000045301010307 = b2000045301010307;
    }

    public BigDecimal getB2000045301010308() {
        return b2000045301010308;
    }

    public void setB2000045301010308(BigDecimal b2000045301010308) {
        this.b2000045301010308 = b2000045301010308;
    }

    public BigDecimal getB2000045301010309() {
        return b2000045301010309;
    }

    public void setB2000045301010309(BigDecimal b2000045301010309) {
        this.b2000045301010309 = b2000045301010309;
    }

    public BigDecimal getB2000045301010310() {
        return b2000045301010310;
    }

    public void setB2000045301010310(BigDecimal b2000045301010310) {
        this.b2000045301010310 = b2000045301010310;
    }

    public BigDecimal getB2000045301010311() {
        return b2000045301010311;
    }

    public void setB2000045301010311(BigDecimal b2000045301010311) {
        this.b2000045301010311 = b2000045301010311;
    }

    public BigDecimal getB2000045301010312() {
        return b2000045301010312;
    }

    public void setB2000045301010312(BigDecimal b2000045301010312) {
        this.b2000045301010312 = b2000045301010312;
    }

    public BigDecimal getB2000045301010313() {
        return b2000045301010313;
    }

    public void setB2000045301010313(BigDecimal b2000045301010313) {
        this.b2000045301010313 = b2000045301010313;
    }

    public BigDecimal getB20000453010104() {
        return b20000453010104;
    }

    public void setB20000453010104(BigDecimal b20000453010104) {
        this.b20000453010104 = b20000453010104;
    }

    public BigDecimal getB2000045301010401() {
        return b2000045301010401;
    }

    public void setB2000045301010401(BigDecimal b2000045301010401) {
        this.b2000045301010401 = b2000045301010401;
    }

    public BigDecimal getB2000045301010402() {
        return b2000045301010402;
    }

    public void setB2000045301010402(BigDecimal b2000045301010402) {
        this.b2000045301010402 = b2000045301010402;
    }

    public BigDecimal getB20000453010105() {
        return b20000453010105;
    }

    public void setB20000453010105(BigDecimal b20000453010105) {
        this.b20000453010105 = b20000453010105;
    }

    public BigDecimal getB2000045301010501() {
        return b2000045301010501;
    }

    public void setB2000045301010501(BigDecimal b2000045301010501) {
        this.b2000045301010501 = b2000045301010501;
    }

    public BigDecimal getB2000045301010502() {
        return b2000045301010502;
    }

    public void setB2000045301010502(BigDecimal b2000045301010502) {
        this.b2000045301010502 = b2000045301010502;
    }

    public BigDecimal getB20000453010106() {
        return b20000453010106;
    }

    public void setB20000453010106(BigDecimal b20000453010106) {
        this.b20000453010106 = b20000453010106;
    }

    public BigDecimal getB2000045301010601() {
        return b2000045301010601;
    }

    public void setB2000045301010601(BigDecimal b2000045301010601) {
        this.b2000045301010601 = b2000045301010601;
    }

    public BigDecimal getB2000045301010602() {
        return b2000045301010602;
    }

    public void setB2000045301010602(BigDecimal b2000045301010602) {
        this.b2000045301010602 = b2000045301010602;
    }

    public BigDecimal getB2000045301010603() {
        return b2000045301010603;
    }

    public void setB2000045301010603(BigDecimal b2000045301010603) {
        this.b2000045301010603 = b2000045301010603;
    }

    public BigDecimal getB2000045301010604() {
        return b2000045301010604;
    }

    public void setB2000045301010604(BigDecimal b2000045301010604) {
        this.b2000045301010604 = b2000045301010604;
    }

    public BigDecimal getB20000453010107() {
        return b20000453010107;
    }

    public void setB20000453010107(BigDecimal b20000453010107) {
        this.b20000453010107 = b20000453010107;
    }

    public BigDecimal getB2000045301010701() {
        return b2000045301010701;
    }

    public void setB2000045301010701(BigDecimal b2000045301010701) {
        this.b2000045301010701 = b2000045301010701;
    }

    public BigDecimal getB2000045301010702() {
        return b2000045301010702;
    }

    public void setB2000045301010702(BigDecimal b2000045301010702) {
        this.b2000045301010702 = b2000045301010702;
    }

    public BigDecimal getB2000045301010703() {
        return b2000045301010703;
    }

    public void setB2000045301010703(BigDecimal b2000045301010703) {
        this.b2000045301010703 = b2000045301010703;
    }

    public BigDecimal getB20000453010108() {
        return b20000453010108;
    }

    public void setB20000453010108(BigDecimal b20000453010108) {
        this.b20000453010108 = b20000453010108;
    }

    public BigDecimal getB2000045301010801() {
        return b2000045301010801;
    }

    public void setB2000045301010801(BigDecimal b2000045301010801) {
        this.b2000045301010801 = b2000045301010801;
    }

    public BigDecimal getB2000045301010802() {
        return b2000045301010802;
    }

    public void setB2000045301010802(BigDecimal b2000045301010802) {
        this.b2000045301010802 = b2000045301010802;
    }

    public BigDecimal getB2000045301010803() {
        return b2000045301010803;
    }

    public void setB2000045301010803(BigDecimal b2000045301010803) {
        this.b2000045301010803 = b2000045301010803;
    }

    public BigDecimal getB2000045301010804() {
        return b2000045301010804;
    }

    public void setB2000045301010804(BigDecimal b2000045301010804) {
        this.b2000045301010804 = b2000045301010804;
    }

    public BigDecimal getB2000045301010805() {
        return b2000045301010805;
    }

    public void setB2000045301010805(BigDecimal b2000045301010805) {
        this.b2000045301010805 = b2000045301010805;
    }

    public BigDecimal getB2000045301010806() {
        return b2000045301010806;
    }

    public void setB2000045301010806(BigDecimal b2000045301010806) {
        this.b2000045301010806 = b2000045301010806;
    }

    public BigDecimal getB20000453010109() {
        return b20000453010109;
    }

    public void setB20000453010109(BigDecimal b20000453010109) {
        this.b20000453010109 = b20000453010109;
    }

    public BigDecimal getB2000045301010901() {
        return b2000045301010901;
    }

    public void setB2000045301010901(BigDecimal b2000045301010901) {
        this.b2000045301010901 = b2000045301010901;
    }

    public BigDecimal getB2000045301010902() {
        return b2000045301010902;
    }

    public void setB2000045301010902(BigDecimal b2000045301010902) {
        this.b2000045301010902 = b2000045301010902;
    }

    public BigDecimal getB2000045301010903() {
        return b2000045301010903;
    }

    public void setB2000045301010903(BigDecimal b2000045301010903) {
        this.b2000045301010903 = b2000045301010903;
    }

    public BigDecimal getB2000045301010904() {
        return b2000045301010904;
    }

    public void setB2000045301010904(BigDecimal b2000045301010904) {
        this.b2000045301010904 = b2000045301010904;
    }

    public BigDecimal getB2000045301010905() {
        return b2000045301010905;
    }

    public void setB2000045301010905(BigDecimal b2000045301010905) {
        this.b2000045301010905 = b2000045301010905;
    }

    public BigDecimal getB2000045301010906() {
        return b2000045301010906;
    }

    public void setB2000045301010906(BigDecimal b2000045301010906) {
        this.b2000045301010906 = b2000045301010906;
    }

    public BigDecimal getB2000045301010907() {
        return b2000045301010907;
    }

    public void setB2000045301010907(BigDecimal b2000045301010907) {
        this.b2000045301010907 = b2000045301010907;
    }

    public BigDecimal getB20000453010110() {
        return b20000453010110;
    }

    public void setB20000453010110(BigDecimal b20000453010110) {
        this.b20000453010110 = b20000453010110;
    }

    public BigDecimal getB20000453010111() {
        return b20000453010111;
    }

    public void setB20000453010111(BigDecimal b20000453010111) {
        this.b20000453010111 = b20000453010111;
    }

    public BigDecimal getB2000045301011101() {
        return b2000045301011101;
    }

    public void setB2000045301011101(BigDecimal b2000045301011101) {
        this.b2000045301011101 = b2000045301011101;
    }

    public BigDecimal getB2000045301011102() {
        return b2000045301011102;
    }

    public void setB2000045301011102(BigDecimal b2000045301011102) {
        this.b2000045301011102 = b2000045301011102;
    }

    public BigDecimal getB2000045301011103() {
        return b2000045301011103;
    }

    public void setB2000045301011103(BigDecimal b2000045301011103) {
        this.b2000045301011103 = b2000045301011103;
    }

    public BigDecimal getB2000045301011104() {
        return b2000045301011104;
    }

    public void setB2000045301011104(BigDecimal b2000045301011104) {
        this.b2000045301011104 = b2000045301011104;
    }

    public BigDecimal getB20000453010112() {
        return b20000453010112;
    }

    public void setB20000453010112(BigDecimal b20000453010112) {
        this.b20000453010112 = b20000453010112;
    }

    public BigDecimal getB20000453010113() {
        return b20000453010113;
    }

    public void setB20000453010113(BigDecimal b20000453010113) {
        this.b20000453010113 = b20000453010113;
    }

    public BigDecimal getB20000453010114() {
        return b20000453010114;
    }

    public void setB20000453010114(BigDecimal b20000453010114) {
        this.b20000453010114 = b20000453010114;
    }

    public BigDecimal getB20000453010115() {
        return b20000453010115;
    }

    public void setB20000453010115(BigDecimal b20000453010115) {
        this.b20000453010115 = b20000453010115;
    }

    public BigDecimal getB20000453010116() {
        return b20000453010116;
    }

    public void setB20000453010116(BigDecimal b20000453010116) {
        this.b20000453010116 = b20000453010116;
    }

    public BigDecimal getB2000045301011601() {
        return b2000045301011601;
    }

    public void setB2000045301011601(BigDecimal b2000045301011601) {
        this.b2000045301011601 = b2000045301011601;
    }

    public BigDecimal getB2000045301011602() {
        return b2000045301011602;
    }

    public void setB2000045301011602(BigDecimal b2000045301011602) {
        this.b2000045301011602 = b2000045301011602;
    }

    public BigDecimal getB2000045301011603() {
        return b2000045301011603;
    }

    public void setB2000045301011603(BigDecimal b2000045301011603) {
        this.b2000045301011603 = b2000045301011603;
    }

    public BigDecimal getB2000045301011604() {
        return b2000045301011604;
    }

    public void setB2000045301011604(BigDecimal b2000045301011604) {
        this.b2000045301011604 = b2000045301011604;
    }

    public BigDecimal getB2000045301011605() {
        return b2000045301011605;
    }

    public void setB2000045301011605(BigDecimal b2000045301011605) {
        this.b2000045301011605 = b2000045301011605;
    }

    public BigDecimal getB2000045301011606() {
        return b2000045301011606;
    }

    public void setB2000045301011606(BigDecimal b2000045301011606) {
        this.b2000045301011606 = b2000045301011606;
    }

    public BigDecimal getB2000045301011607() {
        return b2000045301011607;
    }

    public void setB2000045301011607(BigDecimal b2000045301011607) {
        this.b2000045301011607 = b2000045301011607;
    }

    public BigDecimal getB2000045301011608() {
        return b2000045301011608;
    }

    public void setB2000045301011608(BigDecimal b2000045301011608) {
        this.b2000045301011608 = b2000045301011608;
    }

    public BigDecimal getB2000045301011609() {
        return b2000045301011609;
    }

    public void setB2000045301011609(BigDecimal b2000045301011609) {
        this.b2000045301011609 = b2000045301011609;
    }

    public BigDecimal getB20000453010117() {
        return b20000453010117;
    }

    public void setB20000453010117(BigDecimal b20000453010117) {
        this.b20000453010117 = b20000453010117;
    }

    public BigDecimal getB2000045301011701() {
        return b2000045301011701;
    }

    public void setB2000045301011701(BigDecimal b2000045301011701) {
        this.b2000045301011701 = b2000045301011701;
    }

    public BigDecimal getB2000045301011702() {
        return b2000045301011702;
    }

    public void setB2000045301011702(BigDecimal b2000045301011702) {
        this.b2000045301011702 = b2000045301011702;
    }

    public BigDecimal getB2000045301011703() {
        return b2000045301011703;
    }

    public void setB2000045301011703(BigDecimal b2000045301011703) {
        this.b2000045301011703 = b2000045301011703;
    }

    public BigDecimal getB2000045301011704() {
        return b2000045301011704;
    }

    public void setB2000045301011704(BigDecimal b2000045301011704) {
        this.b2000045301011704 = b2000045301011704;
    }

    public BigDecimal getB20000453010118() {
        return b20000453010118;
    }

    public void setB20000453010118(BigDecimal b20000453010118) {
        this.b20000453010118 = b20000453010118;
    }

    public BigDecimal getB20000453010119() {
        return b20000453010119;
    }

    public void setB20000453010119(BigDecimal b20000453010119) {
        this.b20000453010119 = b20000453010119;
    }

    public BigDecimal getB20000453010120() {
        return b20000453010120;
    }

    public void setB20000453010120(BigDecimal b20000453010120) {
        this.b20000453010120 = b20000453010120;
    }

    public BigDecimal getB20000453010121() {
        return b20000453010121;
    }

    public void setB20000453010121(BigDecimal b20000453010121) {
        this.b20000453010121 = b20000453010121;
    }

    public BigDecimal getB20000453010122() {
        return b20000453010122;
    }

    public void setB20000453010122(BigDecimal b20000453010122) {
        this.b20000453010122 = b20000453010122;
    }

    public BigDecimal getB20000453010123() {
        return b20000453010123;
    }

    public void setB20000453010123(BigDecimal b20000453010123) {
        this.b20000453010123 = b20000453010123;
    }

    public BigDecimal getB20000453010124() {
        return b20000453010124;
    }

    public void setB20000453010124(BigDecimal b20000453010124) {
        this.b20000453010124 = b20000453010124;
    }

    public BigDecimal getB20000453010125() {
        return b20000453010125;
    }

    public void setB20000453010125(BigDecimal b20000453010125) {
        this.b20000453010125 = b20000453010125;
    }

    public BigDecimal getB20000453010126() {
        return b20000453010126;
    }

    public void setB20000453010126(BigDecimal b20000453010126) {
        this.b20000453010126 = b20000453010126;
    }

    public BigDecimal getB2000045301012601() {
        return b2000045301012601;
    }

    public void setB2000045301012601(BigDecimal b2000045301012601) {
        this.b2000045301012601 = b2000045301012601;
    }

    public BigDecimal getB2000045301012602() {
        return b2000045301012602;
    }

    public void setB2000045301012602(BigDecimal b2000045301012602) {
        this.b2000045301012602 = b2000045301012602;
    }

    public BigDecimal getB2000045301012603() {
        return b2000045301012603;
    }

    public void setB2000045301012603(BigDecimal b2000045301012603) {
        this.b2000045301012603 = b2000045301012603;
    }

    public BigDecimal getB2000045301012604() {
        return b2000045301012604;
    }

    public void setB2000045301012604(BigDecimal b2000045301012604) {
        this.b2000045301012604 = b2000045301012604;
    }

    public BigDecimal getB200004530102() {
        return b200004530102;
    }

    public void setB200004530102(BigDecimal b200004530102) {
        this.b200004530102 = b200004530102;
    }

    public BigDecimal getB20000453010201() {
        return b20000453010201;
    }

    public void setB20000453010201(BigDecimal b20000453010201) {
        this.b20000453010201 = b20000453010201;
    }

    public BigDecimal getB2000045301020101() {
        return b2000045301020101;
    }

    public void setB2000045301020101(BigDecimal b2000045301020101) {
        this.b2000045301020101 = b2000045301020101;
    }

    public BigDecimal getB2000045301020102() {
        return b2000045301020102;
    }

    public void setB2000045301020102(BigDecimal b2000045301020102) {
        this.b2000045301020102 = b2000045301020102;
    }

    public BigDecimal getB2000045301020103() {
        return b2000045301020103;
    }

    public void setB2000045301020103(BigDecimal b2000045301020103) {
        this.b2000045301020103 = b2000045301020103;
    }

    public BigDecimal getB2000045301020104() {
        return b2000045301020104;
    }

    public void setB2000045301020104(BigDecimal b2000045301020104) {
        this.b2000045301020104 = b2000045301020104;
    }

    public BigDecimal getB2000045301020105() {
        return b2000045301020105;
    }

    public void setB2000045301020105(BigDecimal b2000045301020105) {
        this.b2000045301020105 = b2000045301020105;
    }

    public BigDecimal getB2000045301020106() {
        return b2000045301020106;
    }

    public void setB2000045301020106(BigDecimal b2000045301020106) {
        this.b2000045301020106 = b2000045301020106;
    }

    public BigDecimal getB2000045301020107() {
        return b2000045301020107;
    }

    public void setB2000045301020107(BigDecimal b2000045301020107) {
        this.b2000045301020107 = b2000045301020107;
    }

    public BigDecimal getB2000045301020108() {
        return b2000045301020108;
    }

    public void setB2000045301020108(BigDecimal b2000045301020108) {
        this.b2000045301020108 = b2000045301020108;
    }

    public BigDecimal getB2000045301020109() {
        return b2000045301020109;
    }

    public void setB2000045301020109(BigDecimal b2000045301020109) {
        this.b2000045301020109 = b2000045301020109;
    }

    public BigDecimal getB2000045301020110() {
        return b2000045301020110;
    }

    public void setB2000045301020110(BigDecimal b2000045301020110) {
        this.b2000045301020110 = b2000045301020110;
    }

    public BigDecimal getB2000045301020111() {
        return b2000045301020111;
    }

    public void setB2000045301020111(BigDecimal b2000045301020111) {
        this.b2000045301020111 = b2000045301020111;
    }

    public BigDecimal getB2000045301020112() {
        return b2000045301020112;
    }

    public void setB2000045301020112(BigDecimal b2000045301020112) {
        this.b2000045301020112 = b2000045301020112;
    }

    public BigDecimal getB2000045301020113() {
        return b2000045301020113;
    }

    public void setB2000045301020113(BigDecimal b2000045301020113) {
        this.b2000045301020113 = b2000045301020113;
    }

    public BigDecimal getB20000453010202() {
        return b20000453010202;
    }

    public void setB20000453010202(BigDecimal b20000453010202) {
        this.b20000453010202 = b20000453010202;
    }

    public BigDecimal getB20000453010203() {
        return b20000453010203;
    }

    public void setB20000453010203(BigDecimal b20000453010203) {
        this.b20000453010203 = b20000453010203;
    }

    public BigDecimal getB2000045301020301() {
        return b2000045301020301;
    }

    public void setB2000045301020301(BigDecimal b2000045301020301) {
        this.b2000045301020301 = b2000045301020301;
    }

    public BigDecimal getB2000045301020302() {
        return b2000045301020302;
    }

    public void setB2000045301020302(BigDecimal b2000045301020302) {
        this.b2000045301020302 = b2000045301020302;
    }

    public BigDecimal getB2000045301020303() {
        return b2000045301020303;
    }

    public void setB2000045301020303(BigDecimal b2000045301020303) {
        this.b2000045301020303 = b2000045301020303;
    }

    public BigDecimal getB2000045301020304() {
        return b2000045301020304;
    }

    public void setB2000045301020304(BigDecimal b2000045301020304) {
        this.b2000045301020304 = b2000045301020304;
    }

    public BigDecimal getB2000045301020305() {
        return b2000045301020305;
    }

    public void setB2000045301020305(BigDecimal b2000045301020305) {
        this.b2000045301020305 = b2000045301020305;
    }

    public BigDecimal getB2000045301020306() {
        return b2000045301020306;
    }

    public void setB2000045301020306(BigDecimal b2000045301020306) {
        this.b2000045301020306 = b2000045301020306;
    }

    public BigDecimal getB2000045301020307() {
        return b2000045301020307;
    }

    public void setB2000045301020307(BigDecimal b2000045301020307) {
        this.b2000045301020307 = b2000045301020307;
    }

    public BigDecimal getB2000045301020308() {
        return b2000045301020308;
    }

    public void setB2000045301020308(BigDecimal b2000045301020308) {
        this.b2000045301020308 = b2000045301020308;
    }

    public BigDecimal getB2000045301020309() {
        return b2000045301020309;
    }

    public void setB2000045301020309(BigDecimal b2000045301020309) {
        this.b2000045301020309 = b2000045301020309;
    }

    public BigDecimal getB2000045301020310() {
        return b2000045301020310;
    }

    public void setB2000045301020310(BigDecimal b2000045301020310) {
        this.b2000045301020310 = b2000045301020310;
    }

    public BigDecimal getB2000045301020311() {
        return b2000045301020311;
    }

    public void setB2000045301020311(BigDecimal b2000045301020311) {
        this.b2000045301020311 = b2000045301020311;
    }

    public BigDecimal getB2000045301020312() {
        return b2000045301020312;
    }

    public void setB2000045301020312(BigDecimal b2000045301020312) {
        this.b2000045301020312 = b2000045301020312;
    }

    public BigDecimal getB2000045301020313() {
        return b2000045301020313;
    }

    public void setB2000045301020313(BigDecimal b2000045301020313) {
        this.b2000045301020313 = b2000045301020313;
    }

    public BigDecimal getB20000453010204() {
        return b20000453010204;
    }

    public void setB20000453010204(BigDecimal b20000453010204) {
        this.b20000453010204 = b20000453010204;
    }

    public BigDecimal getB2000045301020401() {
        return b2000045301020401;
    }

    public void setB2000045301020401(BigDecimal b2000045301020401) {
        this.b2000045301020401 = b2000045301020401;
    }

    public BigDecimal getB2000045301020402() {
        return b2000045301020402;
    }

    public void setB2000045301020402(BigDecimal b2000045301020402) {
        this.b2000045301020402 = b2000045301020402;
    }

    public BigDecimal getB20000453010205() {
        return b20000453010205;
    }

    public void setB20000453010205(BigDecimal b20000453010205) {
        this.b20000453010205 = b20000453010205;
    }

    public BigDecimal getB2000045301020501() {
        return b2000045301020501;
    }

    public void setB2000045301020501(BigDecimal b2000045301020501) {
        this.b2000045301020501 = b2000045301020501;
    }

    public BigDecimal getB2000045301020502() {
        return b2000045301020502;
    }

    public void setB2000045301020502(BigDecimal b2000045301020502) {
        this.b2000045301020502 = b2000045301020502;
    }

    public BigDecimal getB20000453010206() {
        return b20000453010206;
    }

    public void setB20000453010206(BigDecimal b20000453010206) {
        this.b20000453010206 = b20000453010206;
    }

    public BigDecimal getB2000045301020601() {
        return b2000045301020601;
    }

    public void setB2000045301020601(BigDecimal b2000045301020601) {
        this.b2000045301020601 = b2000045301020601;
    }

    public BigDecimal getB2000045301020602() {
        return b2000045301020602;
    }

    public void setB2000045301020602(BigDecimal b2000045301020602) {
        this.b2000045301020602 = b2000045301020602;
    }

    public BigDecimal getB2000045301020603() {
        return b2000045301020603;
    }

    public void setB2000045301020603(BigDecimal b2000045301020603) {
        this.b2000045301020603 = b2000045301020603;
    }

    public BigDecimal getB2000045301020604() {
        return b2000045301020604;
    }

    public void setB2000045301020604(BigDecimal b2000045301020604) {
        this.b2000045301020604 = b2000045301020604;
    }

    public BigDecimal getB20000453010207() {
        return b20000453010207;
    }

    public void setB20000453010207(BigDecimal b20000453010207) {
        this.b20000453010207 = b20000453010207;
    }

    public BigDecimal getB2000045301020701() {
        return b2000045301020701;
    }

    public void setB2000045301020701(BigDecimal b2000045301020701) {
        this.b2000045301020701 = b2000045301020701;
    }

    public BigDecimal getB2000045301020702() {
        return b2000045301020702;
    }

    public void setB2000045301020702(BigDecimal b2000045301020702) {
        this.b2000045301020702 = b2000045301020702;
    }

    public BigDecimal getB2000045301020703() {
        return b2000045301020703;
    }

    public void setB2000045301020703(BigDecimal b2000045301020703) {
        this.b2000045301020703 = b2000045301020703;
    }

    public BigDecimal getB20000453010208() {
        return b20000453010208;
    }

    public void setB20000453010208(BigDecimal b20000453010208) {
        this.b20000453010208 = b20000453010208;
    }

    public BigDecimal getB2000045301020801() {
        return b2000045301020801;
    }

    public void setB2000045301020801(BigDecimal b2000045301020801) {
        this.b2000045301020801 = b2000045301020801;
    }

    public BigDecimal getB2000045301020802() {
        return b2000045301020802;
    }

    public void setB2000045301020802(BigDecimal b2000045301020802) {
        this.b2000045301020802 = b2000045301020802;
    }

    public BigDecimal getB2000045301020803() {
        return b2000045301020803;
    }

    public void setB2000045301020803(BigDecimal b2000045301020803) {
        this.b2000045301020803 = b2000045301020803;
    }

    public BigDecimal getB2000045301020804() {
        return b2000045301020804;
    }

    public void setB2000045301020804(BigDecimal b2000045301020804) {
        this.b2000045301020804 = b2000045301020804;
    }

    public BigDecimal getB2000045301020805() {
        return b2000045301020805;
    }

    public void setB2000045301020805(BigDecimal b2000045301020805) {
        this.b2000045301020805 = b2000045301020805;
    }

    public BigDecimal getB2000045301020806() {
        return b2000045301020806;
    }

    public void setB2000045301020806(BigDecimal b2000045301020806) {
        this.b2000045301020806 = b2000045301020806;
    }

    public BigDecimal getB20000453010209() {
        return b20000453010209;
    }

    public void setB20000453010209(BigDecimal b20000453010209) {
        this.b20000453010209 = b20000453010209;
    }

    public BigDecimal getB2000045301020901() {
        return b2000045301020901;
    }

    public void setB2000045301020901(BigDecimal b2000045301020901) {
        this.b2000045301020901 = b2000045301020901;
    }

    public BigDecimal getB2000045301020902() {
        return b2000045301020902;
    }

    public void setB2000045301020902(BigDecimal b2000045301020902) {
        this.b2000045301020902 = b2000045301020902;
    }

    public BigDecimal getB2000045301020903() {
        return b2000045301020903;
    }

    public void setB2000045301020903(BigDecimal b2000045301020903) {
        this.b2000045301020903 = b2000045301020903;
    }

    public BigDecimal getB2000045301020904() {
        return b2000045301020904;
    }

    public void setB2000045301020904(BigDecimal b2000045301020904) {
        this.b2000045301020904 = b2000045301020904;
    }

    public BigDecimal getB2000045301020905() {
        return b2000045301020905;
    }

    public void setB2000045301020905(BigDecimal b2000045301020905) {
        this.b2000045301020905 = b2000045301020905;
    }

    public BigDecimal getB2000045301020906() {
        return b2000045301020906;
    }

    public void setB2000045301020906(BigDecimal b2000045301020906) {
        this.b2000045301020906 = b2000045301020906;
    }

    public BigDecimal getB2000045301020907() {
        return b2000045301020907;
    }

    public void setB2000045301020907(BigDecimal b2000045301020907) {
        this.b2000045301020907 = b2000045301020907;
    }

    public BigDecimal getB20000453010210() {
        return b20000453010210;
    }

    public void setB20000453010210(BigDecimal b20000453010210) {
        this.b20000453010210 = b20000453010210;
    }

    public BigDecimal getB20000453010211() {
        return b20000453010211;
    }

    public void setB20000453010211(BigDecimal b20000453010211) {
        this.b20000453010211 = b20000453010211;
    }

    public BigDecimal getB2000045301021101() {
        return b2000045301021101;
    }

    public void setB2000045301021101(BigDecimal b2000045301021101) {
        this.b2000045301021101 = b2000045301021101;
    }

    public BigDecimal getB2000045301021102() {
        return b2000045301021102;
    }

    public void setB2000045301021102(BigDecimal b2000045301021102) {
        this.b2000045301021102 = b2000045301021102;
    }

    public BigDecimal getB2000045301021103() {
        return b2000045301021103;
    }

    public void setB2000045301021103(BigDecimal b2000045301021103) {
        this.b2000045301021103 = b2000045301021103;
    }

    public BigDecimal getB2000045301021104() {
        return b2000045301021104;
    }

    public void setB2000045301021104(BigDecimal b2000045301021104) {
        this.b2000045301021104 = b2000045301021104;
    }

    public BigDecimal getB20000453010212() {
        return b20000453010212;
    }

    public void setB20000453010212(BigDecimal b20000453010212) {
        this.b20000453010212 = b20000453010212;
    }

    public BigDecimal getB20000453010213() {
        return b20000453010213;
    }

    public void setB20000453010213(BigDecimal b20000453010213) {
        this.b20000453010213 = b20000453010213;
    }

    public BigDecimal getB20000453010214() {
        return b20000453010214;
    }

    public void setB20000453010214(BigDecimal b20000453010214) {
        this.b20000453010214 = b20000453010214;
    }

    public BigDecimal getB20000453010215() {
        return b20000453010215;
    }

    public void setB20000453010215(BigDecimal b20000453010215) {
        this.b20000453010215 = b20000453010215;
    }

    public BigDecimal getB20000453010216() {
        return b20000453010216;
    }

    public void setB20000453010216(BigDecimal b20000453010216) {
        this.b20000453010216 = b20000453010216;
    }

    public BigDecimal getB2000045301021601() {
        return b2000045301021601;
    }

    public void setB2000045301021601(BigDecimal b2000045301021601) {
        this.b2000045301021601 = b2000045301021601;
    }

    public BigDecimal getB2000045301021602() {
        return b2000045301021602;
    }

    public void setB2000045301021602(BigDecimal b2000045301021602) {
        this.b2000045301021602 = b2000045301021602;
    }

    public BigDecimal getB2000045301021603() {
        return b2000045301021603;
    }

    public void setB2000045301021603(BigDecimal b2000045301021603) {
        this.b2000045301021603 = b2000045301021603;
    }

    public BigDecimal getB2000045301021604() {
        return b2000045301021604;
    }

    public void setB2000045301021604(BigDecimal b2000045301021604) {
        this.b2000045301021604 = b2000045301021604;
    }

    public BigDecimal getB2000045301021605() {
        return b2000045301021605;
    }

    public void setB2000045301021605(BigDecimal b2000045301021605) {
        this.b2000045301021605 = b2000045301021605;
    }

    public BigDecimal getB2000045301021606() {
        return b2000045301021606;
    }

    public void setB2000045301021606(BigDecimal b2000045301021606) {
        this.b2000045301021606 = b2000045301021606;
    }

    public BigDecimal getB2000045301021607() {
        return b2000045301021607;
    }

    public void setB2000045301021607(BigDecimal b2000045301021607) {
        this.b2000045301021607 = b2000045301021607;
    }

    public BigDecimal getB2000045301021608() {
        return b2000045301021608;
    }

    public void setB2000045301021608(BigDecimal b2000045301021608) {
        this.b2000045301021608 = b2000045301021608;
    }

    public BigDecimal getB2000045301021609() {
        return b2000045301021609;
    }

    public void setB2000045301021609(BigDecimal b2000045301021609) {
        this.b2000045301021609 = b2000045301021609;
    }

    public BigDecimal getB20000453010217() {
        return b20000453010217;
    }

    public void setB20000453010217(BigDecimal b20000453010217) {
        this.b20000453010217 = b20000453010217;
    }

    public BigDecimal getB2000045301021701() {
        return b2000045301021701;
    }

    public void setB2000045301021701(BigDecimal b2000045301021701) {
        this.b2000045301021701 = b2000045301021701;
    }

    public BigDecimal getB2000045301021702() {
        return b2000045301021702;
    }

    public void setB2000045301021702(BigDecimal b2000045301021702) {
        this.b2000045301021702 = b2000045301021702;
    }

    public BigDecimal getB2000045301021703() {
        return b2000045301021703;
    }

    public void setB2000045301021703(BigDecimal b2000045301021703) {
        this.b2000045301021703 = b2000045301021703;
    }

    public BigDecimal getB2000045301021704() {
        return b2000045301021704;
    }

    public void setB2000045301021704(BigDecimal b2000045301021704) {
        this.b2000045301021704 = b2000045301021704;
    }

    public BigDecimal getB20000453010218() {
        return b20000453010218;
    }

    public void setB20000453010218(BigDecimal b20000453010218) {
        this.b20000453010218 = b20000453010218;
    }

    public BigDecimal getB20000453010219() {
        return b20000453010219;
    }

    public void setB20000453010219(BigDecimal b20000453010219) {
        this.b20000453010219 = b20000453010219;
    }

    public BigDecimal getB20000453010220() {
        return b20000453010220;
    }

    public void setB20000453010220(BigDecimal b20000453010220) {
        this.b20000453010220 = b20000453010220;
    }

    public BigDecimal getB20000453010221() {
        return b20000453010221;
    }

    public void setB20000453010221(BigDecimal b20000453010221) {
        this.b20000453010221 = b20000453010221;
    }

    public BigDecimal getB20000453010222() {
        return b20000453010222;
    }

    public void setB20000453010222(BigDecimal b20000453010222) {
        this.b20000453010222 = b20000453010222;
    }

    public BigDecimal getB20000453010223() {
        return b20000453010223;
    }

    public void setB20000453010223(BigDecimal b20000453010223) {
        this.b20000453010223 = b20000453010223;
    }

    public BigDecimal getB20000453010224() {
        return b20000453010224;
    }

    public void setB20000453010224(BigDecimal b20000453010224) {
        this.b20000453010224 = b20000453010224;
    }

    public BigDecimal getB20000453010225() {
        return b20000453010225;
    }

    public void setB20000453010225(BigDecimal b20000453010225) {
        this.b20000453010225 = b20000453010225;
    }

    public BigDecimal getB20000453010226() {
        return b20000453010226;
    }

    public void setB20000453010226(BigDecimal b20000453010226) {
        this.b20000453010226 = b20000453010226;
    }

    public BigDecimal getB2000045301022601() {
        return b2000045301022601;
    }

    public void setB2000045301022601(BigDecimal b2000045301022601) {
        this.b2000045301022601 = b2000045301022601;
    }

    public BigDecimal getB2000045301022602() {
        return b2000045301022602;
    }

    public void setB2000045301022602(BigDecimal b2000045301022602) {
        this.b2000045301022602 = b2000045301022602;
    }

    public BigDecimal getB2000045301022603() {
        return b2000045301022603;
    }

    public void setB2000045301022603(BigDecimal b2000045301022603) {
        this.b2000045301022603 = b2000045301022603;
    }

    public BigDecimal getB2000045301022604() {
        return b2000045301022604;
    }

    public void setB2000045301022604(BigDecimal b2000045301022604) {
        this.b2000045301022604 = b2000045301022604;
    }

    public BigDecimal getB2000045401() {
        return b2000045401;
    }

    public void setB2000045401(BigDecimal b2000045401) {
        this.b2000045401 = b2000045401;
    }

    public BigDecimal getB2000045402() {
        return b2000045402;
    }

    public void setB2000045402(BigDecimal b2000045402) {
        this.b2000045402 = b2000045402;
    }

    public BigDecimal getB2000045403() {
        return b2000045403;
    }

    public void setB2000045403(BigDecimal b2000045403) {
        this.b2000045403 = b2000045403;
    }

    public BigDecimal getB2000045501() {
        return b2000045501;
    }

    public void setB2000045501(BigDecimal b2000045501) {
        this.b2000045501 = b2000045501;
    }

    public BigDecimal getB200004550101() {
        return b200004550101;
    }

    public void setB200004550101(BigDecimal b200004550101) {
        this.b200004550101 = b200004550101;
    }

    public BigDecimal getB200004550102() {
        return b200004550102;
    }

    public void setB200004550102(BigDecimal b200004550102) {
        this.b200004550102 = b200004550102;
    }

    public BigDecimal getB200004550103() {
        return b200004550103;
    }

    public void setB200004550103(BigDecimal b200004550103) {
        this.b200004550103 = b200004550103;
    }

    public BigDecimal getB200004550104() {
        return b200004550104;
    }

    public void setB200004550104(BigDecimal b200004550104) {
        this.b200004550104 = b200004550104;
    }

    public BigDecimal getB200004550105() {
        return b200004550105;
    }

    public void setB200004550105(BigDecimal b200004550105) {
        this.b200004550105 = b200004550105;
    }

    public BigDecimal getB200004550106() {
        return b200004550106;
    }

    public void setB200004550106(BigDecimal b200004550106) {
        this.b200004550106 = b200004550106;
    }

    public BigDecimal getB200004550107() {
        return b200004550107;
    }

    public void setB200004550107(BigDecimal b200004550107) {
        this.b200004550107 = b200004550107;
    }

    public BigDecimal getB200004550108() {
        return b200004550108;
    }

    public void setB200004550108(BigDecimal b200004550108) {
        this.b200004550108 = b200004550108;
    }

    public BigDecimal getB200004550109() {
        return b200004550109;
    }

    public void setB200004550109(BigDecimal b200004550109) {
        this.b200004550109 = b200004550109;
    }

    public BigDecimal getB200004550110() {
        return b200004550110;
    }

    public void setB200004550110(BigDecimal b200004550110) {
        this.b200004550110 = b200004550110;
    }

    public BigDecimal getB200004550111() {
        return b200004550111;
    }

    public void setB200004550111(BigDecimal b200004550111) {
        this.b200004550111 = b200004550111;
    }

    public BigDecimal getB200004550112() {
        return b200004550112;
    }

    public void setB200004550112(BigDecimal b200004550112) {
        this.b200004550112 = b200004550112;
    }

    public BigDecimal getB200004550113() {
        return b200004550113;
    }

    public void setB200004550113(BigDecimal b200004550113) {
        this.b200004550113 = b200004550113;
    }

    public BigDecimal getB200004550114() {
        return b200004550114;
    }

    public void setB200004550114(BigDecimal b200004550114) {
        this.b200004550114 = b200004550114;
    }

    public BigDecimal getB200004550115() {
        return b200004550115;
    }

    public void setB200004550115(BigDecimal b200004550115) {
        this.b200004550115 = b200004550115;
    }

    public BigDecimal getB2000045502() {
        return b2000045502;
    }

    public void setB2000045502(BigDecimal b2000045502) {
        this.b2000045502 = b2000045502;
    }

    public BigDecimal getB2000045503() {
        return b2000045503;
    }

    public void setB2000045503(BigDecimal b2000045503) {
        this.b2000045503 = b2000045503;
    }

    public BigDecimal getB2000045504() {
        return b2000045504;
    }

    public void setB2000045504(BigDecimal b2000045504) {
        this.b2000045504 = b2000045504;
    }

    public BigDecimal getB2000046001() {
        return b2000046001;
    }

    public void setB2000046001(BigDecimal b2000046001) {
        this.b2000046001 = b2000046001;
    }

    public BigDecimal getB200004600101() {
        return b200004600101;
    }

    public void setB200004600101(BigDecimal b200004600101) {
        this.b200004600101 = b200004600101;
    }

    public BigDecimal getB200004600102() {
        return b200004600102;
    }

    public void setB200004600102(BigDecimal b200004600102) {
        this.b200004600102 = b200004600102;
    }

    public BigDecimal getB200004600103() {
        return b200004600103;
    }

    public void setB200004600103(BigDecimal b200004600103) {
        this.b200004600103 = b200004600103;
    }

    public BigDecimal getB200004600104() {
        return b200004600104;
    }

    public void setB200004600104(BigDecimal b200004600104) {
        this.b200004600104 = b200004600104;
    }

    public BigDecimal getB200004600105() {
        return b200004600105;
    }

    public void setB200004600105(BigDecimal b200004600105) {
        this.b200004600105 = b200004600105;
    }

    public BigDecimal getB2000046011() {
        return b2000046011;
    }

    public void setB2000046011(BigDecimal b2000046011) {
        this.b2000046011 = b2000046011;
    }

    public BigDecimal getB2000046021() {
        return b2000046021;
    }

    public void setB2000046021(BigDecimal b2000046021) {
        this.b2000046021 = b2000046021;
    }

    public BigDecimal getB2000046031() {
        return b2000046031;
    }

    public void setB2000046031(BigDecimal b2000046031) {
        this.b2000046031 = b2000046031;
    }

    public BigDecimal getB2000046041() {
        return b2000046041;
    }

    public void setB2000046041(BigDecimal b2000046041) {
        this.b2000046041 = b2000046041;
    }

    public BigDecimal getB2000046051() {
        return b2000046051;
    }

    public void setB2000046051(BigDecimal b2000046051) {
        this.b2000046051 = b2000046051;
    }

    public BigDecimal getB200004605101() {
        return b200004605101;
    }

    public void setB200004605101(BigDecimal b200004605101) {
        this.b200004605101 = b200004605101;
    }

    public BigDecimal getB200004605102() {
        return b200004605102;
    }

    public void setB200004605102(BigDecimal b200004605102) {
        this.b200004605102 = b200004605102;
    }

    public BigDecimal getB200004605103() {
        return b200004605103;
    }

    public void setB200004605103(BigDecimal b200004605103) {
        this.b200004605103 = b200004605103;
    }

    public BigDecimal getB200004605104() {
        return b200004605104;
    }

    public void setB200004605104(BigDecimal b200004605104) {
        this.b200004605104 = b200004605104;
    }

    public BigDecimal getB200004605105() {
        return b200004605105;
    }

    public void setB200004605105(BigDecimal b200004605105) {
        this.b200004605105 = b200004605105;
    }

    public BigDecimal getB2000046061() {
        return b2000046061;
    }

    public void setB2000046061(BigDecimal b2000046061) {
        this.b2000046061 = b2000046061;
    }

    public BigDecimal getB2000046101() {
        return b2000046101;
    }

    public void setB2000046101(BigDecimal b2000046101) {
        this.b2000046101 = b2000046101;
    }

    public BigDecimal getB2000046111() {
        return b2000046111;
    }

    public void setB2000046111(BigDecimal b2000046111) {
        this.b2000046111 = b2000046111;
    }

    public BigDecimal getB200004611101() {
        return b200004611101;
    }

    public void setB200004611101(BigDecimal b200004611101) {
        this.b200004611101 = b200004611101;
    }

    public BigDecimal getB20000461110101() {
        return b20000461110101;
    }

    public void setB20000461110101(BigDecimal b20000461110101) {
        this.b20000461110101 = b20000461110101;
    }

    public BigDecimal getB2000046111010101() {
        return b2000046111010101;
    }

    public void setB2000046111010101(BigDecimal b2000046111010101) {
        this.b2000046111010101 = b2000046111010101;
    }

    public BigDecimal getB2000046111010102() {
        return b2000046111010102;
    }

    public void setB2000046111010102(BigDecimal b2000046111010102) {
        this.b2000046111010102 = b2000046111010102;
    }

    public BigDecimal getB20000461110102() {
        return b20000461110102;
    }

    public void setB20000461110102(BigDecimal b20000461110102) {
        this.b20000461110102 = b20000461110102;
    }

    public BigDecimal getB2000046111010201() {
        return b2000046111010201;
    }

    public void setB2000046111010201(BigDecimal b2000046111010201) {
        this.b2000046111010201 = b2000046111010201;
    }

    public BigDecimal getB2000046111010202() {
        return b2000046111010202;
    }

    public void setB2000046111010202(BigDecimal b2000046111010202) {
        this.b2000046111010202 = b2000046111010202;
    }

    public BigDecimal getB20000461110103() {
        return b20000461110103;
    }

    public void setB20000461110103(BigDecimal b20000461110103) {
        this.b20000461110103 = b20000461110103;
    }

    public BigDecimal getB2000046111010301() {
        return b2000046111010301;
    }

    public void setB2000046111010301(BigDecimal b2000046111010301) {
        this.b2000046111010301 = b2000046111010301;
    }

    public BigDecimal getB2000046111010302() {
        return b2000046111010302;
    }

    public void setB2000046111010302(BigDecimal b2000046111010302) {
        this.b2000046111010302 = b2000046111010302;
    }

    public BigDecimal getB20000461110104() {
        return b20000461110104;
    }

    public void setB20000461110104(BigDecimal b20000461110104) {
        this.b20000461110104 = b20000461110104;
    }

    public BigDecimal getB2000046111010401() {
        return b2000046111010401;
    }

    public void setB2000046111010401(BigDecimal b2000046111010401) {
        this.b2000046111010401 = b2000046111010401;
    }

    public BigDecimal getB2000046111010402() {
        return b2000046111010402;
    }

    public void setB2000046111010402(BigDecimal b2000046111010402) {
        this.b2000046111010402 = b2000046111010402;
    }

    public BigDecimal getB20000461110105() {
        return b20000461110105;
    }

    public void setB20000461110105(BigDecimal b20000461110105) {
        this.b20000461110105 = b20000461110105;
    }

    public BigDecimal getB2000046111010501() {
        return b2000046111010501;
    }

    public void setB2000046111010501(BigDecimal b2000046111010501) {
        this.b2000046111010501 = b2000046111010501;
    }

    public BigDecimal getB2000046111010502() {
        return b2000046111010502;
    }

    public void setB2000046111010502(BigDecimal b2000046111010502) {
        this.b2000046111010502 = b2000046111010502;
    }

    public BigDecimal getB20000461110106() {
        return b20000461110106;
    }

    public void setB20000461110106(BigDecimal b20000461110106) {
        this.b20000461110106 = b20000461110106;
    }

    public BigDecimal getB2000046111010601() {
        return b2000046111010601;
    }

    public void setB2000046111010601(BigDecimal b2000046111010601) {
        this.b2000046111010601 = b2000046111010601;
    }

    public BigDecimal getB2000046111010602() {
        return b2000046111010602;
    }

    public void setB2000046111010602(BigDecimal b2000046111010602) {
        this.b2000046111010602 = b2000046111010602;
    }

    public BigDecimal getB20000461110107() {
        return b20000461110107;
    }

    public void setB20000461110107(BigDecimal b20000461110107) {
        this.b20000461110107 = b20000461110107;
    }

    public BigDecimal getB2000046111010701() {
        return b2000046111010701;
    }

    public void setB2000046111010701(BigDecimal b2000046111010701) {
        this.b2000046111010701 = b2000046111010701;
    }

    public BigDecimal getB2000046111010702() {
        return b2000046111010702;
    }

    public void setB2000046111010702(BigDecimal b2000046111010702) {
        this.b2000046111010702 = b2000046111010702;
    }

    public BigDecimal getB20000461110108() {
        return b20000461110108;
    }

    public void setB20000461110108(BigDecimal b20000461110108) {
        this.b20000461110108 = b20000461110108;
    }

    public BigDecimal getB2000046111010801() {
        return b2000046111010801;
    }

    public void setB2000046111010801(BigDecimal b2000046111010801) {
        this.b2000046111010801 = b2000046111010801;
    }

    public BigDecimal getB2000046111010802() {
        return b2000046111010802;
    }

    public void setB2000046111010802(BigDecimal b2000046111010802) {
        this.b2000046111010802 = b2000046111010802;
    }

    public BigDecimal getB200004611102() {
        return b200004611102;
    }

    public void setB200004611102(BigDecimal b200004611102) {
        this.b200004611102 = b200004611102;
    }

    public BigDecimal getB20000461110201() {
        return b20000461110201;
    }

    public void setB20000461110201(BigDecimal b20000461110201) {
        this.b20000461110201 = b20000461110201;
    }

    public BigDecimal getB2000046111020101() {
        return b2000046111020101;
    }

    public void setB2000046111020101(BigDecimal b2000046111020101) {
        this.b2000046111020101 = b2000046111020101;
    }

    public BigDecimal getB2000046111020102() {
        return b2000046111020102;
    }

    public void setB2000046111020102(BigDecimal b2000046111020102) {
        this.b2000046111020102 = b2000046111020102;
    }

    public BigDecimal getB20000461110202() {
        return b20000461110202;
    }

    public void setB20000461110202(BigDecimal b20000461110202) {
        this.b20000461110202 = b20000461110202;
    }

    public BigDecimal getB2000046111020201() {
        return b2000046111020201;
    }

    public void setB2000046111020201(BigDecimal b2000046111020201) {
        this.b2000046111020201 = b2000046111020201;
    }

    public BigDecimal getB2000046111020202() {
        return b2000046111020202;
    }

    public void setB2000046111020202(BigDecimal b2000046111020202) {
        this.b2000046111020202 = b2000046111020202;
    }

    public BigDecimal getB20000461110203() {
        return b20000461110203;
    }

    public void setB20000461110203(BigDecimal b20000461110203) {
        this.b20000461110203 = b20000461110203;
    }

    public BigDecimal getB2000046111020301() {
        return b2000046111020301;
    }

    public void setB2000046111020301(BigDecimal b2000046111020301) {
        this.b2000046111020301 = b2000046111020301;
    }

    public BigDecimal getB2000046111020302() {
        return b2000046111020302;
    }

    public void setB2000046111020302(BigDecimal b2000046111020302) {
        this.b2000046111020302 = b2000046111020302;
    }

    public BigDecimal getB20000461110204() {
        return b20000461110204;
    }

    public void setB20000461110204(BigDecimal b20000461110204) {
        this.b20000461110204 = b20000461110204;
    }

    public BigDecimal getB2000046111020401() {
        return b2000046111020401;
    }

    public void setB2000046111020401(BigDecimal b2000046111020401) {
        this.b2000046111020401 = b2000046111020401;
    }

    public BigDecimal getB2000046111020402() {
        return b2000046111020402;
    }

    public void setB2000046111020402(BigDecimal b2000046111020402) {
        this.b2000046111020402 = b2000046111020402;
    }

    public BigDecimal getB20000461110205() {
        return b20000461110205;
    }

    public void setB20000461110205(BigDecimal b20000461110205) {
        this.b20000461110205 = b20000461110205;
    }

    public BigDecimal getB2000046111020501() {
        return b2000046111020501;
    }

    public void setB2000046111020501(BigDecimal b2000046111020501) {
        this.b2000046111020501 = b2000046111020501;
    }

    public BigDecimal getB2000046111020502() {
        return b2000046111020502;
    }

    public void setB2000046111020502(BigDecimal b2000046111020502) {
        this.b2000046111020502 = b2000046111020502;
    }

    public BigDecimal getB20000461110206() {
        return b20000461110206;
    }

    public void setB20000461110206(BigDecimal b20000461110206) {
        this.b20000461110206 = b20000461110206;
    }

    public BigDecimal getB2000046111020601() {
        return b2000046111020601;
    }

    public void setB2000046111020601(BigDecimal b2000046111020601) {
        this.b2000046111020601 = b2000046111020601;
    }

    public BigDecimal getB2000046111020602() {
        return b2000046111020602;
    }

    public void setB2000046111020602(BigDecimal b2000046111020602) {
        this.b2000046111020602 = b2000046111020602;
    }

    public BigDecimal getB20000461110207() {
        return b20000461110207;
    }

    public void setB20000461110207(BigDecimal b20000461110207) {
        this.b20000461110207 = b20000461110207;
    }

    public BigDecimal getB2000046111020701() {
        return b2000046111020701;
    }

    public void setB2000046111020701(BigDecimal b2000046111020701) {
        this.b2000046111020701 = b2000046111020701;
    }

    public BigDecimal getB2000046111020702() {
        return b2000046111020702;
    }

    public void setB2000046111020702(BigDecimal b2000046111020702) {
        this.b2000046111020702 = b2000046111020702;
    }

    public BigDecimal getB20000461110208() {
        return b20000461110208;
    }

    public void setB20000461110208(BigDecimal b20000461110208) {
        this.b20000461110208 = b20000461110208;
    }

    public BigDecimal getB2000046111020801() {
        return b2000046111020801;
    }

    public void setB2000046111020801(BigDecimal b2000046111020801) {
        this.b2000046111020801 = b2000046111020801;
    }

    public BigDecimal getB2000046111020802() {
        return b2000046111020802;
    }

    public void setB2000046111020802(BigDecimal b2000046111020802) {
        this.b2000046111020802 = b2000046111020802;
    }

    public BigDecimal getB200004611103() {
        return b200004611103;
    }

    public void setB200004611103(BigDecimal b200004611103) {
        this.b200004611103 = b200004611103;
    }

    public BigDecimal getB20000461110301() {
        return b20000461110301;
    }

    public void setB20000461110301(BigDecimal b20000461110301) {
        this.b20000461110301 = b20000461110301;
    }

    public BigDecimal getB2000046111030101() {
        return b2000046111030101;
    }

    public void setB2000046111030101(BigDecimal b2000046111030101) {
        this.b2000046111030101 = b2000046111030101;
    }

    public BigDecimal getB2000046111030102() {
        return b2000046111030102;
    }

    public void setB2000046111030102(BigDecimal b2000046111030102) {
        this.b2000046111030102 = b2000046111030102;
    }

    public BigDecimal getB20000461110302() {
        return b20000461110302;
    }

    public void setB20000461110302(BigDecimal b20000461110302) {
        this.b20000461110302 = b20000461110302;
    }

    public BigDecimal getB2000046111030201() {
        return b2000046111030201;
    }

    public void setB2000046111030201(BigDecimal b2000046111030201) {
        this.b2000046111030201 = b2000046111030201;
    }

    public BigDecimal getB2000046111030202() {
        return b2000046111030202;
    }

    public void setB2000046111030202(BigDecimal b2000046111030202) {
        this.b2000046111030202 = b2000046111030202;
    }

    public BigDecimal getB20000461110303() {
        return b20000461110303;
    }

    public void setB20000461110303(BigDecimal b20000461110303) {
        this.b20000461110303 = b20000461110303;
    }

    public BigDecimal getB2000046111030301() {
        return b2000046111030301;
    }

    public void setB2000046111030301(BigDecimal b2000046111030301) {
        this.b2000046111030301 = b2000046111030301;
    }

    public BigDecimal getB2000046111030302() {
        return b2000046111030302;
    }

    public void setB2000046111030302(BigDecimal b2000046111030302) {
        this.b2000046111030302 = b2000046111030302;
    }

    public BigDecimal getB20000461110304() {
        return b20000461110304;
    }

    public void setB20000461110304(BigDecimal b20000461110304) {
        this.b20000461110304 = b20000461110304;
    }

    public BigDecimal getB2000046111030401() {
        return b2000046111030401;
    }

    public void setB2000046111030401(BigDecimal b2000046111030401) {
        this.b2000046111030401 = b2000046111030401;
    }

    public BigDecimal getB2000046111030402() {
        return b2000046111030402;
    }

    public void setB2000046111030402(BigDecimal b2000046111030402) {
        this.b2000046111030402 = b2000046111030402;
    }

    public BigDecimal getB20000461110305() {
        return b20000461110305;
    }

    public void setB20000461110305(BigDecimal b20000461110305) {
        this.b20000461110305 = b20000461110305;
    }

    public BigDecimal getB2000046111030501() {
        return b2000046111030501;
    }

    public void setB2000046111030501(BigDecimal b2000046111030501) {
        this.b2000046111030501 = b2000046111030501;
    }

    public BigDecimal getB2000046111030502() {
        return b2000046111030502;
    }

    public void setB2000046111030502(BigDecimal b2000046111030502) {
        this.b2000046111030502 = b2000046111030502;
    }

    public BigDecimal getB200004611104() {
        return b200004611104;
    }

    public void setB200004611104(BigDecimal b200004611104) {
        this.b200004611104 = b200004611104;
    }

    public BigDecimal getB20000461110401() {
        return b20000461110401;
    }

    public void setB20000461110401(BigDecimal b20000461110401) {
        this.b20000461110401 = b20000461110401;
    }

    public BigDecimal getB20000461110402() {
        return b20000461110402;
    }

    public void setB20000461110402(BigDecimal b20000461110402) {
        this.b20000461110402 = b20000461110402;
    }

    public BigDecimal getB200004611105() {
        return b200004611105;
    }

    public void setB200004611105(BigDecimal b200004611105) {
        this.b200004611105 = b200004611105;
    }

    public BigDecimal getB20000461110501() {
        return b20000461110501;
    }

    public void setB20000461110501(BigDecimal b20000461110501) {
        this.b20000461110501 = b20000461110501;
    }

    public BigDecimal getB20000461110502() {
        return b20000461110502;
    }

    public void setB20000461110502(BigDecimal b20000461110502) {
        this.b20000461110502 = b20000461110502;
    }

    public BigDecimal getB200004611106() {
        return b200004611106;
    }

    public void setB200004611106(BigDecimal b200004611106) {
        this.b200004611106 = b200004611106;
    }

    public BigDecimal getB20000461110601() {
        return b20000461110601;
    }

    public void setB20000461110601(BigDecimal b20000461110601) {
        this.b20000461110601 = b20000461110601;
    }

    public BigDecimal getB20000461110602() {
        return b20000461110602;
    }

    public void setB20000461110602(BigDecimal b20000461110602) {
        this.b20000461110602 = b20000461110602;
    }

    public BigDecimal getB200004611107() {
        return b200004611107;
    }

    public void setB200004611107(BigDecimal b200004611107) {
        this.b200004611107 = b200004611107;
    }

    public BigDecimal getB20000461110701() {
        return b20000461110701;
    }

    public void setB20000461110701(BigDecimal b20000461110701) {
        this.b20000461110701 = b20000461110701;
    }

    public BigDecimal getB2000046111070101() {
        return b2000046111070101;
    }

    public void setB2000046111070101(BigDecimal b2000046111070101) {
        this.b2000046111070101 = b2000046111070101;
    }

    public BigDecimal getB2000046111070102() {
        return b2000046111070102;
    }

    public void setB2000046111070102(BigDecimal b2000046111070102) {
        this.b2000046111070102 = b2000046111070102;
    }

    public BigDecimal getB20000461110702() {
        return b20000461110702;
    }

    public void setB20000461110702(BigDecimal b20000461110702) {
        this.b20000461110702 = b20000461110702;
    }

    public BigDecimal getB2000046111070201() {
        return b2000046111070201;
    }

    public void setB2000046111070201(BigDecimal b2000046111070201) {
        this.b2000046111070201 = b2000046111070201;
    }

    public BigDecimal getB2000046111070202() {
        return b2000046111070202;
    }

    public void setB2000046111070202(BigDecimal b2000046111070202) {
        this.b2000046111070202 = b2000046111070202;
    }

    public BigDecimal getB20000461110703() {
        return b20000461110703;
    }

    public void setB20000461110703(BigDecimal b20000461110703) {
        this.b20000461110703 = b20000461110703;
    }

    public BigDecimal getB2000046111070301() {
        return b2000046111070301;
    }

    public void setB2000046111070301(BigDecimal b2000046111070301) {
        this.b2000046111070301 = b2000046111070301;
    }

    public BigDecimal getB2000046111070302() {
        return b2000046111070302;
    }

    public void setB2000046111070302(BigDecimal b2000046111070302) {
        this.b2000046111070302 = b2000046111070302;
    }

    public BigDecimal getB20000461110704() {
        return b20000461110704;
    }

    public void setB20000461110704(BigDecimal b20000461110704) {
        this.b20000461110704 = b20000461110704;
    }

    public BigDecimal getB2000046111070401() {
        return b2000046111070401;
    }

    public void setB2000046111070401(BigDecimal b2000046111070401) {
        this.b2000046111070401 = b2000046111070401;
    }

    public BigDecimal getB2000046111070402() {
        return b2000046111070402;
    }

    public void setB2000046111070402(BigDecimal b2000046111070402) {
        this.b2000046111070402 = b2000046111070402;
    }

    public BigDecimal getB20000461110705() {
        return b20000461110705;
    }

    public void setB20000461110705(BigDecimal b20000461110705) {
        this.b20000461110705 = b20000461110705;
    }

    public BigDecimal getB2000046111070501() {
        return b2000046111070501;
    }

    public void setB2000046111070501(BigDecimal b2000046111070501) {
        this.b2000046111070501 = b2000046111070501;
    }

    public BigDecimal getB2000046111070502() {
        return b2000046111070502;
    }

    public void setB2000046111070502(BigDecimal b2000046111070502) {
        this.b2000046111070502 = b2000046111070502;
    }

    public BigDecimal getB200004611108() {
        return b200004611108;
    }

    public void setB200004611108(BigDecimal b200004611108) {
        this.b200004611108 = b200004611108;
    }

    public BigDecimal getB20000461110801() {
        return b20000461110801;
    }

    public void setB20000461110801(BigDecimal b20000461110801) {
        this.b20000461110801 = b20000461110801;
    }

    public BigDecimal getB2000046111080101() {
        return b2000046111080101;
    }

    public void setB2000046111080101(BigDecimal b2000046111080101) {
        this.b2000046111080101 = b2000046111080101;
    }

    public BigDecimal getB2000046111080102() {
        return b2000046111080102;
    }

    public void setB2000046111080102(BigDecimal b2000046111080102) {
        this.b2000046111080102 = b2000046111080102;
    }

    public BigDecimal getB20000461110802() {
        return b20000461110802;
    }

    public void setB20000461110802(BigDecimal b20000461110802) {
        this.b20000461110802 = b20000461110802;
    }

    public BigDecimal getB2000046111080201() {
        return b2000046111080201;
    }

    public void setB2000046111080201(BigDecimal b2000046111080201) {
        this.b2000046111080201 = b2000046111080201;
    }

    public BigDecimal getB2000046111080202() {
        return b2000046111080202;
    }

    public void setB2000046111080202(BigDecimal b2000046111080202) {
        this.b2000046111080202 = b2000046111080202;
    }

    public BigDecimal getB20000461110803() {
        return b20000461110803;
    }

    public void setB20000461110803(BigDecimal b20000461110803) {
        this.b20000461110803 = b20000461110803;
    }

    public BigDecimal getB2000046111080301() {
        return b2000046111080301;
    }

    public void setB2000046111080301(BigDecimal b2000046111080301) {
        this.b2000046111080301 = b2000046111080301;
    }

    public BigDecimal getB2000046111080302() {
        return b2000046111080302;
    }

    public void setB2000046111080302(BigDecimal b2000046111080302) {
        this.b2000046111080302 = b2000046111080302;
    }

    public BigDecimal getB20000461110804() {
        return b20000461110804;
    }

    public void setB20000461110804(BigDecimal b20000461110804) {
        this.b20000461110804 = b20000461110804;
    }

    public BigDecimal getB2000046111080401() {
        return b2000046111080401;
    }

    public void setB2000046111080401(BigDecimal b2000046111080401) {
        this.b2000046111080401 = b2000046111080401;
    }

    public BigDecimal getB2000046111080402() {
        return b2000046111080402;
    }

    public void setB2000046111080402(BigDecimal b2000046111080402) {
        this.b2000046111080402 = b2000046111080402;
    }

    public BigDecimal getB20000461110805() {
        return b20000461110805;
    }

    public void setB20000461110805(BigDecimal b20000461110805) {
        this.b20000461110805 = b20000461110805;
    }

    public BigDecimal getB2000046111080501() {
        return b2000046111080501;
    }

    public void setB2000046111080501(BigDecimal b2000046111080501) {
        this.b2000046111080501 = b2000046111080501;
    }

    public BigDecimal getB2000046111080502() {
        return b2000046111080502;
    }

    public void setB2000046111080502(BigDecimal b2000046111080502) {
        this.b2000046111080502 = b2000046111080502;
    }

    public BigDecimal getB20000461110806() {
        return b20000461110806;
    }

    public void setB20000461110806(BigDecimal b20000461110806) {
        this.b20000461110806 = b20000461110806;
    }

    public BigDecimal getB2000046111080601() {
        return b2000046111080601;
    }

    public void setB2000046111080601(BigDecimal b2000046111080601) {
        this.b2000046111080601 = b2000046111080601;
    }

    public BigDecimal getB2000046111080602() {
        return b2000046111080602;
    }

    public void setB2000046111080602(BigDecimal b2000046111080602) {
        this.b2000046111080602 = b2000046111080602;
    }

    public BigDecimal getB20000461110807() {
        return b20000461110807;
    }

    public void setB20000461110807(BigDecimal b20000461110807) {
        this.b20000461110807 = b20000461110807;
    }

    public BigDecimal getB2000046111080701() {
        return b2000046111080701;
    }

    public void setB2000046111080701(BigDecimal b2000046111080701) {
        this.b2000046111080701 = b2000046111080701;
    }

    public BigDecimal getB2000046111080702() {
        return b2000046111080702;
    }

    public void setB2000046111080702(BigDecimal b2000046111080702) {
        this.b2000046111080702 = b2000046111080702;
    }

    public BigDecimal getB20000461110808() {
        return b20000461110808;
    }

    public void setB20000461110808(BigDecimal b20000461110808) {
        this.b20000461110808 = b20000461110808;
    }

    public BigDecimal getB2000046111080801() {
        return b2000046111080801;
    }

    public void setB2000046111080801(BigDecimal b2000046111080801) {
        this.b2000046111080801 = b2000046111080801;
    }

    public BigDecimal getB2000046111080802() {
        return b2000046111080802;
    }

    public void setB2000046111080802(BigDecimal b2000046111080802) {
        this.b2000046111080802 = b2000046111080802;
    }

    public BigDecimal getB2000046115() {
        return b2000046115;
    }

    public void setB2000046115(BigDecimal b2000046115) {
        this.b2000046115 = b2000046115;
    }

    public BigDecimal getB200004611501() {
        return b200004611501;
    }

    public void setB200004611501(BigDecimal b200004611501) {
        this.b200004611501 = b200004611501;
    }

    public BigDecimal getB200004611502() {
        return b200004611502;
    }

    public void setB200004611502(BigDecimal b200004611502) {
        this.b200004611502 = b200004611502;
    }

    public BigDecimal getB200004611503() {
        return b200004611503;
    }

    public void setB200004611503(BigDecimal b200004611503) {
        this.b200004611503 = b200004611503;
    }

    public BigDecimal getB200004611504() {
        return b200004611504;
    }

    public void setB200004611504(BigDecimal b200004611504) {
        this.b200004611504 = b200004611504;
    }

    public BigDecimal getB200004611505() {
        return b200004611505;
    }

    public void setB200004611505(BigDecimal b200004611505) {
        this.b200004611505 = b200004611505;
    }

    public BigDecimal getB200004611506() {
        return b200004611506;
    }

    public void setB200004611506(BigDecimal b200004611506) {
        this.b200004611506 = b200004611506;
    }

    public BigDecimal getB200004611507() {
        return b200004611507;
    }

    public void setB200004611507(BigDecimal b200004611507) {
        this.b200004611507 = b200004611507;
    }

    public BigDecimal getB2000046117() {
        return b2000046117;
    }

    public void setB2000046117(BigDecimal b2000046117) {
        this.b2000046117 = b2000046117;
    }

    public BigDecimal getB2000046201() {
        return b2000046201;
    }

    public void setB2000046201(BigDecimal b2000046201) {
        this.b2000046201 = b2000046201;
    }

    public BigDecimal getB2000046202() {
        return b2000046202;
    }

    public void setB2000046202(BigDecimal b2000046202) {
        this.b2000046202 = b2000046202;
    }

    public BigDecimal getB2000046203() {
        return b2000046203;
    }

    public void setB2000046203(BigDecimal b2000046203) {
        this.b2000046203 = b2000046203;
    }

    public BigDecimal getB2000046301() {
        return b2000046301;
    }

    public void setB2000046301(BigDecimal b2000046301) {
        this.b2000046301 = b2000046301;
    }

    public BigDecimal getB200004630101() {
        return b200004630101;
    }

    public void setB200004630101(BigDecimal b200004630101) {
        this.b200004630101 = b200004630101;
    }

    public BigDecimal getB200004630102() {
        return b200004630102;
    }

    public void setB200004630102(BigDecimal b200004630102) {
        this.b200004630102 = b200004630102;
    }

    public BigDecimal getB200004630103() {
        return b200004630103;
    }

    public void setB200004630103(BigDecimal b200004630103) {
        this.b200004630103 = b200004630103;
    }

    public BigDecimal getB200004630104() {
        return b200004630104;
    }

    public void setB200004630104(BigDecimal b200004630104) {
        this.b200004630104 = b200004630104;
    }

    public BigDecimal getB200004630105() {
        return b200004630105;
    }

    public void setB200004630105(BigDecimal b200004630105) {
        this.b200004630105 = b200004630105;
    }

    public BigDecimal getB200004630106() {
        return b200004630106;
    }

    public void setB200004630106(BigDecimal b200004630106) {
        this.b200004630106 = b200004630106;
    }

    public BigDecimal getB200004630107() {
        return b200004630107;
    }

    public void setB200004630107(BigDecimal b200004630107) {
        this.b200004630107 = b200004630107;
    }

    public BigDecimal getB200004630108() {
        return b200004630108;
    }

    public void setB200004630108(BigDecimal b200004630108) {
        this.b200004630108 = b200004630108;
    }

    public BigDecimal getB200004630109() {
        return b200004630109;
    }

    public void setB200004630109(BigDecimal b200004630109) {
        this.b200004630109 = b200004630109;
    }

    public BigDecimal getB2000046401() {
        return b2000046401;
    }

    public void setB2000046401(BigDecimal b2000046401) {
        this.b2000046401 = b2000046401;
    }

    public BigDecimal getB200004640101() {
        return b200004640101;
    }

    public void setB200004640101(BigDecimal b200004640101) {
        this.b200004640101 = b200004640101;
    }

    public BigDecimal getB200004640102() {
        return b200004640102;
    }

    public void setB200004640102(BigDecimal b200004640102) {
        this.b200004640102 = b200004640102;
    }

    public BigDecimal getB200004640103() {
        return b200004640103;
    }

    public void setB200004640103(BigDecimal b200004640103) {
        this.b200004640103 = b200004640103;
    }

    public BigDecimal getB200004640104() {
        return b200004640104;
    }

    public void setB200004640104(BigDecimal b200004640104) {
        this.b200004640104 = b200004640104;
    }

    public BigDecimal getB200004640105() {
        return b200004640105;
    }

    public void setB200004640105(BigDecimal b200004640105) {
        this.b200004640105 = b200004640105;
    }

    public BigDecimal getB200004640106() {
        return b200004640106;
    }

    public void setB200004640106(BigDecimal b200004640106) {
        this.b200004640106 = b200004640106;
    }

    public BigDecimal getB200004640107() {
        return b200004640107;
    }

    public void setB200004640107(BigDecimal b200004640107) {
        this.b200004640107 = b200004640107;
    }

    public BigDecimal getB200004640108() {
        return b200004640108;
    }

    public void setB200004640108(BigDecimal b200004640108) {
        this.b200004640108 = b200004640108;
    }

    public BigDecimal getB200004640109() {
        return b200004640109;
    }

    public void setB200004640109(BigDecimal b200004640109) {
        this.b200004640109 = b200004640109;
    }

    public BigDecimal getB200004640110() {
        return b200004640110;
    }

    public void setB200004640110(BigDecimal b200004640110) {
        this.b200004640110 = b200004640110;
    }

    public BigDecimal getB200004640111() {
        return b200004640111;
    }

    public void setB200004640111(BigDecimal b200004640111) {
        this.b200004640111 = b200004640111;
    }

    public BigDecimal getB200004640112() {
        return b200004640112;
    }

    public void setB200004640112(BigDecimal b200004640112) {
        this.b200004640112 = b200004640112;
    }

    public BigDecimal getB200004640113() {
        return b200004640113;
    }

    public void setB200004640113(BigDecimal b200004640113) {
        this.b200004640113 = b200004640113;
    }

    public BigDecimal getB200004640114() {
        return b200004640114;
    }

    public void setB200004640114(BigDecimal b200004640114) {
        this.b200004640114 = b200004640114;
    }

    public BigDecimal getB200004640115() {
        return b200004640115;
    }

    public void setB200004640115(BigDecimal b200004640115) {
        this.b200004640115 = b200004640115;
    }

    public BigDecimal getB200004640116() {
        return b200004640116;
    }

    public void setB200004640116(BigDecimal b200004640116) {
        this.b200004640116 = b200004640116;
    }

    public BigDecimal getB200004640117() {
        return b200004640117;
    }

    public void setB200004640117(BigDecimal b200004640117) {
        this.b200004640117 = b200004640117;
    }

    public BigDecimal getB200004640118() {
        return b200004640118;
    }

    public void setB200004640118(BigDecimal b200004640118) {
        this.b200004640118 = b200004640118;
    }

    public BigDecimal getB200004640119() {
        return b200004640119;
    }

    public void setB200004640119(BigDecimal b200004640119) {
        this.b200004640119 = b200004640119;
    }

    public BigDecimal getB2000046402() {
        return b2000046402;
    }

    public void setB2000046402(BigDecimal b2000046402) {
        this.b2000046402 = b2000046402;
    }

    public BigDecimal getB200004640201() {
        return b200004640201;
    }

    public void setB200004640201(BigDecimal b200004640201) {
        this.b200004640201 = b200004640201;
    }

    public BigDecimal getB200004640202() {
        return b200004640202;
    }

    public void setB200004640202(BigDecimal b200004640202) {
        this.b200004640202 = b200004640202;
    }

    public BigDecimal getB200004640203() {
        return b200004640203;
    }

    public void setB200004640203(BigDecimal b200004640203) {
        this.b200004640203 = b200004640203;
    }

    public BigDecimal getB200004640204() {
        return b200004640204;
    }

    public void setB200004640204(BigDecimal b200004640204) {
        this.b200004640204 = b200004640204;
    }

    public BigDecimal getB200004640205() {
        return b200004640205;
    }

    public void setB200004640205(BigDecimal b200004640205) {
        this.b200004640205 = b200004640205;
    }

    public BigDecimal getB2000046403() {
        return b2000046403;
    }

    public void setB2000046403(BigDecimal b2000046403) {
        this.b2000046403 = b2000046403;
    }

    public BigDecimal getB2000046411() {
        return b2000046411;
    }

    public void setB2000046411(BigDecimal b2000046411) {
        this.b2000046411 = b2000046411;
    }

    public BigDecimal getB2000046421() {
        return b2000046421;
    }

    public void setB2000046421(BigDecimal b2000046421) {
        this.b2000046421 = b2000046421;
    }

    public BigDecimal getB2000046501() {
        return b2000046501;
    }

    public void setB2000046501(BigDecimal b2000046501) {
        this.b2000046501 = b2000046501;
    }

    public BigDecimal getB2000046502() {
        return b2000046502;
    }

    public void setB2000046502(BigDecimal b2000046502) {
        this.b2000046502 = b2000046502;
    }

    public BigDecimal getB2000046511() {
        return b2000046511;
    }

    public void setB2000046511(BigDecimal b2000046511) {
        this.b2000046511 = b2000046511;
    }

    public BigDecimal getB2000046521() {
        return b2000046521;
    }

    public void setB2000046521(BigDecimal b2000046521) {
        this.b2000046521 = b2000046521;
    }

    public BigDecimal getB2000046531() {
        return b2000046531;
    }

    public void setB2000046531(BigDecimal b2000046531) {
        this.b2000046531 = b2000046531;
    }

    public BigDecimal getB2000046541() {
        return b2000046541;
    }

    public void setB2000046541(BigDecimal b2000046541) {
        this.b2000046541 = b2000046541;
    }

    public BigDecimal getB2000046542() {
        return b2000046542;
    }

    public void setB2000046542(BigDecimal b2000046542) {
        this.b2000046542 = b2000046542;
    }

    public BigDecimal getB2000046601() {
        return b2000046601;
    }

    public void setB2000046601(BigDecimal b2000046601) {
        this.b2000046601 = b2000046601;
    }

    public BigDecimal getB200004660101() {
        return b200004660101;
    }

    public void setB200004660101(BigDecimal b200004660101) {
        this.b200004660101 = b200004660101;
    }

    public BigDecimal getB200004660102() {
        return b200004660102;
    }

    public void setB200004660102(BigDecimal b200004660102) {
        this.b200004660102 = b200004660102;
    }

    public BigDecimal getB200004660103() {
        return b200004660103;
    }

    public void setB200004660103(BigDecimal b200004660103) {
        this.b200004660103 = b200004660103;
    }

    public BigDecimal getB200004660104() {
        return b200004660104;
    }

    public void setB200004660104(BigDecimal b200004660104) {
        this.b200004660104 = b200004660104;
    }

    public BigDecimal getB200004660105() {
        return b200004660105;
    }

    public void setB200004660105(BigDecimal b200004660105) {
        this.b200004660105 = b200004660105;
    }

    public BigDecimal getB200004660106() {
        return b200004660106;
    }

    public void setB200004660106(BigDecimal b200004660106) {
        this.b200004660106 = b200004660106;
    }

    public BigDecimal getB200004660107() {
        return b200004660107;
    }

    public void setB200004660107(BigDecimal b200004660107) {
        this.b200004660107 = b200004660107;
    }

    public BigDecimal getB200004660108() {
        return b200004660108;
    }

    public void setB200004660108(BigDecimal b200004660108) {
        this.b200004660108 = b200004660108;
    }

    public BigDecimal getB200004660109() {
        return b200004660109;
    }

    public void setB200004660109(BigDecimal b200004660109) {
        this.b200004660109 = b200004660109;
    }

    public BigDecimal getB200004660110() {
        return b200004660110;
    }

    public void setB200004660110(BigDecimal b200004660110) {
        this.b200004660110 = b200004660110;
    }

    public BigDecimal getB200004660111() {
        return b200004660111;
    }

    public void setB200004660111(BigDecimal b200004660111) {
        this.b200004660111 = b200004660111;
    }

    public BigDecimal getB200004660112() {
        return b200004660112;
    }

    public void setB200004660112(BigDecimal b200004660112) {
        this.b200004660112 = b200004660112;
    }

    public BigDecimal getB200004660113() {
        return b200004660113;
    }

    public void setB200004660113(BigDecimal b200004660113) {
        this.b200004660113 = b200004660113;
    }

    public BigDecimal getB200004660114() {
        return b200004660114;
    }

    public void setB200004660114(BigDecimal b200004660114) {
        this.b200004660114 = b200004660114;
    }

    public BigDecimal getB200004660115() {
        return b200004660115;
    }

    public void setB200004660115(BigDecimal b200004660115) {
        this.b200004660115 = b200004660115;
    }

    public BigDecimal getB200004660116() {
        return b200004660116;
    }

    public void setB200004660116(BigDecimal b200004660116) {
        this.b200004660116 = b200004660116;
    }

    public BigDecimal getB200004660117() {
        return b200004660117;
    }

    public void setB200004660117(BigDecimal b200004660117) {
        this.b200004660117 = b200004660117;
    }

    public BigDecimal getB200004660118() {
        return b200004660118;
    }

    public void setB200004660118(BigDecimal b200004660118) {
        this.b200004660118 = b200004660118;
    }

    public BigDecimal getB200004660119() {
        return b200004660119;
    }

    public void setB200004660119(BigDecimal b200004660119) {
        this.b200004660119 = b200004660119;
    }

    public BigDecimal getB200004660120() {
        return b200004660120;
    }

    public void setB200004660120(BigDecimal b200004660120) {
        this.b200004660120 = b200004660120;
    }

    public BigDecimal getB200004660121() {
        return b200004660121;
    }

    public void setB200004660121(BigDecimal b200004660121) {
        this.b200004660121 = b200004660121;
    }

    public BigDecimal getB200004660122() {
        return b200004660122;
    }

    public void setB200004660122(BigDecimal b200004660122) {
        this.b200004660122 = b200004660122;
    }

    public BigDecimal getB200004660123() {
        return b200004660123;
    }

    public void setB200004660123(BigDecimal b200004660123) {
        this.b200004660123 = b200004660123;
    }

    public BigDecimal getB200004660124() {
        return b200004660124;
    }

    public void setB200004660124(BigDecimal b200004660124) {
        this.b200004660124 = b200004660124;
    }

    public BigDecimal getB200004660125() {
        return b200004660125;
    }

    public void setB200004660125(BigDecimal b200004660125) {
        this.b200004660125 = b200004660125;
    }

    public BigDecimal getB200004660126() {
        return b200004660126;
    }

    public void setB200004660126(BigDecimal b200004660126) {
        this.b200004660126 = b200004660126;
    }

    public BigDecimal getB200004660127() {
        return b200004660127;
    }

    public void setB200004660127(BigDecimal b200004660127) {
        this.b200004660127 = b200004660127;
    }

    public BigDecimal getB200004660128() {
        return b200004660128;
    }

    public void setB200004660128(BigDecimal b200004660128) {
        this.b200004660128 = b200004660128;
    }

    public BigDecimal getB200004660129() {
        return b200004660129;
    }

    public void setB200004660129(BigDecimal b200004660129) {
        this.b200004660129 = b200004660129;
    }

    public BigDecimal getB200004660130() {
        return b200004660130;
    }

    public void setB200004660130(BigDecimal b200004660130) {
        this.b200004660130 = b200004660130;
    }

    public BigDecimal getB200004660131() {
        return b200004660131;
    }

    public void setB200004660131(BigDecimal b200004660131) {
        this.b200004660131 = b200004660131;
    }

    public BigDecimal getB200004660132() {
        return b200004660132;
    }

    public void setB200004660132(BigDecimal b200004660132) {
        this.b200004660132 = b200004660132;
    }

    public BigDecimal getB200004660133() {
        return b200004660133;
    }

    public void setB200004660133(BigDecimal b200004660133) {
        this.b200004660133 = b200004660133;
    }

    public BigDecimal getB200004660134() {
        return b200004660134;
    }

    public void setB200004660134(BigDecimal b200004660134) {
        this.b200004660134 = b200004660134;
    }

    public BigDecimal getB200004660135() {
        return b200004660135;
    }

    public void setB200004660135(BigDecimal b200004660135) {
        this.b200004660135 = b200004660135;
    }

    public BigDecimal getB200004660136() {
        return b200004660136;
    }

    public void setB200004660136(BigDecimal b200004660136) {
        this.b200004660136 = b200004660136;
    }

    public BigDecimal getB200004660137() {
        return b200004660137;
    }

    public void setB200004660137(BigDecimal b200004660137) {
        this.b200004660137 = b200004660137;
    }

    public BigDecimal getB200004660138() {
        return b200004660138;
    }

    public void setB200004660138(BigDecimal b200004660138) {
        this.b200004660138 = b200004660138;
    }

    public BigDecimal getB200004660139() {
        return b200004660139;
    }

    public void setB200004660139(BigDecimal b200004660139) {
        this.b200004660139 = b200004660139;
    }

    public BigDecimal getB200004660140() {
        return b200004660140;
    }

    public void setB200004660140(BigDecimal b200004660140) {
        this.b200004660140 = b200004660140;
    }

    public BigDecimal getB200004660141() {
        return b200004660141;
    }

    public void setB200004660141(BigDecimal b200004660141) {
        this.b200004660141 = b200004660141;
    }

    public BigDecimal getB200004660142() {
        return b200004660142;
    }

    public void setB200004660142(BigDecimal b200004660142) {
        this.b200004660142 = b200004660142;
    }

    public BigDecimal getB200004660143() {
        return b200004660143;
    }

    public void setB200004660143(BigDecimal b200004660143) {
        this.b200004660143 = b200004660143;
    }

    public BigDecimal getB200004660144() {
        return b200004660144;
    }

    public void setB200004660144(BigDecimal b200004660144) {
        this.b200004660144 = b200004660144;
    }

    public BigDecimal getB200004660145() {
        return b200004660145;
    }

    public void setB200004660145(BigDecimal b200004660145) {
        this.b200004660145 = b200004660145;
    }

    public BigDecimal getB200004660146() {
        return b200004660146;
    }

    public void setB200004660146(BigDecimal b200004660146) {
        this.b200004660146 = b200004660146;
    }

    public BigDecimal getB200004660147() {
        return b200004660147;
    }

    public void setB200004660147(BigDecimal b200004660147) {
        this.b200004660147 = b200004660147;
    }

    public BigDecimal getB200004660148() {
        return b200004660148;
    }

    public void setB200004660148(BigDecimal b200004660148) {
        this.b200004660148 = b200004660148;
    }

    public BigDecimal getB200004660149() {
        return b200004660149;
    }

    public void setB200004660149(BigDecimal b200004660149) {
        this.b200004660149 = b200004660149;
    }

    public BigDecimal getB200004660150() {
        return b200004660150;
    }

    public void setB200004660150(BigDecimal b200004660150) {
        this.b200004660150 = b200004660150;
    }

    public BigDecimal getB200004660151() {
        return b200004660151;
    }

    public void setB200004660151(BigDecimal b200004660151) {
        this.b200004660151 = b200004660151;
    }

    public BigDecimal getB200004660152() {
        return b200004660152;
    }

    public void setB200004660152(BigDecimal b200004660152) {
        this.b200004660152 = b200004660152;
    }

    public BigDecimal getB200004660153() {
        return b200004660153;
    }

    public void setB200004660153(BigDecimal b200004660153) {
        this.b200004660153 = b200004660153;
    }

    public BigDecimal getB2000046602() {
        return b2000046602;
    }

    public void setB2000046602(BigDecimal b2000046602) {
        this.b2000046602 = b2000046602;
    }

    public BigDecimal getB200004660201() {
        return b200004660201;
    }

    public void setB200004660201(BigDecimal b200004660201) {
        this.b200004660201 = b200004660201;
    }

    public BigDecimal getB200004660202() {
        return b200004660202;
    }

    public void setB200004660202(BigDecimal b200004660202) {
        this.b200004660202 = b200004660202;
    }

    public BigDecimal getB200004660203() {
        return b200004660203;
    }

    public void setB200004660203(BigDecimal b200004660203) {
        this.b200004660203 = b200004660203;
    }

    public BigDecimal getB200004660204() {
        return b200004660204;
    }

    public void setB200004660204(BigDecimal b200004660204) {
        this.b200004660204 = b200004660204;
    }

    public BigDecimal getB200004660205() {
        return b200004660205;
    }

    public void setB200004660205(BigDecimal b200004660205) {
        this.b200004660205 = b200004660205;
    }

    public BigDecimal getB200004660206() {
        return b200004660206;
    }

    public void setB200004660206(BigDecimal b200004660206) {
        this.b200004660206 = b200004660206;
    }

    public BigDecimal getB200004660207() {
        return b200004660207;
    }

    public void setB200004660207(BigDecimal b200004660207) {
        this.b200004660207 = b200004660207;
    }

    public BigDecimal getB200004660208() {
        return b200004660208;
    }

    public void setB200004660208(BigDecimal b200004660208) {
        this.b200004660208 = b200004660208;
    }

    public BigDecimal getB200004660209() {
        return b200004660209;
    }

    public void setB200004660209(BigDecimal b200004660209) {
        this.b200004660209 = b200004660209;
    }

    public BigDecimal getB200004660210() {
        return b200004660210;
    }

    public void setB200004660210(BigDecimal b200004660210) {
        this.b200004660210 = b200004660210;
    }

    public BigDecimal getB200004660211() {
        return b200004660211;
    }

    public void setB200004660211(BigDecimal b200004660211) {
        this.b200004660211 = b200004660211;
    }

    public BigDecimal getB200004660212() {
        return b200004660212;
    }

    public void setB200004660212(BigDecimal b200004660212) {
        this.b200004660212 = b200004660212;
    }

    public BigDecimal getB200004660213() {
        return b200004660213;
    }

    public void setB200004660213(BigDecimal b200004660213) {
        this.b200004660213 = b200004660213;
    }

    public BigDecimal getB200004660214() {
        return b200004660214;
    }

    public void setB200004660214(BigDecimal b200004660214) {
        this.b200004660214 = b200004660214;
    }

    public BigDecimal getB200004660215() {
        return b200004660215;
    }

    public void setB200004660215(BigDecimal b200004660215) {
        this.b200004660215 = b200004660215;
    }

    public BigDecimal getB200004660216() {
        return b200004660216;
    }

    public void setB200004660216(BigDecimal b200004660216) {
        this.b200004660216 = b200004660216;
    }

    public BigDecimal getB200004660217() {
        return b200004660217;
    }

    public void setB200004660217(BigDecimal b200004660217) {
        this.b200004660217 = b200004660217;
    }

    public BigDecimal getB200004660218() {
        return b200004660218;
    }

    public void setB200004660218(BigDecimal b200004660218) {
        this.b200004660218 = b200004660218;
    }

    public BigDecimal getB200004660219() {
        return b200004660219;
    }

    public void setB200004660219(BigDecimal b200004660219) {
        this.b200004660219 = b200004660219;
    }

    public BigDecimal getB200004660220() {
        return b200004660220;
    }

    public void setB200004660220(BigDecimal b200004660220) {
        this.b200004660220 = b200004660220;
    }

    public BigDecimal getB200004660221() {
        return b200004660221;
    }

    public void setB200004660221(BigDecimal b200004660221) {
        this.b200004660221 = b200004660221;
    }

    public BigDecimal getB200004660222() {
        return b200004660222;
    }

    public void setB200004660222(BigDecimal b200004660222) {
        this.b200004660222 = b200004660222;
    }

    public BigDecimal getB200004660223() {
        return b200004660223;
    }

    public void setB200004660223(BigDecimal b200004660223) {
        this.b200004660223 = b200004660223;
    }

    public BigDecimal getB200004660224() {
        return b200004660224;
    }

    public void setB200004660224(BigDecimal b200004660224) {
        this.b200004660224 = b200004660224;
    }

    public BigDecimal getB200004660225() {
        return b200004660225;
    }

    public void setB200004660225(BigDecimal b200004660225) {
        this.b200004660225 = b200004660225;
    }

    public BigDecimal getB200004660226() {
        return b200004660226;
    }

    public void setB200004660226(BigDecimal b200004660226) {
        this.b200004660226 = b200004660226;
    }

    public BigDecimal getB200004660227() {
        return b200004660227;
    }

    public void setB200004660227(BigDecimal b200004660227) {
        this.b200004660227 = b200004660227;
    }

    public BigDecimal getB200004660228() {
        return b200004660228;
    }

    public void setB200004660228(BigDecimal b200004660228) {
        this.b200004660228 = b200004660228;
    }

    public BigDecimal getB200004660229() {
        return b200004660229;
    }

    public void setB200004660229(BigDecimal b200004660229) {
        this.b200004660229 = b200004660229;
    }

    public BigDecimal getB200004660230() {
        return b200004660230;
    }

    public void setB200004660230(BigDecimal b200004660230) {
        this.b200004660230 = b200004660230;
    }

    public BigDecimal getB200004660231() {
        return b200004660231;
    }

    public void setB200004660231(BigDecimal b200004660231) {
        this.b200004660231 = b200004660231;
    }

    public BigDecimal getB200004660232() {
        return b200004660232;
    }

    public void setB200004660232(BigDecimal b200004660232) {
        this.b200004660232 = b200004660232;
    }

    public BigDecimal getB200004660233() {
        return b200004660233;
    }

    public void setB200004660233(BigDecimal b200004660233) {
        this.b200004660233 = b200004660233;
    }

    public BigDecimal getB200004660234() {
        return b200004660234;
    }

    public void setB200004660234(BigDecimal b200004660234) {
        this.b200004660234 = b200004660234;
    }

    public BigDecimal getB200004660235() {
        return b200004660235;
    }

    public void setB200004660235(BigDecimal b200004660235) {
        this.b200004660235 = b200004660235;
    }

    public BigDecimal getB200004660236() {
        return b200004660236;
    }

    public void setB200004660236(BigDecimal b200004660236) {
        this.b200004660236 = b200004660236;
    }

    public BigDecimal getB200004660237() {
        return b200004660237;
    }

    public void setB200004660237(BigDecimal b200004660237) {
        this.b200004660237 = b200004660237;
    }

    public BigDecimal getB200004660238() {
        return b200004660238;
    }

    public void setB200004660238(BigDecimal b200004660238) {
        this.b200004660238 = b200004660238;
    }

    public BigDecimal getB200004660239() {
        return b200004660239;
    }

    public void setB200004660239(BigDecimal b200004660239) {
        this.b200004660239 = b200004660239;
    }

    public BigDecimal getB200004660240() {
        return b200004660240;
    }

    public void setB200004660240(BigDecimal b200004660240) {
        this.b200004660240 = b200004660240;
    }

    public BigDecimal getB200004660241() {
        return b200004660241;
    }

    public void setB200004660241(BigDecimal b200004660241) {
        this.b200004660241 = b200004660241;
    }

    public BigDecimal getB200004660242() {
        return b200004660242;
    }

    public void setB200004660242(BigDecimal b200004660242) {
        this.b200004660242 = b200004660242;
    }

    public BigDecimal getB200004660243() {
        return b200004660243;
    }

    public void setB200004660243(BigDecimal b200004660243) {
        this.b200004660243 = b200004660243;
    }

    public BigDecimal getB200004660244() {
        return b200004660244;
    }

    public void setB200004660244(BigDecimal b200004660244) {
        this.b200004660244 = b200004660244;
    }

    public BigDecimal getB200004660245() {
        return b200004660245;
    }

    public void setB200004660245(BigDecimal b200004660245) {
        this.b200004660245 = b200004660245;
    }

    public BigDecimal getB200004660246() {
        return b200004660246;
    }

    public void setB200004660246(BigDecimal b200004660246) {
        this.b200004660246 = b200004660246;
    }

    public BigDecimal getB200004660247() {
        return b200004660247;
    }

    public void setB200004660247(BigDecimal b200004660247) {
        this.b200004660247 = b200004660247;
    }

    public BigDecimal getB200004660248() {
        return b200004660248;
    }

    public void setB200004660248(BigDecimal b200004660248) {
        this.b200004660248 = b200004660248;
    }

    public BigDecimal getB200004660249() {
        return b200004660249;
    }

    public void setB200004660249(BigDecimal b200004660249) {
        this.b200004660249 = b200004660249;
    }

    public BigDecimal getB200004660250() {
        return b200004660250;
    }

    public void setB200004660250(BigDecimal b200004660250) {
        this.b200004660250 = b200004660250;
    }

    public BigDecimal getB200004660251() {
        return b200004660251;
    }

    public void setB200004660251(BigDecimal b200004660251) {
        this.b200004660251 = b200004660251;
    }

    public BigDecimal getB200004660252() {
        return b200004660252;
    }

    public void setB200004660252(BigDecimal b200004660252) {
        this.b200004660252 = b200004660252;
    }

    public BigDecimal getB200004660253() {
        return b200004660253;
    }

    public void setB200004660253(BigDecimal b200004660253) {
        this.b200004660253 = b200004660253;
    }

    public BigDecimal getB200004660254() {
        return b200004660254;
    }

    public void setB200004660254(BigDecimal b200004660254) {
        this.b200004660254 = b200004660254;
    }

    public BigDecimal getB200004660255() {
        return b200004660255;
    }

    public void setB200004660255(BigDecimal b200004660255) {
        this.b200004660255 = b200004660255;
    }

    public BigDecimal getB200004660256() {
        return b200004660256;
    }

    public void setB200004660256(BigDecimal b200004660256) {
        this.b200004660256 = b200004660256;
    }

    public BigDecimal getB200004660257() {
        return b200004660257;
    }

    public void setB200004660257(BigDecimal b200004660257) {
        this.b200004660257 = b200004660257;
    }

    public BigDecimal getB200004660258() {
        return b200004660258;
    }

    public void setB200004660258(BigDecimal b200004660258) {
        this.b200004660258 = b200004660258;
    }

    public BigDecimal getB200004660259() {
        return b200004660259;
    }

    public void setB200004660259(BigDecimal b200004660259) {
        this.b200004660259 = b200004660259;
    }

    public BigDecimal getB200004660260() {
        return b200004660260;
    }

    public void setB200004660260(BigDecimal b200004660260) {
        this.b200004660260 = b200004660260;
    }

    public BigDecimal getB200004660261() {
        return b200004660261;
    }

    public void setB200004660261(BigDecimal b200004660261) {
        this.b200004660261 = b200004660261;
    }

    public BigDecimal getB2000046603() {
        return b2000046603;
    }

    public void setB2000046603(BigDecimal b2000046603) {
        this.b2000046603 = b2000046603;
    }

    public BigDecimal getB200004660301() {
        return b200004660301;
    }

    public void setB200004660301(BigDecimal b200004660301) {
        this.b200004660301 = b200004660301;
    }

    public BigDecimal getB200004660302() {
        return b200004660302;
    }

    public void setB200004660302(BigDecimal b200004660302) {
        this.b200004660302 = b200004660302;
    }

    public BigDecimal getB200004660303() {
        return b200004660303;
    }

    public void setB200004660303(BigDecimal b200004660303) {
        this.b200004660303 = b200004660303;
    }

    public BigDecimal getB200004660304() {
        return b200004660304;
    }

    public void setB200004660304(BigDecimal b200004660304) {
        this.b200004660304 = b200004660304;
    }

    public BigDecimal getB200004660305() {
        return b200004660305;
    }

    public void setB200004660305(BigDecimal b200004660305) {
        this.b200004660305 = b200004660305;
    }

    public BigDecimal getB20000466030501() {
        return b20000466030501;
    }

    public void setB20000466030501(BigDecimal b20000466030501) {
        this.b20000466030501 = b20000466030501;
    }

    public BigDecimal getB20000466030502() {
        return b20000466030502;
    }

    public void setB20000466030502(BigDecimal b20000466030502) {
        this.b20000466030502 = b20000466030502;
    }

    public BigDecimal getB200004660306() {
        return b200004660306;
    }

    public void setB200004660306(BigDecimal b200004660306) {
        this.b200004660306 = b200004660306;
    }

    public BigDecimal getB200004660307() {
        return b200004660307;
    }

    public void setB200004660307(BigDecimal b200004660307) {
        this.b200004660307 = b200004660307;
    }

    public BigDecimal getB200004660308() {
        return b200004660308;
    }

    public void setB200004660308(BigDecimal b200004660308) {
        this.b200004660308 = b200004660308;
    }

    public BigDecimal getB2000046604() {
        return b2000046604;
    }

    public void setB2000046604(BigDecimal b2000046604) {
        this.b2000046604 = b2000046604;
    }

    public BigDecimal getB2000046701() {
        return b2000046701;
    }

    public void setB2000046701(BigDecimal b2000046701) {
        this.b2000046701 = b2000046701;
    }

    public BigDecimal getB2000046702() {
        return b2000046702;
    }

    public void setB2000046702(BigDecimal b2000046702) {
        this.b2000046702 = b2000046702;
    }

    public BigDecimal getB2000046711() {
        return b2000046711;
    }

    public void setB2000046711(BigDecimal b2000046711) {
        this.b2000046711 = b2000046711;
    }

    public BigDecimal getB200004671101() {
        return b200004671101;
    }

    public void setB200004671101(BigDecimal b200004671101) {
        this.b200004671101 = b200004671101;
    }

    public BigDecimal getB20000467110101() {
        return b20000467110101;
    }

    public void setB20000467110101(BigDecimal b20000467110101) {
        this.b20000467110101 = b20000467110101;
    }

    public BigDecimal getB20000467110102() {
        return b20000467110102;
    }

    public void setB20000467110102(BigDecimal b20000467110102) {
        this.b20000467110102 = b20000467110102;
    }

    public BigDecimal getB20000467110103() {
        return b20000467110103;
    }

    public void setB20000467110103(BigDecimal b20000467110103) {
        this.b20000467110103 = b20000467110103;
    }

    public BigDecimal getB20000467110104() {
        return b20000467110104;
    }

    public void setB20000467110104(BigDecimal b20000467110104) {
        this.b20000467110104 = b20000467110104;
    }

    public BigDecimal getB20000467110105() {
        return b20000467110105;
    }

    public void setB20000467110105(BigDecimal b20000467110105) {
        this.b20000467110105 = b20000467110105;
    }

    public BigDecimal getB20000467110106() {
        return b20000467110106;
    }

    public void setB20000467110106(BigDecimal b20000467110106) {
        this.b20000467110106 = b20000467110106;
    }

    public BigDecimal getB20000467110107() {
        return b20000467110107;
    }

    public void setB20000467110107(BigDecimal b20000467110107) {
        this.b20000467110107 = b20000467110107;
    }

    public BigDecimal getB200004671102() {
        return b200004671102;
    }

    public void setB200004671102(BigDecimal b200004671102) {
        this.b200004671102 = b200004671102;
    }

    public BigDecimal getB20000467110201() {
        return b20000467110201;
    }

    public void setB20000467110201(BigDecimal b20000467110201) {
        this.b20000467110201 = b20000467110201;
    }

    public BigDecimal getB20000467110202() {
        return b20000467110202;
    }

    public void setB20000467110202(BigDecimal b20000467110202) {
        this.b20000467110202 = b20000467110202;
    }

    public BigDecimal getB20000467110203() {
        return b20000467110203;
    }

    public void setB20000467110203(BigDecimal b20000467110203) {
        this.b20000467110203 = b20000467110203;
    }

    public BigDecimal getB20000467110204() {
        return b20000467110204;
    }

    public void setB20000467110204(BigDecimal b20000467110204) {
        this.b20000467110204 = b20000467110204;
    }

    public BigDecimal getB20000467110205() {
        return b20000467110205;
    }

    public void setB20000467110205(BigDecimal b20000467110205) {
        this.b20000467110205 = b20000467110205;
    }

    public BigDecimal getB20000467110206() {
        return b20000467110206;
    }

    public void setB20000467110206(BigDecimal b20000467110206) {
        this.b20000467110206 = b20000467110206;
    }

    public BigDecimal getB20000467110207() {
        return b20000467110207;
    }

    public void setB20000467110207(BigDecimal b20000467110207) {
        this.b20000467110207 = b20000467110207;
    }

    public BigDecimal getB200004671103() {
        return b200004671103;
    }

    public void setB200004671103(BigDecimal b200004671103) {
        this.b200004671103 = b200004671103;
    }

    public BigDecimal getB200004671104() {
        return b200004671104;
    }

    public void setB200004671104(BigDecimal b200004671104) {
        this.b200004671104 = b200004671104;
    }

    public BigDecimal getB20000467110401() {
        return b20000467110401;
    }

    public void setB20000467110401(BigDecimal b20000467110401) {
        this.b20000467110401 = b20000467110401;
    }

    public BigDecimal getB20000467110402() {
        return b20000467110402;
    }

    public void setB20000467110402(BigDecimal b20000467110402) {
        this.b20000467110402 = b20000467110402;
    }

    public BigDecimal getB20000467110403() {
        return b20000467110403;
    }

    public void setB20000467110403(BigDecimal b20000467110403) {
        this.b20000467110403 = b20000467110403;
    }

    public BigDecimal getB20000467110404() {
        return b20000467110404;
    }

    public void setB20000467110404(BigDecimal b20000467110404) {
        this.b20000467110404 = b20000467110404;
    }

    public BigDecimal getB20000467110405() {
        return b20000467110405;
    }

    public void setB20000467110405(BigDecimal b20000467110405) {
        this.b20000467110405 = b20000467110405;
    }

    public BigDecimal getB20000467110406() {
        return b20000467110406;
    }

    public void setB20000467110406(BigDecimal b20000467110406) {
        this.b20000467110406 = b20000467110406;
    }

    public BigDecimal getB20000467110407() {
        return b20000467110407;
    }

    public void setB20000467110407(BigDecimal b20000467110407) {
        this.b20000467110407 = b20000467110407;
    }

    public BigDecimal getB200004671105() {
        return b200004671105;
    }

    public void setB200004671105(BigDecimal b200004671105) {
        this.b200004671105 = b200004671105;
    }

    public BigDecimal getB200004671106() {
        return b200004671106;
    }

    public void setB200004671106(BigDecimal b200004671106) {
        this.b200004671106 = b200004671106;
    }

    public BigDecimal getB200004671107() {
        return b200004671107;
    }

    public void setB200004671107(BigDecimal b200004671107) {
        this.b200004671107 = b200004671107;
    }

    public BigDecimal getB20000467110701() {
        return b20000467110701;
    }

    public void setB20000467110701(BigDecimal b20000467110701) {
        this.b20000467110701 = b20000467110701;
    }

    public BigDecimal getB20000467110702() {
        return b20000467110702;
    }

    public void setB20000467110702(BigDecimal b20000467110702) {
        this.b20000467110702 = b20000467110702;
    }

    public BigDecimal getB20000467110703() {
        return b20000467110703;
    }

    public void setB20000467110703(BigDecimal b20000467110703) {
        this.b20000467110703 = b20000467110703;
    }

    public BigDecimal getB200004671108() {
        return b200004671108;
    }

    public void setB200004671108(BigDecimal b200004671108) {
        this.b200004671108 = b200004671108;
    }

    public BigDecimal getB20000467110801() {
        return b20000467110801;
    }

    public void setB20000467110801(BigDecimal b20000467110801) {
        this.b20000467110801 = b20000467110801;
    }

    public BigDecimal getB20000467110802() {
        return b20000467110802;
    }

    public void setB20000467110802(BigDecimal b20000467110802) {
        this.b20000467110802 = b20000467110802;
    }

    public BigDecimal getB20000467110803() {
        return b20000467110803;
    }

    public void setB20000467110803(BigDecimal b20000467110803) {
        this.b20000467110803 = b20000467110803;
    }

    public BigDecimal getB20000467110804() {
        return b20000467110804;
    }

    public void setB20000467110804(BigDecimal b20000467110804) {
        this.b20000467110804 = b20000467110804;
    }

    public BigDecimal getB20000467110805() {
        return b20000467110805;
    }

    public void setB20000467110805(BigDecimal b20000467110805) {
        this.b20000467110805 = b20000467110805;
    }

    public BigDecimal getB20000467110806() {
        return b20000467110806;
    }

    public void setB20000467110806(BigDecimal b20000467110806) {
        this.b20000467110806 = b20000467110806;
    }

    public BigDecimal getB20000467110807() {
        return b20000467110807;
    }

    public void setB20000467110807(BigDecimal b20000467110807) {
        this.b20000467110807 = b20000467110807;
    }

    public BigDecimal getB20000467110808() {
        return b20000467110808;
    }

    public void setB20000467110808(BigDecimal b20000467110808) {
        this.b20000467110808 = b20000467110808;
    }

    public BigDecimal getB20000467110809() {
        return b20000467110809;
    }

    public void setB20000467110809(BigDecimal b20000467110809) {
        this.b20000467110809 = b20000467110809;
    }

    public BigDecimal getB20000467110810() {
        return b20000467110810;
    }

    public void setB20000467110810(BigDecimal b20000467110810) {
        this.b20000467110810 = b20000467110810;
    }

    public BigDecimal getB20000467110811() {
        return b20000467110811;
    }

    public void setB20000467110811(BigDecimal b20000467110811) {
        this.b20000467110811 = b20000467110811;
    }

    public BigDecimal getB20000467110812() {
        return b20000467110812;
    }

    public void setB20000467110812(BigDecimal b20000467110812) {
        this.b20000467110812 = b20000467110812;
    }

    public BigDecimal getB20000467110813() {
        return b20000467110813;
    }

    public void setB20000467110813(BigDecimal b20000467110813) {
        this.b20000467110813 = b20000467110813;
    }

    public BigDecimal getB2000046801() {
        return b2000046801;
    }

    public void setB2000046801(BigDecimal b2000046801) {
        this.b2000046801 = b2000046801;
    }

    public BigDecimal getB2000046901() {
        return b2000046901;
    }

    public void setB2000046901(BigDecimal b2000046901) {
        this.b2000046901 = b2000046901;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getSubjectMonth() {
        return subjectMonth;
    }

    public void setSubjectMonth(Integer subjectMonth) {
        this.subjectMonth = subjectMonth;
    }

    public BigDecimal getB200004222124() {
        return b200004222124;
    }

    public void setB200004222124(BigDecimal b200004222124) {
        this.b200004222124 = b200004222124;
    }

    public BigDecimal getB200004222125() {
        return b200004222125;
    }

    public void setB200004222125(BigDecimal b200004222125) {
        this.b200004222125 = b200004222125;
    }

    public BigDecimal getB20000467110501() {
        return b20000467110501;
    }

    public void setB20000467110501(BigDecimal b20000467110501) {
        this.b20000467110501 = b20000467110501;
    }

    public BigDecimal getB20000467110502() {
        return b20000467110502;
    }

    public void setB20000467110502(BigDecimal b20000467110502) {
        this.b20000467110502 = b20000467110502;
    }
}
