package com.hm.base.activiti.vo;

import com.google.common.collect.Lists;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class BatchOperateResponse {
    private Integer total;
    private Integer success;
    private Integer failure;
    private List<Map<String, String>> failures = Lists.newArrayList();

    public BatchOperateResponse(Integer total, Integer success, Integer failure, List<Map<String, String>> failures) {
        this.total = total;
        this.success = success;
        this.failure = failure;
        this.failures = failures;
    }

    public BatchOperateResponse(Integer total) {
        this.total = total;
    }

    public <E extends Throwable> void record(E ex, String key, String value) {
        Map<String, String> map = new HashMap<>();
        map.put(key, value);
        map.put("message", ex.getMessage());
        failures.add(map);
    }

    public Integer getSuccess() {
        return total - failures.size();
    }

    public Integer getFailure() {
        return failures.size();
    }
}