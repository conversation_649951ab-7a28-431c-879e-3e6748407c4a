package com.hm.api.consumer.feign.base.dto;

import com.hm.api.common.aop.LogField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @author: curry.wu
 * @create: 2020/10/25 14:26
 **/
@ApiModel(value = "本年期末借方余额")
public class B200007Entity implements Serializable {

    private static final long serialVersionUID = 7439094975123212910L;

    @LogField(tableName = "b200007", value = "ent_id", valueName = "企业ID")
    @ApiModelProperty(value = "企业ID")
    private String entId;

    @LogField(tableName = "b200007", value = "tax_year", valueName = "所属时间")
    @ApiModelProperty(value = "所属时间")
    private String taxYear;

    @LogField(tableName = "b200007", value = "subject_month", valueName = "月份(年度等于12)")
    @ApiModelProperty(value = "月份(年度等于12)")
    private Integer subjectMonth;

    @LogField(tableName = "b200007", value = "b200007_1001", valueName = "库存现金")
    @ApiModelProperty(value = "库存现金")
    private BigDecimal b2000071001;

    @LogField(tableName = "b200007", value = "b200007_1002", valueName = "银行存款")
    @ApiModelProperty(value = "银行存款")
    private BigDecimal b2000071002;

    @LogField(tableName = "b200007", value = "b200007_1003", valueName = "存放中央银行款项")
    @ApiModelProperty(value = "存放中央银行款项")
    private BigDecimal b2000071003;

    @LogField(tableName = "b200007", value = "b200007_1011", valueName = "存放同业")
    @ApiModelProperty(value = "存放同业")
    private BigDecimal b2000071011;

    @LogField(tableName = "b200007", value = "b200007_1012", valueName = "其他货币资金")
    @ApiModelProperty(value = "其他货币资金")
    private BigDecimal b2000071012;

    @LogField(tableName = "b200007", value = "b200007_1021", valueName = "结算备付金")
    @ApiModelProperty(value = "结算备付金")
    private BigDecimal b2000071021;

    @LogField(tableName = "b200007", value = "b200007_1031", valueName = "存出保证金")
    @ApiModelProperty(value = "存出保证金")
    private BigDecimal b2000071031;

    @LogField(tableName = "b200007", value = "b200007_1101", valueName = "交易性金融资产")
    @ApiModelProperty(value = "交易性金融资产")
    private BigDecimal b2000071101;

    @LogField(tableName = "b200007", value = "b200007_1111", valueName = "买入返售金融资产")
    @ApiModelProperty(value = "买入返售金融资产")
    private BigDecimal b2000071111;

    @LogField(tableName = "b200007", value = "b200007_1121", valueName = "应收票据")
    @ApiModelProperty(value = "应收票据")
    private BigDecimal b2000071121;

    @LogField(tableName = "b200007", value = "b200007_1122", valueName = "应收账款")
    @ApiModelProperty(value = "应收账款")
    private BigDecimal b2000071122;

    @LogField(tableName = "b200007", value = "b200007_1123", valueName = "预付账款")
    @ApiModelProperty(value = "预付账款")
    private BigDecimal b2000071123;

    @LogField(tableName = "b200007", value = "b200007_1124", valueName = "合同资产")
    @ApiModelProperty(value = "合同资产")
    private BigDecimal b2000071124;

    @LogField(tableName = "b200007", value = "b200007_1125", valueName = "合同资产减值准备")
    @ApiModelProperty(value = "合同资产减值准备")
    private BigDecimal b2000071125;

    @LogField(tableName = "b200007", value = "b200007_1131", valueName = "应收股利")
    @ApiModelProperty(value = "应收股利")
    private BigDecimal b2000071131;

    @LogField(tableName = "b200007", value = "b200007_1132", valueName = "应收利息")
    @ApiModelProperty(value = "应收利息")
    private BigDecimal b2000071132;

    @LogField(tableName = "b200007", value = "b200007_1201", valueName = "应收代位追偿款")
    @ApiModelProperty(value = "应收代位追偿款")
    private BigDecimal b2000071201;

    @LogField(tableName = "b200007", value = "b200007_1211", valueName = "应收分保账款")
    @ApiModelProperty(value = "应收分保账款")
    private BigDecimal b2000071211;

    @LogField(tableName = "b200007", value = "b200007_1212", valueName = "应收分保合同准备金")
    @ApiModelProperty(value = "应收分保合同准备金")
    private BigDecimal b2000071212;

    @LogField(tableName = "b200007", value = "b200007_1221", valueName = "其他应收款")
    @ApiModelProperty(value = "其他应收款")
    private BigDecimal b2000071221;

    @LogField(tableName = "b200007", value = "b200007_1231", valueName = "坏账准备")
    @ApiModelProperty(value = "坏账准备")
    private BigDecimal b2000071231;

    @LogField(tableName = "b200007", value = "b200007_1301", valueName = "贴现资产")
    @ApiModelProperty(value = "贴现资产")
    private BigDecimal b2000071301;

    @LogField(tableName = "b200007", value = "b200007_1302", valueName = "拆出资金")
    @ApiModelProperty(value = "拆出资金")
    private BigDecimal b2000071302;

    @LogField(tableName = "b200007", value = "b200007_1303", valueName = "贷款")
    @ApiModelProperty(value = "贷款")
    private BigDecimal b2000071303;

    @LogField(tableName = "b200007", value = "b200007_1304", valueName = "贷款损失准备")
    @ApiModelProperty(value = "贷款损失准备")
    private BigDecimal b2000071304;

    @LogField(tableName = "b200007", value = "b200007_1311", valueName = "代理兑付证券")
    @ApiModelProperty(value = "代理兑付证券")
    private BigDecimal b2000071311;

    @LogField(tableName = "b200007", value = "b200007_1321", valueName = "代理业务资产")
    @ApiModelProperty(value = "代理业务资产")
    private BigDecimal b2000071321;

    @LogField(tableName = "b200007", value = "b200007_1401", valueName = "材料采购")
    @ApiModelProperty(value = "材料采购")
    private BigDecimal b2000071401;

    @LogField(tableName = "b200007", value = "b200007_1402", valueName = "在途物资")
    @ApiModelProperty(value = "在途物资")
    private BigDecimal b2000071402;

    @LogField(tableName = "b200007", value = "b200007_1403", valueName = "原材料")
    @ApiModelProperty(value = "原材料")
    private BigDecimal b2000071403;

    @LogField(tableName = "b200007", value = "b200007_1404", valueName = "材料成本差异")
    @ApiModelProperty(value = "材料成本差异")
    private BigDecimal b2000071404;

    @LogField(tableName = "b200007", value = "b200007_1405", valueName = "库存商品")
    @ApiModelProperty(value = "库存商品")
    private BigDecimal b2000071405;

    @LogField(tableName = "b200007", value = "b200007_1406", valueName = "发出商品")
    @ApiModelProperty(value = "发出商品")
    private BigDecimal b2000071406;

    @LogField(tableName = "b200007", value = "b200007_1407", valueName = "商品进销差价")
    @ApiModelProperty(value = "商品进销差价")
    private BigDecimal b2000071407;

    @LogField(tableName = "b200007", value = "b200007_1408", valueName = "委托加工物资")
    @ApiModelProperty(value = "委托加工物资")
    private BigDecimal b2000071408;

    @LogField(tableName = "b200007", value = "b200007_1411", valueName = "周转材料")
    @ApiModelProperty(value = "周转材料")
    private BigDecimal b2000071411;

    @LogField(tableName = "b200007", value = "b200007_1421", valueName = "消耗性生物资产")
    @ApiModelProperty(value = "消耗性生物资产")
    private BigDecimal b2000071421;

    @LogField(tableName = "b200007", value = "b200007_1431", valueName = "贵金属")
    @ApiModelProperty(value = "贵金属")
    private BigDecimal b2000071431;

    @LogField(tableName = "b200007", value = "b200007_1441", valueName = "抵债资产")
    @ApiModelProperty(value = "抵债资产")
    private BigDecimal b2000071441;

    @LogField(tableName = "b200007", value = "b200007_1451", valueName = "损余物资")
    @ApiModelProperty(value = "损余物资")
    private BigDecimal b2000071451;

    @LogField(tableName = "b200007", value = "b200007_1461", valueName = "融资租赁资产")
    @ApiModelProperty(value = "融资租赁资产")
    private BigDecimal b2000071461;

    @LogField(tableName = "b200007", value = "b200007_1471", valueName = "存货跌价准备")
    @ApiModelProperty(value = "存货跌价准备")
    private BigDecimal b2000071471;

    @LogField(tableName = "b200007", value = "b200007_1481", valueName = "持有待售资产")
    @ApiModelProperty(value = "持有待售资产")
    private BigDecimal b2000071481;

    @LogField(tableName = "b200007", value = "b200007_1482", valueName = "持有待售资产减值准备")
    @ApiModelProperty(value = "持有待售资产减值准备")
    private BigDecimal b2000071482;

    @LogField(tableName = "b200007", value = "b200007_1501", valueName = "持有至到期投资")
    @ApiModelProperty(value = "持有至到期投资")
    private BigDecimal b2000071501;

    @LogField(tableName = "b200007", value = "b200007_1502", valueName = "持有至到期投资减值准备")
    @ApiModelProperty(value = "持有至到期投资减值准备")
    private BigDecimal b2000071502;

    @LogField(tableName = "b200007", value = "b200007_1503", valueName = "可供出售金融资产")
    @ApiModelProperty(value = "可供出售金融资产")
    private BigDecimal b2000071503;

    @LogField(tableName = "b200007", value = "b200007_1511", valueName = "长期股权投资")
    @ApiModelProperty(value = "长期股权投资")
    private BigDecimal b2000071511;

    @LogField(tableName = "b200007", value = "b200007_1512", valueName = "长期股权投资减值准备")
    @ApiModelProperty(value = "长期股权投资减值准备")
    private BigDecimal b2000071512;

    @LogField(tableName = "b200007", value = "b200007_1521", valueName = "投资性房地产")
    @ApiModelProperty(value = "投资性房地产")
    private BigDecimal b2000071521;

    @LogField(tableName = "b200007", value = "b200007_1531", valueName = "长期应收款")
    @ApiModelProperty(value = "长期应收款")
    private BigDecimal b2000071531;

    @LogField(tableName = "b200007", value = "b200007_1532", valueName = "未实现融资收益")
    @ApiModelProperty(value = "未实现融资收益")
    private BigDecimal b2000071532;

    @LogField(tableName = "b200007", value = "b200007_1541", valueName = "存出资本保证金")
    @ApiModelProperty(value = "存出资本保证金")
    private BigDecimal b2000071541;

    @LogField(tableName = "b200007", value = "b200007_1601", valueName = "固定资产")
    @ApiModelProperty(value = "固定资产")
    private BigDecimal b2000071601;

    @LogField(tableName = "b200007", value = "b200007_1602", valueName = "累计折旧")
    @ApiModelProperty(value = "累计折旧")
    private BigDecimal b2000071602;

    @LogField(tableName = "b200007", value = "b200007_1603", valueName = "固定资产减值准备")
    @ApiModelProperty(value = "固定资产减值准备")
    private BigDecimal b2000071603;

    @LogField(tableName = "b200007", value = "b200007_1604", valueName = "在建工程")
    @ApiModelProperty(value = "在建工程")
    private BigDecimal b2000071604;

    @LogField(tableName = "b200007", value = "b200007_1605", valueName = "工程物资")
    @ApiModelProperty(value = "工程物资")
    private BigDecimal b2000071605;

    @LogField(tableName = "b200007", value = "b200007_1606", valueName = "固定资产清理")
    @ApiModelProperty(value = "固定资产清理")
    private BigDecimal b2000071606;

    @LogField(tableName = "b200007", value = "b200007_1611", valueName = "未担保余值")
    @ApiModelProperty(value = "未担保余值")
    private BigDecimal b2000071611;

    @LogField(tableName = "b200007", value = "b200007_1621", valueName = "生产性生物资产")
    @ApiModelProperty(value = "生产性生物资产")
    private BigDecimal b2000071621;

    @LogField(tableName = "b200007", value = "b200007_1622", valueName = "生产性生物资产累计折旧")
    @ApiModelProperty(value = "生产性生物资产累计折旧")
    private BigDecimal b2000071622;

    @LogField(tableName = "b200007", value = "b200007_1623", valueName = "公益性生物资产")
    @ApiModelProperty(value = "公益性生物资产")
    private BigDecimal b2000071623;

    @LogField(tableName = "b200007", value = "b200007_1631", valueName = "油气资产")
    @ApiModelProperty(value = "油气资产")
    private BigDecimal b2000071631;

    @LogField(tableName = "b200007", value = "b200007_1632", valueName = "累计折耗")
    @ApiModelProperty(value = "累计折耗")
    private BigDecimal b2000071632;

    @LogField(tableName = "b200007", value = "b200007_1701", valueName = "无形资产")
    @ApiModelProperty(value = "无形资产")
    private BigDecimal b2000071701;

    @LogField(tableName = "b200007", value = "b200007_1702", valueName = "累计摊销")
    @ApiModelProperty(value = "累计摊销")
    private BigDecimal b2000071702;

    @LogField(tableName = "b200007", value = "b200007_1703", valueName = "无形资产减值准备")
    @ApiModelProperty(value = "无形资产减值准备")
    private BigDecimal b2000071703;

    @LogField(tableName = "b200007", value = "b200007_1711", valueName = "商誉")
    @ApiModelProperty(value = "商誉")
    private BigDecimal b2000071711;

    @LogField(tableName = "b200007", value = "b200007_1801", valueName = "长期待摊费用")
    @ApiModelProperty(value = "长期待摊费用")
    private BigDecimal b2000071801;

    @LogField(tableName = "b200007", value = "b200007_1811", valueName = "递延所得税资产")
    @ApiModelProperty(value = "递延所得税资产")
    private BigDecimal b2000071811;

    @LogField(tableName = "b200007", value = "b200007_1821", valueName = "独立账户资产")
    @ApiModelProperty(value = "独立账户资产")
    private BigDecimal b2000071821;

    @LogField(tableName = "b200007", value = "b200007_1901", valueName = "待处理财产损溢")
    @ApiModelProperty(value = "待处理财产损溢")
    private BigDecimal b2000071901;

    @LogField(tableName = "b200007", value = "b200007_2001", valueName = "短期借款")
    @ApiModelProperty(value = "短期借款")
    private BigDecimal b2000072001;

    @LogField(tableName = "b200007", value = "b200007_2002", valueName = "存入保证金")
    @ApiModelProperty(value = "存入保证金")
    private BigDecimal b2000072002;

    @LogField(tableName = "b200007", value = "b200007_2003", valueName = "拆入资金")
    @ApiModelProperty(value = "拆入资金")
    private BigDecimal b2000072003;

    @LogField(tableName = "b200007", value = "b200007_2004", valueName = "向中央银行借款")
    @ApiModelProperty(value = "向中央银行借款")
    private BigDecimal b2000072004;

    @LogField(tableName = "b200007", value = "b200007_2011", valueName = "吸收存款")
    @ApiModelProperty(value = "吸收存款")
    private BigDecimal b2000072011;

    @LogField(tableName = "b200007", value = "b200007_2012", valueName = "同业存放")
    @ApiModelProperty(value = "同业存放")
    private BigDecimal b2000072012;

    @LogField(tableName = "b200007", value = "b200007_2021", valueName = "贴现负债")
    @ApiModelProperty(value = "贴现负债")
    private BigDecimal b2000072021;

    @LogField(tableName = "b200007", value = "b200007_2101", valueName = "交易性金融负债")
    @ApiModelProperty(value = "交易性金融负债")
    private BigDecimal b2000072101;

    @LogField(tableName = "b200007", value = "b200007_2111", valueName = "卖出回购金融资产款")
    @ApiModelProperty(value = "卖出回购金融资产款")
    private BigDecimal b2000072111;

    @LogField(tableName = "b200007", value = "b200007_2201", valueName = "应付票据")
    @ApiModelProperty(value = "应付票据")
    private BigDecimal b2000072201;

    @LogField(tableName = "b200007", value = "b200007_2202", valueName = "应付账款")
    @ApiModelProperty(value = "应付账款")
    private BigDecimal b2000072202;

    @LogField(tableName = "b200007", value = "b200007_2203", valueName = "预收账款")
    @ApiModelProperty(value = "预收账款")
    private BigDecimal b2000072203;

    @LogField(tableName = "b200007", value = "b200007_2204", valueName = "合同负债")
    @ApiModelProperty(value = "合同负债")
    private BigDecimal b2000072204;

    @LogField(tableName = "b200007", value = "b200007_2211", valueName = "应付职工薪酬")
    @ApiModelProperty(value = "应付职工薪酬")
    private BigDecimal b2000072211;

    @LogField(tableName = "b200007", value = "b200007_221101", valueName = "职工工资")
    @ApiModelProperty(value = "职工工资")
    private BigDecimal b200007221101;

    @LogField(tableName = "b200007", value = "b200007_221102", valueName = "职工福利费")
    @ApiModelProperty(value = "职工福利费")
    private BigDecimal b200007221102;

    @LogField(tableName = "b200007", value = "b200007_221103", valueName = "社会保险费")
    @ApiModelProperty(value = "社会保险费")
    private BigDecimal b200007221103;

    @LogField(tableName = "b200007", value = "b200007_221104", valueName = "住房公积金")
    @ApiModelProperty(value = "住房公积金")
    private BigDecimal b200007221104;

    @LogField(tableName = "b200007", value = "b200007_221105", valueName = "工会经费")
    @ApiModelProperty(value = "工会经费")
    private BigDecimal b200007221105;

    @LogField(tableName = "b200007", value = "b200007_221106", valueName = "职工教育经费")
    @ApiModelProperty(value = "职工教育经费")
    private BigDecimal b200007221106;

    @LogField(tableName = "b200007", value = "b200007_221107", valueName = "年终奖")
    @ApiModelProperty(value = "年终奖")
    private BigDecimal b200007221107;

    @LogField(tableName = "b200007", value = "b200007_221108", valueName = "职工培训费用")
    @ApiModelProperty(value = "职工培训费用")
    private BigDecimal b200007221108;

    @LogField(tableName = "b200007", value = "b200007_221109", valueName = "补充养老保险")
    @ApiModelProperty(value = "补充养老保险")
    private BigDecimal b200007221109;

    @LogField(tableName = "b200007", value = "b200007_221110", valueName = "补充医疗保险")
    @ApiModelProperty(value = "补充医疗保险")
    private BigDecimal b200007221110;

    @LogField(tableName = "b200007", value = "b200007_221111", valueName = "股权激励")
    @ApiModelProperty(value = "股权激励")
    private BigDecimal b200007221111;

    @LogField(tableName = "b200007", value = "b200007_221112", valueName = "商业保险费")
    @ApiModelProperty(value = "商业保险费")
    private BigDecimal b200007221112;

    @LogField(tableName = "b200007", value = "b200007_221113", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200007221113;

    @LogField(tableName = "b200007", value = "b200007_2221", valueName = "应交税费")
    @ApiModelProperty(value = "应交税费")
    private BigDecimal b2000072221;

    @LogField(tableName = "b200007", value = "b200007_222101", valueName = "应交增值税")
    @ApiModelProperty(value = "应交增值税")
    private BigDecimal b200007222101;

    @LogField(tableName = "b200007", value = "b200007_22210101", valueName = "进项税额")
    @ApiModelProperty(value = "进项税额")
    private BigDecimal b20000722210101;

    @LogField(tableName = "b200007", value = "b200007_22210102", valueName = "销项税额")
    @ApiModelProperty(value = "销项税额")
    private BigDecimal b20000722210102;

    @LogField(tableName = "b200007", value = "b200007_22210103", valueName = "已交税金")
    @ApiModelProperty(value = "已交税金")
    private BigDecimal b20000722210103;

    @LogField(tableName = "b200007", value = "b200007_22210104", valueName = "出口抵减内销产品应纳税额")
    @ApiModelProperty(value = "出口抵减内销产品应纳税额")
    private BigDecimal b20000722210104;

    @LogField(tableName = "b200007", value = "b200007_22210105", valueName = "转出未交增值税")
    @ApiModelProperty(value = "转出未交增值税")
    private BigDecimal b20000722210105;

    @LogField(tableName = "b200007", value = "b200007_22210106", valueName = "进项税额转出")
    @ApiModelProperty(value = "进项税额转出")
    private BigDecimal b20000722210106;

    @LogField(tableName = "b200007", value = "b200007_22210107", valueName = "减免税款")
    @ApiModelProperty(value = "减免税款")
    private BigDecimal b20000722210107;

    @LogField(tableName = "b200007", value = "b200007_22210108", valueName = "出口退税")
    @ApiModelProperty(value = "出口退税")
    private BigDecimal b20000722210108;

    @LogField(tableName = "b200007", value = "b200007_22210109", valueName = "转出多交增值税")
    @ApiModelProperty(value = "转出多交增值税")
    private BigDecimal b20000722210109;

    @LogField(tableName = "b200007", value = "b200007_22210110", valueName = "销项税额抵减")
    @ApiModelProperty(value = "销项税额抵减")
    private BigDecimal b20000722210110;

    @LogField(tableName = "b200007", value = "b200007_222102", valueName = "未交增值税")
    @ApiModelProperty(value = "未交增值税")
    private BigDecimal b200007222102;

    @LogField(tableName = "b200007", value = "b200007_222103", valueName = "应交营业税")
    @ApiModelProperty(value = "应交营业税")
    private BigDecimal b200007222103;

    @LogField(tableName = "b200007", value = "b200007_222104", valueName = "应交消费税")
    @ApiModelProperty(value = "应交消费税")
    private BigDecimal b200007222104;

    @LogField(tableName = "b200007", value = "b200007_222105", valueName = "应交资源税")
    @ApiModelProperty(value = "应交资源税")
    private BigDecimal b200007222105;

    @LogField(tableName = "b200007", value = "b200007_222106", valueName = "应交所得税")
    @ApiModelProperty(value = "应交所得税")
    private BigDecimal b200007222106;

    @LogField(tableName = "b200007", value = "b200007_222107", valueName = "应交土地增值税")
    @ApiModelProperty(value = "应交土地增值税")
    private BigDecimal b200007222107;

    @LogField(tableName = "b200007", value = "b200007_222108", valueName = "应交城市维护建设税")
    @ApiModelProperty(value = "应交城市维护建设税")
    private BigDecimal b200007222108;

    @LogField(tableName = "b200007", value = "b200007_222109", valueName = "应交房产税")
    @ApiModelProperty(value = "应交房产税")
    private BigDecimal b200007222109;

    @LogField(tableName = "b200007", value = "b200007_222110", valueName = "应交土地使用税")
    @ApiModelProperty(value = "应交土地使用税")
    private BigDecimal b200007222110;

    @LogField(tableName = "b200007", value = "b200007_222111", valueName = "应交车船税")
    @ApiModelProperty(value = "应交车船税")
    private BigDecimal b200007222111;

    @LogField(tableName = "b200007", value = "b200007_222112", valueName = "应交个人所得税")
    @ApiModelProperty(value = "应交个人所得税")
    private BigDecimal b200007222112;

    @LogField(tableName = "b200007", value = "b200007_222113", valueName = "教育费附加")
    @ApiModelProperty(value = "教育费附加")
    private BigDecimal b200007222113;

    @LogField(tableName = "b200007", value = "b200007_222114", valueName = "地方教育费附加")
    @ApiModelProperty(value = "地方教育费附加")
    private BigDecimal b200007222114;

    @LogField(tableName = "b200007", value = "b200007_222115", valueName = "印花税")
    @ApiModelProperty(value = "印花税")
    private BigDecimal b200007222115;

    @LogField(tableName = "b200007", value = "b200007_222116", valueName = "待抵扣进项税额")
    @ApiModelProperty(value = "待抵扣进项税额")
    private BigDecimal b200007222116;

    @LogField(tableName = "b200007", value = "b200007_222117", valueName = "待认证进项税额")
    @ApiModelProperty(value = "待认证进项税额")
    private BigDecimal b200007222117;

    @LogField(tableName = "b200007", value = "b200007_222118", valueName = "预交增值税")
    @ApiModelProperty(value = "预交增值税")
    private BigDecimal b200007222118;

    @LogField(tableName = "b200007", value = "b200007_222119", valueName = "待转销项税额")
    @ApiModelProperty(value = "待转销项税额")
    private BigDecimal b200007222119;

    @LogField(tableName = "b200007", value = "b200007_222120", valueName = "增值税留抵税额")
    @ApiModelProperty(value = "增值税留抵税额")
    private BigDecimal b200007222120;

    @LogField(tableName = "b200007", value = "b200007_222121", valueName = "简易计税")
    @ApiModelProperty(value = "简易计税")
    private BigDecimal b200007222121;

    @LogField(tableName = "b200007", value = "b200007_222122", valueName = "转让金融商品应交增值税")
    @ApiModelProperty(value = "转让金融商品应交增值税")
    private BigDecimal b200007222122;

    @LogField(tableName = "b200007", value = "b200007_222123", valueName = "代扣代缴增值税")
    @ApiModelProperty(value = "代扣代缴增值税")
    private BigDecimal b200007222123;

    @LogField(tableName = "b200007", value = "b200007_2231", valueName = "应付利息")
    @ApiModelProperty(value = "应付利息")
    private BigDecimal b2000072231;

    @LogField(tableName = "b200007", value = "b200007_2232", valueName = "应付股利")
    @ApiModelProperty(value = "应付股利")
    private BigDecimal b2000072232;

    @LogField(tableName = "b200007", value = "b200007_2241", valueName = "其他应付款")
    @ApiModelProperty(value = "其他应付款")
    private BigDecimal b2000072241;

    @LogField(tableName = "b200007", value = "b200007_2251", valueName = "应付保单红利")
    @ApiModelProperty(value = "应付保单红利")
    private BigDecimal b2000072251;

    @LogField(tableName = "b200007", value = "b200007_2261", valueName = "应付分保账款")
    @ApiModelProperty(value = "应付分保账款")
    private BigDecimal b2000072261;

    @LogField(tableName = "b200007", value = "b200007_2311", valueName = "代理买卖证券款")
    @ApiModelProperty(value = "代理买卖证券款")
    private BigDecimal b2000072311;

    @LogField(tableName = "b200007", value = "b200007_2312", valueName = "代理承销证券款")
    @ApiModelProperty(value = "代理承销证券款")
    private BigDecimal b2000072312;

    @LogField(tableName = "b200007", value = "b200007_2313", valueName = "代理兑付证券款")
    @ApiModelProperty(value = "代理兑付证券款")
    private BigDecimal b2000072313;

    @LogField(tableName = "b200007", value = "b200007_2314", valueName = "代理业务负债")
    @ApiModelProperty(value = "代理业务负债")
    private BigDecimal b2000072314;

    @LogField(tableName = "b200007", value = "b200007_2401", valueName = "递延收益")
    @ApiModelProperty(value = "递延收益")
    private BigDecimal b2000072401;

    @LogField(tableName = "b200007", value = "b200007_2245", valueName = "持有待售负债")
    @ApiModelProperty(value = "持有待售负债")
    private BigDecimal b2000072245;

    @LogField(tableName = "b200007", value = "b200007_2501", valueName = "长期借款")
    @ApiModelProperty(value = "长期借款")
    private BigDecimal b2000072501;

    @LogField(tableName = "b200007", value = "b200007_2502", valueName = "应付债券")
    @ApiModelProperty(value = "应付债券")
    private BigDecimal b2000072502;

    @LogField(tableName = "b200007", value = "b200007_2601", valueName = "未到期责任准备金")
    @ApiModelProperty(value = "未到期责任准备金")
    private BigDecimal b2000072601;

    @LogField(tableName = "b200007", value = "b200007_2602", valueName = "保险责任准备金")
    @ApiModelProperty(value = "保险责任准备金")
    private BigDecimal b2000072602;

    @LogField(tableName = "b200007", value = "b200007_2611", valueName = "保户储金")
    @ApiModelProperty(value = "保户储金")
    private BigDecimal b2000072611;

    @LogField(tableName = "b200007", value = "b200007_2621", valueName = "独立账户负债")
    @ApiModelProperty(value = "独立账户负债")
    private BigDecimal b2000072621;

    @LogField(tableName = "b200007", value = "b200007_2701", valueName = "长期应付款")
    @ApiModelProperty(value = "长期应付款")
    private BigDecimal b2000072701;

    @LogField(tableName = "b200007", value = "b200007_2702", valueName = "未确认融资费用")
    @ApiModelProperty(value = "未确认融资费用")
    private BigDecimal b2000072702;

    @LogField(tableName = "b200007", value = "b200007_2711", valueName = "专项应付款")
    @ApiModelProperty(value = "专项应付款")
    private BigDecimal b2000072711;

    @LogField(tableName = "b200007", value = "b200007_2801", valueName = "预计负债")
    @ApiModelProperty(value = "预计负债")
    private BigDecimal b2000072801;

    @LogField(tableName = "b200007", value = "b200007_2901", valueName = "递延所得税负债")
    @ApiModelProperty(value = "递延所得税负债")
    private BigDecimal b2000072901;

    @LogField(tableName = "b200007", value = "b200007_3001", valueName = "清算资金往来")
    @ApiModelProperty(value = "清算资金往来")
    private BigDecimal b2000073001;

    @LogField(tableName = "b200007", value = "b200007_3002", valueName = "货币兑换")
    @ApiModelProperty(value = "货币兑换")
    private BigDecimal b2000073002;

    @LogField(tableName = "b200007", value = "b200007_3101", valueName = "衍生工具")
    @ApiModelProperty(value = "衍生工具")
    private BigDecimal b2000073101;

    @LogField(tableName = "b200007", value = "b200007_3201", valueName = "套期工具")
    @ApiModelProperty(value = "套期工具")
    private BigDecimal b2000073201;

    @LogField(tableName = "b200007", value = "b200007_3202", valueName = "被套期项目")
    @ApiModelProperty(value = "被套期项目")
    private BigDecimal b2000073202;

    @LogField(tableName = "b200007", value = "b200007_4001", valueName = "实收资本")
    @ApiModelProperty(value = "实收资本")
    private BigDecimal b2000074001;

    @LogField(tableName = "b200007", value = "b200007_4002", valueName = "资本公积")
    @ApiModelProperty(value = "资本公积")
    private BigDecimal b2000074002;

    @LogField(tableName = "b200007", value = "b200007_4003", valueName = "其他综合收益")
    @ApiModelProperty(value = "其他综合收益")
    private BigDecimal b2000074003;

    @LogField(tableName = "b200007", value = "b200007_4101", valueName = "盈余公积")
    @ApiModelProperty(value = "盈余公积")
    private BigDecimal b2000074101;

    @LogField(tableName = "b200007", value = "b200007_4102", valueName = "一般风险准备")
    @ApiModelProperty(value = "一般风险准备")
    private BigDecimal b2000074102;

    @LogField(tableName = "b200007", value = "b200007_4103", valueName = "本年利润")
    @ApiModelProperty(value = "本年利润")
    private BigDecimal b2000074103;

    @LogField(tableName = "b200007", value = "b200007_4104", valueName = "利润分配")
    @ApiModelProperty(value = "利润分配")
    private BigDecimal b2000074104;

    @LogField(tableName = "b200007", value = "b200007_4201", valueName = "库存股")
    @ApiModelProperty(value = "库存股")
    private BigDecimal b2000074201;

    @LogField(tableName = "b200007", value = "b200007_4301", valueName = "专项储备")
    @ApiModelProperty(value = "专项储备")
    private BigDecimal b2000074301;

    @LogField(tableName = "b200007", value = "b200007_5001", valueName = "生产成本")
    @ApiModelProperty(value = "生产成本")
    private BigDecimal b2000075001;

    @LogField(tableName = "b200007", value = "b200007_500101", valueName = "直接人工")
    @ApiModelProperty(value = "直接人工")
    private BigDecimal b200007500101;

    @LogField(tableName = "b200007", value = "b200007_500102", valueName = "直接材料")
    @ApiModelProperty(value = "直接材料")
    private BigDecimal b200007500102;

    @LogField(tableName = "b200007", value = "b200007_500103", valueName = "制造费用")
    @ApiModelProperty(value = "制造费用")
    private BigDecimal b200007500103;

    @LogField(tableName = "b200007", value = "b200007_500104", valueName = "职工工资")
    @ApiModelProperty(value = "职工工资")
    private BigDecimal b200007500104;

    @LogField(tableName = "b200007", value = "b200007_500105", valueName = "年终奖")
    @ApiModelProperty(value = "年终奖")
    private BigDecimal b200007500105;

    @LogField(tableName = "b200007", value = "b200007_500106", valueName = "津贴与补贴")
    @ApiModelProperty(value = "津贴与补贴")
    private BigDecimal b200007500106;

    @LogField(tableName = "b200007", value = "b200007_500107", valueName = "股权激励")
    @ApiModelProperty(value = "股权激励")
    private BigDecimal b200007500107;

    @LogField(tableName = "b200007", value = "b200007_500108", valueName = "职工教育经费")
    @ApiModelProperty(value = "职工教育经费")
    private BigDecimal b200007500108;

    @LogField(tableName = "b200007", value = "b200007_500109", valueName = "职工培训费用")
    @ApiModelProperty(value = "职工培训费用")
    private BigDecimal b200007500109;

    @LogField(tableName = "b200007", value = "b200007_500110", valueName = "工会经费")
    @ApiModelProperty(value = "工会经费")
    private BigDecimal b200007500110;

    @LogField(tableName = "b200007", value = "b200007_500111", valueName = "社会保险费")
    @ApiModelProperty(value = "社会保险费")
    private BigDecimal b200007500111;

    @LogField(tableName = "b200007", value = "b200007_500112", valueName = "住房公积金")
    @ApiModelProperty(value = "住房公积金")
    private BigDecimal b200007500112;

    @LogField(tableName = "b200007", value = "b200007_500113", valueName = "职工福利费")
    @ApiModelProperty(value = "职工福利费")
    private BigDecimal b200007500113;

    @LogField(tableName = "b200007", value = "b200007_500114", valueName = "补充养老保险")
    @ApiModelProperty(value = "补充养老保险")
    private BigDecimal b200007500114;

    @LogField(tableName = "b200007", value = "b200007_500115", valueName = "补充医疗保险")
    @ApiModelProperty(value = "补充医疗保险")
    private BigDecimal b200007500115;

    @LogField(tableName = "b200007", value = "b200007_500116", valueName = "其他职工工资")
    @ApiModelProperty(value = "其他职工工资")
    private BigDecimal b200007500116;

    @LogField(tableName = "b200007", value = "b200007_500117", valueName = "商业保险费")
    @ApiModelProperty(value = "商业保险费")
    private BigDecimal b200007500117;

    @LogField(tableName = "b200007", value = "b200007_500118", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200007500118;

    @LogField(tableName = "b200007", value = "b200007_5101", valueName = "制造费用")
    @ApiModelProperty(value = "制造费用")
    private BigDecimal b2000075101;

    @LogField(tableName = "b200007", value = "b200007_510101", valueName = "职工工资")
    @ApiModelProperty(value = "职工工资")
    private BigDecimal b200007510101;

    @LogField(tableName = "b200007", value = "b200007_510102", valueName = "年终奖")
    @ApiModelProperty(value = "年终奖")
    private BigDecimal b200007510102;

    @LogField(tableName = "b200007", value = "b200007_510103", valueName = "津贴与补贴")
    @ApiModelProperty(value = "津贴与补贴")
    private BigDecimal b200007510103;

    @LogField(tableName = "b200007", value = "b200007_510104", valueName = "股权激励")
    @ApiModelProperty(value = "股权激励")
    private BigDecimal b200007510104;

    @LogField(tableName = "b200007", value = "b200007_510105", valueName = "社会保险费")
    @ApiModelProperty(value = "社会保险费")
    private BigDecimal b200007510105;

    @LogField(tableName = "b200007", value = "b200007_510106", valueName = "住房公积金")
    @ApiModelProperty(value = "住房公积金")
    private BigDecimal b200007510106;

    @LogField(tableName = "b200007", value = "b200007_510107", valueName = "补充养老保险")
    @ApiModelProperty(value = "补充养老保险")
    private BigDecimal b200007510107;

    @LogField(tableName = "b200007", value = "b200007_510108", valueName = "补充医疗保险")
    @ApiModelProperty(value = "补充医疗保险")
    private BigDecimal b200007510108;

    @LogField(tableName = "b200007", value = "b200007_510109", valueName = "职工教育经费")
    @ApiModelProperty(value = "职工教育经费")
    private BigDecimal b200007510109;

    @LogField(tableName = "b200007", value = "b200007_510110", valueName = "职工培训费用")
    @ApiModelProperty(value = "职工培训费用")
    private BigDecimal b200007510110;

    @LogField(tableName = "b200007", value = "b200007_510111", valueName = "其他职工工资")
    @ApiModelProperty(value = "其他职工工资")
    private BigDecimal b200007510111;

    @LogField(tableName = "b200007", value = "b200007_510112", valueName = "职工福利费")
    @ApiModelProperty(value = "职工福利费")
    private BigDecimal b200007510112;

    @LogField(tableName = "b200007", value = "b200007_510113", valueName = "工会经费")
    @ApiModelProperty(value = "工会经费")
    private BigDecimal b200007510113;

    @LogField(tableName = "b200007", value = "b200007_510114", valueName = "商业保险费")
    @ApiModelProperty(value = "商业保险费")
    private BigDecimal b200007510114;

    @LogField(tableName = "b200007", value = "b200007_510115", valueName = "折旧费")
    @ApiModelProperty(value = "折旧费")
    private BigDecimal b200007510115;

    @LogField(tableName = "b200007", value = "b200007_510116", valueName = "修理费")
    @ApiModelProperty(value = "修理费")
    private BigDecimal b200007510116;

    @LogField(tableName = "b200007", value = "b200007_510117", valueName = "机物料消耗")
    @ApiModelProperty(value = "机物料消耗")
    private BigDecimal b200007510117;

    @LogField(tableName = "b200007", value = "b200007_510118", valueName = "低值易耗品摊销")
    @ApiModelProperty(value = "低值易耗品摊销")
    private BigDecimal b200007510118;

    @LogField(tableName = "b200007", value = "b200007_510119", valueName = "租赁费")
    @ApiModelProperty(value = "租赁费")
    private BigDecimal b200007510119;

    @LogField(tableName = "b200007", value = "b200007_510120", valueName = "运杂费")
    @ApiModelProperty(value = "运杂费")
    private BigDecimal b200007510120;

    @LogField(tableName = "b200007", value = "b200007_510121", valueName = "保险费")
    @ApiModelProperty(value = "保险费")
    private BigDecimal b200007510121;

    @LogField(tableName = "b200007", value = "b200007_510122", valueName = "差旅费")
    @ApiModelProperty(value = "差旅费")
    private BigDecimal b200007510122;

    @LogField(tableName = "b200007", value = "b200007_510123", valueName = "水电费")
    @ApiModelProperty(value = "水电费")
    private BigDecimal b200007510123;

    @LogField(tableName = "b200007", value = "b200007_510124", valueName = "外部加工费")
    @ApiModelProperty(value = "外部加工费")
    private BigDecimal b200007510124;

    @LogField(tableName = "b200007", value = "b200007_510125", valueName = "厂房租金")
    @ApiModelProperty(value = "厂房租金")
    private BigDecimal b200007510125;

    @LogField(tableName = "b200007", value = "b200007_510126", valueName = "装卸费")
    @ApiModelProperty(value = "装卸费")
    private BigDecimal b200007510126;

    @LogField(tableName = "b200007", value = "b200007_510127", valueName = "设计制图费")
    @ApiModelProperty(value = "设计制图费")
    private BigDecimal b200007510127;

    @LogField(tableName = "b200007", value = "b200007_510128", valueName = "劳动保护费")
    @ApiModelProperty(value = "劳动保护费")
    private BigDecimal b200007510128;

    @LogField(tableName = "b200007", value = "b200007_510129", valueName = "无形资产摊销费")
    @ApiModelProperty(value = "无形资产摊销费")
    private BigDecimal b200007510129;

    @LogField(tableName = "b200007", value = "b200007_510130", valueName = "长期待摊费用摊销费")
    @ApiModelProperty(value = "长期待摊费用摊销费")
    private BigDecimal b200007510130;

    @LogField(tableName = "b200007", value = "b200007_510131", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200007510131;

    @LogField(tableName = "b200007", value = "b200007_5201", valueName = "劳务成本")
    @ApiModelProperty(value = "劳务成本")
    private BigDecimal b2000075201;

    @LogField(tableName = "b200007", value = "b200007_520101", valueName = "职工工资")
    @ApiModelProperty(value = "职工工资")
    private BigDecimal b200007520101;

    @LogField(tableName = "b200007", value = "b200007_520102", valueName = "年终奖")
    @ApiModelProperty(value = "年终奖")
    private BigDecimal b200007520102;

    @LogField(tableName = "b200007", value = "b200007_520103", valueName = "津贴与补贴")
    @ApiModelProperty(value = "津贴与补贴")
    private BigDecimal b200007520103;

    @LogField(tableName = "b200007", value = "b200007_520104", valueName = "股权激励")
    @ApiModelProperty(value = "股权激励")
    private BigDecimal b200007520104;

    @LogField(tableName = "b200007", value = "b200007_520105", valueName = "职工教育经费")
    @ApiModelProperty(value = "职工教育经费")
    private BigDecimal b200007520105;

    @LogField(tableName = "b200007", value = "b200007_520106", valueName = "职工培训费用")
    @ApiModelProperty(value = "职工培训费用")
    private BigDecimal b200007520106;

    @LogField(tableName = "b200007", value = "b200007_520107", valueName = "工会经费")
    @ApiModelProperty(value = "工会经费")
    private BigDecimal b200007520107;

    @LogField(tableName = "b200007", value = "b200007_520108", valueName = "社会保险费")
    @ApiModelProperty(value = "社会保险费")
    private BigDecimal b200007520108;

    @LogField(tableName = "b200007", value = "b200007_520109", valueName = "住房公积金")
    @ApiModelProperty(value = "住房公积金")
    private BigDecimal b200007520109;

    @LogField(tableName = "b200007", value = "b200007_520110", valueName = "职工福利费")
    @ApiModelProperty(value = "职工福利费")
    private BigDecimal b200007520110;

    @LogField(tableName = "b200007", value = "b200007_520111", valueName = "补充养老保险")
    @ApiModelProperty(value = "补充养老保险")
    private BigDecimal b200007520111;

    @LogField(tableName = "b200007", value = "b200007_520112", valueName = "补充医疗保险")
    @ApiModelProperty(value = "补充医疗保险")
    private BigDecimal b200007520112;

    @LogField(tableName = "b200007", value = "b200007_520113", valueName = "其他职工工资")
    @ApiModelProperty(value = "其他职工工资")
    private BigDecimal b200007520113;

    @LogField(tableName = "b200007", value = "b200007_520114", valueName = "商业保险费")
    @ApiModelProperty(value = "商业保险费")
    private BigDecimal b200007520114;

    @LogField(tableName = "b200007", value = "b200007_520115", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200007520115;

    @LogField(tableName = "b200007", value = "b200007_5301", valueName = "研发支出")
    @ApiModelProperty(value = "研发支出")
    private BigDecimal b2000075301;

    @LogField(tableName = "b200007", value = "b200007_530101", valueName = "资本化支出")
    @ApiModelProperty(value = "资本化支出")
    private BigDecimal b200007530101;

    @LogField(tableName = "b200007", value = "b200007_53010101", valueName = "人工成本")
    @ApiModelProperty(value = "人工成本")
    private BigDecimal b20000753010101;

    @LogField(tableName = "b200007", value = "b200007_5301010101", valueName = "职工工资")
    @ApiModelProperty(value = "职工工资")
    private BigDecimal b2000075301010101;

    @LogField(tableName = "b200007", value = "b200007_5301010102", valueName = "年终奖")
    @ApiModelProperty(value = "年终奖")
    private BigDecimal b2000075301010102;

    @LogField(tableName = "b200007", value = "b200007_5301010103", valueName = "津贴与补贴")
    @ApiModelProperty(value = "津贴与补贴")
    private BigDecimal b2000075301010103;

    @LogField(tableName = "b200007", value = "b200007_5301010104", valueName = "股权激励")
    @ApiModelProperty(value = "股权激励")
    private BigDecimal b2000075301010104;

    @LogField(tableName = "b200007", value = "b200007_5301010105", valueName = "职工教育经费")
    @ApiModelProperty(value = "职工教育经费")
    private BigDecimal b2000075301010105;

    @LogField(tableName = "b200007", value = "b200007_5301010106", valueName = "工会经费")
    @ApiModelProperty(value = "工会经费")
    private BigDecimal b2000075301010106;

    @LogField(tableName = "b200007", value = "b200007_5301010107", valueName = "社会保险费")
    @ApiModelProperty(value = "社会保险费")
    private BigDecimal b2000075301010107;

    @LogField(tableName = "b200007", value = "b200007_5301010108", valueName = "商业保险费")
    @ApiModelProperty(value = "商业保险费")
    private BigDecimal b2000075301010108;

    @LogField(tableName = "b200007", value = "b200007_5301010109", valueName = "住房公积金")
    @ApiModelProperty(value = "住房公积金")
    private BigDecimal b2000075301010109;

    @LogField(tableName = "b200007", value = "b200007_5301010110", valueName = "职工福利费")
    @ApiModelProperty(value = "职工福利费")
    private BigDecimal b2000075301010110;

    @LogField(tableName = "b200007", value = "b200007_5301010111", valueName = "补充养老保险")
    @ApiModelProperty(value = "补充养老保险")
    private BigDecimal b2000075301010111;

    @LogField(tableName = "b200007", value = "b200007_5301010112", valueName = "补充医疗保险")
    @ApiModelProperty(value = "补充医疗保险")
    private BigDecimal b2000075301010112;

    @LogField(tableName = "b200007", value = "b200007_5301010113", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000075301010113;

    @LogField(tableName = "b200007", value = "b200007_53010102", valueName = "劳务费")
    @ApiModelProperty(value = "劳务费")
    private BigDecimal b20000753010102;

    @LogField(tableName = "b200007", value = "b200007_53010103", valueName = "直接投入")
    @ApiModelProperty(value = "直接投入")
    private BigDecimal b20000753010103;

    @LogField(tableName = "b200007", value = "b200007_5301010301", valueName = "材料费用")
    @ApiModelProperty(value = "材料费用")
    private BigDecimal b2000075301010301;

    @LogField(tableName = "b200007", value = "b200007_5301010302", valueName = "燃料费用")
    @ApiModelProperty(value = "燃料费用")
    private BigDecimal b2000075301010302;

    @LogField(tableName = "b200007", value = "b200007_5301010303", valueName = "动力费用")
    @ApiModelProperty(value = "动力费用")
    private BigDecimal b2000075301010303;

    @LogField(tableName = "b200007", value = "b200007_5301010304", valueName = "试用品开发及制造费")
    @ApiModelProperty(value = "试用品开发及制造费")
    private BigDecimal b2000075301010304;

    @LogField(tableName = "b200007", value = "b200007_5301010305", valueName = "样品测试购置费")
    @ApiModelProperty(value = "样品测试购置费")
    private BigDecimal b2000075301010305;

    @LogField(tableName = "b200007", value = "b200007_5301010306", valueName = "试制品检验费")
    @ApiModelProperty(value = "试制品检验费")
    private BigDecimal b2000075301010306;

    @LogField(tableName = "b200007", value = "b200007_5301010307", valueName = "运行维护维修费")
    @ApiModelProperty(value = "运行维护维修费")
    private BigDecimal b2000075301010307;

    @LogField(tableName = "b200007", value = "b200007_5301010308", valueName = "运行调整费")
    @ApiModelProperty(value = "运行调整费")
    private BigDecimal b2000075301010308;

    @LogField(tableName = "b200007", value = "b200007_5301010309", valueName = "运行检测费")
    @ApiModelProperty(value = "运行检测费")
    private BigDecimal b2000075301010309;

    @LogField(tableName = "b200007", value = "b200007_5301010310", valueName = "仪器租赁费")
    @ApiModelProperty(value = "仪器租赁费")
    private BigDecimal b2000075301010310;

    @LogField(tableName = "b200007", value = "b200007_5301010311", valueName = "设备租赁费")
    @ApiModelProperty(value = "设备租赁费")
    private BigDecimal b2000075301010311;

    @LogField(tableName = "b200007", value = "b200007_5301010312", valueName = "房屋租赁费")
    @ApiModelProperty(value = "房屋租赁费")
    private BigDecimal b2000075301010312;

    @LogField(tableName = "b200007", value = "b200007_5301010313", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000075301010313;

    @LogField(tableName = "b200007", value = "b200007_53010104", valueName = "低值易耗品摊销")
    @ApiModelProperty(value = "低值易耗品摊销")
    private BigDecimal b20000753010104;

    @LogField(tableName = "b200007", value = "b200007_5301010401", valueName = "直接消耗")
    @ApiModelProperty(value = "直接消耗")
    private BigDecimal b2000075301010401;

    @LogField(tableName = "b200007", value = "b200007_5301010402", valueName = "其他消耗")
    @ApiModelProperty(value = "其他消耗")
    private BigDecimal b2000075301010402;

    @LogField(tableName = "b200007", value = "b200007_53010105", valueName = "物料消耗")
    @ApiModelProperty(value = "物料消耗")
    private BigDecimal b20000753010105;

    @LogField(tableName = "b200007", value = "b200007_5301010501", valueName = "直接消耗")
    @ApiModelProperty(value = "直接消耗")
    private BigDecimal b2000075301010501;

    @LogField(tableName = "b200007", value = "b200007_5301010502", valueName = "其他消耗")
    @ApiModelProperty(value = "其他消耗")
    private BigDecimal b2000075301010502;

    @LogField(tableName = "b200007", value = "b200007_53010106", valueName = "折旧费用")
    @ApiModelProperty(value = "折旧费用")
    private BigDecimal b20000753010106;

    @LogField(tableName = "b200007", value = "b200007_5301010601", valueName = "仪器折旧费")
    @ApiModelProperty(value = "仪器折旧费")
    private BigDecimal b2000075301010601;

    @LogField(tableName = "b200007", value = "b200007_5301010602", valueName = "设备折旧费")
    @ApiModelProperty(value = "设备折旧费")
    private BigDecimal b2000075301010602;

    @LogField(tableName = "b200007", value = "b200007_5301010603", valueName = "房屋折旧费")
    @ApiModelProperty(value = "房屋折旧费")
    private BigDecimal b2000075301010603;

    @LogField(tableName = "b200007", value = "b200007_5301010604", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000075301010604;

    @LogField(tableName = "b200007", value = "b200007_53010107", valueName = "长期待摊费用")
    @ApiModelProperty(value = "长期待摊费用")
    private BigDecimal b20000753010107;

    @LogField(tableName = "b200007", value = "b200007_5301010701", valueName = "设施改建及改装费")
    @ApiModelProperty(value = "设施改建及改装费")
    private BigDecimal b2000075301010701;

    @LogField(tableName = "b200007", value = "b200007_5301010702", valueName = "设施装修及修理费")
    @ApiModelProperty(value = "设施装修及修理费")
    private BigDecimal b2000075301010702;

    @LogField(tableName = "b200007", value = "b200007_5301010703", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000075301010703;

    @LogField(tableName = "b200007", value = "b200007_53010108", valueName = "无形资产摊销")
    @ApiModelProperty(value = "无形资产摊销")
    private BigDecimal b20000753010108;

    @LogField(tableName = "b200007", value = "b200007_5301010801", valueName = "软件")
    @ApiModelProperty(value = "软件")
    private BigDecimal b2000075301010801;

    @LogField(tableName = "b200007", value = "b200007_5301010802", valueName = "专利权")
    @ApiModelProperty(value = "专利权")
    private BigDecimal b2000075301010802;

    @LogField(tableName = "b200007", value = "b200007_5301010803", valueName = "商标")
    @ApiModelProperty(value = "商标")
    private BigDecimal b2000075301010803;

    @LogField(tableName = "b200007", value = "b200007_5301010804", valueName = "非专利技术")
    @ApiModelProperty(value = "非专利技术")
    private BigDecimal b2000075301010804;

    @LogField(tableName = "b200007", value = "b200007_5301010805", valueName = "知识产权")
    @ApiModelProperty(value = "知识产权")
    private BigDecimal b2000075301010805;

    @LogField(tableName = "b200007", value = "b200007_5301010806", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000075301010806;

    @LogField(tableName = "b200007", value = "b200007_53010109", valueName = "设计费用")
    @ApiModelProperty(value = "设计费用")
    private BigDecimal b20000753010109;

    @LogField(tableName = "b200007", value = "b200007_5301010901", valueName = "新产品设计费")
    @ApiModelProperty(value = "新产品设计费")
    private BigDecimal b2000075301010901;

    @LogField(tableName = "b200007", value = "b200007_5301010902", valueName = "新工艺规程制定费")
    @ApiModelProperty(value = "新工艺规程制定费")
    private BigDecimal b2000075301010902;

    @LogField(tableName = "b200007", value = "b200007_5301010903", valueName = "新产品与新工艺工序设计费")
    @ApiModelProperty(value = "新产品与新工艺工序设计费")
    private BigDecimal b2000075301010903;

    @LogField(tableName = "b200007", value = "b200007_5301010904", valueName = "新产品与新工艺技术规范费")
    @ApiModelProperty(value = "新产品与新工艺技术规范费")
    private BigDecimal b2000075301010904;

    @LogField(tableName = "b200007", value = "b200007_5301010905", valueName = "新产品与新工艺操作设计费")
    @ApiModelProperty(value = "新产品与新工艺操作设计费")
    private BigDecimal b2000075301010905;

    @LogField(tableName = "b200007", value = "b200007_5301010906", valueName = "创意设计活动费")
    @ApiModelProperty(value = "创意设计活动费")
    private BigDecimal b2000075301010906;

    @LogField(tableName = "b200007", value = "b200007_5301010907", valueName = "其他设计费用")
    @ApiModelProperty(value = "其他设计费用")
    private BigDecimal b2000075301010907;

    @LogField(tableName = "b200007", value = "b200007_53010110", valueName = "装备调试费")
    @ApiModelProperty(value = "装备调试费")
    private BigDecimal b20000753010110;

    @LogField(tableName = "b200007", value = "b200007_53010111", valueName = "试验费")
    @ApiModelProperty(value = "试验费")
    private BigDecimal b20000753010111;

    @LogField(tableName = "b200007", value = "b200007_5301011101", valueName = "新药临床试验费")
    @ApiModelProperty(value = "新药临床试验费")
    private BigDecimal b2000075301011101;

    @LogField(tableName = "b200007", value = "b200007_5301011102", valueName = "勘探开发技术现场试验费")
    @ApiModelProperty(value = "勘探开发技术现场试验费")
    private BigDecimal b2000075301011102;

    @LogField(tableName = "b200007", value = "b200007_5301011103", valueName = "田间试验费")
    @ApiModelProperty(value = "田间试验费")
    private BigDecimal b2000075301011103;

    @LogField(tableName = "b200007", value = "b200007_5301011104", valueName = "其他试验费用")
    @ApiModelProperty(value = "其他试验费用")
    private BigDecimal b2000075301011104;

    @LogField(tableName = "b200007", value = "b200007_53010112", valueName = "图书资料费")
    @ApiModelProperty(value = "图书资料费")
    private BigDecimal b20000753010112;

    @LogField(tableName = "b200007", value = "b200007_53010113", valueName = "资料翻译费")
    @ApiModelProperty(value = "资料翻译费")
    private BigDecimal b20000753010113;

    @LogField(tableName = "b200007", value = "b200007_53010114", valueName = "专家咨询费")
    @ApiModelProperty(value = "专家咨询费")
    private BigDecimal b20000753010114;

    @LogField(tableName = "b200007", value = "b200007_53010115", valueName = "高新科技研发保险费")
    @ApiModelProperty(value = "高新科技研发保险费")
    private BigDecimal b20000753010115;

    @LogField(tableName = "b200007", value = "b200007_53010116", valueName = "成果费用")
    @ApiModelProperty(value = "成果费用")
    private BigDecimal b20000753010116;

    @LogField(tableName = "b200007", value = "b200007_5301011601", valueName = "检索费")
    @ApiModelProperty(value = "检索费")
    private BigDecimal b2000075301011601;

    @LogField(tableName = "b200007", value = "b200007_5301011602", valueName = "分析费")
    @ApiModelProperty(value = "分析费")
    private BigDecimal b2000075301011602;

    @LogField(tableName = "b200007", value = "b200007_5301011603", valueName = "评议费")
    @ApiModelProperty(value = "评议费")
    private BigDecimal b2000075301011603;

    @LogField(tableName = "b200007", value = "b200007_5301011604", valueName = "论证费")
    @ApiModelProperty(value = "论证费")
    private BigDecimal b2000075301011604;

    @LogField(tableName = "b200007", value = "b200007_5301011605", valueName = "鉴定费")
    @ApiModelProperty(value = "鉴定费")
    private BigDecimal b2000075301011605;

    @LogField(tableName = "b200007", value = "b200007_5301011606", valueName = "评审费")
    @ApiModelProperty(value = "评审费")
    private BigDecimal b2000075301011606;

    @LogField(tableName = "b200007", value = "b200007_5301011607", valueName = "评估费")
    @ApiModelProperty(value = "评估费")
    private BigDecimal b2000075301011607;

    @LogField(tableName = "b200007", value = "b200007_5301011608", valueName = "验收费")
    @ApiModelProperty(value = "验收费")
    private BigDecimal b2000075301011608;

    @LogField(tableName = "b200007", value = "b200007_5301011609", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000075301011609;

    @LogField(tableName = "b200007", value = "b200007_53010117", valueName = "知识产权费用")
    @ApiModelProperty(value = "知识产权费用")
    private BigDecimal b20000753010117;

    @LogField(tableName = "b200007", value = "b200007_5301011701", valueName = "申请费")
    @ApiModelProperty(value = "申请费")
    private BigDecimal b2000075301011701;

    @LogField(tableName = "b200007", value = "b200007_5301011702", valueName = "注册费")
    @ApiModelProperty(value = "注册费")
    private BigDecimal b2000075301011702;

    @LogField(tableName = "b200007", value = "b200007_5301011703", valueName = "代理费")
    @ApiModelProperty(value = "代理费")
    private BigDecimal b2000075301011703;

    @LogField(tableName = "b200007", value = "b200007_5301011704", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000075301011704;

    @LogField(tableName = "b200007", value = "b200007_53010118", valueName = "差旅费")
    @ApiModelProperty(value = "差旅费")
    private BigDecimal b20000753010118;

    @LogField(tableName = "b200007", value = "b200007_53010119", valueName = "会议费")
    @ApiModelProperty(value = "会议费")
    private BigDecimal b20000753010119;

    @LogField(tableName = "b200007", value = "b200007_53010120", valueName = "通讯费")
    @ApiModelProperty(value = "通讯费")
    private BigDecimal b20000753010120;

    @LogField(tableName = "b200007", value = "b200007_53010121", valueName = "办公费")
    @ApiModelProperty(value = "办公费")
    private BigDecimal b20000753010121;

    @LogField(tableName = "b200007", value = "b200007_53010122", valueName = "外事费")
    @ApiModelProperty(value = "外事费")
    private BigDecimal b20000753010122;

    @LogField(tableName = "b200007", value = "b200007_53010123", valueName = "研发人员培训费")
    @ApiModelProperty(value = "研发人员培训费")
    private BigDecimal b20000753010123;

    @LogField(tableName = "b200007", value = "b200007_53010124", valueName = "研发人员培养费")
    @ApiModelProperty(value = "研发人员培养费")
    private BigDecimal b20000753010124;

    @LogField(tableName = "b200007", value = "b200007_53010125", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b20000753010125;

    @LogField(tableName = "b200007", value = "b200007_53010126", valueName = "外包费用")
    @ApiModelProperty(value = "外包费用")
    private BigDecimal b20000753010126;

    @LogField(tableName = "b200007", value = "b200007_5301012601", valueName = "委托研发（境内进行）")
    @ApiModelProperty(value = "委托研发（境内进行）")
    private BigDecimal b2000075301012601;

    @LogField(tableName = "b200007", value = "b200007_5301012602", valueName = "委托研发（境外机构）")
    @ApiModelProperty(value = "委托研发（境外机构）")
    private BigDecimal b2000075301012602;

    @LogField(tableName = "b200007", value = "b200007_5301012603", valueName = "委托研发（境外个人）")
    @ApiModelProperty(value = "委托研发（境外个人）")
    private BigDecimal b2000075301012603;

    @LogField(tableName = "b200007", value = "b200007_5301012604", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000075301012604;

    @LogField(tableName = "b200007", value = "b200007_530102", valueName = "费用化支出")
    @ApiModelProperty(value = "费用化支出")
    private BigDecimal b200007530102;

    @LogField(tableName = "b200007", value = "b200007_53010201", valueName = "人工成本")
    @ApiModelProperty(value = "人工成本")
    private BigDecimal b20000753010201;

    @LogField(tableName = "b200007", value = "b200007_5301020101", valueName = "职工工资")
    @ApiModelProperty(value = "职工工资")
    private BigDecimal b2000075301020101;

    @LogField(tableName = "b200007", value = "b200007_5301020102", valueName = "年终奖")
    @ApiModelProperty(value = "年终奖")
    private BigDecimal b2000075301020102;

    @LogField(tableName = "b200007", value = "b200007_5301020103", valueName = "津贴与补贴")
    @ApiModelProperty(value = "津贴与补贴")
    private BigDecimal b2000075301020103;

    @LogField(tableName = "b200007", value = "b200007_5301020104", valueName = "股权激励")
    @ApiModelProperty(value = "股权激励")
    private BigDecimal b2000075301020104;

    @LogField(tableName = "b200007", value = "b200007_5301020105", valueName = "职工教育经费")
    @ApiModelProperty(value = "职工教育经费")
    private BigDecimal b2000075301020105;

    @LogField(tableName = "b200007", value = "b200007_5301020106", valueName = "工会经费")
    @ApiModelProperty(value = "工会经费")
    private BigDecimal b2000075301020106;

    @LogField(tableName = "b200007", value = "b200007_5301020107", valueName = "社会保险费")
    @ApiModelProperty(value = "社会保险费")
    private BigDecimal b2000075301020107;

    @LogField(tableName = "b200007", value = "b200007_5301020108", valueName = "商业保险费")
    @ApiModelProperty(value = "商业保险费")
    private BigDecimal b2000075301020108;

    @LogField(tableName = "b200007", value = "b200007_5301020109", valueName = "住房公积金")
    @ApiModelProperty(value = "住房公积金")
    private BigDecimal b2000075301020109;

    @LogField(tableName = "b200007", value = "b200007_5301020110", valueName = "职工福利费")
    @ApiModelProperty(value = "职工福利费")
    private BigDecimal b2000075301020110;

    @LogField(tableName = "b200007", value = "b200007_5301020111", valueName = "补充养老保险")
    @ApiModelProperty(value = "补充养老保险")
    private BigDecimal b2000075301020111;

    @LogField(tableName = "b200007", value = "b200007_5301020112", valueName = "补充养老保险")
    @ApiModelProperty(value = "补充养老保险")
    private BigDecimal b2000075301020112;

    @LogField(tableName = "b200007", value = "b200007_5301020113", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000075301020113;

    @LogField(tableName = "b200007", value = "b200007_53010202", valueName = "劳务费")
    @ApiModelProperty(value = "劳务费")
    private BigDecimal b20000753010202;

    @LogField(tableName = "b200007", value = "b200007_53010203", valueName = "直接投入")
    @ApiModelProperty(value = "直接投入")
    private BigDecimal b20000753010203;

    @LogField(tableName = "b200007", value = "b200007_5301020301", valueName = "材料费用")
    @ApiModelProperty(value = "材料费用")
    private BigDecimal b2000075301020301;

    @LogField(tableName = "b200007", value = "b200007_5301020302", valueName = "燃料费用")
    @ApiModelProperty(value = "燃料费用")
    private BigDecimal b2000075301020302;

    @LogField(tableName = "b200007", value = "b200007_5301020303", valueName = "动力费用")
    @ApiModelProperty(value = "动力费用")
    private BigDecimal b2000075301020303;

    @LogField(tableName = "b200007", value = "b200007_5301020304", valueName = "试用品开发及制造费")
    @ApiModelProperty(value = "试用品开发及制造费")
    private BigDecimal b2000075301020304;

    @LogField(tableName = "b200007", value = "b200007_5301020305", valueName = "样品测试购置费")
    @ApiModelProperty(value = "样品测试购置费")
    private BigDecimal b2000075301020305;

    @LogField(tableName = "b200007", value = "b200007_5301020306", valueName = "试制品检验费")
    @ApiModelProperty(value = "试制品检验费")
    private BigDecimal b2000075301020306;

    @LogField(tableName = "b200007", value = "b200007_5301020307", valueName = "运行维护维修费")
    @ApiModelProperty(value = "运行维护维修费")
    private BigDecimal b2000075301020307;

    @LogField(tableName = "b200007", value = "b200007_5301020308", valueName = "运行调整费")
    @ApiModelProperty(value = "运行调整费")
    private BigDecimal b2000075301020308;

    @LogField(tableName = "b200007", value = "b200007_5301020309", valueName = "运行检测费")
    @ApiModelProperty(value = "运行检测费")
    private BigDecimal b2000075301020309;

    @LogField(tableName = "b200007", value = "b200007_5301020310", valueName = "仪器租赁费")
    @ApiModelProperty(value = "仪器租赁费")
    private BigDecimal b2000075301020310;

    @LogField(tableName = "b200007", value = "b200007_5301020311", valueName = "设备租赁费")
    @ApiModelProperty(value = "设备租赁费")
    private BigDecimal b2000075301020311;

    @LogField(tableName = "b200007", value = "b200007_5301020312", valueName = "房屋租赁费")
    @ApiModelProperty(value = "房屋租赁费")
    private BigDecimal b2000075301020312;

    @LogField(tableName = "b200007", value = "b200007_5301020313", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000075301020313;

    @LogField(tableName = "b200007", value = "b200007_53010204", valueName = "低值易耗品摊销")
    @ApiModelProperty(value = "低值易耗品摊销")
    private BigDecimal b20000753010204;

    @LogField(tableName = "b200007", value = "b200007_5301020401", valueName = "直接消耗")
    @ApiModelProperty(value = "直接消耗")
    private BigDecimal b2000075301020401;

    @LogField(tableName = "b200007", value = "b200007_5301020402", valueName = "其他消耗")
    @ApiModelProperty(value = "其他消耗")
    private BigDecimal b2000075301020402;

    @LogField(tableName = "b200007", value = "b200007_53010205", valueName = "物料消耗")
    @ApiModelProperty(value = "物料消耗")
    private BigDecimal b20000753010205;

    @LogField(tableName = "b200007", value = "b200007_5301020501", valueName = "直接消耗")
    @ApiModelProperty(value = "直接消耗")
    private BigDecimal b2000075301020501;

    @LogField(tableName = "b200007", value = "b200007_5301020502", valueName = "其他消耗")
    @ApiModelProperty(value = "其他消耗")
    private BigDecimal b2000075301020502;

    @LogField(tableName = "b200007", value = "b200007_53010206", valueName = "折旧费用")
    @ApiModelProperty(value = "折旧费用")
    private BigDecimal b20000753010206;

    @LogField(tableName = "b200007", value = "b200007_5301020601", valueName = "仪器折旧费")
    @ApiModelProperty(value = "仪器折旧费")
    private BigDecimal b2000075301020601;

    @LogField(tableName = "b200007", value = "b200007_5301020602", valueName = "设备折旧费")
    @ApiModelProperty(value = "设备折旧费")
    private BigDecimal b2000075301020602;

    @LogField(tableName = "b200007", value = "b200007_5301020603", valueName = "房屋折旧费")
    @ApiModelProperty(value = "房屋折旧费")
    private BigDecimal b2000075301020603;

    @LogField(tableName = "b200007", value = "b200007_5301020604", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000075301020604;

    @LogField(tableName = "b200007", value = "b200007_53010207", valueName = "长期待摊费用")
    @ApiModelProperty(value = "长期待摊费用")
    private BigDecimal b20000753010207;

    @LogField(tableName = "b200007", value = "b200007_5301020701", valueName = "设施改建及改装费")
    @ApiModelProperty(value = "设施改建及改装费")
    private BigDecimal b2000075301020701;

    @LogField(tableName = "b200007", value = "b200007_5301020702", valueName = "设施装修及修理费")
    @ApiModelProperty(value = "设施装修及修理费")
    private BigDecimal b2000075301020702;

    @LogField(tableName = "b200007", value = "b200007_5301020703", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000075301020703;

    @LogField(tableName = "b200007", value = "b200007_53010208", valueName = "无形资产摊销")
    @ApiModelProperty(value = "无形资产摊销")
    private BigDecimal b20000753010208;

    @LogField(tableName = "b200007", value = "b200007_5301020801", valueName = "软件")
    @ApiModelProperty(value = "软件")
    private BigDecimal b2000075301020801;

    @LogField(tableName = "b200007", value = "b200007_5301020802", valueName = "专利权")
    @ApiModelProperty(value = "专利权")
    private BigDecimal b2000075301020802;

    @LogField(tableName = "b200007", value = "b200007_5301020803", valueName = "商标")
    @ApiModelProperty(value = "商标")
    private BigDecimal b2000075301020803;

    @LogField(tableName = "b200007", value = "b200007_5301020804", valueName = "非专利技术")
    @ApiModelProperty(value = "非专利技术")
    private BigDecimal b2000075301020804;

    @LogField(tableName = "b200007", value = "b200007_5301020805", valueName = "知识产权")
    @ApiModelProperty(value = "知识产权")
    private BigDecimal b2000075301020805;

    @LogField(tableName = "b200007", value = "b200007_5301020806", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000075301020806;

    @LogField(tableName = "b200007", value = "b200007_53010209", valueName = "设计费用")
    @ApiModelProperty(value = "设计费用")
    private BigDecimal b20000753010209;

    @LogField(tableName = "b200007", value = "b200007_5301020901", valueName = "新产品设计费")
    @ApiModelProperty(value = "新产品设计费")
    private BigDecimal b2000075301020901;

    @LogField(tableName = "b200007", value = "b200007_5301020902", valueName = "新工艺规程制定费")
    @ApiModelProperty(value = "新工艺规程制定费")
    private BigDecimal b2000075301020902;

    @LogField(tableName = "b200007", value = "b200007_5301020903", valueName = "新产品与新工艺工序设计费")
    @ApiModelProperty(value = "新产品与新工艺工序设计费")
    private BigDecimal b2000075301020903;

    @LogField(tableName = "b200007", value = "b200007_5301020904", valueName = "新产品与新工艺技术规范费")
    @ApiModelProperty(value = "新产品与新工艺技术规范费")
    private BigDecimal b2000075301020904;

    @LogField(tableName = "b200007", value = "b200007_5301020905", valueName = "新产品与新工艺操作设计费")
    @ApiModelProperty(value = "新产品与新工艺操作设计费")
    private BigDecimal b2000075301020905;

    @LogField(tableName = "b200007", value = "b200007_5301020906", valueName = "创意设计活动费")
    @ApiModelProperty(value = "创意设计活动费")
    private BigDecimal b2000075301020906;

    @LogField(tableName = "b200007", value = "b200007_5301020907", valueName = "其他设计费用")
    @ApiModelProperty(value = "其他设计费用")
    private BigDecimal b2000075301020907;

    @LogField(tableName = "b200007", value = "b200007_53010210", valueName = "装备调试费")
    @ApiModelProperty(value = "装备调试费")
    private BigDecimal b20000753010210;

    @LogField(tableName = "b200007", value = "b200007_53010211", valueName = "试验费")
    @ApiModelProperty(value = "试验费")
    private BigDecimal b20000753010211;

    @LogField(tableName = "b200007", value = "b200007_5301021101", valueName = "新药临床试验费")
    @ApiModelProperty(value = "新药临床试验费")
    private BigDecimal b2000075301021101;

    @LogField(tableName = "b200007", value = "b200007_5301021102", valueName = "勘探开发技术现场试验费")
    @ApiModelProperty(value = "勘探开发技术现场试验费")
    private BigDecimal b2000075301021102;

    @LogField(tableName = "b200007", value = "b200007_5301021103", valueName = "田间试验费")
    @ApiModelProperty(value = "田间试验费")
    private BigDecimal b2000075301021103;

    @LogField(tableName = "b200007", value = "b200007_5301021104", valueName = "其他试验费用")
    @ApiModelProperty(value = "其他试验费用")
    private BigDecimal b2000075301021104;

    @LogField(tableName = "b200007", value = "b200007_53010212", valueName = "图书资料费")
    @ApiModelProperty(value = "图书资料费")
    private BigDecimal b20000753010212;

    @LogField(tableName = "b200007", value = "b200007_53010213", valueName = "资料翻译费")
    @ApiModelProperty(value = "资料翻译费")
    private BigDecimal b20000753010213;

    @LogField(tableName = "b200007", value = "b200007_53010214", valueName = "专家咨询费")
    @ApiModelProperty(value = "专家咨询费")
    private BigDecimal b20000753010214;

    @LogField(tableName = "b200007", value = "b200007_53010215", valueName = "高新科技研发保险费")
    @ApiModelProperty(value = "高新科技研发保险费")
    private BigDecimal b20000753010215;

    @LogField(tableName = "b200007", value = "b200007_53010216", valueName = "成果费用")
    @ApiModelProperty(value = "成果费用")
    private BigDecimal b20000753010216;

    @LogField(tableName = "b200007", value = "b200007_5301021601", valueName = "检索费")
    @ApiModelProperty(value = "检索费")
    private BigDecimal b2000075301021601;

    @LogField(tableName = "b200007", value = "b200007_5301021602", valueName = "分析费")
    @ApiModelProperty(value = "分析费")
    private BigDecimal b2000075301021602;

    @LogField(tableName = "b200007", value = "b200007_5301021603", valueName = "评议费")
    @ApiModelProperty(value = "评议费")
    private BigDecimal b2000075301021603;

    @LogField(tableName = "b200007", value = "b200007_5301021604", valueName = "论证费")
    @ApiModelProperty(value = "论证费")
    private BigDecimal b2000075301021604;

    @LogField(tableName = "b200007", value = "b200007_5301021605", valueName = "鉴定费")
    @ApiModelProperty(value = "鉴定费")
    private BigDecimal b2000075301021605;

    @LogField(tableName = "b200007", value = "b200007_5301021606", valueName = "评审费")
    @ApiModelProperty(value = "评审费")
    private BigDecimal b2000075301021606;

    @LogField(tableName = "b200007", value = "b200007_5301021607", valueName = "评估费")
    @ApiModelProperty(value = "评估费")
    private BigDecimal b2000075301021607;

    @LogField(tableName = "b200007", value = "b200007_5301021608", valueName = "验收费")
    @ApiModelProperty(value = "验收费")
    private BigDecimal b2000075301021608;

    @LogField(tableName = "b200007", value = "b200007_5301021609", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000075301021609;

    @LogField(tableName = "b200007", value = "b200007_53010217", valueName = "知识产权费用")
    @ApiModelProperty(value = "知识产权费用")
    private BigDecimal b20000753010217;

    @LogField(tableName = "b200007", value = "b200007_5301021701", valueName = "申请费")
    @ApiModelProperty(value = "申请费")
    private BigDecimal b2000075301021701;

    @LogField(tableName = "b200007", value = "b200007_5301021702", valueName = "注册费")
    @ApiModelProperty(value = "注册费")
    private BigDecimal b2000075301021702;

    @LogField(tableName = "b200007", value = "b200007_5301021703", valueName = "代理费")
    @ApiModelProperty(value = "代理费")
    private BigDecimal b2000075301021703;

    @LogField(tableName = "b200007", value = "b200007_5301021704", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000075301021704;

    @LogField(tableName = "b200007", value = "b200007_53010218", valueName = "差旅费")
    @ApiModelProperty(value = "差旅费")
    private BigDecimal b20000753010218;

    @LogField(tableName = "b200007", value = "b200007_53010219", valueName = "会议费")
    @ApiModelProperty(value = "会议费")
    private BigDecimal b20000753010219;

    @LogField(tableName = "b200007", value = "b200007_53010220", valueName = "通讯费")
    @ApiModelProperty(value = "通讯费")
    private BigDecimal b20000753010220;

    @LogField(tableName = "b200007", value = "b200007_53010221", valueName = "办公费")
    @ApiModelProperty(value = "办公费")
    private BigDecimal b20000753010221;

    @LogField(tableName = "b200007", value = "b200007_53010222", valueName = "外事费")
    @ApiModelProperty(value = "外事费")
    private BigDecimal b20000753010222;

    @LogField(tableName = "b200007", value = "b200007_53010223", valueName = "研发人员培训费")
    @ApiModelProperty(value = "研发人员培训费")
    private BigDecimal b20000753010223;

    @LogField(tableName = "b200007", value = "b200007_53010224", valueName = "研发人员培养费")
    @ApiModelProperty(value = "研发人员培养费")
    private BigDecimal b20000753010224;

    @LogField(tableName = "b200007", value = "b200007_53010225", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b20000753010225;

    @LogField(tableName = "b200007", value = "b200007_53010226", valueName = "外包费用")
    @ApiModelProperty(value = "外包费用")
    private BigDecimal b20000753010226;

    @LogField(tableName = "b200007", value = "b200007_5301022601", valueName = "委托研发（境内进行）")
    @ApiModelProperty(value = "委托研发（境内进行）")
    private BigDecimal b2000075301022601;

    @LogField(tableName = "b200007", value = "b200007_5301022602", valueName = "委托研发（境外机构）")
    @ApiModelProperty(value = "委托研发（境外机构）")
    private BigDecimal b2000075301022602;

    @LogField(tableName = "b200007", value = "b200007_5301022603", valueName = "委托研发（境外个人）")
    @ApiModelProperty(value = "委托研发（境外个人）")
    private BigDecimal b2000075301022603;

    @LogField(tableName = "b200007", value = "b200007_5301022604", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b2000075301022604;

    @LogField(tableName = "b200007", value = "b200007_5401", valueName = "工程施工")
    @ApiModelProperty(value = "工程施工")
    private BigDecimal b2000075401;

    @LogField(tableName = "b200007", value = "b200007_5402", valueName = "工程结算")
    @ApiModelProperty(value = "工程结算")
    private BigDecimal b2000075402;

    @LogField(tableName = "b200007", value = "b200007_5403", valueName = "机械作业")
    @ApiModelProperty(value = "机械作业")
    private BigDecimal b2000075403;

    @LogField(tableName = "b200007", value = "b200007_5501", valueName = "合同履约成本")
    @ApiModelProperty(value = "合同履约成本")
    private BigDecimal b2000075501;

    @LogField(tableName = "b200007", value = "b200007_550101", valueName = "职工工资")
    @ApiModelProperty(value = "职工工资")
    private BigDecimal b200007550101;

    @LogField(tableName = "b200007", value = "b200007_550102", valueName = "年终奖")
    @ApiModelProperty(value = "年终奖")
    private BigDecimal b200007550102;

    @LogField(tableName = "b200007", value = "b200007_550103", valueName = "津贴与补贴")
    @ApiModelProperty(value = "津贴与补贴")
    private BigDecimal b200007550103;

    @LogField(tableName = "b200007", value = "b200007_550104", valueName = "股权激励")
    @ApiModelProperty(value = "股权激励")
    private BigDecimal b200007550104;

    @LogField(tableName = "b200007", value = "b200007_550105", valueName = "职工教育经费")
    @ApiModelProperty(value = "职工教育经费")
    private BigDecimal b200007550105;

    @LogField(tableName = "b200007", value = "b200007_550106", valueName = "职工培训费用")
    @ApiModelProperty(value = "职工培训费用")
    private BigDecimal b200007550106;

    @LogField(tableName = "b200007", value = "b200007_550107", valueName = "工会经费")
    @ApiModelProperty(value = "工会经费")
    private BigDecimal b200007550107;

    @LogField(tableName = "b200007", value = "b200007_550108", valueName = "社会保险费")
    @ApiModelProperty(value = "社会保险费")
    private BigDecimal b200007550108;

    @LogField(tableName = "b200007", value = "b200007_550109", valueName = "住房公积金")
    @ApiModelProperty(value = "住房公积金")
    private BigDecimal b200007550109;

    @LogField(tableName = "b200007", value = "b200007_550110", valueName = "职工福利费")
    @ApiModelProperty(value = "职工福利费")
    private BigDecimal b200007550110;

    @LogField(tableName = "b200007", value = "b200007_550111", valueName = "补充养老保险")
    @ApiModelProperty(value = "补充养老保险")
    private BigDecimal b200007550111;

    @LogField(tableName = "b200007", value = "b200007_550112", valueName = "补充医疗保险")
    @ApiModelProperty(value = "补充医疗保险")
    private BigDecimal b200007550112;

    @LogField(tableName = "b200007", value = "b200007_550113", valueName = "其他职工工资")
    @ApiModelProperty(value = "其他职工工资")
    private BigDecimal b200007550113;

    @LogField(tableName = "b200007", value = "b200007_550114", valueName = "商业保险费")
    @ApiModelProperty(value = "商业保险费")
    private BigDecimal b200007550114;

    @LogField(tableName = "b200007", value = "b200007_550115", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200007550115;

    @LogField(tableName = "b200007", value = "b200007_5502", valueName = "合同履约成本减值准备")
    @ApiModelProperty(value = "合同履约成本减值准备")
    private BigDecimal b2000075502;

    @LogField(tableName = "b200007", value = "b200007_5503", valueName = "合同取得成本")
    @ApiModelProperty(value = "合同取得成本")
    private BigDecimal b2000075503;

    @LogField(tableName = "b200007", value = "b200007_5504", valueName = "合同取得成本减值准备")
    @ApiModelProperty(value = "合同取得成本减值准备")
    private BigDecimal b2000075504;

    @LogField(tableName = "b200007", value = "b200007_6001", valueName = "主营业务收入")
    @ApiModelProperty(value = "主营业务收入")
    private BigDecimal b2000076001;

    @LogField(tableName = "b200007", value = "b200007_600101", valueName = "销售商品收入")
    @ApiModelProperty(value = "销售商品收入")
    private BigDecimal b200007600101;

    @LogField(tableName = "b200007", value = "b200007_600102", valueName = "提供劳务收入")
    @ApiModelProperty(value = "提供劳务收入")
    private BigDecimal b200007600102;

    @LogField(tableName = "b200007", value = "b200007_600103", valueName = "建造合同收入")
    @ApiModelProperty(value = "建造合同收入")
    private BigDecimal b200007600103;

    @LogField(tableName = "b200007", value = "b200007_600104", valueName = "让渡资产使用权收入")
    @ApiModelProperty(value = "让渡资产使用权收入")
    private BigDecimal b200007600104;

    @LogField(tableName = "b200007", value = "b200007_600105", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200007600105;

    @LogField(tableName = "b200007", value = "b200007_6011", valueName = "利息收入")
    @ApiModelProperty(value = "利息收入")
    private BigDecimal b2000076011;

    @LogField(tableName = "b200007", value = "b200007_6021", valueName = "手续费及佣金收入")
    @ApiModelProperty(value = "手续费及佣金收入")
    private BigDecimal b2000076021;

    @LogField(tableName = "b200007", value = "b200007_6031", valueName = "保费收入")
    @ApiModelProperty(value = "保费收入")
    private BigDecimal b2000076031;

    @LogField(tableName = "b200007", value = "b200007_6041", valueName = "租赁收入")
    @ApiModelProperty(value = "租赁收入")
    private BigDecimal b2000076041;

    @LogField(tableName = "b200007", value = "b200007_6051", valueName = "其他业务收入")
    @ApiModelProperty(value = "其他业务收入")
    private BigDecimal b2000076051;

    @LogField(tableName = "b200007", value = "b200007_605101", valueName = "销售材料收入")
    @ApiModelProperty(value = "销售材料收入")
    private BigDecimal b200007605101;

    @LogField(tableName = "b200007", value = "b200007_605102", valueName = "出租固定资产收入")
    @ApiModelProperty(value = "出租固定资产收入")
    private BigDecimal b200007605102;

    @LogField(tableName = "b200007", value = "b200007_605103", valueName = "出租无形资产收入")
    @ApiModelProperty(value = "出租无形资产收入")
    private BigDecimal b200007605103;

    @LogField(tableName = "b200007", value = "b200007_605104", valueName = "出租包装物和商品收入")
    @ApiModelProperty(value = "出租包装物和商品收入")
    private BigDecimal b200007605104;

    @LogField(tableName = "b200007", value = "b200007_605105", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200007605105;

    @LogField(tableName = "b200007", value = "b200007_6061", valueName = "汇兑损益")
    @ApiModelProperty(value = "汇兑损益")
    private BigDecimal b2000076061;

    @LogField(tableName = "b200007", value = "b200007_6101", valueName = "公允价值变动损益")
    @ApiModelProperty(value = "公允价值变动损益")
    private BigDecimal b2000076101;

    @LogField(tableName = "b200007", value = "b200007_6111", valueName = "投资收益")
    @ApiModelProperty(value = "投资收益")
    private BigDecimal b2000076111;

    @LogField(tableName = "b200007", value = "b200007_611101", valueName = "交易性金融资产")
    @ApiModelProperty(value = "交易性金融资产")
    private BigDecimal b200007611101;

    @LogField(tableName = "b200007", value = "b200007_61110101", valueName = "国债")
    @ApiModelProperty(value = "国债")
    private BigDecimal b20000761110101;

    @LogField(tableName = "b200007", value = "b200007_6111010101", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000076111010101;

    @LogField(tableName = "b200007", value = "b200007_6111010102", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000076111010102;

    @LogField(tableName = "b200007", value = "b200007_61110102", valueName = "证券投资基金")
    @ApiModelProperty(value = "证券投资基金")
    private BigDecimal b20000761110102;

    @LogField(tableName = "b200007", value = "b200007_6111010201", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000076111010201;

    @LogField(tableName = "b200007", value = "b200007_6111010202", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000076111010202;

    @LogField(tableName = "b200007", value = "b200007_61110103", valueName = "地方政府债券")
    @ApiModelProperty(value = "地方政府债券")
    private BigDecimal b20000761110103;

    @LogField(tableName = "b200007", value = "b200007_6111010301", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000076111010301;

    @LogField(tableName = "b200007", value = "b200007_6111010302", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000076111010302;

    @LogField(tableName = "b200007", value = "b200007_61110104", valueName = "铁路债券")
    @ApiModelProperty(value = "铁路债券")
    private BigDecimal b20000761110104;

    @LogField(tableName = "b200007", value = "b200007_6111010401", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000076111010401;

    @LogField(tableName = "b200007", value = "b200007_6111010402", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000076111010402;

    @LogField(tableName = "b200007", value = "b200007_61110105", valueName = "连续持有公开发行并上市流通12个月及以上的股票")
    @ApiModelProperty(value = "连续持有公开发行并上市流通12个月及以上的股票")
    private BigDecimal b20000761110105;

    @LogField(tableName = "b200007", value = "b200007_6111010501", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000076111010501;

    @LogField(tableName = "b200007", value = "b200007_6111010502", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000076111010502;

    @LogField(tableName = "b200007", value = "b200007_61110106", valueName = "永续债")
    @ApiModelProperty(value = "永续债")
    private BigDecimal b20000761110106;

    @LogField(tableName = "b200007", value = "b200007_6111010601", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000076111010601;

    @LogField(tableName = "b200007", value = "b200007_6111010602", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000076111010602;

    @LogField(tableName = "b200007", value = "b200007_61110107", valueName = "创新企业CDR")
    @ApiModelProperty(value = "创新企业CDR")
    private BigDecimal b20000761110107;

    @LogField(tableName = "b200007", value = "b200007_6111010701", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000076111010701;

    @LogField(tableName = "b200007", value = "b200007_6111010702", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000076111010702;

    @LogField(tableName = "b200007", value = "b200007_61110108", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b20000761110108;

    @LogField(tableName = "b200007", value = "b200007_6111010801", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000076111010801;

    @LogField(tableName = "b200007", value = "b200007_6111010802", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000076111010802;

    @LogField(tableName = "b200007", value = "b200007_611102", valueName = "可供出售金融资产")
    @ApiModelProperty(value = "可供出售金融资产")
    private BigDecimal b200007611102;

    @LogField(tableName = "b200007", value = "b200007_61110201", valueName = "国债")
    @ApiModelProperty(value = "国债")
    private BigDecimal b20000761110201;

    @LogField(tableName = "b200007", value = "b200007_6111020101", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000076111020101;

    @LogField(tableName = "b200007", value = "b200007_6111020102", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000076111020102;

    @LogField(tableName = "b200007", value = "b200007_61110202", valueName = "证券投资基金")
    @ApiModelProperty(value = "证券投资基金")
    private BigDecimal b20000761110202;

    @LogField(tableName = "b200007", value = "b200007_6111020201", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000076111020201;

    @LogField(tableName = "b200007", value = "b200007_6111020202", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000076111020202;

    @LogField(tableName = "b200007", value = "b200007_61110203", valueName = "地方政府债券")
    @ApiModelProperty(value = "地方政府债券")
    private BigDecimal b20000761110203;

    @LogField(tableName = "b200007", value = "b200007_6111020301", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000076111020301;

    @LogField(tableName = "b200007", value = "b200007_6111020302", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000076111020302;

    @LogField(tableName = "b200007", value = "b200007_61110204", valueName = "铁路债券")
    @ApiModelProperty(value = "铁路债券")
    private BigDecimal b20000761110204;

    @LogField(tableName = "b200007", value = "b200007_6111020401", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000076111020401;

    @LogField(tableName = "b200007", value = "b200007_6111020402", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000076111020402;

    @LogField(tableName = "b200007", value = "b200007_61110205", valueName = "连续持有公开发行并上市流通12个月及以上的股票")
    @ApiModelProperty(value = "连续持有公开发行并上市流通12个月及以上的股票")
    private BigDecimal b20000761110205;

    @LogField(tableName = "b200007", value = "b200007_6111020501", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000076111020501;

    @LogField(tableName = "b200007", value = "b200007_6111020502", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000076111020502;

    @LogField(tableName = "b200007", value = "b200007_61110206", valueName = "永续债")
    @ApiModelProperty(value = "永续债")
    private BigDecimal b20000761110206;

    @LogField(tableName = "b200007", value = "b200007_6111020601", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000076111020601;

    @LogField(tableName = "b200007", value = "b200007_6111020602", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000076111020602;

    @LogField(tableName = "b200007", value = "b200007_61110207", valueName = "创新企业CDR")
    @ApiModelProperty(value = "创新企业CDR")
    private BigDecimal b20000761110207;

    @LogField(tableName = "b200007", value = "b200007_6111020701", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000076111020701;

    @LogField(tableName = "b200007", value = "b200007_6111020702", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000076111020702;

    @LogField(tableName = "b200007", value = "b200007_61110208", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b20000761110208;

    @LogField(tableName = "b200007", value = "b200007_6111020801", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000076111020801;

    @LogField(tableName = "b200007", value = "b200007_6111020802", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000076111020802;

    @LogField(tableName = "b200007", value = "b200007_611103", valueName = "持有至到期投资")
    @ApiModelProperty(value = "持有至到期投资")
    private BigDecimal b200007611103;

    @LogField(tableName = "b200007", value = "b200007_61110301", valueName = "国债")
    @ApiModelProperty(value = "国债")
    private BigDecimal b20000761110301;

    @LogField(tableName = "b200007", value = "b200007_6111030101", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000076111030101;

    @LogField(tableName = "b200007", value = "b200007_6111030102", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000076111030102;

    @LogField(tableName = "b200007", value = "b200007_61110302", valueName = "地方政府债券")
    @ApiModelProperty(value = "地方政府债券")
    private BigDecimal b20000761110302;

    @LogField(tableName = "b200007", value = "b200007_6111030201", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000076111030201;

    @LogField(tableName = "b200007", value = "b200007_6111030202", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000076111030202;

    @LogField(tableName = "b200007", value = "b200007_61110303", valueName = "铁路债券")
    @ApiModelProperty(value = "铁路债券")
    private BigDecimal b20000761110303;

    @LogField(tableName = "b200007", value = "b200007_6111030301", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000076111030301;

    @LogField(tableName = "b200007", value = "b200007_6111030302", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000076111030302;

    @LogField(tableName = "b200007", value = "b200007_61110304", valueName = "永续债")
    @ApiModelProperty(value = "永续债")
    private BigDecimal b20000761110304;

    @LogField(tableName = "b200007", value = "b200007_6111030401", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000076111030401;

    @LogField(tableName = "b200007", value = "b200007_6111030402", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000076111030402;

    @LogField(tableName = "b200007", value = "b200007_61110305", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b20000761110305;

    @LogField(tableName = "b200007", value = "b200007_6111030501", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000076111030501;

    @LogField(tableName = "b200007", value = "b200007_6111030502", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000076111030502;

    @LogField(tableName = "b200007", value = "b200007_611104", valueName = "衍生工具")
    @ApiModelProperty(value = "衍生工具")
    private BigDecimal b200007611104;

    @LogField(tableName = "b200007", value = "b200007_61110401", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b20000761110401;

    @LogField(tableName = "b200007", value = "b200007_61110402", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b20000761110402;

    @LogField(tableName = "b200007", value = "b200007_611105", valueName = "交易性金融负债")
    @ApiModelProperty(value = "交易性金融负债")
    private BigDecimal b200007611105;

    @LogField(tableName = "b200007", value = "b200007_61110501", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b20000761110501;

    @LogField(tableName = "b200007", value = "b200007_61110502", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b20000761110502;

    @LogField(tableName = "b200007", value = "b200007_611106", valueName = "长期股权投资")
    @ApiModelProperty(value = "长期股权投资")
    private BigDecimal b200007611106;

    @LogField(tableName = "b200007", value = "b200007_61110601", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b20000761110601;

    @LogField(tableName = "b200007", value = "b200007_61110602", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b20000761110602;

    @LogField(tableName = "b200007", value = "b200007_611107", valueName = "长期债券投资")
    @ApiModelProperty(value = "长期债券投资")
    private BigDecimal b200007611107;

    @LogField(tableName = "b200007", value = "b200007_61110701", valueName = "国债")
    @ApiModelProperty(value = "国债")
    private BigDecimal b20000761110701;

    @LogField(tableName = "b200007", value = "b200007_6111070101", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000076111070101;

    @LogField(tableName = "b200007", value = "b200007_6111070102", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000076111070102;

    @LogField(tableName = "b200007", value = "b200007_61110702", valueName = "地方政府债券")
    @ApiModelProperty(value = "地方政府债券")
    private BigDecimal b20000761110702;

    @LogField(tableName = "b200007", value = "b200007_6111070201", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000076111070201;

    @LogField(tableName = "b200007", value = "b200007_6111070202", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000076111070202;

    @LogField(tableName = "b200007", value = "b200007_61110703", valueName = "铁路债券")
    @ApiModelProperty(value = "铁路债券")
    private BigDecimal b20000761110703;

    @LogField(tableName = "b200007", value = "b200007_6111070301", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000076111070301;

    @LogField(tableName = "b200007", value = "b200007_6111070302", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000076111070302;

    @LogField(tableName = "b200007", value = "b200007_61110704", valueName = "永续债")
    @ApiModelProperty(value = "永续债")
    private BigDecimal b20000761110704;

    @LogField(tableName = "b200007", value = "b200007_6111070401", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000076111070401;

    @LogField(tableName = "b200007", value = "b200007_6111070402", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000076111070402;

    @LogField(tableName = "b200007", value = "b200007_61110705", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b20000761110705;

    @LogField(tableName = "b200007", value = "b200007_6111070501", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000076111070501;

    @LogField(tableName = "b200007", value = "b200007_6111070502", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000076111070502;

    @LogField(tableName = "b200007", value = "b200007_611108", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200007611108;

    @LogField(tableName = "b200007", value = "b200007_61110801", valueName = "国债")
    @ApiModelProperty(value = "国债")
    private BigDecimal b20000761110801;

    @LogField(tableName = "b200007", value = "b200007_6111080101", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000076111080101;

    @LogField(tableName = "b200007", value = "b200007_6111080102", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000076111080102;

    @LogField(tableName = "b200007", value = "b200007_61110802", valueName = "证券投资基金")
    @ApiModelProperty(value = "证券投资基金")
    private BigDecimal b20000761110802;

    @LogField(tableName = "b200007", value = "b200007_6111080201", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000076111080201;

    @LogField(tableName = "b200007", value = "b200007_6111080202", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000076111080202;

    @LogField(tableName = "b200007", value = "b200007_61110803", valueName = "地方政府债券")
    @ApiModelProperty(value = "地方政府债券")
    private BigDecimal b20000761110803;

    @LogField(tableName = "b200007", value = "b200007_6111080301", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000076111080301;

    @LogField(tableName = "b200007", value = "b200007_6111080302", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000076111080302;

    @LogField(tableName = "b200007", value = "b200007_61110804", valueName = "铁路债券")
    @ApiModelProperty(value = "铁路债券")
    private BigDecimal b20000761110804;

    @LogField(tableName = "b200007", value = "b200007_6111080401", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000076111080401;

    @LogField(tableName = "b200007", value = "b200007_6111080402", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000076111080402;

    @LogField(tableName = "b200007", value = "b200007_61110805", valueName = "连续持有公开发行并上市流通12个月及以上的股票")
    @ApiModelProperty(value = "连续持有公开发行并上市流通12个月及以上的股票")
    private BigDecimal b20000761110805;

    @LogField(tableName = "b200007", value = "b200007_6111080501", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000076111080501;

    @LogField(tableName = "b200007", value = "b200007_6111080502", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000076111080502;

    @LogField(tableName = "b200007", value = "b200007_61110806", valueName = "永续债")
    @ApiModelProperty(value = "永续债")
    private BigDecimal b20000761110806;

    @LogField(tableName = "b200007", value = "b200007_6111080601", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000076111080601;

    @LogField(tableName = "b200007", value = "b200007_6111080602", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000076111080602;

    @LogField(tableName = "b200007", value = "b200007_61110807", valueName = "创新企业CDR")
    @ApiModelProperty(value = "创新企业CDR")
    private BigDecimal b20000761110807;

    @LogField(tableName = "b200007", value = "b200007_6111080701", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000076111080701;

    @LogField(tableName = "b200007", value = "b200007_6111080702", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000076111080702;

    @LogField(tableName = "b200007", value = "b200007_61110808", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b20000761110808;

    @LogField(tableName = "b200007", value = "b200007_6111080801", valueName = "持有")
    @ApiModelProperty(value = "持有")
    private BigDecimal b2000076111080801;

    @LogField(tableName = "b200007", value = "b200007_6111080802", valueName = "处置")
    @ApiModelProperty(value = "处置")
    private BigDecimal b2000076111080802;

    @LogField(tableName = "b200007", value = "b200007_6115", valueName = "资产处置损益")
    @ApiModelProperty(value = "资产处置损益")
    private BigDecimal b2000076115;

    @LogField(tableName = "b200007", value = "b200007_611501", valueName = "固定资产损失")
    @ApiModelProperty(value = "固定资产损失")
    private BigDecimal b200007611501;

    @LogField(tableName = "b200007", value = "b200007_611502", valueName = "无形资产损失")
    @ApiModelProperty(value = "无形资产损失")
    private BigDecimal b200007611502;

    @LogField(tableName = "b200007", value = "b200007_611503", valueName = "在建工程损失")
    @ApiModelProperty(value = "在建工程损失")
    private BigDecimal b200007611503;

    @LogField(tableName = "b200007", value = "b200007_611504", valueName = "生产性生物资产损失")
    @ApiModelProperty(value = "生产性生物资产损失")
    private BigDecimal b200007611504;

    @LogField(tableName = "b200007", value = "b200007_611505", valueName = "打包出售资产损失")
    @ApiModelProperty(value = "打包出售资产损失")
    private BigDecimal b200007611505;

    @LogField(tableName = "b200007", value = "b200007_611506", valueName = "其他资产损失")
    @ApiModelProperty(value = "其他资产损失")
    private BigDecimal b200007611506;

    @LogField(tableName = "b200007", value = "b200007_611507", valueName = "资产处置收益")
    @ApiModelProperty(value = "资产处置收益")
    private BigDecimal b200007611507;

    @LogField(tableName = "b200007", value = "b200007_6117", valueName = "其他收益")
    @ApiModelProperty(value = "其他收益")
    private BigDecimal b2000076117;

    @LogField(tableName = "b200007", value = "b200007_6201", valueName = "摊回保险责任准备金")
    @ApiModelProperty(value = "摊回保险责任准备金")
    private BigDecimal b2000076201;

    @LogField(tableName = "b200007", value = "b200007_6202", valueName = "摊回赔付支出")
    @ApiModelProperty(value = "摊回赔付支出")
    private BigDecimal b2000076202;

    @LogField(tableName = "b200007", value = "b200007_6203", valueName = "摊回分保费用")
    @ApiModelProperty(value = "摊回分保费用")
    private BigDecimal b2000076203;

    @LogField(tableName = "b200007", value = "b200007_6301", valueName = "营业外收入")
    @ApiModelProperty(value = "营业外收入")
    private BigDecimal b2000076301;

    @LogField(tableName = "b200007", value = "b200007_630101", valueName = "非流动资产处置利得")
    @ApiModelProperty(value = "非流动资产处置利得")
    private BigDecimal b200007630101;

    @LogField(tableName = "b200007", value = "b200007_630102", valueName = "非货币性资产交换利得")
    @ApiModelProperty(value = "非货币性资产交换利得")
    private BigDecimal b200007630102;

    @LogField(tableName = "b200007", value = "b200007_630103", valueName = "债务重组利得")
    @ApiModelProperty(value = "债务重组利得")
    private BigDecimal b200007630103;

    @LogField(tableName = "b200007", value = "b200007_630104", valueName = "政府补助利得")
    @ApiModelProperty(value = "政府补助利得")
    private BigDecimal b200007630104;

    @LogField(tableName = "b200007", value = "b200007_630105", valueName = "盘盈利得")
    @ApiModelProperty(value = "盘盈利得")
    private BigDecimal b200007630105;

    @LogField(tableName = "b200007", value = "b200007_630106", valueName = "捐赠利得")
    @ApiModelProperty(value = "捐赠利得")
    private BigDecimal b200007630106;

    @LogField(tableName = "b200007", value = "b200007_630107", valueName = "罚没利得")
    @ApiModelProperty(value = "罚没利得")
    private BigDecimal b200007630107;

    @LogField(tableName = "b200007", value = "b200007_630108", valueName = "确实无法偿付的应付款项")
    @ApiModelProperty(value = "确实无法偿付的应付款项")
    private BigDecimal b200007630108;

    @LogField(tableName = "b200007", value = "b200007_630109", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200007630109;

    @LogField(tableName = "b200007", value = "b200007_6401", valueName = "主营业务成本")
    @ApiModelProperty(value = "主营业务成本")
    private BigDecimal b2000076401;

    @LogField(tableName = "b200007", value = "b200007_640101", valueName = "销售商品成本")
    @ApiModelProperty(value = "销售商品成本")
    private BigDecimal b200007640101;

    @LogField(tableName = "b200007", value = "b200007_640102", valueName = "提供劳务成本")
    @ApiModelProperty(value = "提供劳务成本")
    private BigDecimal b200007640102;

    @LogField(tableName = "b200007", value = "b200007_640103", valueName = "职工工资")
    @ApiModelProperty(value = "职工工资")
    private BigDecimal b200007640103;

    @LogField(tableName = "b200007", value = "b200007_640104", valueName = "年终奖")
    @ApiModelProperty(value = "年终奖")
    private BigDecimal b200007640104;

    @LogField(tableName = "b200007", value = "b200007_640105", valueName = "津贴与补贴")
    @ApiModelProperty(value = "津贴与补贴")
    private BigDecimal b200007640105;

    @LogField(tableName = "b200007", value = "b200007_640106", valueName = "股权激励")
    @ApiModelProperty(value = "股权激励")
    private BigDecimal b200007640106;

    @LogField(tableName = "b200007", value = "b200007_640107", valueName = "社会保险费")
    @ApiModelProperty(value = "社会保险费")
    private BigDecimal b200007640107;

    @LogField(tableName = "b200007", value = "b200007_640108", valueName = "住房公积金")
    @ApiModelProperty(value = "住房公积金")
    private BigDecimal b200007640108;

    @LogField(tableName = "b200007", value = "b200007_640109", valueName = "补充养老保险")
    @ApiModelProperty(value = "补充养老保险")
    private BigDecimal b200007640109;

    @LogField(tableName = "b200007", value = "b200007_640110", valueName = "补充医疗保险")
    @ApiModelProperty(value = "补充医疗保险")
    private BigDecimal b200007640110;

    @LogField(tableName = "b200007", value = "b200007_640111", valueName = "职工福利费")
    @ApiModelProperty(value = "职工福利费")
    private BigDecimal b200007640111;

    @LogField(tableName = "b200007", value = "b200007_640112", valueName = "职工教育经费")
    @ApiModelProperty(value = "职工教育经费")
    private BigDecimal b200007640112;

    @LogField(tableName = "b200007", value = "b200007_640113", valueName = "职工培训费用")
    @ApiModelProperty(value = "职工培训费用")
    private BigDecimal b200007640113;

    @LogField(tableName = "b200007", value = "b200007_640114", valueName = "工会经费")
    @ApiModelProperty(value = "工会经费")
    private BigDecimal b200007640114;

    @LogField(tableName = "b200007", value = "b200007_640115", valueName = "其他职工工资")
    @ApiModelProperty(value = "其他职工工资")
    private BigDecimal b200007640115;

    @LogField(tableName = "b200007", value = "b200007_640116", valueName = "商业保险费")
    @ApiModelProperty(value = "商业保险费")
    private BigDecimal b200007640116;

    @LogField(tableName = "b200007", value = "b200007_640117", valueName = "建造合同成本")
    @ApiModelProperty(value = "建造合同成本")
    private BigDecimal b200007640117;

    @LogField(tableName = "b200007", value = "b200007_640118", valueName = "让渡资产使用权成本")
    @ApiModelProperty(value = "让渡资产使用权成本")
    private BigDecimal b200007640118;

    @LogField(tableName = "b200007", value = "b200007_640119", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200007640119;

    @LogField(tableName = "b200007", value = "b200007_6402", valueName = "其他业务成本")
    @ApiModelProperty(value = "其他业务成本")
    private BigDecimal b2000076402;

    @LogField(tableName = "b200007", value = "b200007_640201", valueName = "销售材料成本")
    @ApiModelProperty(value = "销售材料成本")
    private BigDecimal b200007640201;

    @LogField(tableName = "b200007", value = "b200007_640202", valueName = "出租固定资产成本")
    @ApiModelProperty(value = "出租固定资产成本")
    private BigDecimal b200007640202;

    @LogField(tableName = "b200007", value = "b200007_640203", valueName = "出租无形资产成本")
    @ApiModelProperty(value = "出租无形资产成本")
    private BigDecimal b200007640203;

    @LogField(tableName = "b200007", value = "b200007_640204", valueName = "出租包装物和商品成本")
    @ApiModelProperty(value = "出租包装物和商品成本")
    private BigDecimal b200007640204;

    @LogField(tableName = "b200007", value = "b200007_640205", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200007640205;

    @LogField(tableName = "b200007", value = "b200007_6403", valueName = "税金及附加")
    @ApiModelProperty(value = "税金及附加")
    private BigDecimal b2000076403;

    @LogField(tableName = "b200007", value = "b200007_6411", valueName = "利息支出")
    @ApiModelProperty(value = "利息支出")
    private BigDecimal b2000076411;

    @LogField(tableName = "b200007", value = "b200007_6421", valueName = "手续费及佣金支出")
    @ApiModelProperty(value = "手续费及佣金支出")
    private BigDecimal b2000076421;

    @LogField(tableName = "b200007", value = "b200007_6501", valueName = "提取未到期责任准备金")
    @ApiModelProperty(value = "提取未到期责任准备金")
    private BigDecimal b2000076501;

    @LogField(tableName = "b200007", value = "b200007_6502", valueName = "提取保险责任准备金")
    @ApiModelProperty(value = "提取保险责任准备金")
    private BigDecimal b2000076502;

    @LogField(tableName = "b200007", value = "b200007_6511", valueName = "赔付支出")
    @ApiModelProperty(value = "赔付支出")
    private BigDecimal b2000076511;

    @LogField(tableName = "b200007", value = "b200007_6521", valueName = "保单红利支出")
    @ApiModelProperty(value = "保单红利支出")
    private BigDecimal b2000076521;

    @LogField(tableName = "b200007", value = "b200007_6531", valueName = "退保金")
    @ApiModelProperty(value = "退保金")
    private BigDecimal b2000076531;

    @LogField(tableName = "b200007", value = "b200007_6541", valueName = "分出保费")
    @ApiModelProperty(value = "分出保费")
    private BigDecimal b2000076541;

    @LogField(tableName = "b200007", value = "b200007_6542", valueName = "分保费用")
    @ApiModelProperty(value = "分保费用")
    private BigDecimal b2000076542;

    @LogField(tableName = "b200007", value = "b200007_6601", valueName = "销售费用")
    @ApiModelProperty(value = "销售费用")
    private BigDecimal b2000076601;

    @LogField(tableName = "b200007", value = "b200007_660101", valueName = "职工工资")
    @ApiModelProperty(value = "职工工资")
    private BigDecimal b200007660101;

    @LogField(tableName = "b200007", value = "b200007_660102", valueName = "年终奖")
    @ApiModelProperty(value = "年终奖")
    private BigDecimal b200007660102;

    @LogField(tableName = "b200007", value = "b200007_660103", valueName = "津贴与补贴")
    @ApiModelProperty(value = "津贴与补贴")
    private BigDecimal b200007660103;

    @LogField(tableName = "b200007", value = "b200007_660104", valueName = "股权激励")
    @ApiModelProperty(value = "股权激励")
    private BigDecimal b200007660104;

    @LogField(tableName = "b200007", value = "b200007_660105", valueName = "社会保险费")
    @ApiModelProperty(value = "社会保险费")
    private BigDecimal b200007660105;

    @LogField(tableName = "b200007", value = "b200007_660106", valueName = "住房公积金")
    @ApiModelProperty(value = "住房公积金")
    private BigDecimal b200007660106;

    @LogField(tableName = "b200007", value = "b200007_660107", valueName = "补充医疗保险")
    @ApiModelProperty(value = "补充医疗保险")
    private BigDecimal b200007660107;

    @LogField(tableName = "b200007", value = "b200007_660108", valueName = "补充养老保险")
    @ApiModelProperty(value = "补充养老保险")
    private BigDecimal b200007660108;

    @LogField(tableName = "b200007", value = "b200007_660109", valueName = "职工福利费")
    @ApiModelProperty(value = "职工福利费")
    private BigDecimal b200007660109;

    @LogField(tableName = "b200007", value = "b200007_660110", valueName = "职工教育经费")
    @ApiModelProperty(value = "职工教育经费")
    private BigDecimal b200007660110;

    @LogField(tableName = "b200007", value = "b200007_660111", valueName = "职工培训费用")
    @ApiModelProperty(value = "职工培训费用")
    private BigDecimal b200007660111;

    @LogField(tableName = "b200007", value = "b200007_660112", valueName = "工会经费")
    @ApiModelProperty(value = "工会经费")
    private BigDecimal b200007660112;

    @LogField(tableName = "b200007", value = "b200007_660113", valueName = "其他职工工资")
    @ApiModelProperty(value = "其他职工工资")
    private BigDecimal b200007660113;

    @LogField(tableName = "b200007", value = "b200007_660114", valueName = "商业保险费")
    @ApiModelProperty(value = "商业保险费")
    private BigDecimal b200007660114;

    @LogField(tableName = "b200007", value = "b200007_660115", valueName = "折旧费")
    @ApiModelProperty(value = "折旧费")
    private BigDecimal b200007660115;

    @LogField(tableName = "b200007", value = "b200007_660116", valueName = "无形资产摊销费")
    @ApiModelProperty(value = "无形资产摊销费")
    private BigDecimal b200007660116;

    @LogField(tableName = "b200007", value = "b200007_660117", valueName = "长期待摊费用摊销费")
    @ApiModelProperty(value = "长期待摊费用摊销费")
    private BigDecimal b200007660117;

    @LogField(tableName = "b200007", value = "b200007_660118", valueName = "修理费")
    @ApiModelProperty(value = "修理费")
    private BigDecimal b200007660118;

    @LogField(tableName = "b200007", value = "b200007_660119", valueName = "物料消耗")
    @ApiModelProperty(value = "物料消耗")
    private BigDecimal b200007660119;

    @LogField(tableName = "b200007", value = "b200007_660120", valueName = "低值易耗品摊销")
    @ApiModelProperty(value = "低值易耗品摊销")
    private BigDecimal b200007660120;

    @LogField(tableName = "b200007", value = "b200007_660121", valueName = "办公费")
    @ApiModelProperty(value = "办公费")
    private BigDecimal b200007660121;

    @LogField(tableName = "b200007", value = "b200007_660122", valueName = "差旅费")
    @ApiModelProperty(value = "差旅费")
    private BigDecimal b200007660122;

    @LogField(tableName = "b200007", value = "b200007_660123", valueName = "业务招待费")
    @ApiModelProperty(value = "业务招待费")
    private BigDecimal b200007660123;

    @LogField(tableName = "b200007", value = "b200007_660124", valueName = "通信费")
    @ApiModelProperty(value = "通信费")
    private BigDecimal b200007660124;

    @LogField(tableName = "b200007", value = "b200007_660125", valueName = "车辆费")
    @ApiModelProperty(value = "车辆费")
    private BigDecimal b200007660125;

    @LogField(tableName = "b200007", value = "b200007_660126", valueName = "能源费")
    @ApiModelProperty(value = "能源费")
    private BigDecimal b200007660126;

    @LogField(tableName = "b200007", value = "b200007_660127", valueName = "运杂费")
    @ApiModelProperty(value = "运杂费")
    private BigDecimal b200007660127;

    @LogField(tableName = "b200007", value = "b200007_660128", valueName = "交通费")
    @ApiModelProperty(value = "交通费")
    private BigDecimal b200007660128;

    @LogField(tableName = "b200007", value = "b200007_660129", valueName = "房租")
    @ApiModelProperty(value = "房租")
    private BigDecimal b200007660129;

    @LogField(tableName = "b200007", value = "b200007_660130", valueName = "物业管理费")
    @ApiModelProperty(value = "物业管理费")
    private BigDecimal b200007660130;

    @LogField(tableName = "b200007", value = "b200007_660131", valueName = "水电费")
    @ApiModelProperty(value = "水电费")
    private BigDecimal b200007660131;

    @LogField(tableName = "b200007", value = "b200007_660132", valueName = "中介服务费")
    @ApiModelProperty(value = "中介服务费")
    private BigDecimal b200007660132;

    @LogField(tableName = "b200007", value = "b200007_660133", valueName = "快递费")
    @ApiModelProperty(value = "快递费")
    private BigDecimal b200007660133;

    @LogField(tableName = "b200007", value = "b200007_660134", valueName = "保险费")
    @ApiModelProperty(value = "保险费")
    private BigDecimal b200007660134;

    @LogField(tableName = "b200007", value = "b200007_660135", valueName = "租赁费")
    @ApiModelProperty(value = "租赁费")
    private BigDecimal b200007660135;

    @LogField(tableName = "b200007", value = "b200007_660136", valueName = "装卸费")
    @ApiModelProperty(value = "装卸费")
    private BigDecimal b200007660136;

    @LogField(tableName = "b200007", value = "b200007_660137", valueName = "包装费")
    @ApiModelProperty(value = "包装费")
    private BigDecimal b200007660137;

    @LogField(tableName = "b200007", value = "b200007_660138", valueName = "通关费用")
    @ApiModelProperty(value = "通关费用")
    private BigDecimal b200007660138;

    @LogField(tableName = "b200007", value = "b200007_660139", valueName = "宣传展览费")
    @ApiModelProperty(value = "宣传展览费")
    private BigDecimal b200007660139;

    @LogField(tableName = "b200007", value = "b200007_660140", valueName = "仓储费")
    @ApiModelProperty(value = "仓储费")
    private BigDecimal b200007660140;

    @LogField(tableName = "b200007", value = "b200007_660141", valueName = "调试费")
    @ApiModelProperty(value = "调试费")
    private BigDecimal b200007660141;

    @LogField(tableName = "b200007", value = "b200007_660142", valueName = "广告费")
    @ApiModelProperty(value = "广告费")
    private BigDecimal b200007660142;

    @LogField(tableName = "b200007", value = "b200007_660143", valueName = "业务提成/佣金（销售服务费）")
    @ApiModelProperty(value = "业务提成/佣金（销售服务费）")
    private BigDecimal b200007660143;

    @LogField(tableName = "b200007", value = "b200007_660144", valueName = "投标费")
    @ApiModelProperty(value = "投标费")
    private BigDecimal b200007660144;

    @LogField(tableName = "b200007", value = "b200007_660145", valueName = "售后服务费")
    @ApiModelProperty(value = "售后服务费")
    private BigDecimal b200007660145;

    @LogField(tableName = "b200007", value = "b200007_660146", valueName = "其他经营费用")
    @ApiModelProperty(value = "其他经营费用")
    private BigDecimal b200007660146;

    @LogField(tableName = "b200007", value = "b200007_660147", valueName = "劳务费")
    @ApiModelProperty(value = "劳务费")
    private BigDecimal b200007660147;

    @LogField(tableName = "b200007", value = "b200007_660148", valueName = "董事会费")
    @ApiModelProperty(value = "董事会费")
    private BigDecimal b200007660148;

    @LogField(tableName = "b200007", value = "b200007_660149", valueName = "诉讼费")
    @ApiModelProperty(value = "诉讼费")
    private BigDecimal b200007660149;

    @LogField(tableName = "b200007", value = "b200007_660150", valueName = "技术转让费")
    @ApiModelProperty(value = "技术转让费")
    private BigDecimal b200007660150;

    @LogField(tableName = "b200007", value = "b200007_660151", valueName = "研究费用")
    @ApiModelProperty(value = "研究费用")
    private BigDecimal b200007660151;

    @LogField(tableName = "b200007", value = "b200007_660152", valueName = "税金")
    @ApiModelProperty(value = "税金")
    private BigDecimal b200007660152;

    @LogField(tableName = "b200007", value = "b200007_660153", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200007660153;

    @LogField(tableName = "b200007", value = "b200007_6602", valueName = "管理费用")
    @ApiModelProperty(value = "管理费用")
    private BigDecimal b2000076602;

    @LogField(tableName = "b200007", value = "b200007_660201", valueName = "职工工资")
    @ApiModelProperty(value = "职工工资")
    private BigDecimal b200007660201;

    @LogField(tableName = "b200007", value = "b200007_660202", valueName = "年终奖")
    @ApiModelProperty(value = "年终奖")
    private BigDecimal b200007660202;

    @LogField(tableName = "b200007", value = "b200007_660203", valueName = "津贴与补贴")
    @ApiModelProperty(value = "津贴与补贴")
    private BigDecimal b200007660203;

    @LogField(tableName = "b200007", value = "b200007_660204", valueName = "股权激励")
    @ApiModelProperty(value = "股权激励")
    private BigDecimal b200007660204;

    @LogField(tableName = "b200007", value = "b200007_660205", valueName = "社会保险费")
    @ApiModelProperty(value = "社会保险费")
    private BigDecimal b200007660205;

    @LogField(tableName = "b200007", value = "b200007_660206", valueName = "住房公积金")
    @ApiModelProperty(value = "住房公积金")
    private BigDecimal b200007660206;

    @LogField(tableName = "b200007", value = "b200007_660207", valueName = "补充医疗保险")
    @ApiModelProperty(value = "补充医疗保险")
    private BigDecimal b200007660207;

    @LogField(tableName = "b200007", value = "b200007_660208", valueName = "补充养老保险")
    @ApiModelProperty(value = "补充养老保险")
    private BigDecimal b200007660208;

    @LogField(tableName = "b200007", value = "b200007_660209", valueName = "职工福利费")
    @ApiModelProperty(value = "职工福利费")
    private BigDecimal b200007660209;

    @LogField(tableName = "b200007", value = "b200007_660210", valueName = "职工教育经费")
    @ApiModelProperty(value = "职工教育经费")
    private BigDecimal b200007660210;

    @LogField(tableName = "b200007", value = "b200007_660211", valueName = "职工培训费用")
    @ApiModelProperty(value = "职工培训费用")
    private BigDecimal b200007660211;

    @LogField(tableName = "b200007", value = "b200007_660212", valueName = "工会经费")
    @ApiModelProperty(value = "工会经费")
    private BigDecimal b200007660212;

    @LogField(tableName = "b200007", value = "b200007_660213", valueName = "其他职工工资")
    @ApiModelProperty(value = "其他职工工资")
    private BigDecimal b200007660213;

    @LogField(tableName = "b200007", value = "b200007_660214", valueName = "商业保险费")
    @ApiModelProperty(value = "商业保险费")
    private BigDecimal b200007660214;

    @LogField(tableName = "b200007", value = "b200007_660215", valueName = "差旅费")
    @ApiModelProperty(value = "差旅费")
    private BigDecimal b200007660215;

    @LogField(tableName = "b200007", value = "b200007_660216", valueName = "办公费")
    @ApiModelProperty(value = "办公费")
    private BigDecimal b200007660216;

    @LogField(tableName = "b200007", value = "b200007_660217", valueName = "折旧费")
    @ApiModelProperty(value = "折旧费")
    private BigDecimal b200007660217;

    @LogField(tableName = "b200007", value = "b200007_660218", valueName = "修理费")
    @ApiModelProperty(value = "修理费")
    private BigDecimal b200007660218;

    @LogField(tableName = "b200007", value = "b200007_660219", valueName = "物料消耗")
    @ApiModelProperty(value = "物料消耗")
    private BigDecimal b200007660219;

    @LogField(tableName = "b200007", value = "b200007_660220", valueName = "低值易耗品摊销")
    @ApiModelProperty(value = "低值易耗品摊销")
    private BigDecimal b200007660220;

    @LogField(tableName = "b200007", value = "b200007_660221", valueName = "会议费")
    @ApiModelProperty(value = "会议费")
    private BigDecimal b200007660221;

    @LogField(tableName = "b200007", value = "b200007_660222", valueName = "咨询费")
    @ApiModelProperty(value = "咨询费")
    private BigDecimal b200007660222;

    @LogField(tableName = "b200007", value = "b200007_660223", valueName = "软件使用费")
    @ApiModelProperty(value = "软件使用费")
    private BigDecimal b200007660223;

    @LogField(tableName = "b200007", value = "b200007_660224", valueName = "招聘费")
    @ApiModelProperty(value = "招聘费")
    private BigDecimal b200007660224;

    @LogField(tableName = "b200007", value = "b200007_660225", valueName = "专业服务费")
    @ApiModelProperty(value = "专业服务费")
    private BigDecimal b200007660225;

    @LogField(tableName = "b200007", value = "b200007_660226", valueName = "税金")
    @ApiModelProperty(value = "税金")
    private BigDecimal b200007660226;

    @LogField(tableName = "b200007", value = "b200007_660227", valueName = "技术开发费")
    @ApiModelProperty(value = "技术开发费")
    private BigDecimal b200007660227;

    @LogField(tableName = "b200007", value = "b200007_660228", valueName = "技术转让费")
    @ApiModelProperty(value = "技术转让费")
    private BigDecimal b200007660228;

    @LogField(tableName = "b200007", value = "b200007_660229", valueName = "无形资产摊销费")
    @ApiModelProperty(value = "无形资产摊销费")
    private BigDecimal b200007660229;

    @LogField(tableName = "b200007", value = "b200007_660230", valueName = "业务招待费")
    @ApiModelProperty(value = "业务招待费")
    private BigDecimal b200007660230;

    @LogField(tableName = "b200007", value = "b200007_660231", valueName = "研发费用")
    @ApiModelProperty(value = "研发费用")
    private BigDecimal b200007660231;

    @LogField(tableName = "b200007", value = "b200007_660232", valueName = "仓储费用")
    @ApiModelProperty(value = "仓储费用")
    private BigDecimal b200007660232;

    @LogField(tableName = "b200007", value = "b200007_660233", valueName = "保险费")
    @ApiModelProperty(value = "保险费")
    private BigDecimal b200007660233;

    @LogField(tableName = "b200007", value = "b200007_660234", valueName = "房租")
    @ApiModelProperty(value = "房租")
    private BigDecimal b200007660234;

    @LogField(tableName = "b200007", value = "b200007_660235", valueName = "物业管理费")
    @ApiModelProperty(value = "物业管理费")
    private BigDecimal b200007660235;

    @LogField(tableName = "b200007", value = "b200007_660236", valueName = "快递费")
    @ApiModelProperty(value = "快递费")
    private BigDecimal b200007660236;

    @LogField(tableName = "b200007", value = "b200007_660237", valueName = "中介服务费")
    @ApiModelProperty(value = "中介服务费")
    private BigDecimal b200007660237;

    @LogField(tableName = "b200007", value = "b200007_660238", valueName = "开办费")
    @ApiModelProperty(value = "开办费")
    private BigDecimal b200007660238;

    @LogField(tableName = "b200007", value = "b200007_660239", valueName = "水电费")
    @ApiModelProperty(value = "水电费")
    private BigDecimal b200007660239;

    @LogField(tableName = "b200007", value = "b200007_660240", valueName = "运杂费")
    @ApiModelProperty(value = "运杂费")
    private BigDecimal b200007660240;

    @LogField(tableName = "b200007", value = "b200007_660241", valueName = "劳务费")
    @ApiModelProperty(value = "劳务费")
    private BigDecimal b200007660241;

    @LogField(tableName = "b200007", value = "b200007_660242", valueName = "广告费")
    @ApiModelProperty(value = "广告费")
    private BigDecimal b200007660242;

    @LogField(tableName = "b200007", value = "b200007_660243", valueName = "佣金和手续费")
    @ApiModelProperty(value = "佣金和手续费")
    private BigDecimal b200007660243;

    @LogField(tableName = "b200007", value = "b200007_660244", valueName = "董事会费")
    @ApiModelProperty(value = "董事会费")
    private BigDecimal b200007660244;

    @LogField(tableName = "b200007", value = "b200007_660245", valueName = "诉讼费")
    @ApiModelProperty(value = "诉讼费")
    private BigDecimal b200007660245;

    @LogField(tableName = "b200007", value = "b200007_660246", valueName = "包装费")
    @ApiModelProperty(value = "包装费")
    private BigDecimal b200007660246;

    @LogField(tableName = "b200007", value = "b200007_660247", valueName = "党组织工作经费")
    @ApiModelProperty(value = "党组织工作经费")
    private BigDecimal b200007660247;

    @LogField(tableName = "b200007", value = "b200007_660248", valueName = "长期待摊费用摊销费")
    @ApiModelProperty(value = "长期待摊费用摊销费")
    private BigDecimal b200007660248;

    @LogField(tableName = "b200007", value = "b200007_660249", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200007660249;

    @LogField(tableName = "b200007", value = "b200007_660250", valueName = "现金及银行存款损失")
    @ApiModelProperty(value = "现金及银行存款损失")
    private BigDecimal b200007660250;

    @LogField(tableName = "b200007", value = "b200007_660251", valueName = "应收及预付款项坏账损失")
    @ApiModelProperty(value = "应收及预付款项坏账损失")
    private BigDecimal b200007660251;

    @LogField(tableName = "b200007", value = "b200007_660252", valueName = "存货损失")
    @ApiModelProperty(value = "存货损失")
    private BigDecimal b200007660252;

    @LogField(tableName = "b200007", value = "b200007_660253", valueName = "固定资产损失")
    @ApiModelProperty(value = "固定资产损失")
    private BigDecimal b200007660253;

    @LogField(tableName = "b200007", value = "b200007_660254", valueName = "无形资产损失")
    @ApiModelProperty(value = "无形资产损失")
    private BigDecimal b200007660254;

    @LogField(tableName = "b200007", value = "b200007_660255", valueName = "在建工程损失")
    @ApiModelProperty(value = "在建工程损失")
    private BigDecimal b200007660255;

    @LogField(tableName = "b200007", value = "b200007_660256", valueName = "生产性生物资产损失")
    @ApiModelProperty(value = "生产性生物资产损失")
    private BigDecimal b200007660256;

    @LogField(tableName = "b200007", value = "b200007_660257", valueName = "非金融企业债权性投资损失")
    @ApiModelProperty(value = "非金融企业债权性投资损失")
    private BigDecimal b200007660257;

    @LogField(tableName = "b200007", value = "b200007_660258", valueName = "股权（权益）性投资损失")
    @ApiModelProperty(value = "股权（权益）性投资损失")
    private BigDecimal b200007660258;

    @LogField(tableName = "b200007", value = "b200007_660259", valueName = "通过各种交易场所、市场买卖债券、股票、期货、基金以及金融衍生产品等发生的损失")
    @ApiModelProperty(value = "通过各种交易场所、市场买卖债券、股票、期货、基金以及金融衍生产品等发生的损失")
    private BigDecimal b200007660259;

    @LogField(tableName = "b200007", value = "b200007_660260", valueName = "打包出售资产损失")
    @ApiModelProperty(value = "打包出售资产损失")
    private BigDecimal b200007660260;

    @LogField(tableName = "b200007", value = "b200007_660261", valueName = "其他资产损失")
    @ApiModelProperty(value = "其他资产损失")
    private BigDecimal b200007660261;

    @LogField(tableName = "b200007", value = "b200007_6603", valueName = "财务费用")
    @ApiModelProperty(value = "财务费用")
    private BigDecimal b2000076603;

    @LogField(tableName = "b200007", value = "b200007_660301", valueName = "汇兑损益")
    @ApiModelProperty(value = "汇兑损益")
    private BigDecimal b200007660301;

    @LogField(tableName = "b200007", value = "b200007_660302", valueName = "利息费用")
    @ApiModelProperty(value = "利息费用")
    private BigDecimal b200007660302;

    @LogField(tableName = "b200007", value = "b200007_660303", valueName = "佣金和手续费")
    @ApiModelProperty(value = "佣金和手续费")
    private BigDecimal b200007660303;

    @LogField(tableName = "b200007", value = "b200007_660304", valueName = "账户管理费")
    @ApiModelProperty(value = "账户管理费")
    private BigDecimal b200007660304;

    @LogField(tableName = "b200007", value = "b200007_660305", valueName = "利息收入")
    @ApiModelProperty(value = "利息收入")
    private BigDecimal b200007660305;

    @LogField(tableName = "b200007", value = "b200007_66030501", valueName = "因未实现融资收益确认的利息收入")
    @ApiModelProperty(value = "因未实现融资收益确认的利息收入")
    private BigDecimal b20000766030501;

    @LogField(tableName = "b200007", value = "b200007_66030502", valueName = "其他利息收入")
    @ApiModelProperty(value = "其他利息收入")
    private BigDecimal b20000766030502;

    @LogField(tableName = "b200007", value = "b200007_660306", valueName = "现金折扣")
    @ApiModelProperty(value = "现金折扣")
    private BigDecimal b200007660306;

    @LogField(tableName = "b200007", value = "b200007_660307", valueName = "银行手续费")
    @ApiModelProperty(value = "银行手续费")
    private BigDecimal b200007660307;

    @LogField(tableName = "b200007", value = "b200007_660308", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200007660308;

    @LogField(tableName = "b200007", value = "b200007_6604", valueName = "勘探费用")
    @ApiModelProperty(value = "勘探费用")
    private BigDecimal b2000076604;

    @LogField(tableName = "b200007", value = "b200007_6701", valueName = "资产减值损失")
    @ApiModelProperty(value = "资产减值损失")
    private BigDecimal b2000076701;

    @LogField(tableName = "b200007", value = "b200007_6702", valueName = "信用减值损失")
    @ApiModelProperty(value = "信用减值损失")
    private BigDecimal b2000076702;

    @LogField(tableName = "b200007", value = "b200007_6711", valueName = "营业外支出")
    @ApiModelProperty(value = "营业外支出")
    private BigDecimal b2000076711;

    @LogField(tableName = "b200007", value = "b200007_671101", valueName = "非流动资产处置净损失")
    @ApiModelProperty(value = "非流动资产处置净损失")
    private BigDecimal b200007671101;

    @LogField(tableName = "b200007", value = "b200007_67110101", valueName = "存货损失")
    @ApiModelProperty(value = "存货损失")
    private BigDecimal b20000767110101;

    @LogField(tableName = "b200007", value = "b200007_67110102", valueName = "固定资产损失")
    @ApiModelProperty(value = "固定资产损失")
    private BigDecimal b20000767110102;

    @LogField(tableName = "b200007", value = "b200007_67110103", valueName = "无形资产损失")
    @ApiModelProperty(value = "无形资产损失")
    private BigDecimal b20000767110103;

    @LogField(tableName = "b200007", value = "b200007_67110104", valueName = "在建工程损失")
    @ApiModelProperty(value = "在建工程损失")
    private BigDecimal b20000767110104;

    @LogField(tableName = "b200007", value = "b200007_67110105", valueName = "生产性生物资产损失")
    @ApiModelProperty(value = "生产性生物资产损失")
    private BigDecimal b20000767110105;

    @LogField(tableName = "b200007", value = "b200007_67110106", valueName = "打包出售资产损失")
    @ApiModelProperty(value = "打包出售资产损失")
    private BigDecimal b20000767110106;

    @LogField(tableName = "b200007", value = "b200007_67110107", valueName = "其他资产损失")
    @ApiModelProperty(value = "其他资产损失")
    private BigDecimal b20000767110107;

    @LogField(tableName = "b200007", value = "b200007_671102", valueName = "非货币性资产交换损失")
    @ApiModelProperty(value = "非货币性资产交换损失")
    private BigDecimal b200007671102;

    @LogField(tableName = "b200007", value = "b200007_67110201", valueName = "存货损失")
    @ApiModelProperty(value = "存货损失")
    private BigDecimal b20000767110201;

    @LogField(tableName = "b200007", value = "b200007_67110202", valueName = "固定资产损失")
    @ApiModelProperty(value = "固定资产损失")
    private BigDecimal b20000767110202;

    @LogField(tableName = "b200007", value = "b200007_67110203", valueName = "无形资产损失")
    @ApiModelProperty(value = "无形资产损失")
    private BigDecimal b20000767110203;

    @LogField(tableName = "b200007", value = "b200007_67110204", valueName = "在建工程损失")
    @ApiModelProperty(value = "在建工程损失")
    private BigDecimal b20000767110204;

    @LogField(tableName = "b200007", value = "b200007_67110205", valueName = "生产性生物资产损失")
    @ApiModelProperty(value = "生产性生物资产损失")
    private BigDecimal b20000767110205;

    @LogField(tableName = "b200007", value = "b200007_67110206", valueName = "打包出售资产损失")
    @ApiModelProperty(value = "打包出售资产损失")
    private BigDecimal b20000767110206;

    @LogField(tableName = "b200007", value = "b200007_67110207", valueName = "其他资产损失")
    @ApiModelProperty(value = "其他资产损失")
    private BigDecimal b20000767110207;

    @LogField(tableName = "b200007", value = "b200007_671103", valueName = "债务重组损失")
    @ApiModelProperty(value = "债务重组损失")
    private BigDecimal b200007671103;

    @LogField(tableName = "b200007", value = "b200007_671104", valueName = "非常损失")
    @ApiModelProperty(value = "非常损失")
    private BigDecimal b200007671104;

    @LogField(tableName = "b200007", value = "b200007_67110401", valueName = "存货损失")
    @ApiModelProperty(value = "存货损失")
    private BigDecimal b20000767110401;

    @LogField(tableName = "b200007", value = "b200007_67110402", valueName = "固定资产损失")
    @ApiModelProperty(value = "固定资产损失")
    private BigDecimal b20000767110402;

    @LogField(tableName = "b200007", value = "b200007_67110403", valueName = "无形资产损失")
    @ApiModelProperty(value = "无形资产损失")
    private BigDecimal b20000767110403;

    @LogField(tableName = "b200007", value = "b200007_67110404", valueName = "在建工程损失")
    @ApiModelProperty(value = "在建工程损失")
    private BigDecimal b20000767110404;

    @LogField(tableName = "b200007", value = "b200007_67110405", valueName = "生产性生物资产损失")
    @ApiModelProperty(value = "生产性生物资产损失")
    private BigDecimal b20000767110405;

    @LogField(tableName = "b200007", value = "b200007_67110406", valueName = "打包出售资产损失")
    @ApiModelProperty(value = "打包出售资产损失")
    private BigDecimal b20000767110406;

    @LogField(tableName = "b200007", value = "b200007_67110407", valueName = "其他资产损失")
    @ApiModelProperty(value = "其他资产损失")
    private BigDecimal b20000767110407;

    @LogField(tableName = "b200007", value = "b200007_671105", valueName = "捐赠支出")
    @ApiModelProperty(value = "捐赠支出")
    private BigDecimal b200007671105;

    @LogField(tableName = "b200007", value = "b200007_671106", valueName = "赞助支出")
    @ApiModelProperty(value = "赞助支出")
    private BigDecimal b200007671106;

    @LogField(tableName = "b200007", value = "b200007_671107", valueName = "罚没支出")
    @ApiModelProperty(value = "罚没支出")
    private BigDecimal b200007671107;

    @LogField(tableName = "b200007", value = "b200007_67110701", valueName = "经营性处罚")
    @ApiModelProperty(value = "经营性处罚")
    private BigDecimal b20000767110701;

    @LogField(tableName = "b200007", value = "b200007_67110702", valueName = "税收滞纳金")
    @ApiModelProperty(value = "税收滞纳金")
    private BigDecimal b20000767110702;

    @LogField(tableName = "b200007", value = "b200007_67110703", valueName = "行政性处罚")
    @ApiModelProperty(value = "行政性处罚")
    private BigDecimal b20000767110703;

    @LogField(tableName = "b200007", value = "b200007_671108", valueName = "其他")
    @ApiModelProperty(value = "其他")
    private BigDecimal b200007671108;

    @LogField(tableName = "b200007", value = "b200007_67110801", valueName = "现金及银行存款损失")
    @ApiModelProperty(value = "现金及银行存款损失")
    private BigDecimal b20000767110801;

    @LogField(tableName = "b200007", value = "b200007_67110802", valueName = "应收及预付款项坏账损失")
    @ApiModelProperty(value = "应收及预付款项坏账损失")
    private BigDecimal b20000767110802;

    @LogField(tableName = "b200007", value = "b200007_67110803", valueName = "存货损失")
    @ApiModelProperty(value = "存货损失")
    private BigDecimal b20000767110803;

    @LogField(tableName = "b200007", value = "b200007_67110804", valueName = "固定资产损失")
    @ApiModelProperty(value = "固定资产损失")
    private BigDecimal b20000767110804;

    @LogField(tableName = "b200007", value = "b200007_67110805", valueName = "无形资产损失")
    @ApiModelProperty(value = "无形资产损失")
    private BigDecimal b20000767110805;

    @LogField(tableName = "b200007", value = "b200007_67110806", valueName = "在建工程损失")
    @ApiModelProperty(value = "在建工程损失")
    private BigDecimal b20000767110806;

    @LogField(tableName = "b200007", value = "b200007_67110807", valueName = "生产性生物资产损失")
    @ApiModelProperty(value = "生产性生物资产损失")
    private BigDecimal b20000767110807;

    @LogField(tableName = "b200007", value = "b200007_67110808", valueName = "非金融企业债权性投资损失")
    @ApiModelProperty(value = "非金融企业债权性投资损失")
    private BigDecimal b20000767110808;

    @LogField(tableName = "b200007", value = "b200007_67110809", valueName = "股权（权益）性投资损失")
    @ApiModelProperty(value = "股权（权益）性投资损失")
    private BigDecimal b20000767110809;

    @LogField(tableName = "b200007", value = "b200007_67110810", valueName = "通过各种交易场所、市场买卖债券、股票、期货、基金以及金融衍生产品等发生的损失")
    @ApiModelProperty(value = "通过各种交易场所、市场买卖债券、股票、期货、基金以及金融衍生产品等发生的损失")
    private BigDecimal b20000767110810;

    @LogField(tableName = "b200007", value = "b200007_67110811", valueName = "打包出售资产损失")
    @ApiModelProperty(value = "打包出售资产损失")
    private BigDecimal b20000767110811;

    @LogField(tableName = "b200007", value = "b200007_67110812", valueName = "其他资产损失")
    @ApiModelProperty(value = "其他资产损失")
    private BigDecimal b20000767110812;

    @LogField(tableName = "b200007", value = "b200007_67110813", valueName = "其他支出")
    @ApiModelProperty(value = "其他支出")
    private BigDecimal b20000767110813;

    @LogField(tableName = "b200007", value = "b200007_6801", valueName = "所得税费用")
    @ApiModelProperty(value = "所得税费用")
    private BigDecimal b2000076801;

    @LogField(tableName = "b200007", value = "b200007_6901", valueName = "以前年度损益调整")
    @ApiModelProperty(value = "以前年度损益调整")
    private BigDecimal b2000076901;

    @LogField(tableName = "b200007", value = "b200007_222124", valueName = "应交税费-纳税检查调整")
    @ApiModelProperty(value = "应交税费-纳税检查调整")
    private BigDecimal b200007222124;

    @LogField(tableName = "b200007", value = "b200007_222125", valueName = "应交税费-加计抵减进项税额")
    @ApiModelProperty(value = "应交税费-加计抵减进项税额")
    private BigDecimal b200007222125;

    @LogField(tableName = "b200007", value = "b200007_67110501", valueName = "营业外支出—捐赠支出—公益性捐赠")
    @ApiModelProperty(value = "营业外支出—捐赠支出—公益性捐赠")
    private BigDecimal b20000767110501;

    @LogField(tableName = "b200007", value = "b200007_67110502", valueName = "营业外支出—捐赠支出—非公益性捐赠")
    @ApiModelProperty(value = "营业外支出—捐赠支出—非公益性捐赠")
    private BigDecimal b20000767110502;

    @ApiModelProperty(value = "创建时间")
    private String createTime;

    @ApiModelProperty(value = "更新时间")
    private String updateTime;

    public String getEntId() {
        return entId;
    }

    public void setEntId(String entId) {
        this.entId = entId;
    }

    public String getTaxYear() {
        return taxYear;
    }

    public void setTaxYear(String taxYear) {
        this.taxYear = taxYear;
    }

    public BigDecimal getB2000071001() {
        return b2000071001;
    }

    public void setB2000071001(BigDecimal b2000071001) {
        this.b2000071001 = b2000071001;
    }

    public BigDecimal getB2000071002() {
        return b2000071002;
    }

    public void setB2000071002(BigDecimal b2000071002) {
        this.b2000071002 = b2000071002;
    }

    public BigDecimal getB2000071003() {
        return b2000071003;
    }

    public void setB2000071003(BigDecimal b2000071003) {
        this.b2000071003 = b2000071003;
    }

    public BigDecimal getB2000071011() {
        return b2000071011;
    }

    public void setB2000071011(BigDecimal b2000071011) {
        this.b2000071011 = b2000071011;
    }

    public BigDecimal getB2000071012() {
        return b2000071012;
    }

    public void setB2000071012(BigDecimal b2000071012) {
        this.b2000071012 = b2000071012;
    }

    public BigDecimal getB2000071021() {
        return b2000071021;
    }

    public void setB2000071021(BigDecimal b2000071021) {
        this.b2000071021 = b2000071021;
    }

    public BigDecimal getB2000071031() {
        return b2000071031;
    }

    public void setB2000071031(BigDecimal b2000071031) {
        this.b2000071031 = b2000071031;
    }

    public BigDecimal getB2000071101() {
        return b2000071101;
    }

    public void setB2000071101(BigDecimal b2000071101) {
        this.b2000071101 = b2000071101;
    }

    public BigDecimal getB2000071111() {
        return b2000071111;
    }

    public void setB2000071111(BigDecimal b2000071111) {
        this.b2000071111 = b2000071111;
    }

    public BigDecimal getB2000071121() {
        return b2000071121;
    }

    public void setB2000071121(BigDecimal b2000071121) {
        this.b2000071121 = b2000071121;
    }

    public BigDecimal getB2000071122() {
        return b2000071122;
    }

    public void setB2000071122(BigDecimal b2000071122) {
        this.b2000071122 = b2000071122;
    }

    public BigDecimal getB2000071123() {
        return b2000071123;
    }

    public void setB2000071123(BigDecimal b2000071123) {
        this.b2000071123 = b2000071123;
    }

    public BigDecimal getB2000071124() {
        return b2000071124;
    }

    public void setB2000071124(BigDecimal b2000071124) {
        this.b2000071124 = b2000071124;
    }

    public BigDecimal getB2000071125() {
        return b2000071125;
    }

    public void setB2000071125(BigDecimal b2000071125) {
        this.b2000071125 = b2000071125;
    }

    public BigDecimal getB2000071131() {
        return b2000071131;
    }

    public void setB2000071131(BigDecimal b2000071131) {
        this.b2000071131 = b2000071131;
    }

    public BigDecimal getB2000071132() {
        return b2000071132;
    }

    public void setB2000071132(BigDecimal b2000071132) {
        this.b2000071132 = b2000071132;
    }

    public BigDecimal getB2000071201() {
        return b2000071201;
    }

    public void setB2000071201(BigDecimal b2000071201) {
        this.b2000071201 = b2000071201;
    }

    public BigDecimal getB2000071211() {
        return b2000071211;
    }

    public void setB2000071211(BigDecimal b2000071211) {
        this.b2000071211 = b2000071211;
    }

    public BigDecimal getB2000071212() {
        return b2000071212;
    }

    public void setB2000071212(BigDecimal b2000071212) {
        this.b2000071212 = b2000071212;
    }

    public BigDecimal getB2000071221() {
        return b2000071221;
    }

    public void setB2000071221(BigDecimal b2000071221) {
        this.b2000071221 = b2000071221;
    }

    public BigDecimal getB2000071231() {
        return b2000071231;
    }

    public void setB2000071231(BigDecimal b2000071231) {
        this.b2000071231 = b2000071231;
    }

    public BigDecimal getB2000071301() {
        return b2000071301;
    }

    public void setB2000071301(BigDecimal b2000071301) {
        this.b2000071301 = b2000071301;
    }

    public BigDecimal getB2000071302() {
        return b2000071302;
    }

    public void setB2000071302(BigDecimal b2000071302) {
        this.b2000071302 = b2000071302;
    }

    public BigDecimal getB2000071303() {
        return b2000071303;
    }

    public void setB2000071303(BigDecimal b2000071303) {
        this.b2000071303 = b2000071303;
    }

    public BigDecimal getB2000071304() {
        return b2000071304;
    }

    public void setB2000071304(BigDecimal b2000071304) {
        this.b2000071304 = b2000071304;
    }

    public BigDecimal getB2000071311() {
        return b2000071311;
    }

    public void setB2000071311(BigDecimal b2000071311) {
        this.b2000071311 = b2000071311;
    }

    public BigDecimal getB2000071321() {
        return b2000071321;
    }

    public void setB2000071321(BigDecimal b2000071321) {
        this.b2000071321 = b2000071321;
    }

    public BigDecimal getB2000071401() {
        return b2000071401;
    }

    public void setB2000071401(BigDecimal b2000071401) {
        this.b2000071401 = b2000071401;
    }

    public BigDecimal getB2000071402() {
        return b2000071402;
    }

    public void setB2000071402(BigDecimal b2000071402) {
        this.b2000071402 = b2000071402;
    }

    public BigDecimal getB2000071403() {
        return b2000071403;
    }

    public void setB2000071403(BigDecimal b2000071403) {
        this.b2000071403 = b2000071403;
    }

    public BigDecimal getB2000071404() {
        return b2000071404;
    }

    public void setB2000071404(BigDecimal b2000071404) {
        this.b2000071404 = b2000071404;
    }

    public BigDecimal getB2000071405() {
        return b2000071405;
    }

    public void setB2000071405(BigDecimal b2000071405) {
        this.b2000071405 = b2000071405;
    }

    public BigDecimal getB2000071406() {
        return b2000071406;
    }

    public void setB2000071406(BigDecimal b2000071406) {
        this.b2000071406 = b2000071406;
    }

    public BigDecimal getB2000071407() {
        return b2000071407;
    }

    public void setB2000071407(BigDecimal b2000071407) {
        this.b2000071407 = b2000071407;
    }

    public BigDecimal getB2000071408() {
        return b2000071408;
    }

    public void setB2000071408(BigDecimal b2000071408) {
        this.b2000071408 = b2000071408;
    }

    public BigDecimal getB2000071411() {
        return b2000071411;
    }

    public void setB2000071411(BigDecimal b2000071411) {
        this.b2000071411 = b2000071411;
    }

    public BigDecimal getB2000071421() {
        return b2000071421;
    }

    public void setB2000071421(BigDecimal b2000071421) {
        this.b2000071421 = b2000071421;
    }

    public BigDecimal getB2000071431() {
        return b2000071431;
    }

    public void setB2000071431(BigDecimal b2000071431) {
        this.b2000071431 = b2000071431;
    }

    public BigDecimal getB2000071441() {
        return b2000071441;
    }

    public void setB2000071441(BigDecimal b2000071441) {
        this.b2000071441 = b2000071441;
    }

    public BigDecimal getB2000071451() {
        return b2000071451;
    }

    public void setB2000071451(BigDecimal b2000071451) {
        this.b2000071451 = b2000071451;
    }

    public BigDecimal getB2000071461() {
        return b2000071461;
    }

    public void setB2000071461(BigDecimal b2000071461) {
        this.b2000071461 = b2000071461;
    }

    public BigDecimal getB2000071471() {
        return b2000071471;
    }

    public void setB2000071471(BigDecimal b2000071471) {
        this.b2000071471 = b2000071471;
    }

    public BigDecimal getB2000071481() {
        return b2000071481;
    }

    public void setB2000071481(BigDecimal b2000071481) {
        this.b2000071481 = b2000071481;
    }

    public BigDecimal getB2000071482() {
        return b2000071482;
    }

    public void setB2000071482(BigDecimal b2000071482) {
        this.b2000071482 = b2000071482;
    }

    public BigDecimal getB2000071501() {
        return b2000071501;
    }

    public void setB2000071501(BigDecimal b2000071501) {
        this.b2000071501 = b2000071501;
    }

    public BigDecimal getB2000071502() {
        return b2000071502;
    }

    public void setB2000071502(BigDecimal b2000071502) {
        this.b2000071502 = b2000071502;
    }

    public BigDecimal getB2000071503() {
        return b2000071503;
    }

    public void setB2000071503(BigDecimal b2000071503) {
        this.b2000071503 = b2000071503;
    }

    public BigDecimal getB2000071511() {
        return b2000071511;
    }

    public void setB2000071511(BigDecimal b2000071511) {
        this.b2000071511 = b2000071511;
    }

    public BigDecimal getB2000071512() {
        return b2000071512;
    }

    public void setB2000071512(BigDecimal b2000071512) {
        this.b2000071512 = b2000071512;
    }

    public BigDecimal getB2000071521() {
        return b2000071521;
    }

    public void setB2000071521(BigDecimal b2000071521) {
        this.b2000071521 = b2000071521;
    }

    public BigDecimal getB2000071531() {
        return b2000071531;
    }

    public void setB2000071531(BigDecimal b2000071531) {
        this.b2000071531 = b2000071531;
    }

    public BigDecimal getB2000071532() {
        return b2000071532;
    }

    public void setB2000071532(BigDecimal b2000071532) {
        this.b2000071532 = b2000071532;
    }

    public BigDecimal getB2000071541() {
        return b2000071541;
    }

    public void setB2000071541(BigDecimal b2000071541) {
        this.b2000071541 = b2000071541;
    }

    public BigDecimal getB2000071601() {
        return b2000071601;
    }

    public void setB2000071601(BigDecimal b2000071601) {
        this.b2000071601 = b2000071601;
    }

    public BigDecimal getB2000071602() {
        return b2000071602;
    }

    public void setB2000071602(BigDecimal b2000071602) {
        this.b2000071602 = b2000071602;
    }

    public BigDecimal getB2000071603() {
        return b2000071603;
    }

    public void setB2000071603(BigDecimal b2000071603) {
        this.b2000071603 = b2000071603;
    }

    public BigDecimal getB2000071604() {
        return b2000071604;
    }

    public void setB2000071604(BigDecimal b2000071604) {
        this.b2000071604 = b2000071604;
    }

    public BigDecimal getB2000071605() {
        return b2000071605;
    }

    public void setB2000071605(BigDecimal b2000071605) {
        this.b2000071605 = b2000071605;
    }

    public BigDecimal getB2000071606() {
        return b2000071606;
    }

    public void setB2000071606(BigDecimal b2000071606) {
        this.b2000071606 = b2000071606;
    }

    public BigDecimal getB2000071611() {
        return b2000071611;
    }

    public void setB2000071611(BigDecimal b2000071611) {
        this.b2000071611 = b2000071611;
    }

    public BigDecimal getB2000071621() {
        return b2000071621;
    }

    public void setB2000071621(BigDecimal b2000071621) {
        this.b2000071621 = b2000071621;
    }

    public BigDecimal getB2000071622() {
        return b2000071622;
    }

    public void setB2000071622(BigDecimal b2000071622) {
        this.b2000071622 = b2000071622;
    }

    public BigDecimal getB2000071623() {
        return b2000071623;
    }

    public void setB2000071623(BigDecimal b2000071623) {
        this.b2000071623 = b2000071623;
    }

    public BigDecimal getB2000071631() {
        return b2000071631;
    }

    public void setB2000071631(BigDecimal b2000071631) {
        this.b2000071631 = b2000071631;
    }

    public BigDecimal getB2000071632() {
        return b2000071632;
    }

    public void setB2000071632(BigDecimal b2000071632) {
        this.b2000071632 = b2000071632;
    }

    public BigDecimal getB2000071701() {
        return b2000071701;
    }

    public void setB2000071701(BigDecimal b2000071701) {
        this.b2000071701 = b2000071701;
    }

    public BigDecimal getB2000071702() {
        return b2000071702;
    }

    public void setB2000071702(BigDecimal b2000071702) {
        this.b2000071702 = b2000071702;
    }

    public BigDecimal getB2000071703() {
        return b2000071703;
    }

    public void setB2000071703(BigDecimal b2000071703) {
        this.b2000071703 = b2000071703;
    }

    public BigDecimal getB2000071711() {
        return b2000071711;
    }

    public void setB2000071711(BigDecimal b2000071711) {
        this.b2000071711 = b2000071711;
    }

    public BigDecimal getB2000071801() {
        return b2000071801;
    }

    public void setB2000071801(BigDecimal b2000071801) {
        this.b2000071801 = b2000071801;
    }

    public BigDecimal getB2000071811() {
        return b2000071811;
    }

    public void setB2000071811(BigDecimal b2000071811) {
        this.b2000071811 = b2000071811;
    }

    public BigDecimal getB2000071821() {
        return b2000071821;
    }

    public void setB2000071821(BigDecimal b2000071821) {
        this.b2000071821 = b2000071821;
    }

    public BigDecimal getB2000071901() {
        return b2000071901;
    }

    public void setB2000071901(BigDecimal b2000071901) {
        this.b2000071901 = b2000071901;
    }

    public BigDecimal getB2000072001() {
        return b2000072001;
    }

    public void setB2000072001(BigDecimal b2000072001) {
        this.b2000072001 = b2000072001;
    }

    public BigDecimal getB2000072002() {
        return b2000072002;
    }

    public void setB2000072002(BigDecimal b2000072002) {
        this.b2000072002 = b2000072002;
    }

    public BigDecimal getB2000072003() {
        return b2000072003;
    }

    public void setB2000072003(BigDecimal b2000072003) {
        this.b2000072003 = b2000072003;
    }

    public BigDecimal getB2000072004() {
        return b2000072004;
    }

    public void setB2000072004(BigDecimal b2000072004) {
        this.b2000072004 = b2000072004;
    }

    public BigDecimal getB2000072011() {
        return b2000072011;
    }

    public void setB2000072011(BigDecimal b2000072011) {
        this.b2000072011 = b2000072011;
    }

    public BigDecimal getB2000072012() {
        return b2000072012;
    }

    public void setB2000072012(BigDecimal b2000072012) {
        this.b2000072012 = b2000072012;
    }

    public BigDecimal getB2000072021() {
        return b2000072021;
    }

    public void setB2000072021(BigDecimal b2000072021) {
        this.b2000072021 = b2000072021;
    }

    public BigDecimal getB2000072101() {
        return b2000072101;
    }

    public void setB2000072101(BigDecimal b2000072101) {
        this.b2000072101 = b2000072101;
    }

    public BigDecimal getB2000072111() {
        return b2000072111;
    }

    public void setB2000072111(BigDecimal b2000072111) {
        this.b2000072111 = b2000072111;
    }

    public BigDecimal getB2000072201() {
        return b2000072201;
    }

    public void setB2000072201(BigDecimal b2000072201) {
        this.b2000072201 = b2000072201;
    }

    public BigDecimal getB2000072202() {
        return b2000072202;
    }

    public void setB2000072202(BigDecimal b2000072202) {
        this.b2000072202 = b2000072202;
    }

    public BigDecimal getB2000072203() {
        return b2000072203;
    }

    public void setB2000072203(BigDecimal b2000072203) {
        this.b2000072203 = b2000072203;
    }

    public BigDecimal getB2000072204() {
        return b2000072204;
    }

    public void setB2000072204(BigDecimal b2000072204) {
        this.b2000072204 = b2000072204;
    }

    public BigDecimal getB2000072211() {
        return b2000072211;
    }

    public void setB2000072211(BigDecimal b2000072211) {
        this.b2000072211 = b2000072211;
    }

    public BigDecimal getB200007221101() {
        return b200007221101;
    }

    public void setB200007221101(BigDecimal b200007221101) {
        this.b200007221101 = b200007221101;
    }

    public BigDecimal getB200007221102() {
        return b200007221102;
    }

    public void setB200007221102(BigDecimal b200007221102) {
        this.b200007221102 = b200007221102;
    }

    public BigDecimal getB200007221103() {
        return b200007221103;
    }

    public void setB200007221103(BigDecimal b200007221103) {
        this.b200007221103 = b200007221103;
    }

    public BigDecimal getB200007221104() {
        return b200007221104;
    }

    public void setB200007221104(BigDecimal b200007221104) {
        this.b200007221104 = b200007221104;
    }

    public BigDecimal getB200007221105() {
        return b200007221105;
    }

    public void setB200007221105(BigDecimal b200007221105) {
        this.b200007221105 = b200007221105;
    }

    public BigDecimal getB200007221106() {
        return b200007221106;
    }

    public void setB200007221106(BigDecimal b200007221106) {
        this.b200007221106 = b200007221106;
    }

    public BigDecimal getB200007221107() {
        return b200007221107;
    }

    public void setB200007221107(BigDecimal b200007221107) {
        this.b200007221107 = b200007221107;
    }

    public BigDecimal getB200007221108() {
        return b200007221108;
    }

    public void setB200007221108(BigDecimal b200007221108) {
        this.b200007221108 = b200007221108;
    }

    public BigDecimal getB200007221109() {
        return b200007221109;
    }

    public void setB200007221109(BigDecimal b200007221109) {
        this.b200007221109 = b200007221109;
    }

    public BigDecimal getB200007221110() {
        return b200007221110;
    }

    public void setB200007221110(BigDecimal b200007221110) {
        this.b200007221110 = b200007221110;
    }

    public BigDecimal getB200007221111() {
        return b200007221111;
    }

    public void setB200007221111(BigDecimal b200007221111) {
        this.b200007221111 = b200007221111;
    }

    public BigDecimal getB200007221112() {
        return b200007221112;
    }

    public void setB200007221112(BigDecimal b200007221112) {
        this.b200007221112 = b200007221112;
    }

    public BigDecimal getB200007221113() {
        return b200007221113;
    }

    public void setB200007221113(BigDecimal b200007221113) {
        this.b200007221113 = b200007221113;
    }

    public BigDecimal getB2000072221() {
        return b2000072221;
    }

    public void setB2000072221(BigDecimal b2000072221) {
        this.b2000072221 = b2000072221;
    }

    public BigDecimal getB200007222101() {
        return b200007222101;
    }

    public void setB200007222101(BigDecimal b200007222101) {
        this.b200007222101 = b200007222101;
    }

    public BigDecimal getB20000722210101() {
        return b20000722210101;
    }

    public void setB20000722210101(BigDecimal b20000722210101) {
        this.b20000722210101 = b20000722210101;
    }

    public BigDecimal getB20000722210102() {
        return b20000722210102;
    }

    public void setB20000722210102(BigDecimal b20000722210102) {
        this.b20000722210102 = b20000722210102;
    }

    public BigDecimal getB20000722210103() {
        return b20000722210103;
    }

    public void setB20000722210103(BigDecimal b20000722210103) {
        this.b20000722210103 = b20000722210103;
    }

    public BigDecimal getB20000722210104() {
        return b20000722210104;
    }

    public void setB20000722210104(BigDecimal b20000722210104) {
        this.b20000722210104 = b20000722210104;
    }

    public BigDecimal getB20000722210105() {
        return b20000722210105;
    }

    public void setB20000722210105(BigDecimal b20000722210105) {
        this.b20000722210105 = b20000722210105;
    }

    public BigDecimal getB20000722210106() {
        return b20000722210106;
    }

    public void setB20000722210106(BigDecimal b20000722210106) {
        this.b20000722210106 = b20000722210106;
    }

    public BigDecimal getB20000722210107() {
        return b20000722210107;
    }

    public void setB20000722210107(BigDecimal b20000722210107) {
        this.b20000722210107 = b20000722210107;
    }

    public BigDecimal getB20000722210108() {
        return b20000722210108;
    }

    public void setB20000722210108(BigDecimal b20000722210108) {
        this.b20000722210108 = b20000722210108;
    }

    public BigDecimal getB20000722210109() {
        return b20000722210109;
    }

    public void setB20000722210109(BigDecimal b20000722210109) {
        this.b20000722210109 = b20000722210109;
    }

    public BigDecimal getB20000722210110() {
        return b20000722210110;
    }

    public void setB20000722210110(BigDecimal b20000722210110) {
        this.b20000722210110 = b20000722210110;
    }

    public BigDecimal getB200007222102() {
        return b200007222102;
    }

    public void setB200007222102(BigDecimal b200007222102) {
        this.b200007222102 = b200007222102;
    }

    public BigDecimal getB200007222103() {
        return b200007222103;
    }

    public void setB200007222103(BigDecimal b200007222103) {
        this.b200007222103 = b200007222103;
    }

    public BigDecimal getB200007222104() {
        return b200007222104;
    }

    public void setB200007222104(BigDecimal b200007222104) {
        this.b200007222104 = b200007222104;
    }

    public BigDecimal getB200007222105() {
        return b200007222105;
    }

    public void setB200007222105(BigDecimal b200007222105) {
        this.b200007222105 = b200007222105;
    }

    public BigDecimal getB200007222106() {
        return b200007222106;
    }

    public void setB200007222106(BigDecimal b200007222106) {
        this.b200007222106 = b200007222106;
    }

    public BigDecimal getB200007222107() {
        return b200007222107;
    }

    public void setB200007222107(BigDecimal b200007222107) {
        this.b200007222107 = b200007222107;
    }

    public BigDecimal getB200007222108() {
        return b200007222108;
    }

    public void setB200007222108(BigDecimal b200007222108) {
        this.b200007222108 = b200007222108;
    }

    public BigDecimal getB200007222109() {
        return b200007222109;
    }

    public void setB200007222109(BigDecimal b200007222109) {
        this.b200007222109 = b200007222109;
    }

    public BigDecimal getB200007222110() {
        return b200007222110;
    }

    public void setB200007222110(BigDecimal b200007222110) {
        this.b200007222110 = b200007222110;
    }

    public BigDecimal getB200007222111() {
        return b200007222111;
    }

    public void setB200007222111(BigDecimal b200007222111) {
        this.b200007222111 = b200007222111;
    }

    public BigDecimal getB200007222112() {
        return b200007222112;
    }

    public void setB200007222112(BigDecimal b200007222112) {
        this.b200007222112 = b200007222112;
    }

    public BigDecimal getB200007222113() {
        return b200007222113;
    }

    public void setB200007222113(BigDecimal b200007222113) {
        this.b200007222113 = b200007222113;
    }

    public BigDecimal getB200007222114() {
        return b200007222114;
    }

    public void setB200007222114(BigDecimal b200007222114) {
        this.b200007222114 = b200007222114;
    }

    public BigDecimal getB200007222115() {
        return b200007222115;
    }

    public void setB200007222115(BigDecimal b200007222115) {
        this.b200007222115 = b200007222115;
    }

    public BigDecimal getB200007222116() {
        return b200007222116;
    }

    public void setB200007222116(BigDecimal b200007222116) {
        this.b200007222116 = b200007222116;
    }

    public BigDecimal getB200007222117() {
        return b200007222117;
    }

    public void setB200007222117(BigDecimal b200007222117) {
        this.b200007222117 = b200007222117;
    }

    public BigDecimal getB200007222118() {
        return b200007222118;
    }

    public void setB200007222118(BigDecimal b200007222118) {
        this.b200007222118 = b200007222118;
    }

    public BigDecimal getB200007222119() {
        return b200007222119;
    }

    public void setB200007222119(BigDecimal b200007222119) {
        this.b200007222119 = b200007222119;
    }

    public BigDecimal getB200007222120() {
        return b200007222120;
    }

    public void setB200007222120(BigDecimal b200007222120) {
        this.b200007222120 = b200007222120;
    }

    public BigDecimal getB200007222121() {
        return b200007222121;
    }

    public void setB200007222121(BigDecimal b200007222121) {
        this.b200007222121 = b200007222121;
    }

    public BigDecimal getB200007222122() {
        return b200007222122;
    }

    public void setB200007222122(BigDecimal b200007222122) {
        this.b200007222122 = b200007222122;
    }

    public BigDecimal getB200007222123() {
        return b200007222123;
    }

    public void setB200007222123(BigDecimal b200007222123) {
        this.b200007222123 = b200007222123;
    }

    public BigDecimal getB2000072231() {
        return b2000072231;
    }

    public void setB2000072231(BigDecimal b2000072231) {
        this.b2000072231 = b2000072231;
    }

    public BigDecimal getB2000072232() {
        return b2000072232;
    }

    public void setB2000072232(BigDecimal b2000072232) {
        this.b2000072232 = b2000072232;
    }

    public BigDecimal getB2000072241() {
        return b2000072241;
    }

    public void setB2000072241(BigDecimal b2000072241) {
        this.b2000072241 = b2000072241;
    }

    public BigDecimal getB2000072251() {
        return b2000072251;
    }

    public void setB2000072251(BigDecimal b2000072251) {
        this.b2000072251 = b2000072251;
    }

    public BigDecimal getB2000072261() {
        return b2000072261;
    }

    public void setB2000072261(BigDecimal b2000072261) {
        this.b2000072261 = b2000072261;
    }

    public BigDecimal getB2000072311() {
        return b2000072311;
    }

    public void setB2000072311(BigDecimal b2000072311) {
        this.b2000072311 = b2000072311;
    }

    public BigDecimal getB2000072312() {
        return b2000072312;
    }

    public void setB2000072312(BigDecimal b2000072312) {
        this.b2000072312 = b2000072312;
    }

    public BigDecimal getB2000072313() {
        return b2000072313;
    }

    public void setB2000072313(BigDecimal b2000072313) {
        this.b2000072313 = b2000072313;
    }

    public BigDecimal getB2000072314() {
        return b2000072314;
    }

    public void setB2000072314(BigDecimal b2000072314) {
        this.b2000072314 = b2000072314;
    }

    public BigDecimal getB2000072401() {
        return b2000072401;
    }

    public void setB2000072401(BigDecimal b2000072401) {
        this.b2000072401 = b2000072401;
    }

    public BigDecimal getB2000072245() {
        return b2000072245;
    }

    public void setB2000072245(BigDecimal b2000072245) {
        this.b2000072245 = b2000072245;
    }

    public BigDecimal getB2000072501() {
        return b2000072501;
    }

    public void setB2000072501(BigDecimal b2000072501) {
        this.b2000072501 = b2000072501;
    }

    public BigDecimal getB2000072502() {
        return b2000072502;
    }

    public void setB2000072502(BigDecimal b2000072502) {
        this.b2000072502 = b2000072502;
    }

    public BigDecimal getB2000072601() {
        return b2000072601;
    }

    public void setB2000072601(BigDecimal b2000072601) {
        this.b2000072601 = b2000072601;
    }

    public BigDecimal getB2000072602() {
        return b2000072602;
    }

    public void setB2000072602(BigDecimal b2000072602) {
        this.b2000072602 = b2000072602;
    }

    public BigDecimal getB2000072611() {
        return b2000072611;
    }

    public void setB2000072611(BigDecimal b2000072611) {
        this.b2000072611 = b2000072611;
    }

    public BigDecimal getB2000072621() {
        return b2000072621;
    }

    public void setB2000072621(BigDecimal b2000072621) {
        this.b2000072621 = b2000072621;
    }

    public BigDecimal getB2000072701() {
        return b2000072701;
    }

    public void setB2000072701(BigDecimal b2000072701) {
        this.b2000072701 = b2000072701;
    }

    public BigDecimal getB2000072702() {
        return b2000072702;
    }

    public void setB2000072702(BigDecimal b2000072702) {
        this.b2000072702 = b2000072702;
    }

    public BigDecimal getB2000072711() {
        return b2000072711;
    }

    public void setB2000072711(BigDecimal b2000072711) {
        this.b2000072711 = b2000072711;
    }

    public BigDecimal getB2000072801() {
        return b2000072801;
    }

    public void setB2000072801(BigDecimal b2000072801) {
        this.b2000072801 = b2000072801;
    }

    public BigDecimal getB2000072901() {
        return b2000072901;
    }

    public void setB2000072901(BigDecimal b2000072901) {
        this.b2000072901 = b2000072901;
    }

    public BigDecimal getB2000073001() {
        return b2000073001;
    }

    public void setB2000073001(BigDecimal b2000073001) {
        this.b2000073001 = b2000073001;
    }

    public BigDecimal getB2000073002() {
        return b2000073002;
    }

    public void setB2000073002(BigDecimal b2000073002) {
        this.b2000073002 = b2000073002;
    }

    public BigDecimal getB2000073101() {
        return b2000073101;
    }

    public void setB2000073101(BigDecimal b2000073101) {
        this.b2000073101 = b2000073101;
    }

    public BigDecimal getB2000073201() {
        return b2000073201;
    }

    public void setB2000073201(BigDecimal b2000073201) {
        this.b2000073201 = b2000073201;
    }

    public BigDecimal getB2000073202() {
        return b2000073202;
    }

    public void setB2000073202(BigDecimal b2000073202) {
        this.b2000073202 = b2000073202;
    }

    public BigDecimal getB2000074001() {
        return b2000074001;
    }

    public void setB2000074001(BigDecimal b2000074001) {
        this.b2000074001 = b2000074001;
    }

    public BigDecimal getB2000074002() {
        return b2000074002;
    }

    public void setB2000074002(BigDecimal b2000074002) {
        this.b2000074002 = b2000074002;
    }

    public BigDecimal getB2000074003() {
        return b2000074003;
    }

    public void setB2000074003(BigDecimal b2000074003) {
        this.b2000074003 = b2000074003;
    }

    public BigDecimal getB2000074101() {
        return b2000074101;
    }

    public void setB2000074101(BigDecimal b2000074101) {
        this.b2000074101 = b2000074101;
    }

    public BigDecimal getB2000074102() {
        return b2000074102;
    }

    public void setB2000074102(BigDecimal b2000074102) {
        this.b2000074102 = b2000074102;
    }

    public BigDecimal getB2000074103() {
        return b2000074103;
    }

    public void setB2000074103(BigDecimal b2000074103) {
        this.b2000074103 = b2000074103;
    }

    public BigDecimal getB2000074104() {
        return b2000074104;
    }

    public void setB2000074104(BigDecimal b2000074104) {
        this.b2000074104 = b2000074104;
    }

    public BigDecimal getB2000074201() {
        return b2000074201;
    }

    public void setB2000074201(BigDecimal b2000074201) {
        this.b2000074201 = b2000074201;
    }

    public BigDecimal getB2000074301() {
        return b2000074301;
    }

    public void setB2000074301(BigDecimal b2000074301) {
        this.b2000074301 = b2000074301;
    }

    public BigDecimal getB2000075001() {
        return b2000075001;
    }

    public void setB2000075001(BigDecimal b2000075001) {
        this.b2000075001 = b2000075001;
    }

    public BigDecimal getB200007500101() {
        return b200007500101;
    }

    public void setB200007500101(BigDecimal b200007500101) {
        this.b200007500101 = b200007500101;
    }

    public BigDecimal getB200007500102() {
        return b200007500102;
    }

    public void setB200007500102(BigDecimal b200007500102) {
        this.b200007500102 = b200007500102;
    }

    public BigDecimal getB200007500103() {
        return b200007500103;
    }

    public void setB200007500103(BigDecimal b200007500103) {
        this.b200007500103 = b200007500103;
    }

    public BigDecimal getB200007500104() {
        return b200007500104;
    }

    public void setB200007500104(BigDecimal b200007500104) {
        this.b200007500104 = b200007500104;
    }

    public BigDecimal getB200007500105() {
        return b200007500105;
    }

    public void setB200007500105(BigDecimal b200007500105) {
        this.b200007500105 = b200007500105;
    }

    public BigDecimal getB200007500106() {
        return b200007500106;
    }

    public void setB200007500106(BigDecimal b200007500106) {
        this.b200007500106 = b200007500106;
    }

    public BigDecimal getB200007500107() {
        return b200007500107;
    }

    public void setB200007500107(BigDecimal b200007500107) {
        this.b200007500107 = b200007500107;
    }

    public BigDecimal getB200007500108() {
        return b200007500108;
    }

    public void setB200007500108(BigDecimal b200007500108) {
        this.b200007500108 = b200007500108;
    }

    public BigDecimal getB200007500109() {
        return b200007500109;
    }

    public void setB200007500109(BigDecimal b200007500109) {
        this.b200007500109 = b200007500109;
    }

    public BigDecimal getB200007500110() {
        return b200007500110;
    }

    public void setB200007500110(BigDecimal b200007500110) {
        this.b200007500110 = b200007500110;
    }

    public BigDecimal getB200007500111() {
        return b200007500111;
    }

    public void setB200007500111(BigDecimal b200007500111) {
        this.b200007500111 = b200007500111;
    }

    public BigDecimal getB200007500112() {
        return b200007500112;
    }

    public void setB200007500112(BigDecimal b200007500112) {
        this.b200007500112 = b200007500112;
    }

    public BigDecimal getB200007500113() {
        return b200007500113;
    }

    public void setB200007500113(BigDecimal b200007500113) {
        this.b200007500113 = b200007500113;
    }

    public BigDecimal getB200007500114() {
        return b200007500114;
    }

    public void setB200007500114(BigDecimal b200007500114) {
        this.b200007500114 = b200007500114;
    }

    public BigDecimal getB200007500115() {
        return b200007500115;
    }

    public void setB200007500115(BigDecimal b200007500115) {
        this.b200007500115 = b200007500115;
    }

    public BigDecimal getB200007500116() {
        return b200007500116;
    }

    public void setB200007500116(BigDecimal b200007500116) {
        this.b200007500116 = b200007500116;
    }

    public BigDecimal getB200007500117() {
        return b200007500117;
    }

    public void setB200007500117(BigDecimal b200007500117) {
        this.b200007500117 = b200007500117;
    }

    public BigDecimal getB200007500118() {
        return b200007500118;
    }

    public void setB200007500118(BigDecimal b200007500118) {
        this.b200007500118 = b200007500118;
    }

    public BigDecimal getB2000075101() {
        return b2000075101;
    }

    public void setB2000075101(BigDecimal b2000075101) {
        this.b2000075101 = b2000075101;
    }

    public BigDecimal getB200007510101() {
        return b200007510101;
    }

    public void setB200007510101(BigDecimal b200007510101) {
        this.b200007510101 = b200007510101;
    }

    public BigDecimal getB200007510102() {
        return b200007510102;
    }

    public void setB200007510102(BigDecimal b200007510102) {
        this.b200007510102 = b200007510102;
    }

    public BigDecimal getB200007510103() {
        return b200007510103;
    }

    public void setB200007510103(BigDecimal b200007510103) {
        this.b200007510103 = b200007510103;
    }

    public BigDecimal getB200007510104() {
        return b200007510104;
    }

    public void setB200007510104(BigDecimal b200007510104) {
        this.b200007510104 = b200007510104;
    }

    public BigDecimal getB200007510105() {
        return b200007510105;
    }

    public void setB200007510105(BigDecimal b200007510105) {
        this.b200007510105 = b200007510105;
    }

    public BigDecimal getB200007510106() {
        return b200007510106;
    }

    public void setB200007510106(BigDecimal b200007510106) {
        this.b200007510106 = b200007510106;
    }

    public BigDecimal getB200007510107() {
        return b200007510107;
    }

    public void setB200007510107(BigDecimal b200007510107) {
        this.b200007510107 = b200007510107;
    }

    public BigDecimal getB200007510108() {
        return b200007510108;
    }

    public void setB200007510108(BigDecimal b200007510108) {
        this.b200007510108 = b200007510108;
    }

    public BigDecimal getB200007510109() {
        return b200007510109;
    }

    public void setB200007510109(BigDecimal b200007510109) {
        this.b200007510109 = b200007510109;
    }

    public BigDecimal getB200007510110() {
        return b200007510110;
    }

    public void setB200007510110(BigDecimal b200007510110) {
        this.b200007510110 = b200007510110;
    }

    public BigDecimal getB200007510111() {
        return b200007510111;
    }

    public void setB200007510111(BigDecimal b200007510111) {
        this.b200007510111 = b200007510111;
    }

    public BigDecimal getB200007510112() {
        return b200007510112;
    }

    public void setB200007510112(BigDecimal b200007510112) {
        this.b200007510112 = b200007510112;
    }

    public BigDecimal getB200007510113() {
        return b200007510113;
    }

    public void setB200007510113(BigDecimal b200007510113) {
        this.b200007510113 = b200007510113;
    }

    public BigDecimal getB200007510114() {
        return b200007510114;
    }

    public void setB200007510114(BigDecimal b200007510114) {
        this.b200007510114 = b200007510114;
    }

    public BigDecimal getB200007510115() {
        return b200007510115;
    }

    public void setB200007510115(BigDecimal b200007510115) {
        this.b200007510115 = b200007510115;
    }

    public BigDecimal getB200007510116() {
        return b200007510116;
    }

    public void setB200007510116(BigDecimal b200007510116) {
        this.b200007510116 = b200007510116;
    }

    public BigDecimal getB200007510117() {
        return b200007510117;
    }

    public void setB200007510117(BigDecimal b200007510117) {
        this.b200007510117 = b200007510117;
    }

    public BigDecimal getB200007510118() {
        return b200007510118;
    }

    public void setB200007510118(BigDecimal b200007510118) {
        this.b200007510118 = b200007510118;
    }

    public BigDecimal getB200007510119() {
        return b200007510119;
    }

    public void setB200007510119(BigDecimal b200007510119) {
        this.b200007510119 = b200007510119;
    }

    public BigDecimal getB200007510120() {
        return b200007510120;
    }

    public void setB200007510120(BigDecimal b200007510120) {
        this.b200007510120 = b200007510120;
    }

    public BigDecimal getB200007510121() {
        return b200007510121;
    }

    public void setB200007510121(BigDecimal b200007510121) {
        this.b200007510121 = b200007510121;
    }

    public BigDecimal getB200007510122() {
        return b200007510122;
    }

    public void setB200007510122(BigDecimal b200007510122) {
        this.b200007510122 = b200007510122;
    }

    public BigDecimal getB200007510123() {
        return b200007510123;
    }

    public void setB200007510123(BigDecimal b200007510123) {
        this.b200007510123 = b200007510123;
    }

    public BigDecimal getB200007510124() {
        return b200007510124;
    }

    public void setB200007510124(BigDecimal b200007510124) {
        this.b200007510124 = b200007510124;
    }

    public BigDecimal getB200007510125() {
        return b200007510125;
    }

    public void setB200007510125(BigDecimal b200007510125) {
        this.b200007510125 = b200007510125;
    }

    public BigDecimal getB200007510126() {
        return b200007510126;
    }

    public void setB200007510126(BigDecimal b200007510126) {
        this.b200007510126 = b200007510126;
    }

    public BigDecimal getB200007510127() {
        return b200007510127;
    }

    public void setB200007510127(BigDecimal b200007510127) {
        this.b200007510127 = b200007510127;
    }

    public BigDecimal getB200007510128() {
        return b200007510128;
    }

    public void setB200007510128(BigDecimal b200007510128) {
        this.b200007510128 = b200007510128;
    }

    public BigDecimal getB200007510129() {
        return b200007510129;
    }

    public void setB200007510129(BigDecimal b200007510129) {
        this.b200007510129 = b200007510129;
    }

    public BigDecimal getB200007510130() {
        return b200007510130;
    }

    public void setB200007510130(BigDecimal b200007510130) {
        this.b200007510130 = b200007510130;
    }

    public BigDecimal getB200007510131() {
        return b200007510131;
    }

    public void setB200007510131(BigDecimal b200007510131) {
        this.b200007510131 = b200007510131;
    }

    public BigDecimal getB2000075201() {
        return b2000075201;
    }

    public void setB2000075201(BigDecimal b2000075201) {
        this.b2000075201 = b2000075201;
    }

    public BigDecimal getB200007520101() {
        return b200007520101;
    }

    public void setB200007520101(BigDecimal b200007520101) {
        this.b200007520101 = b200007520101;
    }

    public BigDecimal getB200007520102() {
        return b200007520102;
    }

    public void setB200007520102(BigDecimal b200007520102) {
        this.b200007520102 = b200007520102;
    }

    public BigDecimal getB200007520103() {
        return b200007520103;
    }

    public void setB200007520103(BigDecimal b200007520103) {
        this.b200007520103 = b200007520103;
    }

    public BigDecimal getB200007520104() {
        return b200007520104;
    }

    public void setB200007520104(BigDecimal b200007520104) {
        this.b200007520104 = b200007520104;
    }

    public BigDecimal getB200007520105() {
        return b200007520105;
    }

    public void setB200007520105(BigDecimal b200007520105) {
        this.b200007520105 = b200007520105;
    }

    public BigDecimal getB200007520106() {
        return b200007520106;
    }

    public void setB200007520106(BigDecimal b200007520106) {
        this.b200007520106 = b200007520106;
    }

    public BigDecimal getB200007520107() {
        return b200007520107;
    }

    public void setB200007520107(BigDecimal b200007520107) {
        this.b200007520107 = b200007520107;
    }

    public BigDecimal getB200007520108() {
        return b200007520108;
    }

    public void setB200007520108(BigDecimal b200007520108) {
        this.b200007520108 = b200007520108;
    }

    public BigDecimal getB200007520109() {
        return b200007520109;
    }

    public void setB200007520109(BigDecimal b200007520109) {
        this.b200007520109 = b200007520109;
    }

    public BigDecimal getB200007520110() {
        return b200007520110;
    }

    public void setB200007520110(BigDecimal b200007520110) {
        this.b200007520110 = b200007520110;
    }

    public BigDecimal getB200007520111() {
        return b200007520111;
    }

    public void setB200007520111(BigDecimal b200007520111) {
        this.b200007520111 = b200007520111;
    }

    public BigDecimal getB200007520112() {
        return b200007520112;
    }

    public void setB200007520112(BigDecimal b200007520112) {
        this.b200007520112 = b200007520112;
    }

    public BigDecimal getB200007520113() {
        return b200007520113;
    }

    public void setB200007520113(BigDecimal b200007520113) {
        this.b200007520113 = b200007520113;
    }

    public BigDecimal getB200007520114() {
        return b200007520114;
    }

    public void setB200007520114(BigDecimal b200007520114) {
        this.b200007520114 = b200007520114;
    }

    public BigDecimal getB200007520115() {
        return b200007520115;
    }

    public void setB200007520115(BigDecimal b200007520115) {
        this.b200007520115 = b200007520115;
    }

    public BigDecimal getB2000075301() {
        return b2000075301;
    }

    public void setB2000075301(BigDecimal b2000075301) {
        this.b2000075301 = b2000075301;
    }

    public BigDecimal getB200007530101() {
        return b200007530101;
    }

    public void setB200007530101(BigDecimal b200007530101) {
        this.b200007530101 = b200007530101;
    }

    public BigDecimal getB20000753010101() {
        return b20000753010101;
    }

    public void setB20000753010101(BigDecimal b20000753010101) {
        this.b20000753010101 = b20000753010101;
    }

    public BigDecimal getB2000075301010101() {
        return b2000075301010101;
    }

    public void setB2000075301010101(BigDecimal b2000075301010101) {
        this.b2000075301010101 = b2000075301010101;
    }

    public BigDecimal getB2000075301010102() {
        return b2000075301010102;
    }

    public void setB2000075301010102(BigDecimal b2000075301010102) {
        this.b2000075301010102 = b2000075301010102;
    }

    public BigDecimal getB2000075301010103() {
        return b2000075301010103;
    }

    public void setB2000075301010103(BigDecimal b2000075301010103) {
        this.b2000075301010103 = b2000075301010103;
    }

    public BigDecimal getB2000075301010104() {
        return b2000075301010104;
    }

    public void setB2000075301010104(BigDecimal b2000075301010104) {
        this.b2000075301010104 = b2000075301010104;
    }

    public BigDecimal getB2000075301010105() {
        return b2000075301010105;
    }

    public void setB2000075301010105(BigDecimal b2000075301010105) {
        this.b2000075301010105 = b2000075301010105;
    }

    public BigDecimal getB2000075301010106() {
        return b2000075301010106;
    }

    public void setB2000075301010106(BigDecimal b2000075301010106) {
        this.b2000075301010106 = b2000075301010106;
    }

    public BigDecimal getB2000075301010107() {
        return b2000075301010107;
    }

    public void setB2000075301010107(BigDecimal b2000075301010107) {
        this.b2000075301010107 = b2000075301010107;
    }

    public BigDecimal getB2000075301010108() {
        return b2000075301010108;
    }

    public void setB2000075301010108(BigDecimal b2000075301010108) {
        this.b2000075301010108 = b2000075301010108;
    }

    public BigDecimal getB2000075301010109() {
        return b2000075301010109;
    }

    public void setB2000075301010109(BigDecimal b2000075301010109) {
        this.b2000075301010109 = b2000075301010109;
    }

    public BigDecimal getB2000075301010110() {
        return b2000075301010110;
    }

    public void setB2000075301010110(BigDecimal b2000075301010110) {
        this.b2000075301010110 = b2000075301010110;
    }

    public BigDecimal getB2000075301010111() {
        return b2000075301010111;
    }

    public void setB2000075301010111(BigDecimal b2000075301010111) {
        this.b2000075301010111 = b2000075301010111;
    }

    public BigDecimal getB2000075301010112() {
        return b2000075301010112;
    }

    public void setB2000075301010112(BigDecimal b2000075301010112) {
        this.b2000075301010112 = b2000075301010112;
    }

    public BigDecimal getB2000075301010113() {
        return b2000075301010113;
    }

    public void setB2000075301010113(BigDecimal b2000075301010113) {
        this.b2000075301010113 = b2000075301010113;
    }

    public BigDecimal getB20000753010102() {
        return b20000753010102;
    }

    public void setB20000753010102(BigDecimal b20000753010102) {
        this.b20000753010102 = b20000753010102;
    }

    public BigDecimal getB20000753010103() {
        return b20000753010103;
    }

    public void setB20000753010103(BigDecimal b20000753010103) {
        this.b20000753010103 = b20000753010103;
    }

    public BigDecimal getB2000075301010301() {
        return b2000075301010301;
    }

    public void setB2000075301010301(BigDecimal b2000075301010301) {
        this.b2000075301010301 = b2000075301010301;
    }

    public BigDecimal getB2000075301010302() {
        return b2000075301010302;
    }

    public void setB2000075301010302(BigDecimal b2000075301010302) {
        this.b2000075301010302 = b2000075301010302;
    }

    public BigDecimal getB2000075301010303() {
        return b2000075301010303;
    }

    public void setB2000075301010303(BigDecimal b2000075301010303) {
        this.b2000075301010303 = b2000075301010303;
    }

    public BigDecimal getB2000075301010304() {
        return b2000075301010304;
    }

    public void setB2000075301010304(BigDecimal b2000075301010304) {
        this.b2000075301010304 = b2000075301010304;
    }

    public BigDecimal getB2000075301010305() {
        return b2000075301010305;
    }

    public void setB2000075301010305(BigDecimal b2000075301010305) {
        this.b2000075301010305 = b2000075301010305;
    }

    public BigDecimal getB2000075301010306() {
        return b2000075301010306;
    }

    public void setB2000075301010306(BigDecimal b2000075301010306) {
        this.b2000075301010306 = b2000075301010306;
    }

    public BigDecimal getB2000075301010307() {
        return b2000075301010307;
    }

    public void setB2000075301010307(BigDecimal b2000075301010307) {
        this.b2000075301010307 = b2000075301010307;
    }

    public BigDecimal getB2000075301010308() {
        return b2000075301010308;
    }

    public void setB2000075301010308(BigDecimal b2000075301010308) {
        this.b2000075301010308 = b2000075301010308;
    }

    public BigDecimal getB2000075301010309() {
        return b2000075301010309;
    }

    public void setB2000075301010309(BigDecimal b2000075301010309) {
        this.b2000075301010309 = b2000075301010309;
    }

    public BigDecimal getB2000075301010310() {
        return b2000075301010310;
    }

    public void setB2000075301010310(BigDecimal b2000075301010310) {
        this.b2000075301010310 = b2000075301010310;
    }

    public BigDecimal getB2000075301010311() {
        return b2000075301010311;
    }

    public void setB2000075301010311(BigDecimal b2000075301010311) {
        this.b2000075301010311 = b2000075301010311;
    }

    public BigDecimal getB2000075301010312() {
        return b2000075301010312;
    }

    public void setB2000075301010312(BigDecimal b2000075301010312) {
        this.b2000075301010312 = b2000075301010312;
    }

    public BigDecimal getB2000075301010313() {
        return b2000075301010313;
    }

    public void setB2000075301010313(BigDecimal b2000075301010313) {
        this.b2000075301010313 = b2000075301010313;
    }

    public BigDecimal getB20000753010104() {
        return b20000753010104;
    }

    public void setB20000753010104(BigDecimal b20000753010104) {
        this.b20000753010104 = b20000753010104;
    }

    public BigDecimal getB2000075301010401() {
        return b2000075301010401;
    }

    public void setB2000075301010401(BigDecimal b2000075301010401) {
        this.b2000075301010401 = b2000075301010401;
    }

    public BigDecimal getB2000075301010402() {
        return b2000075301010402;
    }

    public void setB2000075301010402(BigDecimal b2000075301010402) {
        this.b2000075301010402 = b2000075301010402;
    }

    public BigDecimal getB20000753010105() {
        return b20000753010105;
    }

    public void setB20000753010105(BigDecimal b20000753010105) {
        this.b20000753010105 = b20000753010105;
    }

    public BigDecimal getB2000075301010501() {
        return b2000075301010501;
    }

    public void setB2000075301010501(BigDecimal b2000075301010501) {
        this.b2000075301010501 = b2000075301010501;
    }

    public BigDecimal getB2000075301010502() {
        return b2000075301010502;
    }

    public void setB2000075301010502(BigDecimal b2000075301010502) {
        this.b2000075301010502 = b2000075301010502;
    }

    public BigDecimal getB20000753010106() {
        return b20000753010106;
    }

    public void setB20000753010106(BigDecimal b20000753010106) {
        this.b20000753010106 = b20000753010106;
    }

    public BigDecimal getB2000075301010601() {
        return b2000075301010601;
    }

    public void setB2000075301010601(BigDecimal b2000075301010601) {
        this.b2000075301010601 = b2000075301010601;
    }

    public BigDecimal getB2000075301010602() {
        return b2000075301010602;
    }

    public void setB2000075301010602(BigDecimal b2000075301010602) {
        this.b2000075301010602 = b2000075301010602;
    }

    public BigDecimal getB2000075301010603() {
        return b2000075301010603;
    }

    public void setB2000075301010603(BigDecimal b2000075301010603) {
        this.b2000075301010603 = b2000075301010603;
    }

    public BigDecimal getB2000075301010604() {
        return b2000075301010604;
    }

    public void setB2000075301010604(BigDecimal b2000075301010604) {
        this.b2000075301010604 = b2000075301010604;
    }

    public BigDecimal getB20000753010107() {
        return b20000753010107;
    }

    public void setB20000753010107(BigDecimal b20000753010107) {
        this.b20000753010107 = b20000753010107;
    }

    public BigDecimal getB2000075301010701() {
        return b2000075301010701;
    }

    public void setB2000075301010701(BigDecimal b2000075301010701) {
        this.b2000075301010701 = b2000075301010701;
    }

    public BigDecimal getB2000075301010702() {
        return b2000075301010702;
    }

    public void setB2000075301010702(BigDecimal b2000075301010702) {
        this.b2000075301010702 = b2000075301010702;
    }

    public BigDecimal getB2000075301010703() {
        return b2000075301010703;
    }

    public void setB2000075301010703(BigDecimal b2000075301010703) {
        this.b2000075301010703 = b2000075301010703;
    }

    public BigDecimal getB20000753010108() {
        return b20000753010108;
    }

    public void setB20000753010108(BigDecimal b20000753010108) {
        this.b20000753010108 = b20000753010108;
    }

    public BigDecimal getB2000075301010801() {
        return b2000075301010801;
    }

    public void setB2000075301010801(BigDecimal b2000075301010801) {
        this.b2000075301010801 = b2000075301010801;
    }

    public BigDecimal getB2000075301010802() {
        return b2000075301010802;
    }

    public void setB2000075301010802(BigDecimal b2000075301010802) {
        this.b2000075301010802 = b2000075301010802;
    }

    public BigDecimal getB2000075301010803() {
        return b2000075301010803;
    }

    public void setB2000075301010803(BigDecimal b2000075301010803) {
        this.b2000075301010803 = b2000075301010803;
    }

    public BigDecimal getB2000075301010804() {
        return b2000075301010804;
    }

    public void setB2000075301010804(BigDecimal b2000075301010804) {
        this.b2000075301010804 = b2000075301010804;
    }

    public BigDecimal getB2000075301010805() {
        return b2000075301010805;
    }

    public void setB2000075301010805(BigDecimal b2000075301010805) {
        this.b2000075301010805 = b2000075301010805;
    }

    public BigDecimal getB2000075301010806() {
        return b2000075301010806;
    }

    public void setB2000075301010806(BigDecimal b2000075301010806) {
        this.b2000075301010806 = b2000075301010806;
    }

    public BigDecimal getB20000753010109() {
        return b20000753010109;
    }

    public void setB20000753010109(BigDecimal b20000753010109) {
        this.b20000753010109 = b20000753010109;
    }

    public BigDecimal getB2000075301010901() {
        return b2000075301010901;
    }

    public void setB2000075301010901(BigDecimal b2000075301010901) {
        this.b2000075301010901 = b2000075301010901;
    }

    public BigDecimal getB2000075301010902() {
        return b2000075301010902;
    }

    public void setB2000075301010902(BigDecimal b2000075301010902) {
        this.b2000075301010902 = b2000075301010902;
    }

    public BigDecimal getB2000075301010903() {
        return b2000075301010903;
    }

    public void setB2000075301010903(BigDecimal b2000075301010903) {
        this.b2000075301010903 = b2000075301010903;
    }

    public BigDecimal getB2000075301010904() {
        return b2000075301010904;
    }

    public void setB2000075301010904(BigDecimal b2000075301010904) {
        this.b2000075301010904 = b2000075301010904;
    }

    public BigDecimal getB2000075301010905() {
        return b2000075301010905;
    }

    public void setB2000075301010905(BigDecimal b2000075301010905) {
        this.b2000075301010905 = b2000075301010905;
    }

    public BigDecimal getB2000075301010906() {
        return b2000075301010906;
    }

    public void setB2000075301010906(BigDecimal b2000075301010906) {
        this.b2000075301010906 = b2000075301010906;
    }

    public BigDecimal getB2000075301010907() {
        return b2000075301010907;
    }

    public void setB2000075301010907(BigDecimal b2000075301010907) {
        this.b2000075301010907 = b2000075301010907;
    }

    public BigDecimal getB20000753010110() {
        return b20000753010110;
    }

    public void setB20000753010110(BigDecimal b20000753010110) {
        this.b20000753010110 = b20000753010110;
    }

    public BigDecimal getB20000753010111() {
        return b20000753010111;
    }

    public void setB20000753010111(BigDecimal b20000753010111) {
        this.b20000753010111 = b20000753010111;
    }

    public BigDecimal getB2000075301011101() {
        return b2000075301011101;
    }

    public void setB2000075301011101(BigDecimal b2000075301011101) {
        this.b2000075301011101 = b2000075301011101;
    }

    public BigDecimal getB2000075301011102() {
        return b2000075301011102;
    }

    public void setB2000075301011102(BigDecimal b2000075301011102) {
        this.b2000075301011102 = b2000075301011102;
    }

    public BigDecimal getB2000075301011103() {
        return b2000075301011103;
    }

    public void setB2000075301011103(BigDecimal b2000075301011103) {
        this.b2000075301011103 = b2000075301011103;
    }

    public BigDecimal getB2000075301011104() {
        return b2000075301011104;
    }

    public void setB2000075301011104(BigDecimal b2000075301011104) {
        this.b2000075301011104 = b2000075301011104;
    }

    public BigDecimal getB20000753010112() {
        return b20000753010112;
    }

    public void setB20000753010112(BigDecimal b20000753010112) {
        this.b20000753010112 = b20000753010112;
    }

    public BigDecimal getB20000753010113() {
        return b20000753010113;
    }

    public void setB20000753010113(BigDecimal b20000753010113) {
        this.b20000753010113 = b20000753010113;
    }

    public BigDecimal getB20000753010114() {
        return b20000753010114;
    }

    public void setB20000753010114(BigDecimal b20000753010114) {
        this.b20000753010114 = b20000753010114;
    }

    public BigDecimal getB20000753010115() {
        return b20000753010115;
    }

    public void setB20000753010115(BigDecimal b20000753010115) {
        this.b20000753010115 = b20000753010115;
    }

    public BigDecimal getB20000753010116() {
        return b20000753010116;
    }

    public void setB20000753010116(BigDecimal b20000753010116) {
        this.b20000753010116 = b20000753010116;
    }

    public BigDecimal getB2000075301011601() {
        return b2000075301011601;
    }

    public void setB2000075301011601(BigDecimal b2000075301011601) {
        this.b2000075301011601 = b2000075301011601;
    }

    public BigDecimal getB2000075301011602() {
        return b2000075301011602;
    }

    public void setB2000075301011602(BigDecimal b2000075301011602) {
        this.b2000075301011602 = b2000075301011602;
    }

    public BigDecimal getB2000075301011603() {
        return b2000075301011603;
    }

    public void setB2000075301011603(BigDecimal b2000075301011603) {
        this.b2000075301011603 = b2000075301011603;
    }

    public BigDecimal getB2000075301011604() {
        return b2000075301011604;
    }

    public void setB2000075301011604(BigDecimal b2000075301011604) {
        this.b2000075301011604 = b2000075301011604;
    }

    public BigDecimal getB2000075301011605() {
        return b2000075301011605;
    }

    public void setB2000075301011605(BigDecimal b2000075301011605) {
        this.b2000075301011605 = b2000075301011605;
    }

    public BigDecimal getB2000075301011606() {
        return b2000075301011606;
    }

    public void setB2000075301011606(BigDecimal b2000075301011606) {
        this.b2000075301011606 = b2000075301011606;
    }

    public BigDecimal getB2000075301011607() {
        return b2000075301011607;
    }

    public void setB2000075301011607(BigDecimal b2000075301011607) {
        this.b2000075301011607 = b2000075301011607;
    }

    public BigDecimal getB2000075301011608() {
        return b2000075301011608;
    }

    public void setB2000075301011608(BigDecimal b2000075301011608) {
        this.b2000075301011608 = b2000075301011608;
    }

    public BigDecimal getB2000075301011609() {
        return b2000075301011609;
    }

    public void setB2000075301011609(BigDecimal b2000075301011609) {
        this.b2000075301011609 = b2000075301011609;
    }

    public BigDecimal getB20000753010117() {
        return b20000753010117;
    }

    public void setB20000753010117(BigDecimal b20000753010117) {
        this.b20000753010117 = b20000753010117;
    }

    public BigDecimal getB2000075301011701() {
        return b2000075301011701;
    }

    public void setB2000075301011701(BigDecimal b2000075301011701) {
        this.b2000075301011701 = b2000075301011701;
    }

    public BigDecimal getB2000075301011702() {
        return b2000075301011702;
    }

    public void setB2000075301011702(BigDecimal b2000075301011702) {
        this.b2000075301011702 = b2000075301011702;
    }

    public BigDecimal getB2000075301011703() {
        return b2000075301011703;
    }

    public void setB2000075301011703(BigDecimal b2000075301011703) {
        this.b2000075301011703 = b2000075301011703;
    }

    public BigDecimal getB2000075301011704() {
        return b2000075301011704;
    }

    public void setB2000075301011704(BigDecimal b2000075301011704) {
        this.b2000075301011704 = b2000075301011704;
    }

    public BigDecimal getB20000753010118() {
        return b20000753010118;
    }

    public void setB20000753010118(BigDecimal b20000753010118) {
        this.b20000753010118 = b20000753010118;
    }

    public BigDecimal getB20000753010119() {
        return b20000753010119;
    }

    public void setB20000753010119(BigDecimal b20000753010119) {
        this.b20000753010119 = b20000753010119;
    }

    public BigDecimal getB20000753010120() {
        return b20000753010120;
    }

    public void setB20000753010120(BigDecimal b20000753010120) {
        this.b20000753010120 = b20000753010120;
    }

    public BigDecimal getB20000753010121() {
        return b20000753010121;
    }

    public void setB20000753010121(BigDecimal b20000753010121) {
        this.b20000753010121 = b20000753010121;
    }

    public BigDecimal getB20000753010122() {
        return b20000753010122;
    }

    public void setB20000753010122(BigDecimal b20000753010122) {
        this.b20000753010122 = b20000753010122;
    }

    public BigDecimal getB20000753010123() {
        return b20000753010123;
    }

    public void setB20000753010123(BigDecimal b20000753010123) {
        this.b20000753010123 = b20000753010123;
    }

    public BigDecimal getB20000753010124() {
        return b20000753010124;
    }

    public void setB20000753010124(BigDecimal b20000753010124) {
        this.b20000753010124 = b20000753010124;
    }

    public BigDecimal getB20000753010125() {
        return b20000753010125;
    }

    public void setB20000753010125(BigDecimal b20000753010125) {
        this.b20000753010125 = b20000753010125;
    }

    public BigDecimal getB20000753010126() {
        return b20000753010126;
    }

    public void setB20000753010126(BigDecimal b20000753010126) {
        this.b20000753010126 = b20000753010126;
    }

    public BigDecimal getB2000075301012601() {
        return b2000075301012601;
    }

    public void setB2000075301012601(BigDecimal b2000075301012601) {
        this.b2000075301012601 = b2000075301012601;
    }

    public BigDecimal getB2000075301012602() {
        return b2000075301012602;
    }

    public void setB2000075301012602(BigDecimal b2000075301012602) {
        this.b2000075301012602 = b2000075301012602;
    }

    public BigDecimal getB2000075301012603() {
        return b2000075301012603;
    }

    public void setB2000075301012603(BigDecimal b2000075301012603) {
        this.b2000075301012603 = b2000075301012603;
    }

    public BigDecimal getB2000075301012604() {
        return b2000075301012604;
    }

    public void setB2000075301012604(BigDecimal b2000075301012604) {
        this.b2000075301012604 = b2000075301012604;
    }

    public BigDecimal getB200007530102() {
        return b200007530102;
    }

    public void setB200007530102(BigDecimal b200007530102) {
        this.b200007530102 = b200007530102;
    }

    public BigDecimal getB20000753010201() {
        return b20000753010201;
    }

    public void setB20000753010201(BigDecimal b20000753010201) {
        this.b20000753010201 = b20000753010201;
    }

    public BigDecimal getB2000075301020101() {
        return b2000075301020101;
    }

    public void setB2000075301020101(BigDecimal b2000075301020101) {
        this.b2000075301020101 = b2000075301020101;
    }

    public BigDecimal getB2000075301020102() {
        return b2000075301020102;
    }

    public void setB2000075301020102(BigDecimal b2000075301020102) {
        this.b2000075301020102 = b2000075301020102;
    }

    public BigDecimal getB2000075301020103() {
        return b2000075301020103;
    }

    public void setB2000075301020103(BigDecimal b2000075301020103) {
        this.b2000075301020103 = b2000075301020103;
    }

    public BigDecimal getB2000075301020104() {
        return b2000075301020104;
    }

    public void setB2000075301020104(BigDecimal b2000075301020104) {
        this.b2000075301020104 = b2000075301020104;
    }

    public BigDecimal getB2000075301020105() {
        return b2000075301020105;
    }

    public void setB2000075301020105(BigDecimal b2000075301020105) {
        this.b2000075301020105 = b2000075301020105;
    }

    public BigDecimal getB2000075301020106() {
        return b2000075301020106;
    }

    public void setB2000075301020106(BigDecimal b2000075301020106) {
        this.b2000075301020106 = b2000075301020106;
    }

    public BigDecimal getB2000075301020107() {
        return b2000075301020107;
    }

    public void setB2000075301020107(BigDecimal b2000075301020107) {
        this.b2000075301020107 = b2000075301020107;
    }

    public BigDecimal getB2000075301020108() {
        return b2000075301020108;
    }

    public void setB2000075301020108(BigDecimal b2000075301020108) {
        this.b2000075301020108 = b2000075301020108;
    }

    public BigDecimal getB2000075301020109() {
        return b2000075301020109;
    }

    public void setB2000075301020109(BigDecimal b2000075301020109) {
        this.b2000075301020109 = b2000075301020109;
    }

    public BigDecimal getB2000075301020110() {
        return b2000075301020110;
    }

    public void setB2000075301020110(BigDecimal b2000075301020110) {
        this.b2000075301020110 = b2000075301020110;
    }

    public BigDecimal getB2000075301020111() {
        return b2000075301020111;
    }

    public void setB2000075301020111(BigDecimal b2000075301020111) {
        this.b2000075301020111 = b2000075301020111;
    }

    public BigDecimal getB2000075301020112() {
        return b2000075301020112;
    }

    public void setB2000075301020112(BigDecimal b2000075301020112) {
        this.b2000075301020112 = b2000075301020112;
    }

    public BigDecimal getB2000075301020113() {
        return b2000075301020113;
    }

    public void setB2000075301020113(BigDecimal b2000075301020113) {
        this.b2000075301020113 = b2000075301020113;
    }

    public BigDecimal getB20000753010202() {
        return b20000753010202;
    }

    public void setB20000753010202(BigDecimal b20000753010202) {
        this.b20000753010202 = b20000753010202;
    }

    public BigDecimal getB20000753010203() {
        return b20000753010203;
    }

    public void setB20000753010203(BigDecimal b20000753010203) {
        this.b20000753010203 = b20000753010203;
    }

    public BigDecimal getB2000075301020301() {
        return b2000075301020301;
    }

    public void setB2000075301020301(BigDecimal b2000075301020301) {
        this.b2000075301020301 = b2000075301020301;
    }

    public BigDecimal getB2000075301020302() {
        return b2000075301020302;
    }

    public void setB2000075301020302(BigDecimal b2000075301020302) {
        this.b2000075301020302 = b2000075301020302;
    }

    public BigDecimal getB2000075301020303() {
        return b2000075301020303;
    }

    public void setB2000075301020303(BigDecimal b2000075301020303) {
        this.b2000075301020303 = b2000075301020303;
    }

    public BigDecimal getB2000075301020304() {
        return b2000075301020304;
    }

    public void setB2000075301020304(BigDecimal b2000075301020304) {
        this.b2000075301020304 = b2000075301020304;
    }

    public BigDecimal getB2000075301020305() {
        return b2000075301020305;
    }

    public void setB2000075301020305(BigDecimal b2000075301020305) {
        this.b2000075301020305 = b2000075301020305;
    }

    public BigDecimal getB2000075301020306() {
        return b2000075301020306;
    }

    public void setB2000075301020306(BigDecimal b2000075301020306) {
        this.b2000075301020306 = b2000075301020306;
    }

    public BigDecimal getB2000075301020307() {
        return b2000075301020307;
    }

    public void setB2000075301020307(BigDecimal b2000075301020307) {
        this.b2000075301020307 = b2000075301020307;
    }

    public BigDecimal getB2000075301020308() {
        return b2000075301020308;
    }

    public void setB2000075301020308(BigDecimal b2000075301020308) {
        this.b2000075301020308 = b2000075301020308;
    }

    public BigDecimal getB2000075301020309() {
        return b2000075301020309;
    }

    public void setB2000075301020309(BigDecimal b2000075301020309) {
        this.b2000075301020309 = b2000075301020309;
    }

    public BigDecimal getB2000075301020310() {
        return b2000075301020310;
    }

    public void setB2000075301020310(BigDecimal b2000075301020310) {
        this.b2000075301020310 = b2000075301020310;
    }

    public BigDecimal getB2000075301020311() {
        return b2000075301020311;
    }

    public void setB2000075301020311(BigDecimal b2000075301020311) {
        this.b2000075301020311 = b2000075301020311;
    }

    public BigDecimal getB2000075301020312() {
        return b2000075301020312;
    }

    public void setB2000075301020312(BigDecimal b2000075301020312) {
        this.b2000075301020312 = b2000075301020312;
    }

    public BigDecimal getB2000075301020313() {
        return b2000075301020313;
    }

    public void setB2000075301020313(BigDecimal b2000075301020313) {
        this.b2000075301020313 = b2000075301020313;
    }

    public BigDecimal getB20000753010204() {
        return b20000753010204;
    }

    public void setB20000753010204(BigDecimal b20000753010204) {
        this.b20000753010204 = b20000753010204;
    }

    public BigDecimal getB2000075301020401() {
        return b2000075301020401;
    }

    public void setB2000075301020401(BigDecimal b2000075301020401) {
        this.b2000075301020401 = b2000075301020401;
    }

    public BigDecimal getB2000075301020402() {
        return b2000075301020402;
    }

    public void setB2000075301020402(BigDecimal b2000075301020402) {
        this.b2000075301020402 = b2000075301020402;
    }

    public BigDecimal getB20000753010205() {
        return b20000753010205;
    }

    public void setB20000753010205(BigDecimal b20000753010205) {
        this.b20000753010205 = b20000753010205;
    }

    public BigDecimal getB2000075301020501() {
        return b2000075301020501;
    }

    public void setB2000075301020501(BigDecimal b2000075301020501) {
        this.b2000075301020501 = b2000075301020501;
    }

    public BigDecimal getB2000075301020502() {
        return b2000075301020502;
    }

    public void setB2000075301020502(BigDecimal b2000075301020502) {
        this.b2000075301020502 = b2000075301020502;
    }

    public BigDecimal getB20000753010206() {
        return b20000753010206;
    }

    public void setB20000753010206(BigDecimal b20000753010206) {
        this.b20000753010206 = b20000753010206;
    }

    public BigDecimal getB2000075301020601() {
        return b2000075301020601;
    }

    public void setB2000075301020601(BigDecimal b2000075301020601) {
        this.b2000075301020601 = b2000075301020601;
    }

    public BigDecimal getB2000075301020602() {
        return b2000075301020602;
    }

    public void setB2000075301020602(BigDecimal b2000075301020602) {
        this.b2000075301020602 = b2000075301020602;
    }

    public BigDecimal getB2000075301020603() {
        return b2000075301020603;
    }

    public void setB2000075301020603(BigDecimal b2000075301020603) {
        this.b2000075301020603 = b2000075301020603;
    }

    public BigDecimal getB2000075301020604() {
        return b2000075301020604;
    }

    public void setB2000075301020604(BigDecimal b2000075301020604) {
        this.b2000075301020604 = b2000075301020604;
    }

    public BigDecimal getB20000753010207() {
        return b20000753010207;
    }

    public void setB20000753010207(BigDecimal b20000753010207) {
        this.b20000753010207 = b20000753010207;
    }

    public BigDecimal getB2000075301020701() {
        return b2000075301020701;
    }

    public void setB2000075301020701(BigDecimal b2000075301020701) {
        this.b2000075301020701 = b2000075301020701;
    }

    public BigDecimal getB2000075301020702() {
        return b2000075301020702;
    }

    public void setB2000075301020702(BigDecimal b2000075301020702) {
        this.b2000075301020702 = b2000075301020702;
    }

    public BigDecimal getB2000075301020703() {
        return b2000075301020703;
    }

    public void setB2000075301020703(BigDecimal b2000075301020703) {
        this.b2000075301020703 = b2000075301020703;
    }

    public BigDecimal getB20000753010208() {
        return b20000753010208;
    }

    public void setB20000753010208(BigDecimal b20000753010208) {
        this.b20000753010208 = b20000753010208;
    }

    public BigDecimal getB2000075301020801() {
        return b2000075301020801;
    }

    public void setB2000075301020801(BigDecimal b2000075301020801) {
        this.b2000075301020801 = b2000075301020801;
    }

    public BigDecimal getB2000075301020802() {
        return b2000075301020802;
    }

    public void setB2000075301020802(BigDecimal b2000075301020802) {
        this.b2000075301020802 = b2000075301020802;
    }

    public BigDecimal getB2000075301020803() {
        return b2000075301020803;
    }

    public void setB2000075301020803(BigDecimal b2000075301020803) {
        this.b2000075301020803 = b2000075301020803;
    }

    public BigDecimal getB2000075301020804() {
        return b2000075301020804;
    }

    public void setB2000075301020804(BigDecimal b2000075301020804) {
        this.b2000075301020804 = b2000075301020804;
    }

    public BigDecimal getB2000075301020805() {
        return b2000075301020805;
    }

    public void setB2000075301020805(BigDecimal b2000075301020805) {
        this.b2000075301020805 = b2000075301020805;
    }

    public BigDecimal getB2000075301020806() {
        return b2000075301020806;
    }

    public void setB2000075301020806(BigDecimal b2000075301020806) {
        this.b2000075301020806 = b2000075301020806;
    }

    public BigDecimal getB20000753010209() {
        return b20000753010209;
    }

    public void setB20000753010209(BigDecimal b20000753010209) {
        this.b20000753010209 = b20000753010209;
    }

    public BigDecimal getB2000075301020901() {
        return b2000075301020901;
    }

    public void setB2000075301020901(BigDecimal b2000075301020901) {
        this.b2000075301020901 = b2000075301020901;
    }

    public BigDecimal getB2000075301020902() {
        return b2000075301020902;
    }

    public void setB2000075301020902(BigDecimal b2000075301020902) {
        this.b2000075301020902 = b2000075301020902;
    }

    public BigDecimal getB2000075301020903() {
        return b2000075301020903;
    }

    public void setB2000075301020903(BigDecimal b2000075301020903) {
        this.b2000075301020903 = b2000075301020903;
    }

    public BigDecimal getB2000075301020904() {
        return b2000075301020904;
    }

    public void setB2000075301020904(BigDecimal b2000075301020904) {
        this.b2000075301020904 = b2000075301020904;
    }

    public BigDecimal getB2000075301020905() {
        return b2000075301020905;
    }

    public void setB2000075301020905(BigDecimal b2000075301020905) {
        this.b2000075301020905 = b2000075301020905;
    }

    public BigDecimal getB2000075301020906() {
        return b2000075301020906;
    }

    public void setB2000075301020906(BigDecimal b2000075301020906) {
        this.b2000075301020906 = b2000075301020906;
    }

    public BigDecimal getB2000075301020907() {
        return b2000075301020907;
    }

    public void setB2000075301020907(BigDecimal b2000075301020907) {
        this.b2000075301020907 = b2000075301020907;
    }

    public BigDecimal getB20000753010210() {
        return b20000753010210;
    }

    public void setB20000753010210(BigDecimal b20000753010210) {
        this.b20000753010210 = b20000753010210;
    }

    public BigDecimal getB20000753010211() {
        return b20000753010211;
    }

    public void setB20000753010211(BigDecimal b20000753010211) {
        this.b20000753010211 = b20000753010211;
    }

    public BigDecimal getB2000075301021101() {
        return b2000075301021101;
    }

    public void setB2000075301021101(BigDecimal b2000075301021101) {
        this.b2000075301021101 = b2000075301021101;
    }

    public BigDecimal getB2000075301021102() {
        return b2000075301021102;
    }

    public void setB2000075301021102(BigDecimal b2000075301021102) {
        this.b2000075301021102 = b2000075301021102;
    }

    public BigDecimal getB2000075301021103() {
        return b2000075301021103;
    }

    public void setB2000075301021103(BigDecimal b2000075301021103) {
        this.b2000075301021103 = b2000075301021103;
    }

    public BigDecimal getB2000075301021104() {
        return b2000075301021104;
    }

    public void setB2000075301021104(BigDecimal b2000075301021104) {
        this.b2000075301021104 = b2000075301021104;
    }

    public BigDecimal getB20000753010212() {
        return b20000753010212;
    }

    public void setB20000753010212(BigDecimal b20000753010212) {
        this.b20000753010212 = b20000753010212;
    }

    public BigDecimal getB20000753010213() {
        return b20000753010213;
    }

    public void setB20000753010213(BigDecimal b20000753010213) {
        this.b20000753010213 = b20000753010213;
    }

    public BigDecimal getB20000753010214() {
        return b20000753010214;
    }

    public void setB20000753010214(BigDecimal b20000753010214) {
        this.b20000753010214 = b20000753010214;
    }

    public BigDecimal getB20000753010215() {
        return b20000753010215;
    }

    public void setB20000753010215(BigDecimal b20000753010215) {
        this.b20000753010215 = b20000753010215;
    }

    public BigDecimal getB20000753010216() {
        return b20000753010216;
    }

    public void setB20000753010216(BigDecimal b20000753010216) {
        this.b20000753010216 = b20000753010216;
    }

    public BigDecimal getB2000075301021601() {
        return b2000075301021601;
    }

    public void setB2000075301021601(BigDecimal b2000075301021601) {
        this.b2000075301021601 = b2000075301021601;
    }

    public BigDecimal getB2000075301021602() {
        return b2000075301021602;
    }

    public void setB2000075301021602(BigDecimal b2000075301021602) {
        this.b2000075301021602 = b2000075301021602;
    }

    public BigDecimal getB2000075301021603() {
        return b2000075301021603;
    }

    public void setB2000075301021603(BigDecimal b2000075301021603) {
        this.b2000075301021603 = b2000075301021603;
    }

    public BigDecimal getB2000075301021604() {
        return b2000075301021604;
    }

    public void setB2000075301021604(BigDecimal b2000075301021604) {
        this.b2000075301021604 = b2000075301021604;
    }

    public BigDecimal getB2000075301021605() {
        return b2000075301021605;
    }

    public void setB2000075301021605(BigDecimal b2000075301021605) {
        this.b2000075301021605 = b2000075301021605;
    }

    public BigDecimal getB2000075301021606() {
        return b2000075301021606;
    }

    public void setB2000075301021606(BigDecimal b2000075301021606) {
        this.b2000075301021606 = b2000075301021606;
    }

    public BigDecimal getB2000075301021607() {
        return b2000075301021607;
    }

    public void setB2000075301021607(BigDecimal b2000075301021607) {
        this.b2000075301021607 = b2000075301021607;
    }

    public BigDecimal getB2000075301021608() {
        return b2000075301021608;
    }

    public void setB2000075301021608(BigDecimal b2000075301021608) {
        this.b2000075301021608 = b2000075301021608;
    }

    public BigDecimal getB2000075301021609() {
        return b2000075301021609;
    }

    public void setB2000075301021609(BigDecimal b2000075301021609) {
        this.b2000075301021609 = b2000075301021609;
    }

    public BigDecimal getB20000753010217() {
        return b20000753010217;
    }

    public void setB20000753010217(BigDecimal b20000753010217) {
        this.b20000753010217 = b20000753010217;
    }

    public BigDecimal getB2000075301021701() {
        return b2000075301021701;
    }

    public void setB2000075301021701(BigDecimal b2000075301021701) {
        this.b2000075301021701 = b2000075301021701;
    }

    public BigDecimal getB2000075301021702() {
        return b2000075301021702;
    }

    public void setB2000075301021702(BigDecimal b2000075301021702) {
        this.b2000075301021702 = b2000075301021702;
    }

    public BigDecimal getB2000075301021703() {
        return b2000075301021703;
    }

    public void setB2000075301021703(BigDecimal b2000075301021703) {
        this.b2000075301021703 = b2000075301021703;
    }

    public BigDecimal getB2000075301021704() {
        return b2000075301021704;
    }

    public void setB2000075301021704(BigDecimal b2000075301021704) {
        this.b2000075301021704 = b2000075301021704;
    }

    public BigDecimal getB20000753010218() {
        return b20000753010218;
    }

    public void setB20000753010218(BigDecimal b20000753010218) {
        this.b20000753010218 = b20000753010218;
    }

    public BigDecimal getB20000753010219() {
        return b20000753010219;
    }

    public void setB20000753010219(BigDecimal b20000753010219) {
        this.b20000753010219 = b20000753010219;
    }

    public BigDecimal getB20000753010220() {
        return b20000753010220;
    }

    public void setB20000753010220(BigDecimal b20000753010220) {
        this.b20000753010220 = b20000753010220;
    }

    public BigDecimal getB20000753010221() {
        return b20000753010221;
    }

    public void setB20000753010221(BigDecimal b20000753010221) {
        this.b20000753010221 = b20000753010221;
    }

    public BigDecimal getB20000753010222() {
        return b20000753010222;
    }

    public void setB20000753010222(BigDecimal b20000753010222) {
        this.b20000753010222 = b20000753010222;
    }

    public BigDecimal getB20000753010223() {
        return b20000753010223;
    }

    public void setB20000753010223(BigDecimal b20000753010223) {
        this.b20000753010223 = b20000753010223;
    }

    public BigDecimal getB20000753010224() {
        return b20000753010224;
    }

    public void setB20000753010224(BigDecimal b20000753010224) {
        this.b20000753010224 = b20000753010224;
    }

    public BigDecimal getB20000753010225() {
        return b20000753010225;
    }

    public void setB20000753010225(BigDecimal b20000753010225) {
        this.b20000753010225 = b20000753010225;
    }

    public BigDecimal getB20000753010226() {
        return b20000753010226;
    }

    public void setB20000753010226(BigDecimal b20000753010226) {
        this.b20000753010226 = b20000753010226;
    }

    public BigDecimal getB2000075301022601() {
        return b2000075301022601;
    }

    public void setB2000075301022601(BigDecimal b2000075301022601) {
        this.b2000075301022601 = b2000075301022601;
    }

    public BigDecimal getB2000075301022602() {
        return b2000075301022602;
    }

    public void setB2000075301022602(BigDecimal b2000075301022602) {
        this.b2000075301022602 = b2000075301022602;
    }

    public BigDecimal getB2000075301022603() {
        return b2000075301022603;
    }

    public void setB2000075301022603(BigDecimal b2000075301022603) {
        this.b2000075301022603 = b2000075301022603;
    }

    public BigDecimal getB2000075301022604() {
        return b2000075301022604;
    }

    public void setB2000075301022604(BigDecimal b2000075301022604) {
        this.b2000075301022604 = b2000075301022604;
    }

    public BigDecimal getB2000075401() {
        return b2000075401;
    }

    public void setB2000075401(BigDecimal b2000075401) {
        this.b2000075401 = b2000075401;
    }

    public BigDecimal getB2000075402() {
        return b2000075402;
    }

    public void setB2000075402(BigDecimal b2000075402) {
        this.b2000075402 = b2000075402;
    }

    public BigDecimal getB2000075403() {
        return b2000075403;
    }

    public void setB2000075403(BigDecimal b2000075403) {
        this.b2000075403 = b2000075403;
    }

    public BigDecimal getB2000075501() {
        return b2000075501;
    }

    public void setB2000075501(BigDecimal b2000075501) {
        this.b2000075501 = b2000075501;
    }

    public BigDecimal getB200007550101() {
        return b200007550101;
    }

    public void setB200007550101(BigDecimal b200007550101) {
        this.b200007550101 = b200007550101;
    }

    public BigDecimal getB200007550102() {
        return b200007550102;
    }

    public void setB200007550102(BigDecimal b200007550102) {
        this.b200007550102 = b200007550102;
    }

    public BigDecimal getB200007550103() {
        return b200007550103;
    }

    public void setB200007550103(BigDecimal b200007550103) {
        this.b200007550103 = b200007550103;
    }

    public BigDecimal getB200007550104() {
        return b200007550104;
    }

    public void setB200007550104(BigDecimal b200007550104) {
        this.b200007550104 = b200007550104;
    }

    public BigDecimal getB200007550105() {
        return b200007550105;
    }

    public void setB200007550105(BigDecimal b200007550105) {
        this.b200007550105 = b200007550105;
    }

    public BigDecimal getB200007550106() {
        return b200007550106;
    }

    public void setB200007550106(BigDecimal b200007550106) {
        this.b200007550106 = b200007550106;
    }

    public BigDecimal getB200007550107() {
        return b200007550107;
    }

    public void setB200007550107(BigDecimal b200007550107) {
        this.b200007550107 = b200007550107;
    }

    public BigDecimal getB200007550108() {
        return b200007550108;
    }

    public void setB200007550108(BigDecimal b200007550108) {
        this.b200007550108 = b200007550108;
    }

    public BigDecimal getB200007550109() {
        return b200007550109;
    }

    public void setB200007550109(BigDecimal b200007550109) {
        this.b200007550109 = b200007550109;
    }

    public BigDecimal getB200007550110() {
        return b200007550110;
    }

    public void setB200007550110(BigDecimal b200007550110) {
        this.b200007550110 = b200007550110;
    }

    public BigDecimal getB200007550111() {
        return b200007550111;
    }

    public void setB200007550111(BigDecimal b200007550111) {
        this.b200007550111 = b200007550111;
    }

    public BigDecimal getB200007550112() {
        return b200007550112;
    }

    public void setB200007550112(BigDecimal b200007550112) {
        this.b200007550112 = b200007550112;
    }

    public BigDecimal getB200007550113() {
        return b200007550113;
    }

    public void setB200007550113(BigDecimal b200007550113) {
        this.b200007550113 = b200007550113;
    }

    public BigDecimal getB200007550114() {
        return b200007550114;
    }

    public void setB200007550114(BigDecimal b200007550114) {
        this.b200007550114 = b200007550114;
    }

    public BigDecimal getB200007550115() {
        return b200007550115;
    }

    public void setB200007550115(BigDecimal b200007550115) {
        this.b200007550115 = b200007550115;
    }

    public BigDecimal getB2000075502() {
        return b2000075502;
    }

    public void setB2000075502(BigDecimal b2000075502) {
        this.b2000075502 = b2000075502;
    }

    public BigDecimal getB2000075503() {
        return b2000075503;
    }

    public void setB2000075503(BigDecimal b2000075503) {
        this.b2000075503 = b2000075503;
    }

    public BigDecimal getB2000075504() {
        return b2000075504;
    }

    public void setB2000075504(BigDecimal b2000075504) {
        this.b2000075504 = b2000075504;
    }

    public BigDecimal getB2000076001() {
        return b2000076001;
    }

    public void setB2000076001(BigDecimal b2000076001) {
        this.b2000076001 = b2000076001;
    }

    public BigDecimal getB200007600101() {
        return b200007600101;
    }

    public void setB200007600101(BigDecimal b200007600101) {
        this.b200007600101 = b200007600101;
    }

    public BigDecimal getB200007600102() {
        return b200007600102;
    }

    public void setB200007600102(BigDecimal b200007600102) {
        this.b200007600102 = b200007600102;
    }

    public BigDecimal getB200007600103() {
        return b200007600103;
    }

    public void setB200007600103(BigDecimal b200007600103) {
        this.b200007600103 = b200007600103;
    }

    public BigDecimal getB200007600104() {
        return b200007600104;
    }

    public void setB200007600104(BigDecimal b200007600104) {
        this.b200007600104 = b200007600104;
    }

    public BigDecimal getB200007600105() {
        return b200007600105;
    }

    public void setB200007600105(BigDecimal b200007600105) {
        this.b200007600105 = b200007600105;
    }

    public BigDecimal getB2000076011() {
        return b2000076011;
    }

    public void setB2000076011(BigDecimal b2000076011) {
        this.b2000076011 = b2000076011;
    }

    public BigDecimal getB2000076021() {
        return b2000076021;
    }

    public void setB2000076021(BigDecimal b2000076021) {
        this.b2000076021 = b2000076021;
    }

    public BigDecimal getB2000076031() {
        return b2000076031;
    }

    public void setB2000076031(BigDecimal b2000076031) {
        this.b2000076031 = b2000076031;
    }

    public BigDecimal getB2000076041() {
        return b2000076041;
    }

    public void setB2000076041(BigDecimal b2000076041) {
        this.b2000076041 = b2000076041;
    }

    public BigDecimal getB2000076051() {
        return b2000076051;
    }

    public void setB2000076051(BigDecimal b2000076051) {
        this.b2000076051 = b2000076051;
    }

    public BigDecimal getB200007605101() {
        return b200007605101;
    }

    public void setB200007605101(BigDecimal b200007605101) {
        this.b200007605101 = b200007605101;
    }

    public BigDecimal getB200007605102() {
        return b200007605102;
    }

    public void setB200007605102(BigDecimal b200007605102) {
        this.b200007605102 = b200007605102;
    }

    public BigDecimal getB200007605103() {
        return b200007605103;
    }

    public void setB200007605103(BigDecimal b200007605103) {
        this.b200007605103 = b200007605103;
    }

    public BigDecimal getB200007605104() {
        return b200007605104;
    }

    public void setB200007605104(BigDecimal b200007605104) {
        this.b200007605104 = b200007605104;
    }

    public BigDecimal getB200007605105() {
        return b200007605105;
    }

    public void setB200007605105(BigDecimal b200007605105) {
        this.b200007605105 = b200007605105;
    }

    public BigDecimal getB2000076061() {
        return b2000076061;
    }

    public void setB2000076061(BigDecimal b2000076061) {
        this.b2000076061 = b2000076061;
    }

    public BigDecimal getB2000076101() {
        return b2000076101;
    }

    public void setB2000076101(BigDecimal b2000076101) {
        this.b2000076101 = b2000076101;
    }

    public BigDecimal getB2000076111() {
        return b2000076111;
    }

    public void setB2000076111(BigDecimal b2000076111) {
        this.b2000076111 = b2000076111;
    }

    public BigDecimal getB200007611101() {
        return b200007611101;
    }

    public void setB200007611101(BigDecimal b200007611101) {
        this.b200007611101 = b200007611101;
    }

    public BigDecimal getB20000761110101() {
        return b20000761110101;
    }

    public void setB20000761110101(BigDecimal b20000761110101) {
        this.b20000761110101 = b20000761110101;
    }

    public BigDecimal getB2000076111010101() {
        return b2000076111010101;
    }

    public void setB2000076111010101(BigDecimal b2000076111010101) {
        this.b2000076111010101 = b2000076111010101;
    }

    public BigDecimal getB2000076111010102() {
        return b2000076111010102;
    }

    public void setB2000076111010102(BigDecimal b2000076111010102) {
        this.b2000076111010102 = b2000076111010102;
    }

    public BigDecimal getB20000761110102() {
        return b20000761110102;
    }

    public void setB20000761110102(BigDecimal b20000761110102) {
        this.b20000761110102 = b20000761110102;
    }

    public BigDecimal getB2000076111010201() {
        return b2000076111010201;
    }

    public void setB2000076111010201(BigDecimal b2000076111010201) {
        this.b2000076111010201 = b2000076111010201;
    }

    public BigDecimal getB2000076111010202() {
        return b2000076111010202;
    }

    public void setB2000076111010202(BigDecimal b2000076111010202) {
        this.b2000076111010202 = b2000076111010202;
    }

    public BigDecimal getB20000761110103() {
        return b20000761110103;
    }

    public void setB20000761110103(BigDecimal b20000761110103) {
        this.b20000761110103 = b20000761110103;
    }

    public BigDecimal getB2000076111010301() {
        return b2000076111010301;
    }

    public void setB2000076111010301(BigDecimal b2000076111010301) {
        this.b2000076111010301 = b2000076111010301;
    }

    public BigDecimal getB2000076111010302() {
        return b2000076111010302;
    }

    public void setB2000076111010302(BigDecimal b2000076111010302) {
        this.b2000076111010302 = b2000076111010302;
    }

    public BigDecimal getB20000761110104() {
        return b20000761110104;
    }

    public void setB20000761110104(BigDecimal b20000761110104) {
        this.b20000761110104 = b20000761110104;
    }

    public BigDecimal getB2000076111010401() {
        return b2000076111010401;
    }

    public void setB2000076111010401(BigDecimal b2000076111010401) {
        this.b2000076111010401 = b2000076111010401;
    }

    public BigDecimal getB2000076111010402() {
        return b2000076111010402;
    }

    public void setB2000076111010402(BigDecimal b2000076111010402) {
        this.b2000076111010402 = b2000076111010402;
    }

    public BigDecimal getB20000761110105() {
        return b20000761110105;
    }

    public void setB20000761110105(BigDecimal b20000761110105) {
        this.b20000761110105 = b20000761110105;
    }

    public BigDecimal getB2000076111010501() {
        return b2000076111010501;
    }

    public void setB2000076111010501(BigDecimal b2000076111010501) {
        this.b2000076111010501 = b2000076111010501;
    }

    public BigDecimal getB2000076111010502() {
        return b2000076111010502;
    }

    public void setB2000076111010502(BigDecimal b2000076111010502) {
        this.b2000076111010502 = b2000076111010502;
    }

    public BigDecimal getB20000761110106() {
        return b20000761110106;
    }

    public void setB20000761110106(BigDecimal b20000761110106) {
        this.b20000761110106 = b20000761110106;
    }

    public BigDecimal getB2000076111010601() {
        return b2000076111010601;
    }

    public void setB2000076111010601(BigDecimal b2000076111010601) {
        this.b2000076111010601 = b2000076111010601;
    }

    public BigDecimal getB2000076111010602() {
        return b2000076111010602;
    }

    public void setB2000076111010602(BigDecimal b2000076111010602) {
        this.b2000076111010602 = b2000076111010602;
    }

    public BigDecimal getB20000761110107() {
        return b20000761110107;
    }

    public void setB20000761110107(BigDecimal b20000761110107) {
        this.b20000761110107 = b20000761110107;
    }

    public BigDecimal getB2000076111010701() {
        return b2000076111010701;
    }

    public void setB2000076111010701(BigDecimal b2000076111010701) {
        this.b2000076111010701 = b2000076111010701;
    }

    public BigDecimal getB2000076111010702() {
        return b2000076111010702;
    }

    public void setB2000076111010702(BigDecimal b2000076111010702) {
        this.b2000076111010702 = b2000076111010702;
    }

    public BigDecimal getB20000761110108() {
        return b20000761110108;
    }

    public void setB20000761110108(BigDecimal b20000761110108) {
        this.b20000761110108 = b20000761110108;
    }

    public BigDecimal getB2000076111010801() {
        return b2000076111010801;
    }

    public void setB2000076111010801(BigDecimal b2000076111010801) {
        this.b2000076111010801 = b2000076111010801;
    }

    public BigDecimal getB2000076111010802() {
        return b2000076111010802;
    }

    public void setB2000076111010802(BigDecimal b2000076111010802) {
        this.b2000076111010802 = b2000076111010802;
    }

    public BigDecimal getB200007611102() {
        return b200007611102;
    }

    public void setB200007611102(BigDecimal b200007611102) {
        this.b200007611102 = b200007611102;
    }

    public BigDecimal getB20000761110201() {
        return b20000761110201;
    }

    public void setB20000761110201(BigDecimal b20000761110201) {
        this.b20000761110201 = b20000761110201;
    }

    public BigDecimal getB2000076111020101() {
        return b2000076111020101;
    }

    public void setB2000076111020101(BigDecimal b2000076111020101) {
        this.b2000076111020101 = b2000076111020101;
    }

    public BigDecimal getB2000076111020102() {
        return b2000076111020102;
    }

    public void setB2000076111020102(BigDecimal b2000076111020102) {
        this.b2000076111020102 = b2000076111020102;
    }

    public BigDecimal getB20000761110202() {
        return b20000761110202;
    }

    public void setB20000761110202(BigDecimal b20000761110202) {
        this.b20000761110202 = b20000761110202;
    }

    public BigDecimal getB2000076111020201() {
        return b2000076111020201;
    }

    public void setB2000076111020201(BigDecimal b2000076111020201) {
        this.b2000076111020201 = b2000076111020201;
    }

    public BigDecimal getB2000076111020202() {
        return b2000076111020202;
    }

    public void setB2000076111020202(BigDecimal b2000076111020202) {
        this.b2000076111020202 = b2000076111020202;
    }

    public BigDecimal getB20000761110203() {
        return b20000761110203;
    }

    public void setB20000761110203(BigDecimal b20000761110203) {
        this.b20000761110203 = b20000761110203;
    }

    public BigDecimal getB2000076111020301() {
        return b2000076111020301;
    }

    public void setB2000076111020301(BigDecimal b2000076111020301) {
        this.b2000076111020301 = b2000076111020301;
    }

    public BigDecimal getB2000076111020302() {
        return b2000076111020302;
    }

    public void setB2000076111020302(BigDecimal b2000076111020302) {
        this.b2000076111020302 = b2000076111020302;
    }

    public BigDecimal getB20000761110204() {
        return b20000761110204;
    }

    public void setB20000761110204(BigDecimal b20000761110204) {
        this.b20000761110204 = b20000761110204;
    }

    public BigDecimal getB2000076111020401() {
        return b2000076111020401;
    }

    public void setB2000076111020401(BigDecimal b2000076111020401) {
        this.b2000076111020401 = b2000076111020401;
    }

    public BigDecimal getB2000076111020402() {
        return b2000076111020402;
    }

    public void setB2000076111020402(BigDecimal b2000076111020402) {
        this.b2000076111020402 = b2000076111020402;
    }

    public BigDecimal getB20000761110205() {
        return b20000761110205;
    }

    public void setB20000761110205(BigDecimal b20000761110205) {
        this.b20000761110205 = b20000761110205;
    }

    public BigDecimal getB2000076111020501() {
        return b2000076111020501;
    }

    public void setB2000076111020501(BigDecimal b2000076111020501) {
        this.b2000076111020501 = b2000076111020501;
    }

    public BigDecimal getB2000076111020502() {
        return b2000076111020502;
    }

    public void setB2000076111020502(BigDecimal b2000076111020502) {
        this.b2000076111020502 = b2000076111020502;
    }

    public BigDecimal getB20000761110206() {
        return b20000761110206;
    }

    public void setB20000761110206(BigDecimal b20000761110206) {
        this.b20000761110206 = b20000761110206;
    }

    public BigDecimal getB2000076111020601() {
        return b2000076111020601;
    }

    public void setB2000076111020601(BigDecimal b2000076111020601) {
        this.b2000076111020601 = b2000076111020601;
    }

    public BigDecimal getB2000076111020602() {
        return b2000076111020602;
    }

    public void setB2000076111020602(BigDecimal b2000076111020602) {
        this.b2000076111020602 = b2000076111020602;
    }

    public BigDecimal getB20000761110207() {
        return b20000761110207;
    }

    public void setB20000761110207(BigDecimal b20000761110207) {
        this.b20000761110207 = b20000761110207;
    }

    public BigDecimal getB2000076111020701() {
        return b2000076111020701;
    }

    public void setB2000076111020701(BigDecimal b2000076111020701) {
        this.b2000076111020701 = b2000076111020701;
    }

    public BigDecimal getB2000076111020702() {
        return b2000076111020702;
    }

    public void setB2000076111020702(BigDecimal b2000076111020702) {
        this.b2000076111020702 = b2000076111020702;
    }

    public BigDecimal getB20000761110208() {
        return b20000761110208;
    }

    public void setB20000761110208(BigDecimal b20000761110208) {
        this.b20000761110208 = b20000761110208;
    }

    public BigDecimal getB2000076111020801() {
        return b2000076111020801;
    }

    public void setB2000076111020801(BigDecimal b2000076111020801) {
        this.b2000076111020801 = b2000076111020801;
    }

    public BigDecimal getB2000076111020802() {
        return b2000076111020802;
    }

    public void setB2000076111020802(BigDecimal b2000076111020802) {
        this.b2000076111020802 = b2000076111020802;
    }

    public BigDecimal getB200007611103() {
        return b200007611103;
    }

    public void setB200007611103(BigDecimal b200007611103) {
        this.b200007611103 = b200007611103;
    }

    public BigDecimal getB20000761110301() {
        return b20000761110301;
    }

    public void setB20000761110301(BigDecimal b20000761110301) {
        this.b20000761110301 = b20000761110301;
    }

    public BigDecimal getB2000076111030101() {
        return b2000076111030101;
    }

    public void setB2000076111030101(BigDecimal b2000076111030101) {
        this.b2000076111030101 = b2000076111030101;
    }

    public BigDecimal getB2000076111030102() {
        return b2000076111030102;
    }

    public void setB2000076111030102(BigDecimal b2000076111030102) {
        this.b2000076111030102 = b2000076111030102;
    }

    public BigDecimal getB20000761110302() {
        return b20000761110302;
    }

    public void setB20000761110302(BigDecimal b20000761110302) {
        this.b20000761110302 = b20000761110302;
    }

    public BigDecimal getB2000076111030201() {
        return b2000076111030201;
    }

    public void setB2000076111030201(BigDecimal b2000076111030201) {
        this.b2000076111030201 = b2000076111030201;
    }

    public BigDecimal getB2000076111030202() {
        return b2000076111030202;
    }

    public void setB2000076111030202(BigDecimal b2000076111030202) {
        this.b2000076111030202 = b2000076111030202;
    }

    public BigDecimal getB20000761110303() {
        return b20000761110303;
    }

    public void setB20000761110303(BigDecimal b20000761110303) {
        this.b20000761110303 = b20000761110303;
    }

    public BigDecimal getB2000076111030301() {
        return b2000076111030301;
    }

    public void setB2000076111030301(BigDecimal b2000076111030301) {
        this.b2000076111030301 = b2000076111030301;
    }

    public BigDecimal getB2000076111030302() {
        return b2000076111030302;
    }

    public void setB2000076111030302(BigDecimal b2000076111030302) {
        this.b2000076111030302 = b2000076111030302;
    }

    public BigDecimal getB20000761110304() {
        return b20000761110304;
    }

    public void setB20000761110304(BigDecimal b20000761110304) {
        this.b20000761110304 = b20000761110304;
    }

    public BigDecimal getB2000076111030401() {
        return b2000076111030401;
    }

    public void setB2000076111030401(BigDecimal b2000076111030401) {
        this.b2000076111030401 = b2000076111030401;
    }

    public BigDecimal getB2000076111030402() {
        return b2000076111030402;
    }

    public void setB2000076111030402(BigDecimal b2000076111030402) {
        this.b2000076111030402 = b2000076111030402;
    }

    public BigDecimal getB20000761110305() {
        return b20000761110305;
    }

    public void setB20000761110305(BigDecimal b20000761110305) {
        this.b20000761110305 = b20000761110305;
    }

    public BigDecimal getB2000076111030501() {
        return b2000076111030501;
    }

    public void setB2000076111030501(BigDecimal b2000076111030501) {
        this.b2000076111030501 = b2000076111030501;
    }

    public BigDecimal getB2000076111030502() {
        return b2000076111030502;
    }

    public void setB2000076111030502(BigDecimal b2000076111030502) {
        this.b2000076111030502 = b2000076111030502;
    }

    public BigDecimal getB200007611104() {
        return b200007611104;
    }

    public void setB200007611104(BigDecimal b200007611104) {
        this.b200007611104 = b200007611104;
    }

    public BigDecimal getB20000761110401() {
        return b20000761110401;
    }

    public void setB20000761110401(BigDecimal b20000761110401) {
        this.b20000761110401 = b20000761110401;
    }

    public BigDecimal getB20000761110402() {
        return b20000761110402;
    }

    public void setB20000761110402(BigDecimal b20000761110402) {
        this.b20000761110402 = b20000761110402;
    }

    public BigDecimal getB200007611105() {
        return b200007611105;
    }

    public void setB200007611105(BigDecimal b200007611105) {
        this.b200007611105 = b200007611105;
    }

    public BigDecimal getB20000761110501() {
        return b20000761110501;
    }

    public void setB20000761110501(BigDecimal b20000761110501) {
        this.b20000761110501 = b20000761110501;
    }

    public BigDecimal getB20000761110502() {
        return b20000761110502;
    }

    public void setB20000761110502(BigDecimal b20000761110502) {
        this.b20000761110502 = b20000761110502;
    }

    public BigDecimal getB200007611106() {
        return b200007611106;
    }

    public void setB200007611106(BigDecimal b200007611106) {
        this.b200007611106 = b200007611106;
    }

    public BigDecimal getB20000761110601() {
        return b20000761110601;
    }

    public void setB20000761110601(BigDecimal b20000761110601) {
        this.b20000761110601 = b20000761110601;
    }

    public BigDecimal getB20000761110602() {
        return b20000761110602;
    }

    public void setB20000761110602(BigDecimal b20000761110602) {
        this.b20000761110602 = b20000761110602;
    }

    public BigDecimal getB200007611107() {
        return b200007611107;
    }

    public void setB200007611107(BigDecimal b200007611107) {
        this.b200007611107 = b200007611107;
    }

    public BigDecimal getB20000761110701() {
        return b20000761110701;
    }

    public void setB20000761110701(BigDecimal b20000761110701) {
        this.b20000761110701 = b20000761110701;
    }

    public BigDecimal getB2000076111070101() {
        return b2000076111070101;
    }

    public void setB2000076111070101(BigDecimal b2000076111070101) {
        this.b2000076111070101 = b2000076111070101;
    }

    public BigDecimal getB2000076111070102() {
        return b2000076111070102;
    }

    public void setB2000076111070102(BigDecimal b2000076111070102) {
        this.b2000076111070102 = b2000076111070102;
    }

    public BigDecimal getB20000761110702() {
        return b20000761110702;
    }

    public void setB20000761110702(BigDecimal b20000761110702) {
        this.b20000761110702 = b20000761110702;
    }

    public BigDecimal getB2000076111070201() {
        return b2000076111070201;
    }

    public void setB2000076111070201(BigDecimal b2000076111070201) {
        this.b2000076111070201 = b2000076111070201;
    }

    public BigDecimal getB2000076111070202() {
        return b2000076111070202;
    }

    public void setB2000076111070202(BigDecimal b2000076111070202) {
        this.b2000076111070202 = b2000076111070202;
    }

    public BigDecimal getB20000761110703() {
        return b20000761110703;
    }

    public void setB20000761110703(BigDecimal b20000761110703) {
        this.b20000761110703 = b20000761110703;
    }

    public BigDecimal getB2000076111070301() {
        return b2000076111070301;
    }

    public void setB2000076111070301(BigDecimal b2000076111070301) {
        this.b2000076111070301 = b2000076111070301;
    }

    public BigDecimal getB2000076111070302() {
        return b2000076111070302;
    }

    public void setB2000076111070302(BigDecimal b2000076111070302) {
        this.b2000076111070302 = b2000076111070302;
    }

    public BigDecimal getB20000761110704() {
        return b20000761110704;
    }

    public void setB20000761110704(BigDecimal b20000761110704) {
        this.b20000761110704 = b20000761110704;
    }

    public BigDecimal getB2000076111070401() {
        return b2000076111070401;
    }

    public void setB2000076111070401(BigDecimal b2000076111070401) {
        this.b2000076111070401 = b2000076111070401;
    }

    public BigDecimal getB2000076111070402() {
        return b2000076111070402;
    }

    public void setB2000076111070402(BigDecimal b2000076111070402) {
        this.b2000076111070402 = b2000076111070402;
    }

    public BigDecimal getB20000761110705() {
        return b20000761110705;
    }

    public void setB20000761110705(BigDecimal b20000761110705) {
        this.b20000761110705 = b20000761110705;
    }

    public BigDecimal getB2000076111070501() {
        return b2000076111070501;
    }

    public void setB2000076111070501(BigDecimal b2000076111070501) {
        this.b2000076111070501 = b2000076111070501;
    }

    public BigDecimal getB2000076111070502() {
        return b2000076111070502;
    }

    public void setB2000076111070502(BigDecimal b2000076111070502) {
        this.b2000076111070502 = b2000076111070502;
    }

    public BigDecimal getB200007611108() {
        return b200007611108;
    }

    public void setB200007611108(BigDecimal b200007611108) {
        this.b200007611108 = b200007611108;
    }

    public BigDecimal getB20000761110801() {
        return b20000761110801;
    }

    public void setB20000761110801(BigDecimal b20000761110801) {
        this.b20000761110801 = b20000761110801;
    }

    public BigDecimal getB2000076111080101() {
        return b2000076111080101;
    }

    public void setB2000076111080101(BigDecimal b2000076111080101) {
        this.b2000076111080101 = b2000076111080101;
    }

    public BigDecimal getB2000076111080102() {
        return b2000076111080102;
    }

    public void setB2000076111080102(BigDecimal b2000076111080102) {
        this.b2000076111080102 = b2000076111080102;
    }

    public BigDecimal getB20000761110802() {
        return b20000761110802;
    }

    public void setB20000761110802(BigDecimal b20000761110802) {
        this.b20000761110802 = b20000761110802;
    }

    public BigDecimal getB2000076111080201() {
        return b2000076111080201;
    }

    public void setB2000076111080201(BigDecimal b2000076111080201) {
        this.b2000076111080201 = b2000076111080201;
    }

    public BigDecimal getB2000076111080202() {
        return b2000076111080202;
    }

    public void setB2000076111080202(BigDecimal b2000076111080202) {
        this.b2000076111080202 = b2000076111080202;
    }

    public BigDecimal getB20000761110803() {
        return b20000761110803;
    }

    public void setB20000761110803(BigDecimal b20000761110803) {
        this.b20000761110803 = b20000761110803;
    }

    public BigDecimal getB2000076111080301() {
        return b2000076111080301;
    }

    public void setB2000076111080301(BigDecimal b2000076111080301) {
        this.b2000076111080301 = b2000076111080301;
    }

    public BigDecimal getB2000076111080302() {
        return b2000076111080302;
    }

    public void setB2000076111080302(BigDecimal b2000076111080302) {
        this.b2000076111080302 = b2000076111080302;
    }

    public BigDecimal getB20000761110804() {
        return b20000761110804;
    }

    public void setB20000761110804(BigDecimal b20000761110804) {
        this.b20000761110804 = b20000761110804;
    }

    public BigDecimal getB2000076111080401() {
        return b2000076111080401;
    }

    public void setB2000076111080401(BigDecimal b2000076111080401) {
        this.b2000076111080401 = b2000076111080401;
    }

    public BigDecimal getB2000076111080402() {
        return b2000076111080402;
    }

    public void setB2000076111080402(BigDecimal b2000076111080402) {
        this.b2000076111080402 = b2000076111080402;
    }

    public BigDecimal getB20000761110805() {
        return b20000761110805;
    }

    public void setB20000761110805(BigDecimal b20000761110805) {
        this.b20000761110805 = b20000761110805;
    }

    public BigDecimal getB2000076111080501() {
        return b2000076111080501;
    }

    public void setB2000076111080501(BigDecimal b2000076111080501) {
        this.b2000076111080501 = b2000076111080501;
    }

    public BigDecimal getB2000076111080502() {
        return b2000076111080502;
    }

    public void setB2000076111080502(BigDecimal b2000076111080502) {
        this.b2000076111080502 = b2000076111080502;
    }

    public BigDecimal getB20000761110806() {
        return b20000761110806;
    }

    public void setB20000761110806(BigDecimal b20000761110806) {
        this.b20000761110806 = b20000761110806;
    }

    public BigDecimal getB2000076111080601() {
        return b2000076111080601;
    }

    public void setB2000076111080601(BigDecimal b2000076111080601) {
        this.b2000076111080601 = b2000076111080601;
    }

    public BigDecimal getB2000076111080602() {
        return b2000076111080602;
    }

    public void setB2000076111080602(BigDecimal b2000076111080602) {
        this.b2000076111080602 = b2000076111080602;
    }

    public BigDecimal getB20000761110807() {
        return b20000761110807;
    }

    public void setB20000761110807(BigDecimal b20000761110807) {
        this.b20000761110807 = b20000761110807;
    }

    public BigDecimal getB2000076111080701() {
        return b2000076111080701;
    }

    public void setB2000076111080701(BigDecimal b2000076111080701) {
        this.b2000076111080701 = b2000076111080701;
    }

    public BigDecimal getB2000076111080702() {
        return b2000076111080702;
    }

    public void setB2000076111080702(BigDecimal b2000076111080702) {
        this.b2000076111080702 = b2000076111080702;
    }

    public BigDecimal getB20000761110808() {
        return b20000761110808;
    }

    public void setB20000761110808(BigDecimal b20000761110808) {
        this.b20000761110808 = b20000761110808;
    }

    public BigDecimal getB2000076111080801() {
        return b2000076111080801;
    }

    public void setB2000076111080801(BigDecimal b2000076111080801) {
        this.b2000076111080801 = b2000076111080801;
    }

    public BigDecimal getB2000076111080802() {
        return b2000076111080802;
    }

    public void setB2000076111080802(BigDecimal b2000076111080802) {
        this.b2000076111080802 = b2000076111080802;
    }

    public BigDecimal getB2000076115() {
        return b2000076115;
    }

    public void setB2000076115(BigDecimal b2000076115) {
        this.b2000076115 = b2000076115;
    }

    public BigDecimal getB200007611501() {
        return b200007611501;
    }

    public void setB200007611501(BigDecimal b200007611501) {
        this.b200007611501 = b200007611501;
    }

    public BigDecimal getB200007611502() {
        return b200007611502;
    }

    public void setB200007611502(BigDecimal b200007611502) {
        this.b200007611502 = b200007611502;
    }

    public BigDecimal getB200007611503() {
        return b200007611503;
    }

    public void setB200007611503(BigDecimal b200007611503) {
        this.b200007611503 = b200007611503;
    }

    public BigDecimal getB200007611504() {
        return b200007611504;
    }

    public void setB200007611504(BigDecimal b200007611504) {
        this.b200007611504 = b200007611504;
    }

    public BigDecimal getB200007611505() {
        return b200007611505;
    }

    public void setB200007611505(BigDecimal b200007611505) {
        this.b200007611505 = b200007611505;
    }

    public BigDecimal getB200007611506() {
        return b200007611506;
    }

    public void setB200007611506(BigDecimal b200007611506) {
        this.b200007611506 = b200007611506;
    }

    public BigDecimal getB200007611507() {
        return b200007611507;
    }

    public void setB200007611507(BigDecimal b200007611507) {
        this.b200007611507 = b200007611507;
    }

    public BigDecimal getB2000076117() {
        return b2000076117;
    }

    public void setB2000076117(BigDecimal b2000076117) {
        this.b2000076117 = b2000076117;
    }

    public BigDecimal getB2000076201() {
        return b2000076201;
    }

    public void setB2000076201(BigDecimal b2000076201) {
        this.b2000076201 = b2000076201;
    }

    public BigDecimal getB2000076202() {
        return b2000076202;
    }

    public void setB2000076202(BigDecimal b2000076202) {
        this.b2000076202 = b2000076202;
    }

    public BigDecimal getB2000076203() {
        return b2000076203;
    }

    public void setB2000076203(BigDecimal b2000076203) {
        this.b2000076203 = b2000076203;
    }

    public BigDecimal getB2000076301() {
        return b2000076301;
    }

    public void setB2000076301(BigDecimal b2000076301) {
        this.b2000076301 = b2000076301;
    }

    public BigDecimal getB200007630101() {
        return b200007630101;
    }

    public void setB200007630101(BigDecimal b200007630101) {
        this.b200007630101 = b200007630101;
    }

    public BigDecimal getB200007630102() {
        return b200007630102;
    }

    public void setB200007630102(BigDecimal b200007630102) {
        this.b200007630102 = b200007630102;
    }

    public BigDecimal getB200007630103() {
        return b200007630103;
    }

    public void setB200007630103(BigDecimal b200007630103) {
        this.b200007630103 = b200007630103;
    }

    public BigDecimal getB200007630104() {
        return b200007630104;
    }

    public void setB200007630104(BigDecimal b200007630104) {
        this.b200007630104 = b200007630104;
    }

    public BigDecimal getB200007630105() {
        return b200007630105;
    }

    public void setB200007630105(BigDecimal b200007630105) {
        this.b200007630105 = b200007630105;
    }

    public BigDecimal getB200007630106() {
        return b200007630106;
    }

    public void setB200007630106(BigDecimal b200007630106) {
        this.b200007630106 = b200007630106;
    }

    public BigDecimal getB200007630107() {
        return b200007630107;
    }

    public void setB200007630107(BigDecimal b200007630107) {
        this.b200007630107 = b200007630107;
    }

    public BigDecimal getB200007630108() {
        return b200007630108;
    }

    public void setB200007630108(BigDecimal b200007630108) {
        this.b200007630108 = b200007630108;
    }

    public BigDecimal getB200007630109() {
        return b200007630109;
    }

    public void setB200007630109(BigDecimal b200007630109) {
        this.b200007630109 = b200007630109;
    }

    public BigDecimal getB2000076401() {
        return b2000076401;
    }

    public void setB2000076401(BigDecimal b2000076401) {
        this.b2000076401 = b2000076401;
    }

    public BigDecimal getB200007640101() {
        return b200007640101;
    }

    public void setB200007640101(BigDecimal b200007640101) {
        this.b200007640101 = b200007640101;
    }

    public BigDecimal getB200007640102() {
        return b200007640102;
    }

    public void setB200007640102(BigDecimal b200007640102) {
        this.b200007640102 = b200007640102;
    }

    public BigDecimal getB200007640103() {
        return b200007640103;
    }

    public void setB200007640103(BigDecimal b200007640103) {
        this.b200007640103 = b200007640103;
    }

    public BigDecimal getB200007640104() {
        return b200007640104;
    }

    public void setB200007640104(BigDecimal b200007640104) {
        this.b200007640104 = b200007640104;
    }

    public BigDecimal getB200007640105() {
        return b200007640105;
    }

    public void setB200007640105(BigDecimal b200007640105) {
        this.b200007640105 = b200007640105;
    }

    public BigDecimal getB200007640106() {
        return b200007640106;
    }

    public void setB200007640106(BigDecimal b200007640106) {
        this.b200007640106 = b200007640106;
    }

    public BigDecimal getB200007640107() {
        return b200007640107;
    }

    public void setB200007640107(BigDecimal b200007640107) {
        this.b200007640107 = b200007640107;
    }

    public BigDecimal getB200007640108() {
        return b200007640108;
    }

    public void setB200007640108(BigDecimal b200007640108) {
        this.b200007640108 = b200007640108;
    }

    public BigDecimal getB200007640109() {
        return b200007640109;
    }

    public void setB200007640109(BigDecimal b200007640109) {
        this.b200007640109 = b200007640109;
    }

    public BigDecimal getB200007640110() {
        return b200007640110;
    }

    public void setB200007640110(BigDecimal b200007640110) {
        this.b200007640110 = b200007640110;
    }

    public BigDecimal getB200007640111() {
        return b200007640111;
    }

    public void setB200007640111(BigDecimal b200007640111) {
        this.b200007640111 = b200007640111;
    }

    public BigDecimal getB200007640112() {
        return b200007640112;
    }

    public void setB200007640112(BigDecimal b200007640112) {
        this.b200007640112 = b200007640112;
    }

    public BigDecimal getB200007640113() {
        return b200007640113;
    }

    public void setB200007640113(BigDecimal b200007640113) {
        this.b200007640113 = b200007640113;
    }

    public BigDecimal getB200007640114() {
        return b200007640114;
    }

    public void setB200007640114(BigDecimal b200007640114) {
        this.b200007640114 = b200007640114;
    }

    public BigDecimal getB200007640115() {
        return b200007640115;
    }

    public void setB200007640115(BigDecimal b200007640115) {
        this.b200007640115 = b200007640115;
    }

    public BigDecimal getB200007640116() {
        return b200007640116;
    }

    public void setB200007640116(BigDecimal b200007640116) {
        this.b200007640116 = b200007640116;
    }

    public BigDecimal getB200007640117() {
        return b200007640117;
    }

    public void setB200007640117(BigDecimal b200007640117) {
        this.b200007640117 = b200007640117;
    }

    public BigDecimal getB200007640118() {
        return b200007640118;
    }

    public void setB200007640118(BigDecimal b200007640118) {
        this.b200007640118 = b200007640118;
    }

    public BigDecimal getB200007640119() {
        return b200007640119;
    }

    public void setB200007640119(BigDecimal b200007640119) {
        this.b200007640119 = b200007640119;
    }

    public BigDecimal getB2000076402() {
        return b2000076402;
    }

    public void setB2000076402(BigDecimal b2000076402) {
        this.b2000076402 = b2000076402;
    }

    public BigDecimal getB200007640201() {
        return b200007640201;
    }

    public void setB200007640201(BigDecimal b200007640201) {
        this.b200007640201 = b200007640201;
    }

    public BigDecimal getB200007640202() {
        return b200007640202;
    }

    public void setB200007640202(BigDecimal b200007640202) {
        this.b200007640202 = b200007640202;
    }

    public BigDecimal getB200007640203() {
        return b200007640203;
    }

    public void setB200007640203(BigDecimal b200007640203) {
        this.b200007640203 = b200007640203;
    }

    public BigDecimal getB200007640204() {
        return b200007640204;
    }

    public void setB200007640204(BigDecimal b200007640204) {
        this.b200007640204 = b200007640204;
    }

    public BigDecimal getB200007640205() {
        return b200007640205;
    }

    public void setB200007640205(BigDecimal b200007640205) {
        this.b200007640205 = b200007640205;
    }

    public BigDecimal getB2000076403() {
        return b2000076403;
    }

    public void setB2000076403(BigDecimal b2000076403) {
        this.b2000076403 = b2000076403;
    }

    public BigDecimal getB2000076411() {
        return b2000076411;
    }

    public void setB2000076411(BigDecimal b2000076411) {
        this.b2000076411 = b2000076411;
    }

    public BigDecimal getB2000076421() {
        return b2000076421;
    }

    public void setB2000076421(BigDecimal b2000076421) {
        this.b2000076421 = b2000076421;
    }

    public BigDecimal getB2000076501() {
        return b2000076501;
    }

    public void setB2000076501(BigDecimal b2000076501) {
        this.b2000076501 = b2000076501;
    }

    public BigDecimal getB2000076502() {
        return b2000076502;
    }

    public void setB2000076502(BigDecimal b2000076502) {
        this.b2000076502 = b2000076502;
    }

    public BigDecimal getB2000076511() {
        return b2000076511;
    }

    public void setB2000076511(BigDecimal b2000076511) {
        this.b2000076511 = b2000076511;
    }

    public BigDecimal getB2000076521() {
        return b2000076521;
    }

    public void setB2000076521(BigDecimal b2000076521) {
        this.b2000076521 = b2000076521;
    }

    public BigDecimal getB2000076531() {
        return b2000076531;
    }

    public void setB2000076531(BigDecimal b2000076531) {
        this.b2000076531 = b2000076531;
    }

    public BigDecimal getB2000076541() {
        return b2000076541;
    }

    public void setB2000076541(BigDecimal b2000076541) {
        this.b2000076541 = b2000076541;
    }

    public BigDecimal getB2000076542() {
        return b2000076542;
    }

    public void setB2000076542(BigDecimal b2000076542) {
        this.b2000076542 = b2000076542;
    }

    public BigDecimal getB2000076601() {
        return b2000076601;
    }

    public void setB2000076601(BigDecimal b2000076601) {
        this.b2000076601 = b2000076601;
    }

    public BigDecimal getB200007660101() {
        return b200007660101;
    }

    public void setB200007660101(BigDecimal b200007660101) {
        this.b200007660101 = b200007660101;
    }

    public BigDecimal getB200007660102() {
        return b200007660102;
    }

    public void setB200007660102(BigDecimal b200007660102) {
        this.b200007660102 = b200007660102;
    }

    public BigDecimal getB200007660103() {
        return b200007660103;
    }

    public void setB200007660103(BigDecimal b200007660103) {
        this.b200007660103 = b200007660103;
    }

    public BigDecimal getB200007660104() {
        return b200007660104;
    }

    public void setB200007660104(BigDecimal b200007660104) {
        this.b200007660104 = b200007660104;
    }

    public BigDecimal getB200007660105() {
        return b200007660105;
    }

    public void setB200007660105(BigDecimal b200007660105) {
        this.b200007660105 = b200007660105;
    }

    public BigDecimal getB200007660106() {
        return b200007660106;
    }

    public void setB200007660106(BigDecimal b200007660106) {
        this.b200007660106 = b200007660106;
    }

    public BigDecimal getB200007660107() {
        return b200007660107;
    }

    public void setB200007660107(BigDecimal b200007660107) {
        this.b200007660107 = b200007660107;
    }

    public BigDecimal getB200007660108() {
        return b200007660108;
    }

    public void setB200007660108(BigDecimal b200007660108) {
        this.b200007660108 = b200007660108;
    }

    public BigDecimal getB200007660109() {
        return b200007660109;
    }

    public void setB200007660109(BigDecimal b200007660109) {
        this.b200007660109 = b200007660109;
    }

    public BigDecimal getB200007660110() {
        return b200007660110;
    }

    public void setB200007660110(BigDecimal b200007660110) {
        this.b200007660110 = b200007660110;
    }

    public BigDecimal getB200007660111() {
        return b200007660111;
    }

    public void setB200007660111(BigDecimal b200007660111) {
        this.b200007660111 = b200007660111;
    }

    public BigDecimal getB200007660112() {
        return b200007660112;
    }

    public void setB200007660112(BigDecimal b200007660112) {
        this.b200007660112 = b200007660112;
    }

    public BigDecimal getB200007660113() {
        return b200007660113;
    }

    public void setB200007660113(BigDecimal b200007660113) {
        this.b200007660113 = b200007660113;
    }

    public BigDecimal getB200007660114() {
        return b200007660114;
    }

    public void setB200007660114(BigDecimal b200007660114) {
        this.b200007660114 = b200007660114;
    }

    public BigDecimal getB200007660115() {
        return b200007660115;
    }

    public void setB200007660115(BigDecimal b200007660115) {
        this.b200007660115 = b200007660115;
    }

    public BigDecimal getB200007660116() {
        return b200007660116;
    }

    public void setB200007660116(BigDecimal b200007660116) {
        this.b200007660116 = b200007660116;
    }

    public BigDecimal getB200007660117() {
        return b200007660117;
    }

    public void setB200007660117(BigDecimal b200007660117) {
        this.b200007660117 = b200007660117;
    }

    public BigDecimal getB200007660118() {
        return b200007660118;
    }

    public void setB200007660118(BigDecimal b200007660118) {
        this.b200007660118 = b200007660118;
    }

    public BigDecimal getB200007660119() {
        return b200007660119;
    }

    public void setB200007660119(BigDecimal b200007660119) {
        this.b200007660119 = b200007660119;
    }

    public BigDecimal getB200007660120() {
        return b200007660120;
    }

    public void setB200007660120(BigDecimal b200007660120) {
        this.b200007660120 = b200007660120;
    }

    public BigDecimal getB200007660121() {
        return b200007660121;
    }

    public void setB200007660121(BigDecimal b200007660121) {
        this.b200007660121 = b200007660121;
    }

    public BigDecimal getB200007660122() {
        return b200007660122;
    }

    public void setB200007660122(BigDecimal b200007660122) {
        this.b200007660122 = b200007660122;
    }

    public BigDecimal getB200007660123() {
        return b200007660123;
    }

    public void setB200007660123(BigDecimal b200007660123) {
        this.b200007660123 = b200007660123;
    }

    public BigDecimal getB200007660124() {
        return b200007660124;
    }

    public void setB200007660124(BigDecimal b200007660124) {
        this.b200007660124 = b200007660124;
    }

    public BigDecimal getB200007660125() {
        return b200007660125;
    }

    public void setB200007660125(BigDecimal b200007660125) {
        this.b200007660125 = b200007660125;
    }

    public BigDecimal getB200007660126() {
        return b200007660126;
    }

    public void setB200007660126(BigDecimal b200007660126) {
        this.b200007660126 = b200007660126;
    }

    public BigDecimal getB200007660127() {
        return b200007660127;
    }

    public void setB200007660127(BigDecimal b200007660127) {
        this.b200007660127 = b200007660127;
    }

    public BigDecimal getB200007660128() {
        return b200007660128;
    }

    public void setB200007660128(BigDecimal b200007660128) {
        this.b200007660128 = b200007660128;
    }

    public BigDecimal getB200007660129() {
        return b200007660129;
    }

    public void setB200007660129(BigDecimal b200007660129) {
        this.b200007660129 = b200007660129;
    }

    public BigDecimal getB200007660130() {
        return b200007660130;
    }

    public void setB200007660130(BigDecimal b200007660130) {
        this.b200007660130 = b200007660130;
    }

    public BigDecimal getB200007660131() {
        return b200007660131;
    }

    public void setB200007660131(BigDecimal b200007660131) {
        this.b200007660131 = b200007660131;
    }

    public BigDecimal getB200007660132() {
        return b200007660132;
    }

    public void setB200007660132(BigDecimal b200007660132) {
        this.b200007660132 = b200007660132;
    }

    public BigDecimal getB200007660133() {
        return b200007660133;
    }

    public void setB200007660133(BigDecimal b200007660133) {
        this.b200007660133 = b200007660133;
    }

    public BigDecimal getB200007660134() {
        return b200007660134;
    }

    public void setB200007660134(BigDecimal b200007660134) {
        this.b200007660134 = b200007660134;
    }

    public BigDecimal getB200007660135() {
        return b200007660135;
    }

    public void setB200007660135(BigDecimal b200007660135) {
        this.b200007660135 = b200007660135;
    }

    public BigDecimal getB200007660136() {
        return b200007660136;
    }

    public void setB200007660136(BigDecimal b200007660136) {
        this.b200007660136 = b200007660136;
    }

    public BigDecimal getB200007660137() {
        return b200007660137;
    }

    public void setB200007660137(BigDecimal b200007660137) {
        this.b200007660137 = b200007660137;
    }

    public BigDecimal getB200007660138() {
        return b200007660138;
    }

    public void setB200007660138(BigDecimal b200007660138) {
        this.b200007660138 = b200007660138;
    }

    public BigDecimal getB200007660139() {
        return b200007660139;
    }

    public void setB200007660139(BigDecimal b200007660139) {
        this.b200007660139 = b200007660139;
    }

    public BigDecimal getB200007660140() {
        return b200007660140;
    }

    public void setB200007660140(BigDecimal b200007660140) {
        this.b200007660140 = b200007660140;
    }

    public BigDecimal getB200007660141() {
        return b200007660141;
    }

    public void setB200007660141(BigDecimal b200007660141) {
        this.b200007660141 = b200007660141;
    }

    public BigDecimal getB200007660142() {
        return b200007660142;
    }

    public void setB200007660142(BigDecimal b200007660142) {
        this.b200007660142 = b200007660142;
    }

    public BigDecimal getB200007660143() {
        return b200007660143;
    }

    public void setB200007660143(BigDecimal b200007660143) {
        this.b200007660143 = b200007660143;
    }

    public BigDecimal getB200007660144() {
        return b200007660144;
    }

    public void setB200007660144(BigDecimal b200007660144) {
        this.b200007660144 = b200007660144;
    }

    public BigDecimal getB200007660145() {
        return b200007660145;
    }

    public void setB200007660145(BigDecimal b200007660145) {
        this.b200007660145 = b200007660145;
    }

    public BigDecimal getB200007660146() {
        return b200007660146;
    }

    public void setB200007660146(BigDecimal b200007660146) {
        this.b200007660146 = b200007660146;
    }

    public BigDecimal getB200007660147() {
        return b200007660147;
    }

    public void setB200007660147(BigDecimal b200007660147) {
        this.b200007660147 = b200007660147;
    }

    public BigDecimal getB200007660148() {
        return b200007660148;
    }

    public void setB200007660148(BigDecimal b200007660148) {
        this.b200007660148 = b200007660148;
    }

    public BigDecimal getB200007660149() {
        return b200007660149;
    }

    public void setB200007660149(BigDecimal b200007660149) {
        this.b200007660149 = b200007660149;
    }

    public BigDecimal getB200007660150() {
        return b200007660150;
    }

    public void setB200007660150(BigDecimal b200007660150) {
        this.b200007660150 = b200007660150;
    }

    public BigDecimal getB200007660151() {
        return b200007660151;
    }

    public void setB200007660151(BigDecimal b200007660151) {
        this.b200007660151 = b200007660151;
    }

    public BigDecimal getB200007660152() {
        return b200007660152;
    }

    public void setB200007660152(BigDecimal b200007660152) {
        this.b200007660152 = b200007660152;
    }

    public BigDecimal getB200007660153() {
        return b200007660153;
    }

    public void setB200007660153(BigDecimal b200007660153) {
        this.b200007660153 = b200007660153;
    }

    public BigDecimal getB2000076602() {
        return b2000076602;
    }

    public void setB2000076602(BigDecimal b2000076602) {
        this.b2000076602 = b2000076602;
    }

    public BigDecimal getB200007660201() {
        return b200007660201;
    }

    public void setB200007660201(BigDecimal b200007660201) {
        this.b200007660201 = b200007660201;
    }

    public BigDecimal getB200007660202() {
        return b200007660202;
    }

    public void setB200007660202(BigDecimal b200007660202) {
        this.b200007660202 = b200007660202;
    }

    public BigDecimal getB200007660203() {
        return b200007660203;
    }

    public void setB200007660203(BigDecimal b200007660203) {
        this.b200007660203 = b200007660203;
    }

    public BigDecimal getB200007660204() {
        return b200007660204;
    }

    public void setB200007660204(BigDecimal b200007660204) {
        this.b200007660204 = b200007660204;
    }

    public BigDecimal getB200007660205() {
        return b200007660205;
    }

    public void setB200007660205(BigDecimal b200007660205) {
        this.b200007660205 = b200007660205;
    }

    public BigDecimal getB200007660206() {
        return b200007660206;
    }

    public void setB200007660206(BigDecimal b200007660206) {
        this.b200007660206 = b200007660206;
    }

    public BigDecimal getB200007660207() {
        return b200007660207;
    }

    public void setB200007660207(BigDecimal b200007660207) {
        this.b200007660207 = b200007660207;
    }

    public BigDecimal getB200007660208() {
        return b200007660208;
    }

    public void setB200007660208(BigDecimal b200007660208) {
        this.b200007660208 = b200007660208;
    }

    public BigDecimal getB200007660209() {
        return b200007660209;
    }

    public void setB200007660209(BigDecimal b200007660209) {
        this.b200007660209 = b200007660209;
    }

    public BigDecimal getB200007660210() {
        return b200007660210;
    }

    public void setB200007660210(BigDecimal b200007660210) {
        this.b200007660210 = b200007660210;
    }

    public BigDecimal getB200007660211() {
        return b200007660211;
    }

    public void setB200007660211(BigDecimal b200007660211) {
        this.b200007660211 = b200007660211;
    }

    public BigDecimal getB200007660212() {
        return b200007660212;
    }

    public void setB200007660212(BigDecimal b200007660212) {
        this.b200007660212 = b200007660212;
    }

    public BigDecimal getB200007660213() {
        return b200007660213;
    }

    public void setB200007660213(BigDecimal b200007660213) {
        this.b200007660213 = b200007660213;
    }

    public BigDecimal getB200007660214() {
        return b200007660214;
    }

    public void setB200007660214(BigDecimal b200007660214) {
        this.b200007660214 = b200007660214;
    }

    public BigDecimal getB200007660215() {
        return b200007660215;
    }

    public void setB200007660215(BigDecimal b200007660215) {
        this.b200007660215 = b200007660215;
    }

    public BigDecimal getB200007660216() {
        return b200007660216;
    }

    public void setB200007660216(BigDecimal b200007660216) {
        this.b200007660216 = b200007660216;
    }

    public BigDecimal getB200007660217() {
        return b200007660217;
    }

    public void setB200007660217(BigDecimal b200007660217) {
        this.b200007660217 = b200007660217;
    }

    public BigDecimal getB200007660218() {
        return b200007660218;
    }

    public void setB200007660218(BigDecimal b200007660218) {
        this.b200007660218 = b200007660218;
    }

    public BigDecimal getB200007660219() {
        return b200007660219;
    }

    public void setB200007660219(BigDecimal b200007660219) {
        this.b200007660219 = b200007660219;
    }

    public BigDecimal getB200007660220() {
        return b200007660220;
    }

    public void setB200007660220(BigDecimal b200007660220) {
        this.b200007660220 = b200007660220;
    }

    public BigDecimal getB200007660221() {
        return b200007660221;
    }

    public void setB200007660221(BigDecimal b200007660221) {
        this.b200007660221 = b200007660221;
    }

    public BigDecimal getB200007660222() {
        return b200007660222;
    }

    public void setB200007660222(BigDecimal b200007660222) {
        this.b200007660222 = b200007660222;
    }

    public BigDecimal getB200007660223() {
        return b200007660223;
    }

    public void setB200007660223(BigDecimal b200007660223) {
        this.b200007660223 = b200007660223;
    }

    public BigDecimal getB200007660224() {
        return b200007660224;
    }

    public void setB200007660224(BigDecimal b200007660224) {
        this.b200007660224 = b200007660224;
    }

    public BigDecimal getB200007660225() {
        return b200007660225;
    }

    public void setB200007660225(BigDecimal b200007660225) {
        this.b200007660225 = b200007660225;
    }

    public BigDecimal getB200007660226() {
        return b200007660226;
    }

    public void setB200007660226(BigDecimal b200007660226) {
        this.b200007660226 = b200007660226;
    }

    public BigDecimal getB200007660227() {
        return b200007660227;
    }

    public void setB200007660227(BigDecimal b200007660227) {
        this.b200007660227 = b200007660227;
    }

    public BigDecimal getB200007660228() {
        return b200007660228;
    }

    public void setB200007660228(BigDecimal b200007660228) {
        this.b200007660228 = b200007660228;
    }

    public BigDecimal getB200007660229() {
        return b200007660229;
    }

    public void setB200007660229(BigDecimal b200007660229) {
        this.b200007660229 = b200007660229;
    }

    public BigDecimal getB200007660230() {
        return b200007660230;
    }

    public void setB200007660230(BigDecimal b200007660230) {
        this.b200007660230 = b200007660230;
    }

    public BigDecimal getB200007660231() {
        return b200007660231;
    }

    public void setB200007660231(BigDecimal b200007660231) {
        this.b200007660231 = b200007660231;
    }

    public BigDecimal getB200007660232() {
        return b200007660232;
    }

    public void setB200007660232(BigDecimal b200007660232) {
        this.b200007660232 = b200007660232;
    }

    public BigDecimal getB200007660233() {
        return b200007660233;
    }

    public void setB200007660233(BigDecimal b200007660233) {
        this.b200007660233 = b200007660233;
    }

    public BigDecimal getB200007660234() {
        return b200007660234;
    }

    public void setB200007660234(BigDecimal b200007660234) {
        this.b200007660234 = b200007660234;
    }

    public BigDecimal getB200007660235() {
        return b200007660235;
    }

    public void setB200007660235(BigDecimal b200007660235) {
        this.b200007660235 = b200007660235;
    }

    public BigDecimal getB200007660236() {
        return b200007660236;
    }

    public void setB200007660236(BigDecimal b200007660236) {
        this.b200007660236 = b200007660236;
    }

    public BigDecimal getB200007660237() {
        return b200007660237;
    }

    public void setB200007660237(BigDecimal b200007660237) {
        this.b200007660237 = b200007660237;
    }

    public BigDecimal getB200007660238() {
        return b200007660238;
    }

    public void setB200007660238(BigDecimal b200007660238) {
        this.b200007660238 = b200007660238;
    }

    public BigDecimal getB200007660239() {
        return b200007660239;
    }

    public void setB200007660239(BigDecimal b200007660239) {
        this.b200007660239 = b200007660239;
    }

    public BigDecimal getB200007660240() {
        return b200007660240;
    }

    public void setB200007660240(BigDecimal b200007660240) {
        this.b200007660240 = b200007660240;
    }

    public BigDecimal getB200007660241() {
        return b200007660241;
    }

    public void setB200007660241(BigDecimal b200007660241) {
        this.b200007660241 = b200007660241;
    }

    public BigDecimal getB200007660242() {
        return b200007660242;
    }

    public void setB200007660242(BigDecimal b200007660242) {
        this.b200007660242 = b200007660242;
    }

    public BigDecimal getB200007660243() {
        return b200007660243;
    }

    public void setB200007660243(BigDecimal b200007660243) {
        this.b200007660243 = b200007660243;
    }

    public BigDecimal getB200007660244() {
        return b200007660244;
    }

    public void setB200007660244(BigDecimal b200007660244) {
        this.b200007660244 = b200007660244;
    }

    public BigDecimal getB200007660245() {
        return b200007660245;
    }

    public void setB200007660245(BigDecimal b200007660245) {
        this.b200007660245 = b200007660245;
    }

    public BigDecimal getB200007660246() {
        return b200007660246;
    }

    public void setB200007660246(BigDecimal b200007660246) {
        this.b200007660246 = b200007660246;
    }

    public BigDecimal getB200007660247() {
        return b200007660247;
    }

    public void setB200007660247(BigDecimal b200007660247) {
        this.b200007660247 = b200007660247;
    }

    public BigDecimal getB200007660248() {
        return b200007660248;
    }

    public void setB200007660248(BigDecimal b200007660248) {
        this.b200007660248 = b200007660248;
    }

    public BigDecimal getB200007660249() {
        return b200007660249;
    }

    public void setB200007660249(BigDecimal b200007660249) {
        this.b200007660249 = b200007660249;
    }

    public BigDecimal getB200007660250() {
        return b200007660250;
    }

    public void setB200007660250(BigDecimal b200007660250) {
        this.b200007660250 = b200007660250;
    }

    public BigDecimal getB200007660251() {
        return b200007660251;
    }

    public void setB200007660251(BigDecimal b200007660251) {
        this.b200007660251 = b200007660251;
    }

    public BigDecimal getB200007660252() {
        return b200007660252;
    }

    public void setB200007660252(BigDecimal b200007660252) {
        this.b200007660252 = b200007660252;
    }

    public BigDecimal getB200007660253() {
        return b200007660253;
    }

    public void setB200007660253(BigDecimal b200007660253) {
        this.b200007660253 = b200007660253;
    }

    public BigDecimal getB200007660254() {
        return b200007660254;
    }

    public void setB200007660254(BigDecimal b200007660254) {
        this.b200007660254 = b200007660254;
    }

    public BigDecimal getB200007660255() {
        return b200007660255;
    }

    public void setB200007660255(BigDecimal b200007660255) {
        this.b200007660255 = b200007660255;
    }

    public BigDecimal getB200007660256() {
        return b200007660256;
    }

    public void setB200007660256(BigDecimal b200007660256) {
        this.b200007660256 = b200007660256;
    }

    public BigDecimal getB200007660257() {
        return b200007660257;
    }

    public void setB200007660257(BigDecimal b200007660257) {
        this.b200007660257 = b200007660257;
    }

    public BigDecimal getB200007660258() {
        return b200007660258;
    }

    public void setB200007660258(BigDecimal b200007660258) {
        this.b200007660258 = b200007660258;
    }

    public BigDecimal getB200007660259() {
        return b200007660259;
    }

    public void setB200007660259(BigDecimal b200007660259) {
        this.b200007660259 = b200007660259;
    }

    public BigDecimal getB200007660260() {
        return b200007660260;
    }

    public void setB200007660260(BigDecimal b200007660260) {
        this.b200007660260 = b200007660260;
    }

    public BigDecimal getB200007660261() {
        return b200007660261;
    }

    public void setB200007660261(BigDecimal b200007660261) {
        this.b200007660261 = b200007660261;
    }

    public BigDecimal getB2000076603() {
        return b2000076603;
    }

    public void setB2000076603(BigDecimal b2000076603) {
        this.b2000076603 = b2000076603;
    }

    public BigDecimal getB200007660301() {
        return b200007660301;
    }

    public void setB200007660301(BigDecimal b200007660301) {
        this.b200007660301 = b200007660301;
    }

    public BigDecimal getB200007660302() {
        return b200007660302;
    }

    public void setB200007660302(BigDecimal b200007660302) {
        this.b200007660302 = b200007660302;
    }

    public BigDecimal getB200007660303() {
        return b200007660303;
    }

    public void setB200007660303(BigDecimal b200007660303) {
        this.b200007660303 = b200007660303;
    }

    public BigDecimal getB200007660304() {
        return b200007660304;
    }

    public void setB200007660304(BigDecimal b200007660304) {
        this.b200007660304 = b200007660304;
    }

    public BigDecimal getB200007660305() {
        return b200007660305;
    }

    public void setB200007660305(BigDecimal b200007660305) {
        this.b200007660305 = b200007660305;
    }

    public BigDecimal getB20000766030501() {
        return b20000766030501;
    }

    public void setB20000766030501(BigDecimal b20000766030501) {
        this.b20000766030501 = b20000766030501;
    }

    public BigDecimal getB20000766030502() {
        return b20000766030502;
    }

    public void setB20000766030502(BigDecimal b20000766030502) {
        this.b20000766030502 = b20000766030502;
    }

    public BigDecimal getB200007660306() {
        return b200007660306;
    }

    public void setB200007660306(BigDecimal b200007660306) {
        this.b200007660306 = b200007660306;
    }

    public BigDecimal getB200007660307() {
        return b200007660307;
    }

    public void setB200007660307(BigDecimal b200007660307) {
        this.b200007660307 = b200007660307;
    }

    public BigDecimal getB200007660308() {
        return b200007660308;
    }

    public void setB200007660308(BigDecimal b200007660308) {
        this.b200007660308 = b200007660308;
    }

    public BigDecimal getB2000076604() {
        return b2000076604;
    }

    public void setB2000076604(BigDecimal b2000076604) {
        this.b2000076604 = b2000076604;
    }

    public BigDecimal getB2000076701() {
        return b2000076701;
    }

    public void setB2000076701(BigDecimal b2000076701) {
        this.b2000076701 = b2000076701;
    }

    public BigDecimal getB2000076702() {
        return b2000076702;
    }

    public void setB2000076702(BigDecimal b2000076702) {
        this.b2000076702 = b2000076702;
    }

    public BigDecimal getB2000076711() {
        return b2000076711;
    }

    public void setB2000076711(BigDecimal b2000076711) {
        this.b2000076711 = b2000076711;
    }

    public BigDecimal getB200007671101() {
        return b200007671101;
    }

    public void setB200007671101(BigDecimal b200007671101) {
        this.b200007671101 = b200007671101;
    }

    public BigDecimal getB20000767110101() {
        return b20000767110101;
    }

    public void setB20000767110101(BigDecimal b20000767110101) {
        this.b20000767110101 = b20000767110101;
    }

    public BigDecimal getB20000767110102() {
        return b20000767110102;
    }

    public void setB20000767110102(BigDecimal b20000767110102) {
        this.b20000767110102 = b20000767110102;
    }

    public BigDecimal getB20000767110103() {
        return b20000767110103;
    }

    public void setB20000767110103(BigDecimal b20000767110103) {
        this.b20000767110103 = b20000767110103;
    }

    public BigDecimal getB20000767110104() {
        return b20000767110104;
    }

    public void setB20000767110104(BigDecimal b20000767110104) {
        this.b20000767110104 = b20000767110104;
    }

    public BigDecimal getB20000767110105() {
        return b20000767110105;
    }

    public void setB20000767110105(BigDecimal b20000767110105) {
        this.b20000767110105 = b20000767110105;
    }

    public BigDecimal getB20000767110106() {
        return b20000767110106;
    }

    public void setB20000767110106(BigDecimal b20000767110106) {
        this.b20000767110106 = b20000767110106;
    }

    public BigDecimal getB20000767110107() {
        return b20000767110107;
    }

    public void setB20000767110107(BigDecimal b20000767110107) {
        this.b20000767110107 = b20000767110107;
    }

    public BigDecimal getB200007671102() {
        return b200007671102;
    }

    public void setB200007671102(BigDecimal b200007671102) {
        this.b200007671102 = b200007671102;
    }

    public BigDecimal getB20000767110201() {
        return b20000767110201;
    }

    public void setB20000767110201(BigDecimal b20000767110201) {
        this.b20000767110201 = b20000767110201;
    }

    public BigDecimal getB20000767110202() {
        return b20000767110202;
    }

    public void setB20000767110202(BigDecimal b20000767110202) {
        this.b20000767110202 = b20000767110202;
    }

    public BigDecimal getB20000767110203() {
        return b20000767110203;
    }

    public void setB20000767110203(BigDecimal b20000767110203) {
        this.b20000767110203 = b20000767110203;
    }

    public BigDecimal getB20000767110204() {
        return b20000767110204;
    }

    public void setB20000767110204(BigDecimal b20000767110204) {
        this.b20000767110204 = b20000767110204;
    }

    public BigDecimal getB20000767110205() {
        return b20000767110205;
    }

    public void setB20000767110205(BigDecimal b20000767110205) {
        this.b20000767110205 = b20000767110205;
    }

    public BigDecimal getB20000767110206() {
        return b20000767110206;
    }

    public void setB20000767110206(BigDecimal b20000767110206) {
        this.b20000767110206 = b20000767110206;
    }

    public BigDecimal getB20000767110207() {
        return b20000767110207;
    }

    public void setB20000767110207(BigDecimal b20000767110207) {
        this.b20000767110207 = b20000767110207;
    }

    public BigDecimal getB200007671103() {
        return b200007671103;
    }

    public void setB200007671103(BigDecimal b200007671103) {
        this.b200007671103 = b200007671103;
    }

    public BigDecimal getB200007671104() {
        return b200007671104;
    }

    public void setB200007671104(BigDecimal b200007671104) {
        this.b200007671104 = b200007671104;
    }

    public BigDecimal getB20000767110401() {
        return b20000767110401;
    }

    public void setB20000767110401(BigDecimal b20000767110401) {
        this.b20000767110401 = b20000767110401;
    }

    public BigDecimal getB20000767110402() {
        return b20000767110402;
    }

    public void setB20000767110402(BigDecimal b20000767110402) {
        this.b20000767110402 = b20000767110402;
    }

    public BigDecimal getB20000767110403() {
        return b20000767110403;
    }

    public void setB20000767110403(BigDecimal b20000767110403) {
        this.b20000767110403 = b20000767110403;
    }

    public BigDecimal getB20000767110404() {
        return b20000767110404;
    }

    public void setB20000767110404(BigDecimal b20000767110404) {
        this.b20000767110404 = b20000767110404;
    }

    public BigDecimal getB20000767110405() {
        return b20000767110405;
    }

    public void setB20000767110405(BigDecimal b20000767110405) {
        this.b20000767110405 = b20000767110405;
    }

    public BigDecimal getB20000767110406() {
        return b20000767110406;
    }

    public void setB20000767110406(BigDecimal b20000767110406) {
        this.b20000767110406 = b20000767110406;
    }

    public BigDecimal getB20000767110407() {
        return b20000767110407;
    }

    public void setB20000767110407(BigDecimal b20000767110407) {
        this.b20000767110407 = b20000767110407;
    }

    public BigDecimal getB200007671105() {
        return b200007671105;
    }

    public void setB200007671105(BigDecimal b200007671105) {
        this.b200007671105 = b200007671105;
    }

    public BigDecimal getB200007671106() {
        return b200007671106;
    }

    public void setB200007671106(BigDecimal b200007671106) {
        this.b200007671106 = b200007671106;
    }

    public BigDecimal getB200007671107() {
        return b200007671107;
    }

    public void setB200007671107(BigDecimal b200007671107) {
        this.b200007671107 = b200007671107;
    }

    public BigDecimal getB20000767110701() {
        return b20000767110701;
    }

    public void setB20000767110701(BigDecimal b20000767110701) {
        this.b20000767110701 = b20000767110701;
    }

    public BigDecimal getB20000767110702() {
        return b20000767110702;
    }

    public void setB20000767110702(BigDecimal b20000767110702) {
        this.b20000767110702 = b20000767110702;
    }

    public BigDecimal getB20000767110703() {
        return b20000767110703;
    }

    public void setB20000767110703(BigDecimal b20000767110703) {
        this.b20000767110703 = b20000767110703;
    }

    public BigDecimal getB200007671108() {
        return b200007671108;
    }

    public void setB200007671108(BigDecimal b200007671108) {
        this.b200007671108 = b200007671108;
    }

    public BigDecimal getB20000767110801() {
        return b20000767110801;
    }

    public void setB20000767110801(BigDecimal b20000767110801) {
        this.b20000767110801 = b20000767110801;
    }

    public BigDecimal getB20000767110802() {
        return b20000767110802;
    }

    public void setB20000767110802(BigDecimal b20000767110802) {
        this.b20000767110802 = b20000767110802;
    }

    public BigDecimal getB20000767110803() {
        return b20000767110803;
    }

    public void setB20000767110803(BigDecimal b20000767110803) {
        this.b20000767110803 = b20000767110803;
    }

    public BigDecimal getB20000767110804() {
        return b20000767110804;
    }

    public void setB20000767110804(BigDecimal b20000767110804) {
        this.b20000767110804 = b20000767110804;
    }

    public BigDecimal getB20000767110805() {
        return b20000767110805;
    }

    public void setB20000767110805(BigDecimal b20000767110805) {
        this.b20000767110805 = b20000767110805;
    }

    public BigDecimal getB20000767110806() {
        return b20000767110806;
    }

    public void setB20000767110806(BigDecimal b20000767110806) {
        this.b20000767110806 = b20000767110806;
    }

    public BigDecimal getB20000767110807() {
        return b20000767110807;
    }

    public void setB20000767110807(BigDecimal b20000767110807) {
        this.b20000767110807 = b20000767110807;
    }

    public BigDecimal getB20000767110808() {
        return b20000767110808;
    }

    public void setB20000767110808(BigDecimal b20000767110808) {
        this.b20000767110808 = b20000767110808;
    }

    public BigDecimal getB20000767110809() {
        return b20000767110809;
    }

    public void setB20000767110809(BigDecimal b20000767110809) {
        this.b20000767110809 = b20000767110809;
    }

    public BigDecimal getB20000767110810() {
        return b20000767110810;
    }

    public void setB20000767110810(BigDecimal b20000767110810) {
        this.b20000767110810 = b20000767110810;
    }

    public BigDecimal getB20000767110811() {
        return b20000767110811;
    }

    public void setB20000767110811(BigDecimal b20000767110811) {
        this.b20000767110811 = b20000767110811;
    }

    public BigDecimal getB20000767110812() {
        return b20000767110812;
    }

    public void setB20000767110812(BigDecimal b20000767110812) {
        this.b20000767110812 = b20000767110812;
    }

    public BigDecimal getB20000767110813() {
        return b20000767110813;
    }

    public void setB20000767110813(BigDecimal b20000767110813) {
        this.b20000767110813 = b20000767110813;
    }

    public BigDecimal getB2000076801() {
        return b2000076801;
    }

    public void setB2000076801(BigDecimal b2000076801) {
        this.b2000076801 = b2000076801;
    }

    public BigDecimal getB2000076901() {
        return b2000076901;
    }

    public void setB2000076901(BigDecimal b2000076901) {
        this.b2000076901 = b2000076901;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getSubjectMonth() {
        return subjectMonth;
    }

    public void setSubjectMonth(Integer subjectMonth) {
        this.subjectMonth = subjectMonth;
    }

    public BigDecimal getB200007222124() {
        return b200007222124;
    }

    public void setB200007222124(BigDecimal b200007222124) {
        this.b200007222124 = b200007222124;
    }

    public BigDecimal getB200007222125() {
        return b200007222125;
    }

    public void setB200007222125(BigDecimal b200007222125) {
        this.b200007222125 = b200007222125;
    }

    public BigDecimal getB20000767110501() {
        return b20000767110501;
    }

    public void setB20000767110501(BigDecimal b20000767110501) {
        this.b20000767110501 = b20000767110501;
    }

    public BigDecimal getB20000767110502() {
        return b20000767110502;
    }

    public void setB20000767110502(BigDecimal b20000767110502) {
        this.b20000767110502 = b20000767110502;
    }
}
