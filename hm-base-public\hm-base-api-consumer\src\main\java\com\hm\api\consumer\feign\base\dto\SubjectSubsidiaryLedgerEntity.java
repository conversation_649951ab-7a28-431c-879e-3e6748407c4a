package com.hm.api.consumer.feign.base.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @author: curry.wu
 * @create: 2021/3/11 15:13
 **/
@ApiModel(value = "科目明细账")
public class SubjectSubsidiaryLedgerEntity implements Serializable {

    private static final long serialVersionUID = 8607211540070863768L;

    @ApiModelProperty(value = "日期")
    private String date;

    @ApiModelProperty(value = "凭证字号")
    private String voucherNo;

    @ApiModelProperty(value = "科目编码")
    private String subjectNo;

    @ApiModelProperty(value = "科目名称")
    private String subjectName;

    @ApiModelProperty(value = "摘要")
    private String keyword;

    @ApiModelProperty(value = "借方金额")
    private BigDecimal debit;

    @ApiModelProperty(value = "贷方金额")
    private BigDecimal credit;

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public String getVoucherNo() {
        return voucherNo;
    }

    public void setVoucherNo(String voucherNo) {
        this.voucherNo = voucherNo;
    }

    public String getSubjectNo() {
        return subjectNo;
    }

    public void setSubjectNo(String subjectNo) {
        this.subjectNo = subjectNo;
    }

    public String getSubjectName() {
        return subjectName;
    }

    public void setSubjectName(String subjectName) {
        this.subjectName = subjectName;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public BigDecimal getDebit() {
        return debit;
    }

    public void setDebit(BigDecimal debit) {
        this.debit = debit;
    }

    public BigDecimal getCredit() {
        return credit;
    }

    public void setCredit(BigDecimal credit) {
        this.credit = credit;
    }

    @Override
    public String toString() {
        return "SubjectSubsidiaryLedgerEntity{" +
                "date='" + date + '\'' +
                ", voucherNo='" + voucherNo + '\'' +
                ", subjectNo='" + subjectNo + '\'' +
                ", subjectName='" + subjectName + '\'' +
                ", keyword='" + keyword + '\'' +
                ", debit=" + debit +
                ", credit=" + credit +
                '}';
    }

}
