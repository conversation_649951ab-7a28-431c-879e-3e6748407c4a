package com.hm.base.activiti.service;

import com.hm.base.activiti.dto.CompleteDto;
import com.hm.base.activiti.dto.HistoricDto;
import com.hm.base.activiti.vo.HistoricActivityVo;
import com.hm.base.activiti.vo.TaskVo;
import org.activiti.engine.history.HistoricActivityInstance;
import org.activiti.engine.runtime.ProcessInstance;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface ProcessService {
    ProcessInstance submitApply(String businessKey, String userId, String definitionKey, Map<String, Object> variables);

    void complete(String userId, CompleteDto dto);

    List<TaskVo> findTodoTasks(String userId, List<String> roleIdList, String processDefinitionKey);

    List<Map<String, Object>> findDoneTasks(String userId, String processDefinitionKey);

    String getProcessDefIdByInstanceId(String instanceId);

    String getExcGatewayExpression(String processDefId, String taskDefKey);

    void cancelApply(String instanceId, String deleteReason);

    void suspendOrActiveApply(String instanceId, String suspendState);

    List<HistoricActivityVo> selectHistoryList(HistoricDto historicActivity);

    void deleteCandidate(String taskId, String groupId);

    void addCandidate(String taskId, String groupId);

    List<HistoricActivityInstance> findHisTasks(String instanceId);

    void readResource(String pProcessInstanceId, HttpServletResponse response);

    Object businessHisList(String businessKey, String processDefinitionKey);
}
