package com.hm.base.activiti.exception;

import com.google.common.collect.ImmutableMap;
import com.hm.api.common.exception.BaseException;
import com.hm.api.common.exception.BaseRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.validation.ObjectError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 异常全局处理 <br/>
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {
    @Autowired(required = false)
    private HttpServletRequest request;

    @ExceptionHandler({MissingServletRequestParameterException.class, HttpRequestMethodNotSupportedException.class, BaseException.class, BaseRuntimeException.class})
    public ResponseEntity<Object> parameterExceptionHandler(Exception e) {
        log.warn(e.getMessage());
        return handleResponse(e.getMessage());
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<Object> parameterExceptionHandler(MethodArgumentNotValidException e) {
        log.warn("参数不合法：" + e.getMessage());
        BindingResult exceptions = e.getBindingResult();
        if (exceptions.hasErrors()) {
            List<ObjectError> errors = exceptions.getAllErrors();
            return handleResponse(errors.stream().map(DefaultMessageSourceResolvable::getDefaultMessage).collect(Collectors.joining(",")));
        }
        return handleResponse("参数不合法：" + e.getMessage());
    }

    @ExceptionHandler({Exception.class})
    public ResponseEntity<Object> handleException(Exception ex) {
        log.error(ex.getMessage(), ex);

        return handleResponse(ex.getMessage());
    }

    private ResponseEntity<Object> handleResponse(String message) {
        return (ResponseEntity.status(400)).body(ImmutableMap.of(
                "timestamp", new Date(),
                "status", 400,
                "error", "error",
                "message", message,
                "path", request.getServletPath())
        );
    }
}
