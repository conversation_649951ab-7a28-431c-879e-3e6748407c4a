package com.hm.base.activiti.service;

import com.hm.base.activiti.pojo.ProcessDefinition;
import com.hm.base.activiti.vo.DeploymentVo;
import com.hm.base.activiti.vo.UserTaskVo;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface ProcessDefinitionService {
    DeploymentVo deployProcessDefinition(MultipartFile file);

    DeploymentVo deployProcessDefinition(InputStream inputStream,String fileName);

    List<ProcessDefinition> listProcessDefinition(ProcessDefinition processDefinition);

    void deleteProcessDeploymentByIds(List<String> ids);

    void suspendOrActiveApply(String id, String suspendState);

    List<UserTaskVo> selectUserTask(String processDefinitionId);
}
