package com.hm.base.activiti.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("任务列表 响应类")
public class TaskVo {
    @ApiModelProperty("处理任务 变量 key值")
    private String variables;

    @ApiModelProperty("业务系统对接的业务key")
    private String businessKey;

    @ApiModelProperty("任务id")
    private String taskId;

    @ApiModelProperty("实例id")
    private String instanceId;

    @ApiModelProperty("任务名称")
    private String taskName;
}
