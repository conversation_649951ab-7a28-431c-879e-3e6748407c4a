package com.hm.base.activiti.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.activiti.engine.impl.persistence.entity.ExecutionEntity;

/**
 * <AUTHOR>
 */
@ApiModel("提交任务 响应类")
public class ExecutionEntityVo {
    @ApiModelProperty("实例ID")
    private String processInstanceId;
    @ApiModelProperty("流程定义ID")
    private String processDefinitionId;
    @ApiModelProperty("流程定义名称")
    private String processDefinitionName;
    @ApiModelProperty("流程定义key")
    private String processDefinitionKey;

    private ExecutionEntity entity;

    public ExecutionEntityVo(ExecutionEntity entity) {
        this.entity = entity;
    }

    public String getProcessDefinitionKey() {
        return entity.getProcessDefinitionKey();
    }

    public String getProcessInstanceId() {
        return entity.getProcessInstanceId();
    }

    public String getProcessDefinitionId() {
        return entity.getProcessDefinitionId();
    }

    public String getProcessDefinitionName() {
        return entity.getProcessDefinitionName();
    }
}
