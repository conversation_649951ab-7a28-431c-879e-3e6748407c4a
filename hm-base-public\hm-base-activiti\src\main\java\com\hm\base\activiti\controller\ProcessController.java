package com.hm.base.activiti.controller;

import com.google.common.collect.Lists;
import com.hm.api.common.exception.BaseRuntimeException;
import com.hm.api.common.response.Result;
import com.hm.base.activiti.dto.CompleteDto;
import com.hm.base.activiti.dto.HistoricDto;
import com.hm.base.activiti.dto.SubmitDto;
import com.hm.base.activiti.service.ProcessService;
import com.hm.base.activiti.vo.BatchOperateResponse;
import com.hm.base.activiti.vo.ExecutionEntityVo;
import com.hm.base.activiti.vo.TaskVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.activiti.engine.impl.persistence.entity.ExecutionEntityImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 */
@Api(tags = "流程处理相关")
@RestController
@RequestMapping("/process")
public class ProcessController {
    @Autowired
    private ProcessService processService;
    @Autowired
    private HttpServletRequest request;

    @ApiOperation("提交流程")
    @PostMapping("/submit-apply")
    public ExecutionEntityVo submitApply(@RequestBody @Validated SubmitDto submitDto) {
        ExecutionEntityImpl instance = (ExecutionEntityImpl) processService.submitApply(submitDto.getBusinessKey(),
                submitDto.getUserId(), submitDto.getProcessDefinitionKey(), new HashMap<>());

        return new ExecutionEntityVo(instance);
    }

    @ApiOperation("待办列表")
    @GetMapping("/taskList")
    public List<TaskVo> taskList(@ApiParam(value = "用户id") @RequestParam String userId,
                                 @ApiParam(value = "角色集合") @RequestParam List<String> roleList,
                                 @ApiParam(value = "流程定义KEY") @RequestParam String processDefinitionKey) {
        return processService.findTodoTasks(userId, roleList, processDefinitionKey);
    }

    @ApiOperation("业务key已执行列表")
    @GetMapping("/businessHisList")
    public Object businessHisList(@ApiParam(value = "业务key") @RequestParam String businessKey,
                                  @ApiParam(value = "定义key") @RequestParam String processDefinitionKey) {
        return processService.businessHisList(businessKey, processDefinitionKey);
    }

    @ApiOperation("实例已执行列表")
    @GetMapping("/taskHisList")
    public Object taskHisList(@ApiParam(value = "实例id") @RequestParam String instanceId) {
        return processService.findHisTasks(instanceId);
    }

    @ApiOperation("已办列表")
    @GetMapping("/taskDoneList")
    public Object taskDoneList(@ApiParam(value = "用户id") @RequestParam String userId,
                               @ApiParam(value = "流程定义key") @RequestParam String processDefinitionKey) {
        return processService.findDoneTasks(userId, processDefinitionKey);
    }

    @ApiOperation("处理任务")
    @PostMapping("/complete")
    public Result<BatchOperateResponse> complete(@RequestBody @Validated CompleteDto dto) {
        return completes(Lists.newArrayList(dto));
    }

    @ApiOperation("处理任务")
    @PostMapping("/completes")
    public Result<BatchOperateResponse> completes(@RequestBody @Validated List<CompleteDto> params) {
        BatchOperateResponse response = new BatchOperateResponse(params.size());
        params.forEach(dto -> {
            try {
                processService.complete(dto.getUserId(), dto);
            } catch (Exception exception) {
                exception.printStackTrace();
                response.record(exception, "taskId", dto.getTaskId());
            }
        });
        return Result.success(response);
    }

    @ApiOperation("撤销任务")
    @PostMapping("/cancel-apply")
    public Result<?> cancelApply(@ApiParam(value = "实例id") @RequestParam String instanceId) {
        processService.cancelApply(instanceId, "用户撤销");
        return Result.success();
    }

    @ApiOperation("删除候选人")
    @DeleteMapping("/candidate/group")
    public Result<?> candidate(@ApiParam(value = "任务id") @RequestParam String taskId,
                               @ApiParam(value = "候选人组") @RequestParam String groupId) {
        processService.deleteCandidate(taskId, groupId);
        return Result.success();
    }

    @ApiOperation("新增候选人")
    @PostMapping("/candidate/group")
    public Result<?> saveCandidate(@ApiParam(value = "任务id") @RequestParam String taskId,
                                   @ApiParam(value = "候选人组") @RequestParam String groupId) {
        processService.addCandidate(taskId, groupId);
        return Result.success();
    }

    @ApiOperation("流程实例激活或挂起")
    @PostMapping("/suspendOrActiveApply")
    public Result<?> suspendOrActiveApply(@ApiParam(value = "实例id") @RequestParam String instanceId,
                                          @ApiParam(value = "1：挂起，2：激活") @RequestParam String suspendState) {
        processService.suspendOrActiveApply(instanceId, suspendState);
        return Result.success();
    }

    @ApiOperation("审批历史")
    @GetMapping("/history")
    public Object listHistory(HistoricDto dto) {
        if (StringUtils.isEmpty(dto.getInstanceId())) throw new BaseRuntimeException("PARAM_ERROR", "实例id不能为空");
        return processService.selectHistoryList(dto);
    }

    @ApiOperation("读取流程图片")
    @GetMapping(value = "/read-resource")
    public void readResource(@ApiParam(value = "实例id") @RequestParam String instanceId, HttpServletResponse response) {
        // 设置页面不缓存
        response.setHeader("Pragma", "No-cache");
        response.setHeader("Cache-Control", "no-cache");
        response.setDateHeader("Expires", 0);

        processService.readResource(instanceId, response);
    }
}
