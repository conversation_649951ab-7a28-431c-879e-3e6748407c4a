package com.hm.base.activiti.config;

import com.hm.base.activiti.service.ProcessDefinitionService;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.RepositoryService;
import org.activiti.engine.repository.ProcessDefinition;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class ApplicationConfigure {
    @Component
    public static class ActivitiProcessInitialize implements ApplicationRunner {
        @Autowired
        private ProcessDefinitionService processDefinitionService;
        @Autowired
        private RepositoryService repositoryService;

        @Override
        public void run(ApplicationArguments args) {
            try {
                Resource[] resources = new PathMatchingResourcePatternResolver().getResources("classpath:processes/*.bpmn");
                log.info("文件数量：{}", resources.length);
                Stream.of(resources).forEach(resource -> {
                    try {
                        String filename = Optional.ofNullable(resource.getFilename()).orElse("");

                        List<ProcessDefinition> list = repositoryService.createProcessDefinitionQuery()
                                .processDefinitionKey(filename.substring(0, filename.lastIndexOf(".")))
                                .list();

                        if (CollectionUtils.isEmpty(list)) {
                            processDefinitionService.deployProcessDefinition(resource.getInputStream(), filename);
                            log.info("流程文件 {} 初始化成功", filename);
                        }
                    } catch (Exception exception) {
                        log.warn("文件：{} 初始化失败，原因：{}", resource.getFilename(), exception.getMessage());
                    }
                });
            } catch (Exception exception) {
                log.warn("流程引擎文件初始化失败：" + exception.getMessage());
            }
        }
    }
}
