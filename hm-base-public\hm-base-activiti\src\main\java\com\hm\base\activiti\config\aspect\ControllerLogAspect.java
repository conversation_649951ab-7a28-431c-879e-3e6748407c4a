package com.hm.base.activiti.config.aspect;

import io.swagger.annotations.ApiOperation;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.*;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;

@Aspect
@Component
public class ControllerLogAspect {
    private static final Logger logger = LoggerFactory.getLogger(ControllerLogAspect.class);
    ThreadLocal<Long> startTime = new ThreadLocal<>();  //线程副本类:记录各个线程的开始时间

    /**
     * 1、execution 表达式主体
     * 2、第1个* 表示返回值类型  *表示所有类型
     * 3、包名  com.*.*.controller下
     * 4、第5个* 类名，com.*.*.controller包下所有类
     * 5、第6个* 方法名，com.*.*.controller包下所有类所有方法
     * 6、(..) 表示方法参数，..表示任何参数
     */
    @Pointcut("execution(public * com.hm.base.activiti.controller.*.*(..))")
    public void logAspect() {

    }

    @Before("logAspect()")
    public void before(JoinPoint joinPoint) {
        //info ,debug ,warn ,erro四种级别，这里我们注入info级别
        startTime.set(System.currentTimeMillis());
        //获取servlet请求对象---因为这不是控制器，这里不能注入HttpServletRequest，但springMVC本身提供ServletRequestAttributes可以拿到
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        // 获取方法上 ApiOperation 注解
        ApiOperation apiOperation = ((MethodSignature) joinPoint.getSignature()).getMethod().getAnnotation(ApiOperation.class);
        String apiOperationValue = "";
        if (null != apiOperation) {
            apiOperationValue = apiOperation.value();
        }
        logger.info("前置通知:" + "\r\n"
                + " 请求路径:" + request.getRequestURL().toString() + "\r\n"
                + " 请求方式:" + request.getMethod() + "\r\n"
                + " 请求方法:" + joinPoint.getSignature().getDeclaringTypeName() + "." + joinPoint.getSignature().getName() + "\r\n"
                + " 方法描述:" + apiOperationValue + "\r\n"
                + " 请求参数:" + Arrays.toString(joinPoint.getArgs()));
    }

    @Around("logAspect()")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        Object result = null;
        try {
            // 执行方法
            result = point.proceed();
        } catch (Exception e) {
            logger.error("捕获异常:" + e.getMessage());
            throw e;
        }
        return result;
    }

    // 方法的返回值注入给ret
    @AfterReturning(returning = "ret", pointcut = "logAspect()")
    public void afterReturning(Object ret) {
        logger.info("后置通知:" + "\r\n"
                + " 响应内容:" + ret);
    }

    @After(value = "logAspect()")
    public void after() {
        logger.info("最终通知:" + "\r\n"
                + " 请求耗时:" + (System.currentTimeMillis() - startTime.get()) + " 毫秒");
        // 使用完后清除ThreadLocal，以防内存泄露
        startTime.remove();
    }

}
