package com.hm.api.consumer.feign.apitax.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * @author: time.hou
 * @Description: 文件解析处理情况回调
 * @date: 2022-11-03 10:40
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class HmlTaxFileParseCallBackDto {
    /**
     * 解析任务id
     */
    @NotBlank(message = "parseId 不能为空")
    @ApiModelProperty(value = "解析任务id")
    private String parseId;
    /**
     * 解析状态：1-成功，2-失败，3-接口平台端完成
     */
    @NotNull(message = "parseStatus 不能为空")
    @ApiModelProperty(value = "解析状态：1-成功，2-失败，3-接口平台端完成")
    private Integer parseStatus;
    /**
     * 解析信息
     */
    @ApiModelProperty(value = "解析信息")
    private String parseMsg;
    /**
     * 解析过程中的错误信息列表
     */
    @ApiModelProperty(value = "解析过程中的错误信息列表")
    private List<Map> errorInfoList;


}
