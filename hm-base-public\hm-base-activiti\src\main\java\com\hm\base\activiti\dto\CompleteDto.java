package com.hm.base.activiti.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("处理任务实体类")
public class CompleteDto {
    @ApiModelProperty("用户ID")
    @NotNull(message = "用户id不能为空")
    private String userId;

    @ApiModelProperty("任务ID")
    @NotNull(message = "任务id不能为空")
    private String taskId;

    @ApiModelProperty("实例ID")
    @NotNull(message = "实例id不能为空")
    private String instanceId;

    @ApiModelProperty("任务变量")
    private Map<String, Object> variables;
}
