package com.hm.base.activiti.service.impl;

import cn.hutool.core.io.FileUtil;
import com.hm.api.common.exception.BizException;
import com.hm.base.activiti.pojo.ProcessDefinition;
import com.hm.base.activiti.service.ProcessDefinitionService;
import com.hm.base.activiti.vo.DeploymentVo;
import com.hm.base.activiti.vo.UserTaskVo;
import lombok.extern.slf4j.Slf4j;
import org.activiti.bpmn.model.BpmnModel;
import org.activiti.bpmn.model.FlowElement;
import org.activiti.bpmn.model.Process;
import org.activiti.bpmn.model.UserTask;
import org.activiti.engine.RepositoryService;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.impl.persistence.entity.ProcessDefinitionEntityImpl;
import org.activiti.engine.repository.Deployment;
import org.activiti.engine.repository.ProcessDefinitionQuery;
import org.activiti.engine.runtime.ProcessInstance;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.zip.ZipInputStream;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class ProcessDefinitionServiceImpl implements ProcessDefinitionService {
    @Autowired
    private RepositoryService repositoryService;
    @Autowired
    private RuntimeService runtimeService;
    @Value("${file.path}")
    private String path;

    @Override
    public DeploymentVo deployProcessDefinition(MultipartFile file) {
        String extensionName = Objects.requireNonNull(file.getOriginalFilename()).substring(file.getOriginalFilename().lastIndexOf('.') + 1).toLowerCase();
        if (!Arrays.asList("bpmn", "zip").contains(extensionName)) {
            throw new BizException("流程定义文件仅支持 bpmn, zip格式！");
        }
        try {
            return deployProcessDefinition(file.getInputStream(), file.getOriginalFilename());
        } catch (IOException exception) {
            log.error(exception.getMessage(), exception);
            return null;
        }
    }

    @Override
    public DeploymentVo deployProcessDefinition(InputStream inputStream, String fileName) {
        File deployFile = FileUtil.writeFromStream(inputStream, path + fileName);
        try (FileInputStream newInputStream = new FileInputStream(deployFile);) {
            Deployment deploy;
            if (fileName.endsWith(".zip")) {
                deploy = repositoryService.createDeployment()
                        .addZipInputStream(new ZipInputStream(newInputStream))
                        .deploy();
            } else if (fileName.endsWith(".bpmn")) {
                deploy = repositoryService.createDeployment()
                        .addInputStream(fileName, newInputStream)
                        .deploy();
            } else {
                throw new BizException("流程定义文件仅支持 bpmn, zip格式！");
            }
            ProcessDefinitionQuery processDefinitionQuery = repositoryService.createProcessDefinitionQuery();
            org.activiti.engine.repository.ProcessDefinition definition = processDefinitionQuery.deploymentId(deploy.getId()).singleResult();

            DeploymentVo deploymentVo = new DeploymentVo();
            deploymentVo.setDeploymentId(deploy.getId());
            deploymentVo.setProcessDefinitionKey(definition.getKey());
            deploymentVo.setProcessDefinitionId(definition.getId());
            return deploymentVo;
        } catch (IOException exception) {
            log.error(exception.getMessage(), exception);
            throw new BizException(exception.getMessage());
        } finally {
            IOUtils.closeQuietly(inputStream);
        }
    }

    @Override
    public List<ProcessDefinition> listProcessDefinition(ProcessDefinition processDefinition) {
        ProcessDefinitionQuery processDefinitionQuery = repositoryService.createProcessDefinitionQuery();
        processDefinitionQuery.orderByProcessDefinitionId().orderByProcessDefinitionVersion().desc();
        if (StringUtils.isNotBlank(processDefinition.getName())) {
            processDefinitionQuery.processDefinitionNameLike("%" + processDefinition.getName() + "%");
        }
        if (StringUtils.isNotBlank(processDefinition.getKey())) {
            processDefinitionQuery.processDefinitionKeyLike("%" + processDefinition.getKey() + "%");
        }
        if (StringUtils.isNotBlank(processDefinition.getCategory())) {
            processDefinitionQuery.processDefinitionCategoryLike("%" + processDefinition.getCategory() + "%");
        }
        List<ProcessDefinition> list = new ArrayList<>();
        List<org.activiti.engine.repository.ProcessDefinition> processDefinitionList = processDefinitionQuery.list();
        for (org.activiti.engine.repository.ProcessDefinition definition : processDefinitionList) {
            ProcessDefinitionEntityImpl entityImpl = (ProcessDefinitionEntityImpl) definition;
            ProcessDefinition entity = new ProcessDefinition();
            entity.setId(definition.getId());
            entity.setKey(definition.getKey());
            entity.setName(definition.getName());
            entity.setCategory(definition.getCategory());
            entity.setVersion(definition.getVersion());
            entity.setDescription(definition.getDescription());
            entity.setDeploymentId(definition.getDeploymentId());
            Deployment deployment = repositoryService.createDeploymentQuery()
                    .deploymentId(definition.getDeploymentId())
                    .singleResult();
            entity.setDeploymentTime(deployment.getDeploymentTime());
            entity.setDiagramResourceName(definition.getDiagramResourceName());
            entity.setResourceName(definition.getResourceName());
            entity.setSuspendState(entityImpl.getSuspensionState() + "");
            if (entityImpl.getSuspensionState() == 1) {
                entity.setSuspendStateName("已激活");
            } else {
                entity.setSuspendStateName("已挂起");
            }
            list.add(entity);
        }
        return list;
    }

    @Override
    public void deleteProcessDeploymentByIds(List<String> ids) {
        for (String deploymentId : ids) {
            List<ProcessInstance> instanceList = runtimeService.createProcessInstanceQuery()
                    .deploymentId(deploymentId)
                    .list();
            if (!CollectionUtils.isEmpty(instanceList)) {
                throw new BizException("删除失败，存在运行中的流程实例");
            }
            // true 表示级联删除引用，比如 act_ru_execution 数据
            try {
                repositoryService.deleteDeployment(deploymentId, true);
            } catch (Exception e) {
                throw new BizException(e.getMessage());
            }
        }
    }

    @Override
    public void suspendOrActiveApply(String id, String suspendState) {
        if ("1".equals(suspendState)) {
            // 当流程定义被挂起时，已经发起的该流程定义的流程实例不受影响（如果选择级联挂起则流程实例也会被挂起）。
            // 当流程定义被挂起时，无法发起新的该流程定义的流程实例。
            // 直观变化：act_re_procdef 的 SUSPENSION_STATE_ 为 2
            repositoryService.suspendProcessDefinitionById(id);
        } else if ("2".equals(suspendState)) {
            repositoryService.activateProcessDefinitionById(id);
        }
    }

    @Override
    public List<UserTaskVo> selectUserTask(String processDefinitionId) {
        BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinitionId);
        List<Process> processes = bpmnModel.getProcesses();
        List<UserTaskVo> list = new ArrayList<>();
        Process process = processes.get(0);
        for (FlowElement flowElement : process.getFlowElements()) {
            if (flowElement instanceof UserTask) {
                UserTask element = (UserTask) flowElement;
                UserTaskVo userTaskVo = new UserTaskVo();
                userTaskVo.setAttributes(element.getAttributes());
                userTaskVo.setName(element.getName());
                userTaskVo.setId(userTaskVo.getId());
                list.add(userTaskVo);
            }
        }
        return list;
    }
}
