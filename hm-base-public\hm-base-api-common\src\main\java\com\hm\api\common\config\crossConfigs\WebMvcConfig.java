package com.hm.api.common.config.crossConfigs;

import com.hm.api.common.config.interCeptor.MDCInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport;

/**
 * <AUTHOR> @Time 2019/4/10 17:20
 * @Matt 2022/03/25
 */

/**
 * springboot整合swagger2,设置静态文件访问的config类如下所示,
 * 在高版本的springboot项目中,原来的继承WebMvcConfigurerAdapter类,已经过时,
 * 代替方法有下面两种:
 * 1.继承WebMvcConfigurationSupport类,addResourceHandlers(ResourceHandlerRegistry registry)方法中最后要加上:
 * super.addResourceHandlers(registry);
 * 2.实现WebMvcConfigurer接口,实现接口的话,addResourceHandlers(ResourceHandlerRegistry registry)方法中最后不需要加上:
 * super.addResourceHandlers(registry);
 */
@Configuration
public class WebMvcConfig extends WebMvcConfigurationSupport {

    @Autowired
    MDCInterceptor mdcInterceptor;

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {

        registry.addResourceHandler("swagger-ui.html", "doc.html")
                .addResourceLocations("classpath:/META-INF/resources/");
        registry.addResourceHandler("/webjars/**")
                .addResourceLocations("classpath:/META-INF/resources/webjars/");
        super.addResourceHandlers(registry);
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {

        registry.addInterceptor(mdcInterceptor).order(2)
                .addPathPatterns("/**")
                .excludePathPatterns("/login/**")
                .excludePathPatterns("/test/**")
                .excludePathPatterns("/operationLog/**")
                .excludePathPatterns("/WechatRegisterController/**")
                .excludePathPatterns("/error", "/swagger-resources/**", "/webjars/**", "/v2/**", "/swagger-ui.html/**");
    }

}
