package com.hm.base.activiti.pojo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class ProcessDefinition implements Serializable {
    private String id;

    private String name;

    private String key;

    private int version;

    private String category;

    private String description;

    private String deploymentId;

    private Date deploymentTime;

    private String diagramResourceName;

    private String resourceName;

    private String suspendState;

    private String suspendStateName;
}
